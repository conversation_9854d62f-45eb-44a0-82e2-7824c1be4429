import react from "@vitejs/plugin-react";

import { defineConfig } from "vite";

import path from "path";

import { viteStaticCopy } from "vite-plugin-static-copy";
import svgr from "vite-plugin-svgr";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    svgr({ include: "**/*.svg" }),
    viteStaticCopy({
      targets: [
        {
          src: "bin/example.wasm",
          dest: "wasm-files",
        },
      ],
    }),
  ],
  resolve: {
    alias: {
      // eslint-disable-next-line no-undef
      "@": path.resolve(__dirname, "src"),
    },
  },
  esbuild: {
    pure: ["console.log"],
  },
  build: {
    rollupOptions: {
      output: {
        entryFileNames: "static/user/react/[name].[hash].js",
        chunkFileNames: "static/user/react/[name].[hash].js",
        assetFileNames: "static/user/react/[name].[ext]",
      },
    },
  },
});
