version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 20
  build:
    commands:
      - echo "VITE_ENVIRONMENT=${VITE_ENVIRONMENT}" >> .env
      - echo "VITE_APP_API=${VITE_APP_API}" >> .env
      - echo "VITE_APP_API_V2=${VITE_APP_API_V2}" >> .env
      - echo "VITE_GOOGLE_API_KEY=${VITE_GOOGLE_API_KEY}" >> .env
      - echo "VITE_STORAGE_URL=${VITE_STORAGE_URL}" >> .env
      - npm install -g yarn
      - yarn install
      - yarn build

artifacts:
  files:
    - appspec.yml
    - scripts/**
    - dist/**/*
  discard-paths: no

cache:
  paths:
    - 'node_modules/**/*'
