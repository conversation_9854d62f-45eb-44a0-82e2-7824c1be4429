/**
 * null 이나 빈값을 기본값으로 변경
 * @param str            입력값
 * @param defaultVal    기본값(옵션)
 * @returns {String}    체크 결과값
 */
export function nvl(str, defaultVal) {
  var defaultValue = "";

  if (typeof defaultVal != "undefined") {
    defaultValue = defaultVal;
  }

  if (typeof str == "undefined" || str == null || str == "" || str == "undefined") {
    return defaultValue;
  }

  return str;
}

export function saveToLocalStorage(key, value) {
  localStorage.setItem(key, value);
}

export function getFromLocalStorage(key) {
  return localStorage.getItem(key);
}

export function removeFromLocalStorage(key) {
  localStorage.removeItem(key);
}

export function clearLocalStorage() {
  localStorage.clear();
}
