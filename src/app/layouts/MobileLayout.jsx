import { Fragment, useEffect } from "react";
import MobileFooter from "@/app/components/mobile/Footer";
import roading_payment_wh from "@/assets/mobile/images/loading/roading_payment_wh.gif";
import MobileHeader from "@/app/components/mobile/Header";
import MobileLoading from "@/app/components/mobile/MobileLoading";
import { useDispatch } from "react-redux";
import { actionGetCountryList, actionGetUserInfo } from "@/store/userSlice";
import AirCompareSchedule from "@/app/pages/mobile/travel/overseas/include/AirCompareSchedule";
import { MOBILE_URL } from "@/constants/url";
import { actionTravelRuleBase } from "@/store/airSlice";
import ProgressBar from "@/app/components/mobile/common/ProgressBar";
import "@/styles/mobile/css/index.css";

const EXCLUDE_FOOTER_PAGES = [MOBILE_URL.OverseasAirBooking, MOBILE_URL.TravelAirView, MOBILE_URL.JoinForm];

const MobileLayout = (props) => {
  const { children } = props;
  const isShowFooter = !EXCLUDE_FOOTER_PAGES.some((path) => location.pathname.includes(path.split(":")[0]));
  const dispatch = useDispatch();

  useEffect(() => {
    if (!window.location.href.includes(MOBILE_URL.JoinForm)) {
      dispatch(actionGetUserInfo());
      dispatch(actionGetCountryList());
      dispatch(actionTravelRuleBase());
    }
  }, [dispatch]);

  return (
    <Fragment>
      <MobileHeader />
      <div>
        {children}
        {isShowFooter && <MobileFooter />}
      </div>
      <MobileLoading />
      <ProgressBar />
      <div id="popPayIng" className="modal-wrap">
        <div className="modal-cotn">
          <p className="load-img">
            <img src={roading_payment_wh} alt="로딩 이미지" />
          </p>
          <p className="tit">결제 진행 중입니다.</p>
          <dl>
            <dt>결제 완료까지 다소 시간이 걸릴 수 있습니다.</dt>
            <dd>
              결제 진행 중 브라우저를 닫거나, <br />
              새로고침 하시면 결제 오류가 발생할 수 있습니다. <br />
              결제가 완료 될 때 까지 잠시만 기다려주세요.
            </dd>
          </dl>
        </div>
      </div>
      <AirCompareSchedule />
    </Fragment>
  );
};

export default MobileLayout;
