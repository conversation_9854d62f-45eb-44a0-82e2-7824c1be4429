import { useEffect } from "react";
import { useDispatch } from "react-redux";
import Header from "@/app/components/user_v2/Header";
import Footer from "@/app/components/user_v2/Footer";
import { actionGetUserInfo, actionGetCountryList, actionGetRewardMileSetting } from "@/store/userSlice";

const PrivateLayout = (props) => {
  const { children } = props;
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(actionGetUserInfo());
    dispatch(actionGetCountryList());
    dispatch(actionGetRewardMileSetting());
  }, []);

  return (
    <div id="wrap">
      <Header />
      {children}
      <Footer />
    </div>
  );
};

export default PrivateLayout;
