import { Fragment, useLayoutEffect } from "react";
import { useLocation } from "react-router-dom";
import Loading from "@/app/components/user_v2/common/Loading";
import "@/styles/user_v2/index.css";

const DefaultLayout = (props) => {
  const { children } = props;
  const location = useLocation();

  useLayoutEffect(() => {
    document.documentElement.scrollTo({ top: 0, left: 0, behavior: "instant" });
  }, [location.pathname]);

  return (
    <Fragment>
      {children}
      <Loading />
    </Fragment>
  );
};

export default DefaultLayout;
