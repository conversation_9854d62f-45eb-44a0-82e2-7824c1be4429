import useMobile from "@/app/hooks/useMobile";
import { EMAIL_REGEX } from "@/constants/app";
import { getCompanyByDomain, login } from "@/service/login";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";
import { lazy, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { MOBILE_URL } from "@/constants/url";
import { getFromLocalStorage, removeFromLocalStorage, saveToLocalStorage } from "@/common";

const GENERAL_VERSION_DOMAIN = import.meta.env.VITE_GENERAL_VERSION_DOMAIN;

const MLogin = lazy(() => import("@/app/pages/mobile/MLogin"));
const LoginNor = lazy(() => import("@/app/pages/user_v2/LoginNor"));

export default function Login() {
  const isMobile = useMobile();
  const navigate = useNavigate();

  const [company, setCompany] = useState({});
  const [isGeneralVersion, setIsGeneralVersion] = useState(false);
  const [dataLogin, setDataLogin] = useState({
    email: "",
    password: "",
  });
  const [errorLoginMessage, setErrorLoginMessage] = useState("");
  const [isSaveLoginId, setIsSaveLoginId] = useState(false);

  const handleValidate = () => {
    if (dataLogin.email == "") {
      setErrorLoginMessage("이메일을 입력해주세요.");
      return false;
    }

    if (!EMAIL_REGEX.test(dataLogin.email)) {
      setErrorLoginMessage("유효하지 않은 이메일 주소입니다.");
      return false;
    }

    if (dataLogin.password == "") {
      setErrorLoginMessage("비밀번호를 입력해주세요.");
      return false;
    }

    return true;
  };

  const handleLogin = async () => {
    setErrorLoginMessage("");
    try {
      if (!handleValidate()) return;
      const res = await login(dataLogin);

      const accessToken = res.data.accessToken;
      const decode = jwtDecode(accessToken);
      Cookies.remove("jwt");
      Cookies.set("jwt", accessToken, { expires: new Date(decode.exp * 1000) });

      if (isSaveLoginId) {
        saveToLocalStorage("loginId", dataLogin.email);
        saveToLocalStorage("isSaveLoginId", isSaveLoginId);
      } else {
        removeFromLocalStorage("loginId");
        removeFromLocalStorage("isSaveLoginId");
      }

      if (res.data?.needUpdatePassword) {
        navigate("/customer/v2/account/passwordUpdate");
      } else {
        if (isMobile) {
          window.location.href = MOBILE_URL.Main;
        } else {
          window.location.href = "/";
        }
      }
    } catch (error) {
      setErrorLoginMessage("로그인 정보가 맞지 않습니다. 이메일과 비밀번호를 확인해주세요.");
    }
  };

  useEffect(() => {
    if (window.location.hostname === GENERAL_VERSION_DOMAIN) {
      setIsGeneralVersion(true);
      return;
    }

    const data = {
      domain: window.location.hostname == "localhost" ? "hnbtms.tourvis.com" : window.location.hostname,
    };
    const fetchData = async () => {
      try {
        const res = await getCompanyByDomain(data);
        setCompany(res.data);
      } catch (error) {
        Cookies.remove("jwt");
        navigate("/accessDenied");
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    const loginId = getFromLocalStorage("loginId") || "";
    setDataLogin({ ...dataLogin, email: loginId });
    const isSaveLoginId = getFromLocalStorage("isSaveLoginId") === "true";
    setIsSaveLoginId(isSaveLoginId);
  }, []);

  return isMobile ? (
    <MLogin
      data={dataLogin}
      setDataLogin={setDataLogin}
      onLogin={handleLogin}
      errorLoginMessage={errorLoginMessage}
      setErrorLoginMessage={setErrorLoginMessage}
      company={company}
      isGeneralVersion={isGeneralVersion}
      isSaveLoginId={isSaveLoginId}
      setIsSaveLoginId={setIsSaveLoginId}
    />
  ) : (
    <LoginNor
      data={dataLogin}
      setDataLogin={setDataLogin}
      onLogin={handleLogin}
      errorLoginMessage={errorLoginMessage}
      setErrorLoginMessage={setErrorLoginMessage}
      company={company}
      isGeneralVersion={isGeneralVersion}
      isSaveLoginId={isSaveLoginId}
      setIsSaveLoginId={setIsSaveLoginId}
    />
  );
}
