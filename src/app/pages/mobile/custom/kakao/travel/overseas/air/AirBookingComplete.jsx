import { useAppDispatch, useAppSelector } from "@/store";
import { useParams } from "react-router-dom";
import { comma, divisionComma, returnPhoneNumber } from "@/utils/common";
import { useEffect, useMemo } from "react";
import { actionBtmsManager, actionReservationTravelView, selectBtmsManager4Visa, selectReservationTravelView } from "@/store/travelViewSlice";
import { appMobileLoading, getTravelCities } from "@/utils/app";
import { BTMS_MANAGER_CODE, TYPE_MOBILE_LOADING } from "@/constants/app";
import BookingCompleteDocumentNumber from "@/app/pages/mobile/travel/overseas/include/Booking/BookingCompleteDocumentNumber";
import "@/styles/mobile/css/search.css";

function AirBookingComplete() {
  const { id: travelId } = useParams();
  const dispatch = useAppDispatch();
  const {
    data: { travel = {} },
  } = useAppSelector(selectReservationTravelView);
  const btmsManager4Visa = useAppSelector(selectBtmsManager4Visa);
  const documentNumbers = useMemo(() => travel.documentNumbers?.map((item) => item.documentNo) ?? [], [travel.documentNumbers]);

  useEffect(() => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    Promise.all(
      [actionReservationTravelView]
        .map((action) => dispatch(action({ travelId })))
        .concat(dispatch(actionBtmsManager({ managerType: BTMS_MANAGER_CODE.PASSPORT_VISA }))),
    ).finally(() => appMobileLoading.off());
  }, [dispatch, travelId]);

  return (
    <>
      <div id="wrap">
        <div id="container" className="pg-search">
          <div className="contents">
            <div className="reserv-complete">
              <div className="box-top" style={{ height: "fit-content" }}>
                <dl>
                  <dt>
                    {getTravelCities(travel).join(" - ")}
                    &nbsp;
                    <br />
                    예약을 요청했습니다.
                  </dt>
                  <dd>
                    여행사 예약담당자와 최종 확인 후 발권이 진행 되며, <br />
                    예약시 기재하신 휴대폰번호/이메일로 예약 안내 메일이 나갈 예정입니다.
                    <br />
                    입국 시 비자 등 추가 필요 서류가 있을 경우 담당자가 함께 안내 드릴 예정입니다.
                  </dd>
                  <dd>
                    <a
                      style={{ color: "white", fontWeight: "bold" }}
                      href="https://www.0404.go.kr/dev/notice_view.mofa?id=ATC0000000009020&pagenum=1&mst_id=MST0000000000045&st=title&stext="
                      target="_blank"
                      className="txt-big"
                      rel="noopener noreferrer"
                    >
                      <b>외교부 해외안전여행 사이트 (각국의 입국허가 요건) 바로가기</b>
                    </a>
                    <br />- 지역별 무비자 입국과 비자가 필요한 국가 확인
                    <br />- 경로: 외교부&gt;영사서비스&gt;공지사항&gt;각국의 입국허가(여권, 사증 등) 요건 안내
                    {btmsManager4Visa && (
                      <>
                        <br />
                        비자 신청 문의 :{" "}
                        <a
                          style={{ color: "white" }}
                          href="tel: /* {{returnPhoneNumber btmsManager4Visa.travelAgencyUser.phoneNumber 'FIRST'}} - {{returnPhoneNumber btmsManager4Visa.travelAgencyUser.phoneNumber 'MIDDLE'}} - {{returnPhoneNumber btmsManager4Visa.travelAgencyUser.phoneNumber 'END'}} */"
                        >
                          {returnPhoneNumber(btmsManager4Visa.travelAgencyUser?.phoneNumber, "FIRST")} -
                          {returnPhoneNumber(btmsManager4Visa.travelAgencyUser?.phoneNumber, "MIDDLE")} -
                          {returnPhoneNumber(btmsManager4Visa.travelAgencyUser?.phoneNumber, "END")}
                        </a>
                      </>
                    )}
                  </dd>
                </dl>
                <div className="code">
                  BTMS 예약번호 <strong>{travelId}</strong>
                </div>
                <br />
              </div>
              <div className="box-middle fare-info">
                <p className="tit">총 예상 결제요금</p>
                <div className="inner">
                  <p className="person">
                    성인 <span>{travel.bookingAir?.adultCount}</span>명
                  </p>
                  <div className="clearfix">
                    <span className="txt">항공료</span>
                    <span className="val">
                      {divisionComma(travel.bookingAir?.fareAmount, travel.bookingAir?.adultCount)}원 X {travel.bookingAir?.adultCount}
                    </span>
                  </div>
                  <div className="clearfix">
                    <span className="txt">TAX</span>
                    <span className="val">
                      {divisionComma(travel.bookingAir?.taxAmount, travel.bookingAir?.adultCount)}원 X {travel.bookingAir?.adultCount}
                    </span>
                  </div>
                  <div className="clearfix">
                    <span className="txt">발권수수료</span>
                    <span className="val">
                      {divisionComma(travel.bookingAir?.commissionAmount, travel.bookingAir?.adultCount)}원 X {travel.bookingAir?.adultCount}
                    </span>
                  </div>
                </div>
                <div className="total">
                  <p className="txt">총 예상 결제금액</p>
                  <p className="val">{comma(travel.bookingAir?.totalAmount)}원</p>
                </div>
              </div>
              <div className="box-middle fare-info">
                <p className="tit">결재 요청 의견</p>
                <div className="inner">
                  <div className="clearfix">
                    <span className="txt">{travel.violationReason}</span>
                  </div>
                </div>
              </div>
              <BookingCompleteDocumentNumber travel={travel} documentNumbers={documentNumbers} />
              <div className="box-btn mt-5">
                <a href="/m/main" className="btns-cmm round-basic color-w w-100">
                  홈으로
                </a>
                <a href="/m/reservation/travel/list" className="btns-cmm round-basic color-b w-100">
                  예약 내역 보기
                </a>
              </div>
              <div className="btn-close">
                <a href="/m/overseas/air/main" className="btns-cmm"></a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="popViewTicketDateWarning" className="modal-wrap inner-haed type-alrt" style={{ padding: "15px", maxWidth: "480px" }}>
        <input type="hidden" name="isViewTicketDateWarning" value="{/* {{travel.bookingAir.isViewTicketDateWarning}} */}" />
        <div className="modal-cotn">
          <div>
            <dl>
              <p>
                예약하신 항공권은 출발일 임박 또는 항공사 발권시한이 임박한 예약으로 <br />
                여행사 담당자와 예약 확인 후 진행하셔야 하며, 결제 진행 시 <br />
                1:1문의로 확인 요청 또는 담당 여행사 직원에게 문의 부탁 드립니다. <br />
                <br />
                업무시간 (평일9시~18시) 내 연락처 : 02-6911-8185
                <br />
                업무시간 외 긴급서비스 번호 : 1544-2351
              </p>
            </dl>
          </div>
          <div className="box-btn" style={{ padding: "15px 0 0 0" }}>
            <a href="#" className="btns-cmm round-basic color-b w-50" rel="modal:close">
              닫기
            </a>
          </div>
        </div>
      </div>
    </>
  );
}

export default AirBookingComplete;
