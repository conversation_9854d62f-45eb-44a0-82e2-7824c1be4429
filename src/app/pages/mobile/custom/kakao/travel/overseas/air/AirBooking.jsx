import { useAppDispatch, useAppSelector } from "@/store";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useNavigate } from "react-router-dom";
import { MOBILE_URL } from "@/constants/url";
import AgreeRule from "@/app/pages/mobile/travel/overseas/include/Booking/AgreeRule";
import { comma, emailValidationCheck, getJourneyAmount, isIncludedCorporateFare } from "@/utils/common";
import { selectUserInfo } from "@/store/userSlice";
import PassengerConfirm from "@/app/pages/mobile/travel/overseas/include/Booking/PassengerConfirm";
import PassengerForm from "@/app/pages/mobile/travel/overseas/include/Booking/PassengerForm";
import { actionOpenAirCompareSchedule } from "@/store/mainSlice.mobile";
import SecScheduleInfo from "@/app/pages/mobile/travel/overseas/include/DetailInfoSearch/SecScheduleInfo";
import { momentKR } from "@/utils/date";
import DocumentAttachment from "@/app/pages/mobile/travel/overseas/include/Booking/DocumentAttachment";
import DocumentNumber from "@/app/pages/mobile/custom/kakao/travel/overseas/include/Booking/DocumentNumber";
import "@/styles/mobile/css/custom.css";
import "@/styles/mobile/css/booking.css";

const INIT_SINGLE_FORM_PASSENGERS = {
  userID: 0,
  dateOfBirth: "",
  gender: "Male",
  nationality: "KR",
  koreanName: "",
  englishFirstName: "",
  englishLastName: "",
  phoneNumber: "",
  email: "",
  passportDocumentNo: "",
  expireYear: momentKR().format("YYYY"),
  expireMonth: "01",
  expireDay: "01",
  passportIssuedByCode: "KR",
  mileageMemberships: [
    {
      airline: "",
      memberNo: "",
    },
    {
      airline: "",
      memberNo: "",
    },
  ],
};

function AirBooking() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { data, filter: params } = useAppSelector(selectAirOverseasSessionData);
  const { userInfo } = useAppSelector(selectUserInfo);
  const journeyAmount = getJourneyAmount(data);
  const initFormPassengers = Array.from({ length: Number(params.adultCount ?? 1) }, () => INIT_SINGLE_FORM_PASSENGERS);
  const [formPassengers, setFormPassengers] = useState(initFormPassengers);
  const [violationReason, setViolationReason] = useState("");
  const [isAgreeReserverInfo, setIsAgreeReserverInfo] = useState(false);
  const [isNameSamePassportRule, setIsNameSamePassportRule] = useState(false);
  const [confirmedRule, setConfirmedRule] = useState({
    ruleConfirmAllCheck: false,
    "reservationRuleAgreementDTO.isReservationRule": false,
    "reservationRuleAgreementDTO.isBaggageRule": false,
    "reservationRuleAgreementDTO.isPassportVisaEtcRule": false,
    "reservationRuleAgreementDTO.isTravelRule": false,
    "reservationRuleAgreementDTO.isAgreeServiceRule": false,
    "reservationRuleAgreementDTO.isAgreePrivacyRule": false,
    "reservationRuleAgreementDTO.isAgreePrivacyProvideRule": false,
  });
  const [passengerConfirm, setPassengerConfirm] = useState({ data: [], isOpen: false });
  const [activedElement, setActivedElement] = useState({ selAir: true, fareInfo: true, payOpinion: true, userInfo: true });
  const [attachFileList, setAttachFileList] = useState([]);
  const isDocumentNumberUse = userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberUse;
  const isDocumentNumberRequired = userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberRequired;
  const [documentNumberList, setDocumentNumberList] = useState([""]);

  const handleConfirmBooking = () => {
    const passengers = passengerConfirm.data;
    if (!userInfo.name?.trim()) {
      alert("예약자 한글이름을 입력해주세요.");
      return;
    }
    if (!userInfo.cellPhoneNumber?.trim()) {
      alert("예약자 휴대폰 번호를 입력해주세요.");
      return;
    }
    if (userInfo.cellPhoneNumber?.trim()?.length < 11) {
      alert("예약자 휴대폰 번호 11자리를 입력해주세요.");
      return;
    }
    if (!userInfo.email?.trim()) {
      alert("예약자 이메일을 입력해주세요.");
      return;
    }
    if (!emailValidationCheck(userInfo.email?.trim())) {
      alert("유효하지 않은 예약자 이메일 주소입니다.\n이메일 주소를 확인해 주세요.");
      return;
    }
    if (!isAgreeReserverInfo) {
      alert("예약자 정보 확인 및 동의를 체크해 주세요.");
      return;
    }
    if (passengers.some((item) => !item.koreanName?.trim())) {
      alert("탑승객 이름을 입력해주세요.");
      return;
    }
    if (passengers.some((item) => !item.englishLastName?.trim())) {
      alert("탑승객 영문 성을 입력해주세요.");
      return;
    }
    if (passengers.some((item) => item.englishLastName?.trim().length < 2)) {
      alert("탑승객 영문 성이 잘 못 되었습니다.");
      return;
    }
    if (passengers.some((item) => !item.englishFirstName?.trim())) {
      alert("탑승객 영문 이름을 입력해주세요.");
      return;
    }
    if (passengers.some((item) => item.englishFirstName?.trim().length < 2)) {
      alert("탑승객 영문 이름이 잘 못 되었습니다.");
      return;
    }
    if (passengers.some((item) => !item.dateOfBirth?.trim())) {
      alert("탑승객 생년월일을 입력해주세요.");
      return;
    }
    if (passengers.some((item) => !item.phoneNumber?.trim())) {
      alert("탑승객 휴대폰 번호를 입력해주세요.");
      return;
    }
    if (passengers.some((item) => item.phoneNumber?.trim()?.length < 11)) {
      alert("탑승객 휴대폰 번호 11자리를 입력해주세요.");
      return;
    }
    if (passengers.some((item) => !item.passportIssuedByCode?.trim())) {
      alert("탑승객 여권 발행국가를 선택해주세요.");
      return;
    }
    if (!isNameSamePassportRule) {
      alert("탑승객 정보 확인 및 동의를 체크해 주세요.");
      return;
    }
    if (Object.values(confirmedRule).includes(false)) {
      alert("예약 규정 동의를 체크해주세요.");
      return;
    }
    if (!violationReason.trim()) {
      alert("결재 요청 의견을 입력해주세요.");
      return;
    }
    if (isDocumentNumberUse && isDocumentNumberRequired && documentNumberList.some((value) => isEmpty(value))) {
      alert("문서번호를 입력하셔야 합니다.");
      return;
    }

    setPassengerConfirm((prev) => ({ ...prev, isOpen: true }));
  };

  const handleOpenAirCompareSchedule = () => {
    dispatch(actionOpenAirCompareSchedule({ isOnlyView: true }));
  };

  useEffect(() => {
    if (isEmpty(data)) {
      navigate(MOBILE_URL.OverseasAirMain);
    }
  }, [data, navigate]);

  useEffect(() => {
    const passengers = formPassengers.map((item) => {
      const { expireYear, expireMonth, expireDay, ...nextItem } = { ...item };
      nextItem.passportExpirationDate = expireYear && expireMonth && expireDay ? `${expireYear}-${expireMonth}-${expireDay}` : "";
      nextItem.dateOfBirth = momentKR(nextItem.dateOfBirth, "YYYYMMDD").format("YYYY-MM-DD");
      nextItem.englishFirstName = nextItem.englishFirstName.toUpperCase();
      nextItem.englishLastName = nextItem.englishLastName.toUpperCase();
      nextItem.phoneNumber = nextItem.phoneNumber.replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3");
      return nextItem;
    });
    setPassengerConfirm((prev) => ({ ...prev, data: passengers }));
  }, [formPassengers]);

  return (
    <div className="bg-gray pt-[57px]">
      <div className="head-fixed">
        <header className="page-header type-black">
          <h1 className="pg-title">예약 요청</h1>
          <a href="/m/overseas/air/main" className="btn-close-x"></a>
        </header>
        <div id="container" className="pg-search">
          <form id="airBookingForm">
            <input type="radio" name="travelType" value="0" style={{ visibility: "hidden" }} />
            <div className="contents reserv-request">
              <div className={`box-item sel-air ${activedElement.selAir ? "active" : ""}`}>
                <dl>
                  <dt>선택한 항공편</dt>
                  <dd>
                    <div className="box-tab sec-schedule-info active">
                      {data?.flights?.map((item, index) => {
                        return <SecScheduleInfo data={item} key={`${item.journeyKey}_${index}`} />;
                      })}
                    </div>
                    {userInfo.workspace?.company?.btmsSetting?.isComparativePrice && (
                      <div className="bottom-btn">
                        <button type="button" className="btns-cmm round-basic color-w w-100" onClick={handleOpenAirCompareSchedule}>
                          비교 견적서
                        </button>
                      </div>
                    )}
                  </dd>
                </dl>
                <div className="box-btn-arrow">
                  <button type="button" className="btns-cmm" onClick={() => setActivedElement((prev) => ({ ...prev, selAir: !prev.selAir }))} />
                </div>
              </div>
              <div className={`box-item fare-info ${activedElement.fareInfo ? "active" : ""}`}>
                <dl>
                  <dt>요금 정보</dt>
                  <dd id="lowestDisplayLayout" style={{ display: "none" }}>
                    <div className="clearfix">
                      <span className="txt">최저가 요금</span>
                      <span id="lowestPriceStrong" className="val">
                        0원
                      </span>
                    </div>
                  </dd>
                  <dd>
                    <p className="tit">
                      성인 <span>{params.adultCount}</span>명
                    </p>
                    <div className="clearfix">
                      <span className="txt">항공료</span>
                      <span className="val">
                        {comma(journeyAmount.fareAmount)}원 x {params.adultCount}
                      </span>
                    </div>
                    <div className="clearfix">
                      <span className="txt">TAX</span>
                      <span className="val">
                        {comma(journeyAmount.taxAmount)}원 x {params.adultCount}
                      </span>
                    </div>
                    <div className="clearfix">
                      <span className="txt">발권수수료</span>
                      <span className="val" id="tasfAmountSpan">
                        {comma(journeyAmount.tasfAmount)}원 x {params.adultCount}
                      </span>
                    </div>
                    <div className="total">
                      <p className="txt">총 예상 결제금액</p>
                      <p className="val" name="totalPaymentStrong">
                        {comma((journeyAmount.fareAmount + journeyAmount.taxAmount + journeyAmount.tasfAmount) * params?.adultCount || 1)}원
                      </p>
                    </div>
                  </dd>
                </dl>
                <div className="box-btn-arrow">
                  <button type="button" className="btns-cmm" onClick={() => setActivedElement((prev) => ({ ...prev, fareInfo: !prev.fareInfo }))} />
                </div>
              </div>
              <div className={`box-item pay-opinion ${activedElement.payOpinion ? "active" : ""}`}>
                <dl>
                  <dt>결재 요청 의견</dt>
                  <dd>
                    <textarea
                      name="violationReason"
                      id="violationReason"
                      maxLength="500"
                      placeholder="- 비즈니스 클래스 발권 요청하는 경우 사유 작성 바랍니다. (ex임신부 등)&#13;&#10;- 규정 외 발권 요청이 없는 경우 “없음”으로 기재 바랍니다"
                      value={violationReason}
                      onChange={(event) => setViolationReason(event.target.value)}
                    />
                  </dd>
                </dl>
                <div className="box-btn-arrow">
                  <button
                    type="button"
                    className="btns-cmm"
                    onClick={() => setActivedElement((prev) => ({ ...prev, payOpinion: !prev.payOpinion }))}
                  />
                </div>
              </div>
              <div className={`box-item user-info ${activedElement.userInfo ? "active" : ""}`}>
                <div style={{ display: "none" }}>userInfo.name</div>
                <div style={{ display: "none" }}>userInfo.email</div>
                <dl>
                  <dt>예약자 정보</dt>
                  <dd>
                    <div className="box-gruoop">
                      <p className="form-tit ">
                        이름<span>*</span>
                      </p>
                      <div className="form-cn">
                        <span className="input-cmm">
                          <input type="text" id="name" name="travelers[0].name" value={userInfo.name} maxLength="20" readOnly />
                        </span>
                      </div>
                      <p className="form-tit ">
                        휴대전화<span>*</span>
                      </p>
                      <div className="form-cn">
                        <span className="input-cmm">
                          <input
                            type="text"
                            id="reservationCellPhoneNumber"
                            name="travelers[0].cellPhoneNumber"
                            value={userInfo.cellPhoneNumber}
                            maxLength="11"
                            readOnly
                          />
                        </span>
                        <span className="form-desc">예약에 문제가 있을 경우 입력하신 번호로 연락드립니다.</span>
                      </div>
                      <p className="form-tit ">
                        이메일 주소<span>*</span>
                      </p>
                      <div className="form-cn">
                        <span className="input-cmm">
                          <input type="text" id="email" name="travelers[0].email" value={userInfo.email} maxLength="100" readOnly />
                        </span>
                        <span className="form-desc">입력한 이메일 주소로 예약 확정 이메일을 발송합니다.</span>
                      </div>
                      <div className="box-agree">
                        <label className="form-chkbox">
                          <input
                            type="checkbox"
                            id="isAgreeReserverInfo"
                            name="isAgreeReserverInfo"
                            checked={isAgreeReserverInfo}
                            onChange={(event) => setIsAgreeReserverInfo(event.target.checked)}
                          />
                          <span>예약진행상황 및 정보전달을 위하여 정확한 연락처 및 이메일 정보를 입력했음에 동의합니다.</span>
                        </label>
                      </div>
                    </div>
                  </dd>
                </dl>
                <div className="box-btn-arrow">
                  <button type="button" className="btns-cmm" onClick={() => setActivedElement((prev) => ({ ...prev, userInfo: !prev.userInfo }))} />
                </div>
              </div>
              {Array.from({ length: Number(params.adultCount ?? 1) }, (_, i) => i + 1).map((item, index) => (
                <PassengerForm
                  key={item}
                  formIndex={index}
                  formItem={item}
                  formPassengers={formPassengers}
                  onChangeFormPassengers={setFormPassengers}
                  params={params}
                  isNameSamePassportRule={isNameSamePassportRule}
                  onChangeIsSameNamePassportRule={setIsNameSamePassportRule}
                  onResetFormPassengers={() => setFormPassengers(initFormPassengers)}
                />
              ))}
              <DocumentAttachment files={attachFileList} onChangeFiles={setAttachFileList} />
              {userInfo.workspace?.company?.btmsSetting?.isDocumentNumberUse && (
                <DocumentNumber documentNumberList={documentNumberList} onChange={setDocumentNumberList} />
              )}
              <AgreeRule confirmedRule={confirmedRule} onChangeConfirmedRule={setConfirmedRule} />
            </div>
            <div className="sec-bottom-price">
              <div className="total-price">
                <p className="tit">총 결제요금</p>
                <p className="fare" name="totalPaymentStrong">
                  {comma(journeyAmount.fareAmount + journeyAmount.taxAmount + journeyAmount.tasfAmount)}원
                </p>
                <p className="type">{isIncludedCorporateFare(data.flights) ? "기업요금" : ""}</p>
              </div>
              <div className="right-col">
                <button type="button" className="btns-cmm round-basic color-b w-90" onClick={handleConfirmBooking}>
                  예약 요청
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
      <PassengerConfirm
        isOpen={passengerConfirm.isOpen}
        passengers={passengerConfirm.data}
        violationReason={violationReason}
        documentNumbers={documentNumberList}
        attachFileList={attachFileList}
        checkedJourney={data}
        params={params}
        onClose={() => setPassengerConfirm((prev) => ({ ...prev, isOpen: false }))}
      />
    </div>
  );
}

export default AirBooking;
