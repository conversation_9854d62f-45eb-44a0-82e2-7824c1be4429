import SecScheduleInfo from "@/app/pages/mobile/travel/overseas/include/DetailInfoSearch/SecScheduleInfo";
import { comma, isIncludedCorporateFare } from "@/utils/common";
import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import FareCondition from "@/app/pages/mobile/custom/kakao/travel/overseas/include/DetailInfoSearch/FareCondition";
import TripRule from "@/app/pages/mobile/custom/kakao/travel/overseas/include/DetailInfoSearch/TripRule";

const secTab = [
  {
    type: "FlightDetailView",
    name: "여정정보",
  },
  {
    type: "FareRuleView",
    name: "운임규정",
  },
  {
    type: "MobileTravelRule",
    name: "출장규정",
  },
];

const getAmount = (data) => {
  let fareAmount = 0;
  let taxAmount = 0;

  data.forEach(({ fares }) => {
    fareAmount += fares.paxTypeFares[0].airFare;
    taxAmount += fares.paxTypeFares[0].airTax + fares.paxTypeFares[0].fuelChg;
  });

  return {
    fareAmount,
    taxAmount,
  };
};

const getCorporateFare = (data) => {
  const isInclude = isIncludedCorporateFare(data);
  return isInclude ? <span style={{ color: "#ff4e50" }}>기업 운임</span> : null;
};

function DetailInfoSearch(props) {
  const { open, onClose, journey, travelRule, isCompare, onSelectFare, onBooking } = props;
  const data = journey?.flights ?? [];
  const [tabType, setTabType] = useState("FlightDetailView");
  const { userInfo } = useAppSelector(selectUserInfo);

  const handleSelectTab = (currentTabType) => {
    setTabType(currentTabType);
  };
  return (
    <div className="layer-pages detail-info z-[111]" id="detailInfo" style={{ display: open ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">상세정보</p>
        <div className="sec-tab-top n03" hidden={isCompare}>
          <ul>
            {secTab.map(({ type, name }) => {
              return (
                <li key={type} className={`${tabType === type && "active"}`}>
                  <button type="button" onClick={() => handleSelectTab(type)}>
                    {name}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
      <div className={`layer-cotn tab-cotn ${isCompare ? "!pt-[58px]" : ""}`}>
        {tabType === "FlightDetailView" && (
          <div className="box-tab sec-schedule-info  active">
            {data.map((item, index) => {
              return <SecScheduleInfo data={item} key={index} />;
            })}
          </div>
        )}
        {tabType === "FareRuleView" && (
          <div className="box-tab fare-condition active">
            <FareCondition data={data} />
          </div>
        )}
        {tabType === "MobileTravelRule" && (
          <div className="box-tab trip-rule active">
            <TripRule data={{ ...data[0], flights: data }} travelRule={travelRule} />
          </div>
        )}
      </div>
      <div className="sec-bottom-price" hidden={isCompare}>
        <div className="total-price">
          <p className="tit">총 결제요금</p>
          <p className="fare">{comma(getAmount(data).fareAmount + getAmount(data).taxAmount)} 원</p>
          <p className="type">{getCorporateFare(data)}</p>
        </div>
        <div className="right-col">
          {userInfo.workspace?.company?.btmsSetting?.isComparativePrice ? (
            <button
              className="btns-cmm round-basic color-b w-75"
              onClick={() => {
                onClose();
                onSelectFare(journey);
                setTabType("FlightDetailView");
              }}
            >
              비교함 추가
            </button>
          ) : (
            <button
              className="btns-cmm round-basic color-b w-75"
              onClick={() => {
                onClose();
                onBooking(journey);
                setTabType("FlightDetailView");
              }}
            >
              예약하기
            </button>
          )}
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button
          type="button"
          className="btns-cmm"
          onClick={() => {
            onClose();
            setTabType("FlightDetailView");
          }}
        >
          닫기
        </button>
      </div>
    </div>
  );
}

export default DetailInfoSearch;
