import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { getFareRule } from "@/service/air";
import { useAppSelector } from "@/store";
import { selectAppLoading } from "@/store/loadingSlice.mobile";
import { appMobileLoading } from "@/utils/app";
import { replaceCarriageReturns } from "@/utils/common";
import { isEmpty, isEqual } from "lodash";
import { useEffect, useState } from "react";

const FareCondition = (props) => {
  const { data } = props;
  const [journey, setJourney] = useState({});
  const [fareBasis, setFareBasis] = useState({});
  const { open: isLoading } = useAppSelector(selectAppLoading);

  useEffect(() => {
    const getData = async () => {
      try {
        appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
        const journeys = data.map(({ journeyKey, pairKey, fares, airline, deptAirport, arrAirport, departureDate }) => {
          return {
            journeyKey,
            fareKey: fares.fareKey,
            pairKey,
            airline,
            deptAirport,
            arrAirport,
            departureDate,
            promotionId: "",
            fopPromotionId: "",
          };
        });
        const res = await getFareRule({
          journeys,
          ssCode: "BTMS",
        });
        setJourney(res.data.data?.journeys?.[0]);
        setFareBasis(res.data.data?.journeys?.[0]?.fareBasisRules?.[0]);
      } catch (error) {
        console.log(error);
        setJourney({});
      } finally {
        appMobileLoading.off();
      }
    };
    getData();
  }, []);

  return (
    <>
      {!isLoading && !isEmpty(journey) && (
        <>
          <ul className="tab-btm-fare">
            {journey.fareBasisRules.map((fareBasisRulesItem, index) => {
              return (
                <li key={index} className={`${isEqual(fareBasisRulesItem, fareBasis) ? "active" : ""}`}>
                  <a
                    href="#none;"
                    className="btn-default"
                    onClick={() => {
                      setFareBasis(fareBasisRulesItem);
                    }}
                  >
                    운임규정 {index + 1}
                  </a>
                </li>
              );
            })}
          </ul>

          <div className="tbl-cmm" id="fareRuleView_">
            <table className="table-fixed">
              <colgroup>
                <col style={{ width: "101px" }} />
                <col />
              </colgroup>
              <tbody>
                {[...(fareBasis?.items ?? []), ...journey.agencyItems]?.map((item, itemIndex) => {
                  return (
                    <tr key={itemIndex}>
                      <th>{item.name}</th>
                      <td dangerouslySetInnerHTML={{ __html: replaceCarriageReturns(item.description) }}></td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      )}
    </>
  );
};

export default FareCondition;
