import useTravelRule from "@/app/hooks/useTravelRule";

const TripRule = (props) => {
  const { data } = props;
  const { isTravelLimitDayViolation } = useTravelRule();
  const isTravelLimitDayViolationFlag = isTravelLimitDayViolation(data);

  return (
    <>
      <p className="desc">항공권 예약 규정입니다.</p>
      <div className="box-info">
        <ul>
          <li className={`${isTravelLimitDayViolationFlag ? "fail" : isTravelLimitDayViolationFlag == null ? "default" : "success"}`}>
            <dl>
              <dt>항공권</dt>
              <dd>
                출장 일정에 맞는 항공권 중, <strong>Low fare 이용 원칙</strong>
              </dd>
            </dl>
          </li>
          <li>
            <dl>
              <dt>좌석등급</dt>
              <dd>
                <strong>economy</strong> (부득이한 경우 사내 upgrade 기준 참고)
              </dd>
            </dl>
          </li>
        </ul>
      </div>
    </>
  );
};

export default TripRule;
