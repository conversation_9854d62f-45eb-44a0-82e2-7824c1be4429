import "@/styles/mobile/css/index.css";
import "@/styles/mobile/css/login.css";

import logo from "@/assets/mobile/images/cmm/logo.png";

import { Link } from "react-router-dom";
import { MOBILE_URL, URL } from "@/constants/url";

export default function MLogin({ data, setDataLogin, onLogin, errorLoginMessage, setErrorLoginMessage, company, isGeneralVersion }) {
  return (
    <form id="loginForm" name="loginForm">
      <div id="wrap" style={{ minWidth: "unset" }}>
        <div id="container" className="pg-login side-padding">
          <h1 style={{ display: "flex", justifyContent: "center" }}>
            <Link to="/login">
              <img
                src={
                  isGeneralVersion || !company || !company.homepageSetting || company.homepageSetting.isUseDefaultLogo
                    ? logo
                    : import.meta.env.VITE_STORAGE_URL +
                      "/" +
                      company.homepageSetting?.loginLogoAttachFile?.fileUploadPath +
                      company.homepageSetting?.loginLogoAttachFile?.tempFileName
                }
                alt="LOGO"
                style={{ height: "40px", width: "auto" }}
              />
            </Link>
          </h1>
          <div className="contents login-cotn" style={{ width: "100%" }}>
            <div className="id">
              <div className="box-col">
                <input
                  type="text"
                  id="email"
                  name="email"
                  maxLength={50}
                  placeholder="이메일"
                  style={{ paddingRight: 0, width: "100%" }}
                  value={data.email}
                  onChange={(e) => {
                    setDataLogin({ ...data, email: e.target.value });
                    setErrorLoginMessage("");
                  }}
                />
              </div>
            </div>
            <div className="pw">
              <div className="box-col">
                <input
                  type="password"
                  id="password"
                  name="password"
                  placeholder="비밀번호"
                  autoComplete="off"
                  value={data.password}
                  onChange={(e) => {
                    setDataLogin({ ...data, password: e.target.value });
                    setErrorLoginMessage("");
                  }}
                />
              </div>
            </div>
            <p id="loginMessage" className="validate" style={{ display: errorLoginMessage ? "block" : "none" }}>
              {errorLoginMessage}
            </p>
            <div className="sec-class" style={{ marginTop: 20 }}>
              <label className="form-chkbox">
                <input type="checkbox" id="saveId" name="saveId" />
                <span style={{ color: "#9da9be" }}>아이디 저장</span>
              </label>
            </div>
            <div className="btn-func">
              <button type="button" id="loginBtn" className="btns-cmm round-basic w-75 color-b" onClick={onLogin}>
                로그인
              </button>
            </div>
            <div className="etc-col">
              <div className="btns" style={{ width: "100%" }}>
                <Link to={MOBILE_URL.JoinForm} className="jsPolicyAgreeButton">
                  회원가입
                </Link>
                <a href="/customer/m/find/emailIdentity">비밀번호 찾기 </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
