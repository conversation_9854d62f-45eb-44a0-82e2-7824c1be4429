function QnaRegistPop({ isOpen, onClose }) {
  return (
    <div className="jquery-modal blocker current" style={{ display: isOpen ? "block" : "none" }}>
      <div id="qnaRegistPop" className="modal-wrap type-alrt modal !inline-block">
        <div className="modal-cotn">
          <p className="desc">등록이 완료 되었습니다.</p>
          <div className="box-btn">
            <a href="#changeRequestPop" className="btns-cmm round-basic color-b w-50" rel="modal:close" onClick={onClose}>
              확인
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default QnaRegistPop;
