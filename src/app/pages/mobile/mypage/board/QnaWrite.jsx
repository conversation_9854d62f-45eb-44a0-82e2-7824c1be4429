import { useEffect, useState } from "react";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { isEmpty } from "lodash";
import { requestWithMobileLoading } from "@/utils/app";
import { useNavigate } from "react-router-dom";
import { deleteFile, uploadFile } from "@/utils/fileUpload";
import QueryString from "qs";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const INIT_FORM_DATA = { title: "", contents: "", "categoryCode.id": "" };
const FILE_UPLOAD_CONFIG = { MAX_COUNT: 1, MAX_SIZE: 5, FILE_TYPE: "img", FILE_VARIABLE_NAME: "attachFiles" };

const QnaWrite = (props) => {
  const { onRetrieve, onClose, onOpenQnaRegistPop } = props;
  const navigate = useNavigate();
  const { codeList } = useAppSelector(selectReservationTravelView);
  const [formData, setFormData] = useState(INIT_FORM_DATA);
  const [uploadedFiles, setUploadedFiles] = useState([]);

  const handleUploadFile = (event) => {
    const params = {
      file: event.target.files[0],
      uploadConfig: FILE_UPLOAD_CONFIG,
      uploadedFiles,
      formData,
      onUpdateUploadedFiles: setUploadedFiles,
    };
    uploadFile(params);
  };

  const qnaAddProcess = () => {
    if (!formData["categoryCode.id"]) {
      alert("분류를 선택해주세요.");
      return;
    }
    if (!formData.title.trim()) {
      alert("제목을 입력해주세요.");
      return;
    }
    if (!formData.contents.trim()) {
      alert("문의 내용을 입력해주세요.");
      return;
    }
    const payload = {
      ...formData,
      [FILE_UPLOAD_CONFIG.FILE_VARIABLE_NAME]: uploadedFiles.map((attachFile) => ({
        originFileName: attachFile.originFileName,
        tempFileName: attachFile.tempFileName,
        fileUploadPath: attachFile.fileUploadPath,
        fileSize: attachFile.fileSize,
        s3BucketPath: attachFile.s3BucketPath,
      })),
    };
    requestWithMobileLoading({
      url: "/m/board/qna/addProcess",
      method: "POST",
      data: QueryString.stringify(payload, { allowDots: true }),
      success: () => {
        onOpenQnaRegistPop();
        onRetrieve && onRetrieve();
        handleClose();
      },
      fail: (error) => {
        if (error.response?.status === 401) {
          navigate("/loginExpired");
          return;
        }
        if (error.response?.status === 406) {
          navigate("/accessDenied");
          return;
        }
        alert(`등록중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
      },
    });
  };

  const handleClose = () => {
    onClose();
    setFormData(INIT_FORM_DATA);
    setUploadedFiles([]);
  };

  useEffect(() => {
    if (!isEmpty(codeList)) {
      setFormData((prev) => ({ ...prev, "categoryCode.id": codeList[0].id }));
    }
  }, [codeList]);

  return (
    <div className="layer-pages qna-write !block">
      <div className="layer-head">
        <p className="tit">1:1 문의 작성</p>
      </div>
      <div className="layer-cotn">
        <form id="qnaAddFrom" method="POST" encType="multipart/form-data">
          <div className="select-cmm">
            <select
              className="select-menu select-cmm"
              id="categoryCodeId"
              name="categoryCodeId"
              value={formData["categoryCode.id"]}
              onChange={(event) => setFormData((prev) => ({ ...prev, "categoryCode.id": event.target.value }))}
            >
              {codeList.map((code) => (
                <option key={code.id} value={code.id}>
                  {code.name}
                </option>
              ))}
            </select>
          </div>
          <div className="form-cn">
            <span className="input-cmm">
              <input
                type="text"
                id="title"
                name="title"
                maxLength="50"
                placeholder="제목"
                value={formData.title}
                onChange={(event) => setFormData((prev) => ({ ...prev, title: event.target.value }))}
              />
            </span>
          </div>
          <div className="form-cn file">
            <textarea
              id="qnaContents"
              name="contents"
              placeholder="문의내용을 입력해주세요. (2000자 이내)"
              maxLength="2000"
              value={formData.contents}
              onChange={(event) => setFormData((prev) => ({ ...prev, contents: event.target.value }))}
            />

            <div className="clearfix">
              <p className="desc">5MB 이하의 이미지파일(JPG, PNG, GIF) 1개를 첨부하실 수 있습니다.</p>
              <label className="box-file-upload">
                <input type="file" name="File" id="fileSelectBtn" onChange={handleUploadFile} />
                <span className="txt">첨부파일</span>
              </label>
            </div>
            <span id="fileListArea" className="desc">
              {uploadedFiles.map((attachFile, index) => (
                <li key={attachFile.createDate} id={`fileViewLi_${index + 1}`} name="arrayFlieViewLi">
                  {attachFile.originFileName}
                  <button
                    type="button"
                    id={index + 1}
                    name="deleteFileBtn"
                    className="!text-red-500 !pl-[5px]"
                    onClick={() => deleteFile({ file: attachFile, onUpdateUploadedFiles: setUploadedFiles })}
                  >
                    삭제
                  </button>
                </li>
              ))}
            </span>
          </div>
          <div className="alram-feedback">
            <div className="desc">
              <p>• 문의 주신 내용은 확인 후 빠른 시간 내 답변 드리겠습니다.</p>
              <p>• 주말 및 공휴일에는 휴무인 관계로 답변이 지연될 수 있으니 양해 바랍니다.</p>
              <p>• 한번 등록한 문의 내용은 수정이 불가능합니다.</p>
            </div>
          </div>
          <div className="box-btn">
            <button type="button" className="btns-cmm round-basic color-w btn-request layer-pages-close" onClick={handleClose}>
              취소
            </button>
            <button type="button" className="btns-cmm round-basic color-b btn-request" onClick={qnaAddProcess}>
              등록
            </button>
          </div>
        </form>
      </div>
      <div className="btn-prev-page layer-pages-close" onClick={handleClose}>
        <button type="button" className="btns-cmm"></button>
      </div>
    </div>
  );
};

export default QnaWrite;
