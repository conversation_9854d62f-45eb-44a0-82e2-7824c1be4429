import { requestWithMobileLoading } from "@/utils/app";
import { Fragment, useEffect, useState } from "react";
import { unescapeHtmlByDiv } from "@/utils/common";
import ActivedElement from "@/app/components/mobile/common/ActivedElement";
import moment from "moment";
import qs from "qs";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const MAPPED_TAB_NAME = {
  59: "AIR",
  58: "HOTEL",
};

const INIT_FORM_DATA = { categoryCodeId: "", subCategoryCodeId: "", searchWord: "" };

function FaqList(props) {
  const { onClose } = props;
  const [faqCategories, setFaqCategories] = useState([]);
  const [faqSubCategories, setSubCategories] = useState([]);
  const [faqTable, setFaqTable] = useState([]);
  const [formData, setFormData] = useState(INIT_FORM_DATA);
  const [activedElements, setActivedElements] = useState([]);

  const handleSearch = (event) => {
    if (event.keyCode == 13) {
      event.preventDefault();
      getFaqListLayer(formData);
    }
  };

  const getFaqSubCategoryList = (prevPayload, categoryCodeId) => {
    const payload = { ...prevPayload, categoryCodeId };
    const tabName = MAPPED_TAB_NAME[Number(categoryCodeId)] ?? "";
    requestWithMobileLoading({
      url: "/common/code/listAjax",
      data: qs.stringify({ categoryCode: "FAQ_SUB_CATEGORY_CODE_" + tabName }),
      method: "POST",
      success: (res) => {
        payload.subCategoryCodeId = res.data ? res.data[0].id : "";
        setSubCategories(res.data ?? []);
        getFaqListLayer(payload);
      },
      fail: () => alert(`공통코드를 조회할 수 없습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`),
      end: () => setFormData(payload),
    });
  };

  const getFaqListLayer = (payload) => {
    requestWithMobileLoading({
      url: "/m/board/faq/listAjax",
      data: qs.stringify({ pages: 1, rowCountPerPage: 10, parameterFlag: "MOBILE", ...payload }),
      method: "POST",
      success: (res) => setFaqTable(res.data?.list ?? []),
      end: () => {
        setFormData(payload);
        setActivedElements([]);
      },
    });
  };

  useEffect(() => {
    requestWithMobileLoading({
      url: "/common/code/listAjax",
      data: qs.stringify({ categoryCode: "FAQ_CATEGORY_CODE" }),
      method: "POST",
      success: (res) => {
        setFaqCategories(res.data ?? []);
        if (res.data) {
          getFaqSubCategoryList(INIT_FORM_DATA, res.data[0].id);
        }
      },
    });
  }, []);

  return (
    <form id="faqSearchForm">
      <div className="layer-pages faq-list !block">
        <div className="layer-head">
          <p className="tit">자주 묻는 질문</p>
        </div>
        <div className="layer-cotn">
          <div className="box-search">
            <input
              type="saerch"
              id="faqSearchWord"
              name="searchWord"
              placeholder="검색어를 입력하세요"
              onChange={(event) => setFormData((prev) => ({ ...prev, searchWord: event.target.value }))}
              onKeyDown={handleSearch}
            />
          </div>
          <div className="box-tab-item">
            <ul id="tabList">
              {faqCategories.map((item, index) => (
                <li key={item.id}>
                  <label>
                    <input
                      type="radio"
                      name="typeItem"
                      id={`tabLi_${item.id}`}
                      defaultChecked={index === 0}
                      onClick={() => getFaqSubCategoryList(formData, item.id)}
                    />
                    <span className="txt">{item.name}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
          <div className="box-tab-sub">
            <ul id="subCategoryList">
              {faqSubCategories.map((item, index) => (
                <Fragment key={item.id}>
                  <li>
                    <label>
                      <input
                        type="radio"
                        name="typeSub"
                        id={`subTabLi_${item.id}`}
                        defaultChecked={index === 0}
                        onClick={() => getFaqListLayer({ ...formData, subCategoryCodeId: item.id })}
                      />
                      <span className="txt">{item.name}</span>
                    </label>
                  </li>
                  {index === 3 && <br />}
                </Fragment>
              ))}
            </ul>
          </div>
          <div className="box-accodi" id="faqTable-data">
            {faqTable.length ? (
              faqTable.map((item) => (
                <ActivedElement key={item.boardId} id={item.boardId} elements={activedElements} onChange={setActivedElements}>
                  <dl>
                    <dt>
                      {unescapeHtmlByDiv(item.title)}&nbsp;&nbsp;<span className="date">{moment(item.createDate).format("YYYY-MM-DD")}</span>
                    </dt>
                    <dd>{unescapeHtmlByDiv(item.contents)}</dd>
                  </dl>
                </ActivedElement>
              ))
            ) : (
              <div className="box-item">
                <dl>
                  <dt>내용이 없습니다.</dt>
                </dl>
              </div>
            )}
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}></button>
        </div>
      </div>
    </form>
  );
}

export default FaqList;
