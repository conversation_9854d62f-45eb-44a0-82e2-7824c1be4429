import { useEffect, useState } from "react";
import { unescapeHtmlByDiv } from "@/utils/common";
import ActivedElement from "@/app/components/mobile/common/ActivedElement";
import moment from "moment";

function QnaList(props) {
  const { data, onRetrieve, onClose, onOpenQnaWrite } = props;
  const [activedElements, setActivedElements] = useState([]);

  const replaceContents = (text) =>
    text
      ?.replace(/<br>/gi, "\n")
      ?.replace(/<(\/)?([a-zA-Z]*)(\s[a-zA-Z]*=[^>]*)?(\s)*(\/)?>/gi, "")
      ?.replace(/\r?\n/g, "<br />") ?? "";

  useEffect(() => {
    onRetrieve();
  }, [onRetrieve]);

  return (
    <div className="layer-pages qna-list !block">
      <div className="layer-head">
        <p className="tit">1:1 문의</p>
      </div>
      <div className="layer-cotn">
        <div className="desc">
          <dl>
            <dt>상담시간</dt>
            <dd>
              평일(월 ~ 금) 09:00 ~ 17:00 <br />
              (Off-time 11:30 ~ 13:00, 토/일/공휴일 휴무)
            </dd>
          </dl>
        </div>
        <div className="box-btn">
          <button type="button" className="btns-cmm round-basic color-b w-75" data-page-layer="qna-write" onClick={onOpenQnaWrite}>
            1:1 문의 작성
          </button>
        </div>
        <div className="sec-qna-list" id="qnaTable-data">
          {data.length ? (
            <>
              <p className="title">1:1 문의 내역</p>
              {data.map((item) => (
                <ActivedElement key={item.boardId} id={item.boardId} elements={activedElements} onChange={setActivedElements}>
                  <div className="box-top">
                    {item.isAnswer ? <p className="status complete">답변완료</p> : <p className="status ready">답변대기</p>}
                    <p className="tit">
                      {unescapeHtmlByDiv(item.title)}
                      <span className="date">{moment(item.createDate).format("YYYY.MM.DD")}</span>
                    </p>
                    <p className="cotn">{unescapeHtmlByDiv(replaceContents(item.contents))}</p>
                  </div>
                  {item.isAnswer && (
                    <div className="box-bottom">
                      <p className="label">
                        <span>답변</span>
                      </p>
                      <div className="desc-reply">{replaceContents(unescapeHtmlByDiv(item.answerContents))}</div>
                      <p className="date">{moment(item.answerDate).format("YYYY.MM.DD")}</p>
                    </div>
                  )}
                </ActivedElement>
              ))}
            </>
          ) : (
            <div className="box-none">등록하신 1:1 문의 내역이 없습니다.</div>
          )}
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={onClose}>
          닫기
        </button>
      </div>
    </div>
  );
}

export default QnaList;
