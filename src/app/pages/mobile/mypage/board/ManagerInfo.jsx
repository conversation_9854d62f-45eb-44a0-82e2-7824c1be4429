import useCsCallNumber from "@/app/hooks/useCsCallNumber";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { returnPhoneNumber } from "@/utils/common";
import { Fragment } from "react/jsx-runtime";

const MAPPED_MANAGER_TYPE = {
  Air: {
    titleClass: "plan",
    spanText: "항공",
    pText: "예약 담당자",
  },
  Hotel_RentCar: {
    titleClass: "hotel",
    spanText: "호텔",
    pText: "예약 담당자",
  },
  BizTraining: {
    titleClass: "mice",
    spanText: "MICE",
    pText: "행사 상담 담당자",
  },
  Passport_Visa: {
    titleClass: "visa",
    spanText: "VISA",
    pText: "신청 담당자",
  },
};

function ManagerInfo(props) {
  const { onClose } = props;
  const { gnbUserInfo } = useAppSelector(selectUserInfo);
  const csCallNumber = useCsCallNumber({ btmsSetting: gnbUserInfo.workspace?.company?.btmsSetting });

  return (
    <div className="layer-pages manager-info !block">
      <div className="layer-head ta-c">
        <p className="tit">담당자 안내</p>
      </div>
      <div className="layer-cotn">
        <div className="cs-call">
          <dl>
            <dt>전화 문의</dt>
            <dd>운영시간: 09:00~18:00 (월~금, 공휴일 제외)</dd>
          </dl>
        </div>
        <div className="list-manager">
          {Object.keys(MAPPED_MANAGER_TYPE).map((key) => (
            <div className="box-item" key={key}>
              <p className="tit">
                <i className={`box-ico ${MAPPED_MANAGER_TYPE[key].titleClass}`}>아이콘</i>
                <span className="type">{MAPPED_MANAGER_TYPE[key].spanText}</span> {MAPPED_MANAGER_TYPE[key].pText}
              </p>
              <dl>
                {gnbUserInfo.workspace?.company?.btmsManagers?.map(
                  (manager, index) =>
                    Object.keys(MAPPED_MANAGER_TYPE).includes(manager.managerType) &&
                    manager.travelAgencyUser &&
                    manager.isOverseas &&
                    manager.displayOrder == 1 && (
                      <Fragment key={`${manager.id ?? 0}_${index}`}>
                        <dt>{manager.travelAgencyUser.name}</dt>
                        <dd>
                          <i className="box-ico call">아이콘</i>
                          <span>
                            {manager.travelAgencyUser.phoneNumber &&
                              `${returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "FIRST")}-${returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "MIDDLE")}-${returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "END")}`}
                          </span>
                        </dd>
                        <dd>
                          <i className="box-ico email">아이콘</i>
                          <span>{manager.travelAgencyUser.email}</span>
                        </dd>
                      </Fragment>
                    ),
                )}
              </dl>
            </div>
          ))}
        </div>
        <div className="special-call">
          <dl>
            <dt>긴급 서비스</dt>
            <dd className="num tit">
              <span className="box-ico call"></span>
              {csCallNumber}
            </dd>
            <dd className="time">운영시간(평일 9시-6시) 외</dd>
          </dl>
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={onClose}>
          닫기
        </button>
      </div>
    </div>
  );
}

export default ManagerInfo;
