import { useEffect, useState } from "react";
import { requestWithMobileLoading } from "@/utils/app";
import { unescapeHtmlByDiv } from "@/utils/common";
import ActivedElement from "@/app/components/mobile/common/ActivedElement";
import moment from "moment";
import qs from "qs";

function NoticeList(props) {
  const { onClose } = props;
  const [notices, setNotices] = useState([]);
  const [activedElements, setActivedElements] = useState([])

  useEffect(() => {
    requestWithMobileLoading({
      url: "/m/board/notice/listAjax",
      method: "POST",
      data: qs.stringify({ pages: 1, rowCountPerPage: 10, parameterFlag: "MOBILE" }),
      success: (res) => setNotices(res.data?.list ?? []),
    });
  }, []);

  return (
    <form id="noticeSearchForm">
      <div className="layer-pages notice-list !block">
        <div className="layer-head">
          <p className="tit">공지사항</p>
        </div>
        <div className="layer-cotn">
          <div className="box-accodi" id="noticeTable-data">
            {notices.length ? (
              notices.map((item, index) => (
                <ActivedElement key={`${item.boardId}_${index}`} id={index} elements={activedElements} onChange={setActivedElements}>
                  <dl>
                    <dt>
                      {unescapeHtmlByDiv(item.title)}&nbsp;&nbsp;<span className="date">{moment(item.createDate).format("YYYY-MM-DD")}</span>
                    </dt>
                    <dd>
                      <div className="txt" dangerouslySetInnerHTML={{ __html: unescapeHtmlByDiv(item.contents) }}></div>
                      {item.attachFileCount > 0 &&
                        item.attachFiles.map((file) => {
                          const ext = file.tempFileName.split(".").pop().toLowerCase();
                          return (
                            ["gif", "png", "jpg", "jpeg"].includes(ext) && (
                              <img src={`${import.meta.VITE_STORAGE_URL}${file.fileUploadPath.substring(11)}${file.tempFileName}`} alt="이미지" />
                            )
                          );
                        })}
                    </dd>
                  </dl>
                </ActivedElement>
              ))
            ) : (
              <div className="box-item">
                <dl>
                  <dt>내용이 없습니다.</dt>
                </dl>
              </div>
            )}
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}></button>
        </div>
      </div>
    </form>
  );
}

export default NoticeList;
