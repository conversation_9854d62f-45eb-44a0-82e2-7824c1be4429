import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";

function User() {
  const {
    data: { travel = {} },
  } = useAppSelector(selectReservationTravelView);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className={`box-info user ${isActive ? "active" : ""}`}>
      {travel?.reserver && (
        <dl>
          <dt className="box-dt">예약자 정보</dt>
          <dd className="box-dd">
            <p className="form-tit ">예약자 이름</p>
            <div className="form-cn">
              <p className="box-val">{travel?.reserver?.name}</p>
            </div>
            <p className="form-tit ">휴대전화</p>
            <div className="form-cn">
              <p className="box-val">{travel?.reserver?.cellPhoneNumber}</p>
            </div>
            <p className="form-tit ">이메일 주소</p>
            <div className="form-cn">
              <p className="box-val">{travel?.reserver?.email}</p>
            </div>
          </dd>
        </dl>
      )}
      <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)}></button>
    </div>
  );
}

export default User;
