import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import StayUsa from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/StayUsa";
import { MAPPED_STATUS_CODE } from "@/constants/app";

const INIT_AMERICA_STAY_DYNAMIC = {
  americaStayId: 0,
  buttonText: "등록",
};

function Stay() {
  const {
    data: { travel = {}, americaStayDTO },
  } = useAppSelector(selectReservationTravelView);
  const [isActive, setIsActive] = useState(true);
  const [isOpenStayUsa, setIsOpenStayUsa] = useState(false);
  const [americaStayDynamic, setAmericaStayDynamic] = useState(INIT_AMERICA_STAY_DYNAMIC);

  useEffect(() => {
    const nextAmericaStayDynamic = INIT_AMERICA_STAY_DYNAMIC;
    if (!isEmpty(americaStayDTO)) {
      nextAmericaStayDynamic.americaStayId = americaStayDTO?.americaStayId ?? 0;
      nextAmericaStayDynamic.buttonText = "수정";
    }
    setAmericaStayDynamic(nextAmericaStayDynamic);
  }, [americaStayDTO]);

  return (
    <>
      <div className={`box-info stay ${isActive ? "active" : ""}`}>
        <dl>
          <dt className="box-dt">미주 내 체류지 정보</dt>
          <dd className="box-dd">
            <div className="flex flex-col !items-start gap-1 mb-5 text-sm text-[#557ffe]">
              <span>• 출발 72시간 전까지 정확한 체류지 정보를 등록 및 수정하시기 바랍니다.</span>
              <span>• 체류 도시, 주소에 현금과 특수문자는 입력이 불가하며, 영문과 숫자만 합력하시기 바랍니다.</span>
              <span>• 잘못된 체류지 정보 입력으로 인한 탑승 및 입국 거절은 투어비스에서 책임지지 않습니다.</span>
            </div>
            {travel.statusCode?.id !== MAPPED_STATUS_CODE.Completed.id && travel.statusCode?.id !== MAPPED_STATUS_CODE.Cancelled.id && (
              <button
                type="button"
                id="travelAirViewStayBtn"
                className="btns-cmm round-basic color-w w-100 btn-stay-regist"
                data-page-layer="stay-usa"
                onClick={() => setIsOpenStayUsa(true)}
              >
                {americaStayDynamic.buttonText}
              </button>
            )}
          </dd>
        </dl>
        <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)}></button>
      </div>
      <StayUsa
        travel={travel}
        americaStayDTO={americaStayDTO}
        isOpen={isOpenStayUsa}
        onClose={() => setIsOpenStayUsa(false)}
        americaStayDynamic={americaStayDynamic}
        onChangeAmericaStayDynamic={setAmericaStayDynamic}
      />
    </>
  );
}

export default Stay;
