import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { trimString } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { isEmpty } from "lodash";
import AirCompareSchedule from "@/app/pages/mobile/mypage/reservation/include/AirCompareSchedule";
import AIR_ICONS_PATH from "@/assets/airIcon";
import useBookingAirScheduleMap from "@/app/hooks/useBookingAirScheduleMap";
import { MAPPED_SEAT_TYPE } from "@/constants/app";

function Plan(props) {
  const { sectionType } = props;
  const {
    data: { travel = {} },
    compareAirSchedules,
  } = useAppSelector(selectReservationTravelView);
  const [isOpenCompareSchedule, setIsOpenCompareSchedule] = useState(false);
  const { bookingAirScheduleMap, bookingAirScheduleMapSize } = useBookingAirScheduleMap({
    bookingAirSchedule: travel.bookingAir?.bookingAirSchedules,
  });

  return (
    <div className="box-info plan active">
      <dl>
        <dt className="box-dt">여정 정보</dt>
        <dt className="box-dd">
          {Object.entries(bookingAirScheduleMap).map(([key, schedules]) =>
            schedules.map((schedule, index) => (
              <div className="box-group" key={`${key}-${index}`}>
                <div className="box-top">
                  <span className="label">
                    {sectionType === "OneWay" && "편도"}
                    {sectionType === "RoundTrip" && (key === bookingAirScheduleMapSize ? "오는편" : "가는편")}
                    {sectionType === "MultiCity" && `여정 ${key}`}
                  </span>
                  <span className="date">{momentKR(schedule.fromDate).format("MM월 DD일 (ddd)")}</span>
                </div>
                <div className="aircraft">
                  <img
                    src={AIR_ICONS_PATH[schedule.airline?.code]}
                    onError={(e) => (e.target.src = AIR_ICONS_PATH.default_airline)}
                    alt={`${schedule.airline?.name} logo`}
                  />
                  <p className="name">
                    {schedule.airline?.name}
                    {schedule.opAirline?.code && schedule.airline?.code !== schedule.opAirline?.code && (
                      <>
                        <br />
                        [실제운항] {schedule.opAirline?.name}
                      </>
                    )}
                  </p>
                  <p className="seat">
                    {MAPPED_SEAT_TYPE[schedule.seatType]} ({schedule.bookingClassCode})
                  </p>
                </div>
                <div className="time-city">
                  <p>
                    {schedule.fromAirport?.code} {momentKR.utc(schedule.fromDate).format("HH:mm")}
                  </p>
                  <p>
                    {schedule.toAirport?.code} {momentKR.utc(schedule.toDate).format("HH:mm")}
                  </p>
                </div>
                <div className="box-right">
                  <span className="total-time">
                    총 {trimString(schedule.leadTime, 0, 2)}시간 {trimString(schedule.leadTime, 2, 4)}분
                  </span>
                </div>
              </div>
            )),
          )}
        </dt>
      </dl>
      {!isEmpty(compareAirSchedules) && (
        <a href="#none;" className="btn-compare-bill" onClick={() => setIsOpenCompareSchedule(true)}>
          비교견적서
        </a>
      )}
      <AirCompareSchedule
        travel={travel}
        compareAirSchedules={compareAirSchedules}
        isOpen={isOpenCompareSchedule}
        onClose={() => setIsOpenCompareSchedule(false)}
      />
    </div>
  );
}

export default Plan;
