import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { useState } from "react";

function DocumentNumber(props) {
  const { documentNumbers } = props;
  const { userInfo } = useAppSelector(selectUserInfo);
  const { documentNumberUseTitle } = userInfo.workspace?.company?.btmsSetting ?? {};
  const [isActive, setIsActive] = useState(true);

  return (
    <div className={`box-info ${isActive ? "active" : ""}`}>
      <dl>
        <dt className="box-dt">{documentNumberUseTitle || "문서번호"}</dt>
        <dd className="box-dd">
          <ul className="form-doc-number">
            {documentNumbers.map((documentNumber, index) => (
              <li key={index}>
                <span className="doc-number">{documentNumber}</span>
              </li>
            ))}
          </ul>
        </dd>
      </dl>
      <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)} />
    </div>
  );
}

export default DocumentNumber;
