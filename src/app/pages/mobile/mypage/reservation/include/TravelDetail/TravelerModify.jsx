import { useAppSelector } from "@/store";
import { selectCountryList } from "@/store/userSlice";
import { emailValidationCheck, isValidationInput } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { useEffect, useState } from "react";
import { cloneDeep, get, isEmpty } from "lodash";
import { requestWithMobileLoading } from "@/utils/app";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const INIT_FORM_DATA = {
  birthday: "",
  nationalityCode: "KR",
  cellPhoneNumber: "",
  email: "",
  passportNumber: "",
  expireYear: "",
  expireMonth: "",
  expireDay: "",
  passportNation: "KR",
  travelerMileageInfos: [
    { travelerMileageInfoId: 0, bookingAirTravelerId: "", airline: "", mileageMemberNo: "" },
    { travelerMileageInfoId: 0, bookingAirTravelerId: "", airline: "", mileageMemberNo: "" },
  ],
};

function TravelerModify(props) {
  const { travel, traveler, passengerIdx, isOpen, onClose } = props;
  const countryList = useAppSelector(selectCountryList);
  const [formData, setFormData] = useState(INIT_FORM_DATA);
  const [isDisableSubmit, setIsDisableSubmit] = useState(false);

  const handleChangeInput = (event) => {
    const { name, value } = event.target;
    if (!isValidationInput(event)) return;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleChangeMileageInfos = (event, mileageIndex) => {
    const { name, value } = event.target;
    const nextMileageInfos = cloneDeep(formData.travelerMileageInfos);
    if (!nextMileageInfos[mileageIndex]) {
      nextMileageInfos[mileageIndex] = INIT_FORM_DATA.travelerMileageInfos[0];
    }
    nextMileageInfos[mileageIndex][name] = value;
    setFormData((prev) => ({ ...prev, travelerMileageInfos: nextMileageInfos }));
  };

  const modifyPassportProcess = () => {
    if (!isValidationForm()) return;
    const { passportNumber, nationalityCode, expireYear, expireMonth, expireDay, birthday, passportNation, travelerMileageInfos } = formData;
    const { id, travelerLastName, travelerFirstName, travelerName, cellPhoneNumber, email } = traveler;
    const payload = {
      orderKey: travel.bookingAir?.orderKey,
      passengerIdx,
      updateType: "UpdatePassport",
      bookingAirTravelerId: id,
      travelId: travel.id,
      lastName: travelerLastName,
      firstName: travelerFirstName,
      passportNumber,
      passportLimitDate: `${expireYear}-${expireMonth}-${expireDay}`,
      birthday: momentKR(birthday, "YYYYMMDD").format("YYYY-MM-DD"),
      passportNation,
      nationalityCode,
      email,
      cellPhoneNumber,
      travelerName: travelerName,
      travelerMileageInfos,
    };
    setIsDisableSubmit(true);
    requestWithMobileLoading({
      url: "/api_v2/user/update-passport",
      method: "POST",
      data: payload,
      success: (res) => {
        const resultCode = get(res, "data.resultCode");
        if (resultCode?.toLocaleUpperCase() !== "SUCCESS") {
          alert(`처리 중 에러가 발생하였습니다.\n관리자에게 문의 하시기 바랍니다.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        alert("수정되었습니다.");
        location.reload();
      },
      fail: () => {
        alert(`처리 중 에러가 발생하였습니다.\n관리자에게 문의 하시기 바랍니다.${CONTACT_ADMIN_ALERT}`);
      },
      end: () => setIsDisableSubmit(false),
    });
  };

  const isValidationForm = () => {
    if (!formData.cellPhoneNumber.trim()) {
      alert("휴대전화 번호를 입력해주세요.");
      return false;
    }
    if (!emailValidationCheck(formData.email.trim())) {
      alert("이메일 주소 형식으로 입력해주세요.");
      return false;
    }
    if (!formData.passportNumber.trim()) {
      alert("여권 번호를 입력해주세요.");
      return false;
    }
    if (!formData.passportNation.trim()) {
      alert("여권 발행국을 선택해주세요.");
      return false;
    }
    return true;
  };

  const subStringPassportLimitDate = (first, last) =>
    traveler.passportLimitDate && !isNaN(traveler.passportLimitDate) ? traveler.passportLimitDate?.substring(first, last) : "";

  useEffect(() => {
    if (!isEmpty(traveler) && isOpen) {
      const nextFormData = {
        ...INIT_FORM_DATA,
        ...traveler,
        expireYear: subStringPassportLimitDate(0, 4),
        expireMonth: subStringPassportLimitDate(4, 6),
        expireDay: subStringPassportLimitDate(6),
        travelerMileageInfos: cloneDeep(traveler.travelerMileageInfos) ?? cloneDeep(INIT_FORM_DATA.travelerMileageInfos),
      };
      setFormData(nextFormData);
    }
  }, [isOpen, traveler]);

  return (
    <>
      <div
        key={traveler.id}
        id="traveler-modify"
        className={`layer-pages traveler-modify traveler-modify-${traveler.id}`}
        style={{ display: isOpen ? "block" : "none" }}
      >
        <div className="layer-head">
          <p className="tit">여행자 정보 수정</p>
        </div>
        <div className="layer-cotn">
          <p className="form-tit">한글이름</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p className="box-val">{traveler.travelerName}</p>
            </span>
          </div>
          <p className="form-tit">영문 성</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p className="box-val">{traveler.travelerLastName}</p>
              <input type="hidden" id={`passportLastName_${traveler.id}`} value={traveler.travelerLastName} readOnly />
            </span>
          </div>
          <p className="form-tit">영문 이름</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p className="box-val">{traveler.travelerFirstName}</p>
              <input type="hidden" id={`passportFirstName_${traveler.id}`} value={traveler.travelerFirstName} readOnly />
            </span>
          </div>
          <p className="form-tit">성별</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p className="box-val">{traveler.gender === "Male" ? "남성" : "여성"}</p>
            </span>
          </div>
          <p className="form-tit">생년월일</p>
          <div className="form-cn">
            <span className="input-cmm">
              <input
                type="text"
                id={`birthDate_${traveler.id}`}
                placeholder="예) 19900531"
                maxLength="8"
                value={formData.birthday}
                name="birthday"
                onChange={handleChangeInput}
              />
            </span>
          </div>
          <p className="form-tit">국적</p>
          <div className="form-cn">
            <span className="select-cmm">
              <select id={`nationalityCode_${traveler.id}`} value={formData.nationalityCode} name="nationalityCode" onChange={handleChangeInput}>
                {countryList.map((country) => (
                  <option key={country.code2} value={country.code2}>
                    {country.name}
                  </option>
                ))}
              </select>
            </span>
          </div>
          <p className="form-tit">휴대전화</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p id={`cellPhoneNumber_${traveler.id}`} name="cellPhoneNumber">
                {traveler.cellPhoneNumber}
              </p>
            </span>
          </div>
          <p className="form-tit">이메일</p>
          <div className="form-cn">
            <span className="input-cmm">
              <p id={`email_${traveler.id}`}>{traveler.email}</p>
            </span>
          </div>
          <div className="desc-passport">
            <strong>미주 노선은 여권정보가 반드시 필요합니다. 정확한 여권정보를 입력하세요.</strong> <br />※ 여권 발급 예정자이거나 확인이 어려운 경우
            임의의 값을 입력한 후, 반드시 출발 72시간 전까지 유효한 여권정보를 입력하세요.
            <br />※ 여권 유효기간은 출국일 기준 6개월 이상 남아 있어야 출국이 가능합니다.
          </div>
          <p className="form-tit">여권번호</p>
          <div className="form-cn">
            <span className="input-cmm">
              <input
                type="text"
                id={`passportNumber_${traveler.id}`}
                value={formData.passportNumber}
                placeholder="예) *********"
                maxLength="30"
                name="passportNumber"
                onChange={handleChangeInput}
              />
            </span>
            <span style={{ color: "red", fontSize: "11px" }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</span>
          </div>
          <p className="form-tit">여권 만료 기간</p>
          <div className="form-cn">
            <div className="box-yymmdd">
              <input type="hidden" id={`passportLimitDate_${traveler.id}`} value={traveler.passportLimitDate} />
              <span className="select-cmm year">
                <select name="expireYear" value={formData.expireYear} onChange={handleChangeInput}>
                  {Array.from({ length: 20 }, (_, i) => ({
                    value: `${momentKR().year() + i}`,
                    label: `${momentKR().year() + i}년`,
                  })).map(({ value, label }) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </span>
              <span className="select-cmm month">
                <select name="expireMonth" value={formData.expireMonth} onChange={handleChangeInput}>
                  {Array.from({ length: 12 }, (_, i) => ({
                    value: String(i + 1).padStart(2, "0"),
                    label: `${i + 1}월`,
                  })).map(({ value, label }) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </span>
              <span className="select-cmm day">
                <select name="expireDay" value={formData.expireDay} onChange={handleChangeInput}>
                  {Array.from({ length: 31 }, (_, i) => ({
                    value: String(i + 1).padStart(2, "0"),
                    label: `${i + 1}일`,
                  })).map(({ value, label }) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </span>
              <span style={{ color: "red", fontSize: "11px" }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</span>
            </div>
          </div>
          <p className="form-tit">여권 발행국가</p>
          <div className="form-cn">
            <span className="select-cmm">
              <select id={`passportNation_${traveler.id}`} value={formData.passportNation} name="passportNation" onChange={handleChangeInput}>
                <option value="">국적</option>
                {countryList.map((country) => (
                  <option key={country.code2} value={country.code2}>
                    {country.name}
                  </option>
                ))}
              </select>
            </span>
          </div>
          <p className="form-tit">마일리지 회원번호</p>
          <div className="form-cn mileage">
            <div className="content">
              <div>
                <span>항공사1</span>
                <input
                  type="text"
                  id={`travelerMileageInfos_${traveler.id}_airline_0`}
                  value={formData.travelerMileageInfos?.[0]?.airline ?? ""}
                  placeholder="한글/영문 입력"
                  name="airline"
                  onChange={(event) => handleChangeMileageInfos(event, 0)}
                />
              </div>
              <div>
                <span>회원번호1</span>
                <input
                  type="text"
                  id={`travelerMileageInfos_${traveler.id}_mileageMemberNo_0`}
                  value={formData.travelerMileageInfos?.[0]?.mileageMemberNo ?? ""}
                  placeholder="숫자/알파벳 입력"
                  name="mileageMemberNo"
                  onChange={(event) => handleChangeMileageInfos(event, 0)}
                />
              </div>
            </div>
            <div className="content">
              <div>
                <span>항공사2</span>
                <input
                  type="text"
                  id={`travelerMileageInfos_${traveler.id}_airline_1`}
                  value={formData.travelerMileageInfos?.[1]?.airline ?? ""}
                  placeholder="한글/영문 입력"
                  name="airline"
                  onChange={(event) => handleChangeMileageInfos(event, 1)}
                />
              </div>
              <div>
                <span>회원번호2</span>
                <input
                  type="text"
                  id={`travelerMileageInfos_${traveler.id}_mileageMemberNo_1`}
                  value={formData.travelerMileageInfos?.[1]?.mileageMemberNo ?? ""}
                  placeholder="숫자/알파벳 입력"
                  name="mileageMemberNo"
                  onChange={(event) => handleChangeMileageInfos(event, 1)}
                />
              </div>
            </div>
          </div>
          <div className="box-btn">
            <button
              disabled={isDisableSubmit}
              id="modifyTravelerBtn"
              className="btns-cmm round-basic color-b w-75 btn-regist"
              onClick={() => modifyPassportProcess(traveler.id)}
            >
              수정
            </button>
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}>
            여행자 정보 수정 닫기
          </button>
        </div>
      </div>
      <form id="modifyBookingAirTravelerPassportForm"></form>
    </>
  );
}

export default TravelerModify;
