import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { momentKR } from "@/utils/date";
import { MAPPED_STATUS_CODE } from "@/constants/app";

function Reserv() {
  const { travelStatusHistories } = useAppSelector(selectReservationTravelView);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className={`box-info reserv ${isActive ? "active" : ""}`}>
      <dl>
        <dt className="box-dt">예약 진행 내역</dt>
        <dd className="box-dd">
          <ul>
            {travelStatusHistories?.length > 0 ? (
              travelStatusHistories?.map((history, index) => (
                <li key={index}>
                  <p className="tit">{history.modifyInfo}</p>
                  <div className="name-date">
                    <span>
                      {history.modifier?.name}({history.modifier?.workspace?.name})
                    </span>
                    <span>{momentKR(history.modifyDate).format("YYYY.MM.DD HH:mm")}</span>
                  </div>
                  <p className="status blue">{MAPPED_STATUS_CODE[history.status]?.name}</p>
                </li>
              ))
            ) : (
              <li>
                <p className="tit">예약 진행 내역이 없습니다.</p>
              </li>
            )}
          </ul>
        </dd>
      </dl>
      <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)}></button>
    </div>
  );
}

export default Reserv;
