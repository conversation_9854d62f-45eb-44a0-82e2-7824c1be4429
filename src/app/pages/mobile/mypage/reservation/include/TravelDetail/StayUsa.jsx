import StayInfoAutoComplete from "@/app/components/user_v2/common/StayInfoAutoComplete";
import { MAPPED_STATUS_CODE } from "@/constants/app";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionSearchUsaState, actionSearchUsaStayCity, selectUsaCities, selectUsaStates, selectUsaStayLoading } from "@/store/travelViewSlice";
import { requestWithMobileLoading } from "@/utils/app";
import { debounce, get, isEmpty } from "lodash";
import { useEffect, useState } from "react";

const debouncedSearch = debounce((callback) => callback(), 500);

function StayUsa(props) {
  const { travel, americaStayDTO, isOpen, onClose, americaStayDynamic, onChangeAmericaStayDynamic } = props;
  const dispatch = useAppDispatch();
  const [isDisableSubmit, setIsDisableSubmit] = useState(false);
  const [isOpenStayUsaInfo, setIsOpenStayUsaInfo] = useState(false);
  const [data, setData] = useState({
    state: { display: "", name: "", code: "" },
    city: { display: "", name: "", code: "" },
    zipcode: "",
    address: "",
  });
  const isLoadingUsaStay = useAppSelector(selectUsaStayLoading);
  const usaCities = useAppSelector(selectUsaCities);
  const usaStates = useAppSelector(selectUsaStates);

  const handleSearchUsaState = (event) => {
    const { value } = event.target;
    setData((prev) => ({ ...prev, state: { display: value, name: "", code: "" } }));
    debouncedSearch(() => {
      if (!value) return;
      dispatch(actionSearchUsaState({ keyword: value }));
    });
  };

  const handleSearchUsaCity = (event) => {
    const { value } = event.target;
    setData((prev) => ({ ...prev, city: { display: value, name: "", code: "" } }));
    debouncedSearch(() => {
      if (!value) return;
      dispatch(actionSearchUsaStayCity({ keyword: value }));
    });
  };

  const addAmericaStay = () => {
    if (isEmpty(data.state.display)) {
      alert("주를 입력해주세요.");
      return;
    }
    if (isEmpty(data.city.display)) {
      alert("도시를 입력해주세요.");
      return;
    }
    if (isEmpty(data.address)) {
      alert("주소를 입력해주세요.");
      return;
    }
    const payload = {
      travelId: travel.id,
      stateCode: data.state.code,
      stateName: data.state.name || data.state.display,
      cityCode: data.city.code,
      cityName: data.city.name || data.city.display,
      zipcode: data.zipcode,
      address: data.address,
    };
    requestWithMobileLoading({
      url: "/api_v2/user/update-stayinfo",
      data: payload,
      method: "POST",
      success: (res) => {
        const { resultCode } = get(res, "data", {});
        onChangeAmericaStayDynamic({ americaStayId: resultCode.americaStayId ?? 0, buttonText: "수정" });
        setIsOpenStayUsaInfo(true);
      },
      fail: () => alert(`처리 중 에러가 발생하였습니다.\n다시 시도해 주세요.${CONTACT_ADMIN_ALERT}`),
      end: () => setIsDisableSubmit(false),
    });
  };

  useEffect(() => {
    const { stateCode = "", stateName = "", cityCode = "", cityName = "", zipcode = "", address = "" } = americaStayDTO ?? {};
    setData({
      state: { display: stateCode ? `${stateName} (${stateCode})` : stateName, name: stateName, code: stateCode },
      city: { display: cityCode ? `${cityName} (${cityCode})` : cityName, name: cityName, code: cityCode },
      zipcode,
      address,
    });
  }, [americaStayDTO]);

  return (
    <>
      <div id="stayUsaLayer" className="layer-pages stay-usa" style={{ display: isOpen ? "block" : "none" }}>
        <div className="layer-head">
          <p className="tit">미주 내 체류지 정보</p>
        </div>
        <div className="layer-cotn">
          <p className="form-tit ">
            체류 주<span>*</span>
          </p>
          <div className="search-form !z-[11]">
            <div className="form-cn">
              <span className="input-cmm">
                <StayInfoAutoComplete
                  value={data.state.display}
                  options={usaStates}
                  debounceSearch={handleSearchUsaState}
                  isLoading={isLoadingUsaStay}
                  labelText="name"
                  valueText="code"
                  onChange={(value) =>
                    setData((prev) => ({ ...prev, state: { display: `${value.name} (${value.code})`, name: value.name, code: value.code } }))
                  }
                  onClear={() => setData((prev) => ({ ...prev, state: { display: "", name: "", code: "" } }))}
                />
              </span>
            </div>
          </div>
          <p className="form-tit ">
            체류 도시<span>*</span>
          </p>
          <div className="search-form">
            <div className="form-cn">
              <span className="input-cmm">
                <StayInfoAutoComplete
                  value={data.city.display}
                  options={usaCities}
                  debounceSearch={handleSearchUsaCity}
                  isLoading={isLoadingUsaStay}
                  labelText="name"
                  valueText="cityCode"
                  onChange={(value) =>
                    setData((prev) => ({ ...prev, city: { display: `${value.name} (${value.cityCode})`, name: value.name, code: value.cityCode } }))
                  }
                  onClear={() => setData((prev) => ({ ...prev, city: { display: "", name: "", code: "" } }))}
                />
              </span>
            </div>
          </div>
          <p className="form-tit ">우편번호</p>
          <div className="form-cn">
            <span className="input-cmm">
              <input
                type="text"
                id="zipcode"
                value={data.zipcode}
                onChange={(event) => setData((prev) => ({ ...prev, zipcode: event.target.value }))}
              />
            </span>
          </div>
          <p className="form-tit ">
            체류 주소<span>*</span>
          </p>
          <div className="form-cn">
            <span className="input-cmm">
              <input
                type="text"
                id="address"
                value={data.address}
                onChange={(event) => setData((prev) => ({ ...prev, address: event.target.value }))}
              />
            </span>
          </div>
          {travel.statusCode?.id !== MAPPED_STATUS_CODE.Completed && travel.statusCode?.id !== MAPPED_STATUS_CODE.Cancelled && (
            <div className="box-btn">
              <button
                disabled={isDisableSubmit}
                id="addAmericaStayBtn"
                className="btns-cmm round-basic color-b w-75 btn-regist"
                onClick={addAmericaStay}
              >
                {americaStayDynamic.buttonText}
              </button>
            </div>
          )}
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}>
            미주 내 체류지 정보 닫기
          </button>
        </div>
      </div>
      <div className="jquery-modal blocker current" style={{ display: isOpenStayUsaInfo ? "block" : "none" }}>
        <div id="stayUsaInfo" className="modal-wrap type-alrt modal !inline-block">
          <div className="modal-cotn">
            <p className="desc">정보가 등록 되었습니다.</p>
            <div className="box-btn">
              <a href="#stayUsaInfo" className="btns-cmm round-basic color-b w-50" rel="modal:close" onClick={() => setIsOpenStayUsaInfo(false)}>
                확인
              </a>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default StayUsa;
