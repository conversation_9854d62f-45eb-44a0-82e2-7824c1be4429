import { useEffect, useMemo, useState } from "react";
import { timeLimitTL } from "@/utils/common";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionETicketListMap, selectReservationTravelView } from "@/store/travelViewSlice";
import { selectUserInfo } from "@/store/userSlice";
import Plan from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/Plan";
import User from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/User";
import Passenger from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/Passenger";
import Stay from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/Stay";
import Payment from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/Payment";
import Reserv from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/Reserv";
import ETicket from "@/app/pages/mobile/mypage/reservation/include/ETicket";
import DocumentAttachment from "@/app/pages/mobile/travel/overseas/include/Booking/DocumentAttachment";
import DocumentNumber from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/DocumentNumber";
import { appMobileLoading, requestWithMobileLoading } from "@/utils/app";
import { MAPPED_STATUS_CODE, TYPE_MOBILE_LOADING } from "@/constants/app";
import "@/styles/mobile/css/travelDetail.css";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

function TravelDetail(props) {
  const { sectionType } = props;
  const dispatch = useAppDispatch();
  const { userInfo } = useAppSelector(selectUserInfo);
  const {
    data: { travel = {}, isAmericaSchedule },
    viewFile: { attachFiles },
  } = useAppSelector(selectReservationTravelView);
  const [isOpenETicket, setIsOpenETicket] = useState(false);
  const [attachFileList, setAttachFileList] = useState([]);
  const documentNumbers = useMemo(() => travel.documentNumbers?.map((item) => item.documentNo) ?? [], [travel.documentNumbers]);

  const handleTicketRequest = () => {
    if (travel.bookingAir?.ticketRequest) {
      alert("발권 요청 중입니다.");
      location.reload();
      return;
    }
    requestWithMobileLoading({
      url: `/user/reservation/travel/air/ticketRequest/${travel.id}`,
      method: "POST",
      data: { travelId: travel.id },
      success: () => alert("발권을 요청하였습니다."),
      fail: () => alert(`발권요청에 실패하였습니다. 잠시 후 다시 시도 해 주시기 바랍니다.${CONTACT_ADMIN_ALERT}`),
      end: () => location.reload(),
    });
  };

  const handleTicketLimitAlert = () => {
    alert("발권시한이 지나 발권 요청이 불가합니다.");
    location.reload();
  };

  const handleSubmitTicket = () => {
    if (!timeLimitTL(travel.bookingAir?.viewTicketDate)) {
      handleTicketLimitAlert();
      return;
    }
    handleTicketRequest();
  };

  const handleOpenETicket = () => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING });
    dispatch(actionETicketListMap({ travelId: travel.id })).finally(() => {
      appMobileLoading.off();
      setIsOpenETicket(true);
    });
  };

  const downloadFile = (file) => {
    var hiddenIFrameId = "hiddenDownloader";
    var iframe = document.getElementById(hiddenIFrameId);
    if (iframe === null) {
      iframe = document.createElement("iframe");
      iframe.id = hiddenIFrameId;
      iframe.style.display = "none";
      document.body.appendChild(iframe);
    }
    iframe.src = "/common/file/custom/download?attachFileId=" + file.id;
  };

  useEffect(() => {
    if (attachFiles.length) {
      setAttachFileList(attachFiles);
    }
  }, [attachFiles]);

  return (
    <div className="content reserv-complete-cotn">
      <Plan sectionType={sectionType} />
      <User />
      <Passenger />
      {isAmericaSchedule && <Stay />}
      {userInfo.workspace?.company?.btmsSetting?.isDocumentEvidenceFile && (
        <DocumentAttachment
          className="added"
          files={attachFileList}
          onChangeFiles={setAttachFileList}
          onDownload={downloadFile}
          bookingAirId={travel.bookingAir?.id}
        />
      )}
      <Payment />
      {userInfo.workspace?.company?.btmsSetting?.isDocumentNumberUse && !!documentNumbers?.length && (
        <DocumentNumber documentNumbers={documentNumbers} />
      )}
      <Reserv />
      <div
        className={`sec-bottom-btns ${travel.statusCode?.id !== MAPPED_STATUS_CODE.Completed.id && travel.bookingAir?.ticketDate && !travel.bookingAir?.isViewTicketDateWarning ? "btn-double" : ""}`}
      >
        {travel.statusCode?.id !== MAPPED_STATUS_CODE.Cancelled.id &&
          travel.statusCode?.id !== MAPPED_STATUS_CODE.Completed.id &&
          travel.statusCode?.id !== MAPPED_STATUS_CODE.Rejected.id &&
          userInfo.workspace?.company?.btmsSetting?.bookingAdminApprovalType === "NotUsed" &&
          travel.bookingAir?.ticketDate && (
            <>
              {!travel.bookingAir?.isViewTicketDateWarning ||
                (travel.bookingAir?.isViewTicketDateWarning && travel.bookingAir.viewTicketDateModified && (
                  <button type="button" className="btns-cmm round-basic color-b w-75" onClick={handleSubmitTicket}>
                    발권요청
                  </button>
                ))}
            </>
          )}
        {travel.statusCode?.id === MAPPED_STATUS_CODE.Completed.id && (
          <button type="button" className="btns-cmm round-basic color-w w-75" onClick={handleOpenETicket}>
            E-Ticket
          </button>
        )}
      </div>
      <ETicket isOpen={isOpenETicket} onClose={() => setIsOpenETicket(false)} travel={travel} eticketListMap={{}} />
    </div>
  );
}

export default TravelDetail;
