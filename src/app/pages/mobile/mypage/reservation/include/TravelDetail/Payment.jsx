import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectTravelRuleBase } from "@/store/airSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { momentKR } from "@/utils/date";

const MAPPED_SEAT_TYPE = {
  M: "일반석",
  W: "프리미엄이코노미석",
  C: "비즈니스석",
  F: "일등석",
};

function Payment() {
  const {
    data: { travel = {} },
  } = useAppSelector(selectReservationTravelView);
  const { data: travelRuleBase } = useAppSelector(selectTravelRuleBase);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className={`box-info payment ${isActive ? "active" : ""}`}>
      <dl>
        <dt className="box-dt">결재 요청 의견</dt>
        <dd className="box-dd">
          <p className="desc-rule">
            사전 예약 규정 출발일 기준{" "}
            <strong>{travelRuleBase?.travelLimitDay === 0 ? "제한없음" : <>{travelRuleBase?.travelLimitDay}일 전</>}</strong>
          </p>
          <p className="desc-rule">
            기본 좌석등급{" "}
            <strong>
              {travelRuleBase?.baseSeatTypeCode}
              {MAPPED_SEAT_TYPE[travelRuleBase?.baseSeatTypeCode]}
            </strong>
          </p>
          {travelRuleBase?.isExceptUse && (
            <p className="desc-rule">
              {travelRuleBase?.exceptTimeType.value}
              <strong>{travelRuleBase?.exceptBordingTime} 시간</strong> 이상 일 경우{" "}
              <strong>{MAPPED_SEAT_TYPE[travelRuleBase?.exceptSeatTypeCode]}</strong>
            </p>
          )}
          <p className="desc-request">{travel.violationReason}</p>
          <div className="box-status">
            <p className="tit">결재 상태 - {travel.statusCode?.name}</p>
            {[396, 397].includes(travel.statusCode?.id) && (
              <p className="date">
                <strong>{travel.statusCode?.id === 396 ? "결재일시" : "취소일시"}</strong>{" "}
                {momentKR(travel.modifyDate).format("YYYY년 MM월 DD일 (ddd) HH:mm")}
              </p>
            )}
          </div>
        </dd>
      </dl>
      <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)}></button>
    </div>
  );
}

export default Payment;
