import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectCountryList } from "@/store/userSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import TravelerModify from "@/app/pages/mobile/mypage/reservation/include/TravelDetail/TravelerModify";
import { MAPPED_STATUS_CODE } from "@/constants/app";

const INIT_TRAVELER_STATE = { isOpen: false, data: {}, passengerIdx: "" };

function Passenger() {
  const {
    data: { travel = {} },
  } = useAppSelector(selectReservationTravelView);
  const countryList = useAppSelector(selectCountryList);
  const [travelerModify, setTravelerModify] = useState(INIT_TRAVELER_STATE);
  const [isActive, setIsActive] = useState(true);

  return (
    <div className={`box-info passenger ${isActive ? "active" : ""}`}>
      <dl>
        <dt className="box-dt">탑승객 정보</dt>
        <dd className="box-dd">
          {travel?.bookingAir?.bookingAirTravelers?.map((traveler, index) => (
            <div className="box-group" key={index}>
              <p className="tit">
                성인 <span>{index + 1}</span>
              </p>
              <div className="clearfix col-02">
                <div className="item-col">
                  <p className="form-tit">한글 이름</p>
                  <div className="form-cn">
                    <p className="box-val">{traveler.travelerName}</p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">영문 이름</p>
                  <div className="form-cn name">
                    <p className="box-val">
                      {traveler.travelerLastName} {traveler.travelerFirstName}
                    </p>
                  </div>
                </div>
              </div>
              <div className="clearfix col-02">
                <div className="item-col">
                  <p className="form-tit">휴대전화</p>
                  <div className="form-cn">
                    <p className="box-val">{traveler.cellPhoneNumber}</p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">이메일 주소</p>
                  <div className="form-cn email">
                    <p className="box-val">{traveler.email}</p>
                  </div>
                </div>
              </div>
              <div className="clearfix col-03">
                <div className="item-col">
                  <p className="form-tit">성별</p>
                  <div className="form-cn">
                    <p className="box-val">{traveler.gender === "Male" ? "남성" : "여성"}</p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">생년월일</p>
                  <div className="form-cn">
                    <p className="box-val">
                      {traveler.birthday?.substring(0, 4)}년 {traveler.birthday?.substring(4, 6)}월 {traveler.birthday?.substring(6, 8)}일
                    </p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">국적</p>
                  <div className="form-cn">
                    <p className="box-val">{countryList.find((country) => country.code2 === traveler.nationalityCode)?.name}</p>
                  </div>
                </div>
              </div>
              <div className="clearfix col-03">
                <div className="item-col">
                  <p className="form-tit">여권번호</p>
                  <div className="form-cn">
                    <p className="box-val">{traveler.passportNumber}</p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">여권만료기간</p>
                  <div className="form-cn">
                    <p className="box-val">
                      {!isNaN(traveler.passportLimitDate) ? (
                        <>
                          {traveler.passportLimitDate?.substring(0, 4)}년 {traveler.passportLimitDate?.substring(4, 6)}월{" "}
                          {traveler.passportLimitDate?.substring(6, 8)}일
                        </>
                      ) : (
                        "년 월 일"
                      )}
                    </p>
                  </div>
                </div>
                <div className="item-col">
                  <p className="form-tit">여권 발행 국가</p>
                  <div className="form-cn">
                    <p className="box-val">{countryList.find((country) => country.code2 === traveler.passportNation)?.name}</p>
                  </div>
                </div>
              </div>
              <div className="clearfix col-03">
                <p className="form-tit">마일리지 회원번호</p>
                <div className="form-cn mileage">
                  <div className="content">
                    <div>
                      <span>항공사1</span>
                      <input type="text" value={traveler.travelerMileageInfos?.[0]?.airline} readOnly />
                    </div>
                    <div>
                      <span>회원번호1</span>
                      <input type="text" value={traveler.travelerMileageInfos?.[0]?.mileageMemberNo} readOnly />
                    </div>
                  </div>
                  <div className="content">
                    <div>
                      <span>항공사2</span>
                      <input type="text" value={traveler.travelerMileageInfos?.[1]?.airline} readOnly />
                    </div>
                    <div>
                      <span>회원번호2</span>
                      <input type="text" value={traveler.travelerMileageInfos?.[1]?.mileageMemberNo} readOnly />
                    </div>
                  </div>
                </div>
              </div>
              {travel.statusCode?.id !== MAPPED_STATUS_CODE.Completed.id && travel.statusCode?.id !== MAPPED_STATUS_CODE.Cancelled.id && (
                <button
                  type="button"
                  id="travelAirViewStayBtn"
                  className="btns-cmm round-basic color-w w-100 btn-stay-regist"
                  data-page-layer={`traveler-modify-${traveler.id}`}
                  onClick={() => setTravelerModify({ data: traveler, passengerIdx: index + 1, isOpen: true })}
                >
                  수정
                </button>
              )}
            </div>
          ))}
        </dd>
      </dl>
      <button type="button" className="btn-box-arrow" onClick={() => setIsActive((prev) => !prev)}></button>
      <TravelerModify
        travel={travel}
        traveler={travelerModify.data}
        passengerIdx={travelerModify.passengerIdx}
        isOpen={travelerModify.isOpen}
        onClose={() => setTravelerModify(INIT_TRAVELER_STATE)}
      />
    </div>
  );
}

export default Passenger;
