import AIR_ICONS_PATH from "@/assets/airIcon";
import { inequalitySign, onErrorImgAirline } from "@/utils/common";
import { momentKR } from "@/utils/date";

const AirCompareSchedule = (props) => {
  const { travel, compareAirSchedules, isOpen, onClose } = props;
  return (
    <div className="layer-pages compare-ticket" style={{ display: isOpen ? "block" : "none", paddingBottom: 0 }}>
      <div className="layer-head">
        <p className="tit">항공 스케줄 비교</p>
        <div className="search-result-top">
          <div className="clearfix policy-box">
            <div className="left-col label-type">
              <p className="in">In Policy</p>
              <p className="out">Out of Policy</p>
            </div>
          </div>
          {travel.bookingAir?.bookingAirLowest && (
            <div id="lowestPriceLayer" className="clearfix policy-box">
              <div className="right-col" style={{ padding: "12px 0" }}>
                <div id="lowestPrice" className="lowest">
                  최저가 요금 : {travel.bookingAir?.bookingAirLowest?.totalAmount?.toLocaleString()}원
                </div>
              </div>
            </div>
          )}
        </div>
        <div className="btn-tip-compare">
          <span>비교견적표의 요금은 실제 예약된 금액이 아니며 담당자의 최종 확인이 필요합니다.</span>
        </div>
      </div>
      <div className="layer-cotn !h-[100%]" style={{ paddingTop: travel.bookingAir?.bookingAirLowest ? 150 : 100 }}>
        <div className="saerch-result-wrap">
          {compareAirSchedules?.map((schedule, index) => (
            <div key={index} className={`box-item pl-${schedule.isRuleViolation ? "out" : "in"}`}>
              <div className="box-top !before:left-0">
                <label className="form-radio !ml-5">
                  <input type="radio" disabled checked={schedule.isMasterSchedule} />
                  <span className="tit">스케줄 {index + 1}</span>
                </label>
              </div>
              <div className="box-inner">
                {schedule.compareScheduleDetails.map((detail, idx) => (
                  <div key={idx} className="air-plan">
                    <div className="left-col">
                      <img
                        className="left-col aircraft"
                        src={AIR_ICONS_PATH[detail.flightDetails?.[0]?.airlineCode] || ""}
                        onError={onErrorImgAirline}
                        alt="airline"
                      />
                      <div className="left-col plan">
                        <span className="time">{momentKR(detail.flightDetails?.[0]?.fromDate).format("HH:mm")}</span>
                        <span className="city">{detail.flightDetails?.[0]?.fromAirportCode}</span>
                      </div>
                      <div className={`left-col way-to n${detail.stopoverNo}`}></div>
                      <div className="left-col plan">
                        <span
                          className="time"
                          data-more-time={
                            detail.flightDetails?.[0]?.dateVariation !== 0
                              ? `${inequalitySign(detail.flightDetails?.[0]?.dateVariation)}${detail.flightDetails?.[0]?.dateVariation}`
                              : ""
                          }
                        >
                          {momentKR(detail.flightDetails?.[detail.flightDetails?.length - 1]?.toDate).format("HH:mm")}
                        </span>
                        <span className="city">{detail.flightDetails?.[detail.flightDetails?.length - 1]?.toAirportCode}</span>
                      </div>
                      <p className="share">
                        {detail.flightDetails?.[0]?.airlineName}
                        {detail.flightDetails?.[0]?.airlineCode !== detail.flightDetails?.[0]?.operatingAirlineCode && ", 공동운항"}
                      </p>
                    </div>
                    <div className="right-col">
                      <p className="via none">{detail.stopoverNo === "0" ? <p>직항</p> : <p>{detail.stopoverNo}회 경유</p>}</p>
                      <p className="total-time">
                        {detail.totalHour}시간 {detail.totalMin}분
                      </p>
                    </div>
                  </div>
                ))}
                <div className="sec-bottom">
                  <div className="total-price">
                    <strong>{schedule.totalAmount.toLocaleString()}원</strong>
                    {schedule.isCorporateFare && "기업운임"}
                  </div>
                  <div className="box-btn"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      <div className="box-btn-close layer-pages-close type-arrow">
        <button type="button" className="btns-cmm" onClick={onClose}>
          닫기
        </button>
      </div>
    </div>
  );
};

export default AirCompareSchedule;
