import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { appProgressBar } from "@/utils/app";
import { momentKR } from "@/utils/date";
import { useState } from "react";
import { Fragment } from "react/jsx-runtime";
import { usePgReceiptView } from "@/app/hooks/usePgReceiptView";
import { CHARGE_TYPE } from "@/constants/app";
import request from "@/utils/request";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

function InvoiceAndReceipt(props) {
  const { isOpen, onClose } = props;
  const {
    data: { travel = {} },
    invoice: { invoiceMasterList },
    payInfo: { paymentList },
    bookingAirTickets,
  } = useAppSelector(selectReservationTravelView);
  const [activedElement, setActivedElement] = useState({ invoice: true, payInfo: true });
  const { pgReceiptView } = usePgReceiptView();

  const viewCrsCardReceipt = () => {
    appProgressBar.on();
    request({ url: `/m/travels/${travel.id}/crsCardReceipt/link/${travel.bookingAir?.id}`, method: "GET" })
      .then((res) => {
        const url = res.data;
        if (!url) {
          alert(`CRS카드전표 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        if (url.indexOf("Error") > 0 || url.indexOf("Exception") > 0) {
          alert(`CRS카드전표 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        window.open(url, "crsCardReceipt_" + travel.bookingAir?.id);
      })
      .catch(() => alert(`CRS카드전표 발행 오류.${CONTACT_ADMIN_ALERT}`))
      .finally(() => appProgressBar.off());
  };

  return (
    <div className="jquery-modal blocker current" style={{ display: isOpen ? "block" : "none" }}>
      <div id="receiptInvoicePop" className="modal-wrap inner-haed type-alrt modal" style={{ display: "inline-block" }}>
        <div className="modal-cotn">
          <div className="modal-head">영수증 / 인보이스</div>
          <div className="modal-body">
            <div className="box-acoodi">
              <div className={`box-item receipt ${activedElement.payInfo ? "active" : ""}`}>
                <p className="tit">영수증</p>
                <div className="tbl-cmm">
                  <table className="table">
                    <colgroup>
                      <col style={{ width: "120px" }} />
                      <col style={{ width: "*" }} />
                      <col style={{ width: "56px" }} />
                    </colgroup>
                    <thead>
                      <tr>
                        <th>티켓번호</th>
                        <th>항목</th>
                        <th>영수증</th>
                      </tr>
                    </thead>
                    <tbody>
                      <Fragment>
                        {bookingAirTickets?.map((ticket, ticketIndex) =>
                          ticket?.bookingAirTicketPayments?.map((payment, paymentIndex) => {
                            const isMatching = payment.paymentType === "CARD" && travel?.bookingAir?.gdsType === "AMADEUS";
                            return (
                              isMatching && (
                                <tr key={`${ticketIndex}-${paymentIndex}`}>
                                  <td>{ticket.ticketNo}</td>
                                  <td>{payment?.airFareType?.value}</td>
                                  {payment?.voidType !== "Emd" && payment?.voidType !== "EmdVoid" && payment?.voidType !== "EmdRefund" && (
                                    <td>
                                      <button
                                        type="button"
                                        className="btn-default btn-view-receipt"
                                        onClick={() => viewCrsCardReceipt("user", ticket?.id)}
                                      >
                                        보기
                                      </button>
                                    </td>
                                  )}
                                </tr>
                              )
                            );
                          }),
                        )}
                        {paymentList?.map(
                          (payment, paymentIndex) =>
                            payment?.paymentStatus !== "ERROR" &&
                            payment?.gubun !== "GROUP_DEPOSIT" &&
                            payment?.gubun !== "VISA_DEPOSIT" &&
                            payment?.gubun !== "GROUP_REFUND" &&
                            payment?.gubun !== "VISA_REFUND" &&
                            payment?.gubun !== "MULTI_DEPOSIT" &&
                            payment?.gubun !== "MULTI_REFUND" &&
                            payment?.gubun !== "REFUND" &&
                            payment?.cardList?.map((card, cardIndex) => {
                              return (
                                <tr key={`${paymentIndex}-${cardIndex}`}>
                                  <td>
                                    {payment.paymentChargeList.map((charge, ticketIndex) => (
                                      <Fragment key={ticketIndex}>
                                        {charge.ticketNumber}
                                        {ticketIndex < payment.paymentChargeList.length - 1 && ", "}
                                      </Fragment>
                                    ))}
                                  </td>
                                  <td>
                                    {payment?.paymentChargeList?.map((charge, chargeIndex) => (
                                      <Fragment key={chargeIndex}>
                                        {CHARGE_TYPE[charge?.chargeType]}
                                        {chargeIndex < payment?.paymentChargeList?.length - 1 && ", "}
                                      </Fragment>
                                    ))}
                                  </td>
                                  <td>
                                    <button
                                      type="button"
                                      className="btn-default btn-view-receipt"
                                      onClick={() => pgReceiptView(card?.tId, card?.approvalNumber)}
                                    >
                                      보기
                                    </button>
                                  </td>
                                </tr>
                              );
                            }),
                        )}
                      </Fragment>
                    </tbody>
                  </table>
                </div>
                <button className="btn-arrow" onClick={() => setActivedElement((prev) => ({ ...prev, payInfo: !prev.payInfo }))}></button>
              </div>
              <div className={`box-item invoice ${activedElement.invoice ? "active" : ""}`}>
                <p className="tit">인보이스</p>
                <div className="tbl-cmm">
                  <table className="table">
                    <colgroup>
                      <col style={{ width: "*" }} />
                      <col style={{ width: "100px" }} />
                      <col style={{ width: "110px" }} />
                    </colgroup>
                    <thead>
                      <tr>
                        <th>번호</th>
                        <th>등록일</th>
                        <th>다운로드</th>
                      </tr>
                    </thead>
                    <tbody>
                      {!invoiceMasterList ? (
                        <tr>
                          <td colSpan="3">인보이스가 없습니다.</td>
                        </tr>
                      ) : (
                        invoiceMasterList.map((invoice, index) => (
                          <tr key={index}>
                            <td>{invoice.invoiceId}</td>
                            <td>{momentKR(invoice.createDate).format("YYYY-MM-DD")}</td>
                            <td>
                              <a href={`${import.meta.VITE_STORAGE_URL}/btms/invc/${invoice.fileName}`} target="_blank" className="btns-cmm">
                                PDF 다운로드
                              </a>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
                <button className="btn-arrow" onClick={() => setActivedElement((prev) => ({ ...prev, invoice: !prev.invoice }))}></button>
              </div>
            </div>
          </div>
          <a href="#receiptInvoicePop" className="btn-close" rel="modal:close" onClick={onClose}></a>
        </div>
      </div>
    </div>
  );
}

export default InvoiceAndReceipt;
