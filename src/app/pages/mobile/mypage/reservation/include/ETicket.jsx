import { CONTACT_ADMIN_ALERT } from "@/constants/common";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import "@/styles/mobile/css/eticket.css";
import { appProgressBar } from "@/utils/app";
import request from "@/utils/request";

function ETicket(props) {
  const { isOpen, onClose } = props;
  const {
    data: { travel },
    eticketListMap,
  } = useAppSelector(selectReservationTravelView);

  const viewTicket = (event, ticket) => {
    if (ticket.gdsType === "DIRECT" && !ticket.eticket4Direct) {
      event.preventDefault();
      return;
    }
    const eticket4DirectFilePath = ticket.eticket4Direct;
    if (eticket4DirectFilePath) {
      const eTicketUrl = `${import.meta.env.VITE_STORAGE_URL}/btms/${eticket4DirectFilePath}`;
      window.open(eTicketUrl, "_blank");
      return;
    }
    const eTicketUrls =
      travel.bookingAir?.gdsType === "SABRE" ? Object.entries(eticketListMap).flatMap(([key, value]) => value.map((item) => item.eticket)) : [];
    const sabreEticketUrl = ticket.eticket;
    if (eTicketUrls.length > 0 && typeof sabreEticketUrl != "undefined" && sabreEticketUrl != "") {
      window.open(sabreEticketUrl, "eticket_" + ticket.id);
      return;
    }
    if (eTicketUrls.length > 0) {
      alert(`E-Ticket SABRE 발행 오류.${CONTACT_ADMIN_ALERT}`);
      return;
    }
    appProgressBar.on();
    request({ url: `/user/travels/${travel.id}/ticket/link/${travel.bookingAir?.id}`, method: "GET" })
      .then(function (response) {
        const url = response.data;
        if (!url) {
          alert(`E-Ticket 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        if (url.indexOf("Error") > 0 || url.indexOf("Exception") > 0) {
          alert(`E-Ticket 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        window.open(url, "eticket_" + travel.bookingAir?.id);
      })
      .catch(() => alert(`E-Ticket 발행 오류.${CONTACT_ADMIN_ALERT}`))
      .finally(() => appProgressBar.off());
  };

  return (
    <div id="eTicketLayerPop" className="modal-wrap inner-haed type-alrt" style={{ display: isOpen ? "block" : "none" }}>
      <div className="modal-cotn">
        <div className="modal-head">E-Ticket</div>
        <div className="modal-body">
          <div className="box-acoodi">
            <div className="box-item receipt active">
              <div className="tbl-cmm">
                <table className="table">
                  <colgroup>
                    <col style={{ width: "180px" }} />
                    <col style={{ width: "*" }} />
                  </colgroup>
                  <thead>
                    <tr>
                      <th>이름</th>
                      <th>E-ticket</th>
                    </tr>
                  </thead>
                  <tbody>
                    {travel.bookingAir?.bookingAirTravelers?.map((traveler) =>
                      Object.entries(eticketListMap).map(
                        ([key, value], index) =>
                          index === parseInt(key) &&
                          !travel.isCancel &&
                          value.map(
                            (ticket) =>
                              !["Emd", "EmdVoid", "EmdRefund", "AgentVoid", "Refund"].includes(ticket.voidType) && (
                                <tr key={`${traveler.id}_${ticket.id}`}>
                                  <td>
                                    {traveler.lastName} {traveler.firstName}
                                  </td>
                                  <td>
                                    <a href="#" className="btns-cmm" onClick={viewTicket}>
                                      확인
                                    </a>
                                  </td>
                                </tr>
                              ),
                          ),
                      ),
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <a href="#eTicketLayerPop" className="btn-close" rel="modal:close" onClick={onClose}></a>
      </div>
    </div>
  );
}

export default ETicket;
