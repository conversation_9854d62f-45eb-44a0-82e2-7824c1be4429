import { useMemo, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store";
import { appMobileLoading } from "@/utils/app";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { actionCodeList, selectReservationTravelView } from "@/store/travelViewSlice";
import { momentKR } from "@/utils/date";
import CancelRequest from "@/app/pages/mobile/mypage/reservation/include/ModifyRequest/CancelRequest";
import QnaWrite from "@/app/pages/mobile/mypage/board/QnaWrite";
import QnaRegistPop from "@/app/pages/mobile/mypage/board/include/QnaRegistPop";

function ModifyRequest() {
  const {
    data: { travel = {}, travelModifyRequestRes = [] },
  } = useAppSelector(selectReservationTravelView);
  const dispatch = useAppDispatch();
  const [isOpenModal, setIsOpenModal] = useState({ cancel: false, qnaWrite: false, qnaRegistPop: false });
  const [activedElement, setActivedElement] = useState([]);

  const splitedTravelModifyRequest = useMemo(() => {
    const parents = [];
    const childs = {};
    travelModifyRequestRes?.forEach((item) => {
      if (item.parentTravelModifyRequestId === 0) {
        parents.push(item);
        return;
      }
      if (!childs[item.parentTravelModifyRequestId]) {
        childs[item.parentTravelModifyRequestId] = [];
      }
      childs[item.parentTravelModifyRequestId].push(item);
    });
    return { parents, childs };
  }, [travelModifyRequestRes]);

  const handleOpenQnaWrite = () => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    dispatch(actionCodeList({ groupCode: "QNA_CATEGORY_CODE" })).finally(() => {
      appMobileLoading.off();
      setIsOpenModal((prev) => ({ ...prev, qnaWrite: true }));
    });
  };

  const handleToggleBoxItem = (requestId) => {
    setActivedElement((prev) => {
      if (!prev.includes(requestId)) {
        return [...prev, requestId];
      }
      return prev.filter((item) => item !== requestId);
    });
  };

  return (
    <>
      <div className="contents reserv-payment-cancel" id="includeModifyRequest">
        <div className="sec-penalty">
          <div className="box-btns ta-c">
            <button
              type="button"
              className="btns-cmm round-basic color-b w-75 btn-request"
              data-page-layer="cancel-request"
              onClick={() => setIsOpenModal((prev) => ({ ...prev, cancel: true }))}
            >
              변경 / 취소 요청
            </button>
          </div>
          <p className="onetoone">
            해당 예약 이외 궁금하신 점은{" "}
            <a href="#none;" className="more" data-page-layer="qna-write" onClick={handleOpenQnaWrite}>
              1:1 문의
            </a>{" "}
            를 이용해 주시기 바랍니다.
          </p>
        </div>
        <div className="sec-cancel">
          <p className="sec-tit">변경/취소 요청 내역</p>
          {travelModifyRequestRes?.length > 0 ? (
            <div className="sec-qna-list">
              {splitedTravelModifyRequest.parents?.map((parent) => (
                <div
                  className={`box-item ${activedElement.includes(parent.travelModifyRequestId) ? "active" : ""}`}
                  key={parent.travelModifyRequestId}
                >
                  <div className="box-top">
                    {parent.isLeaf === "0" ? <p className="status complete">답변완료</p> : <p className="status ready">답변대기</p>}
                    <p className="tit">
                      {parent.requestInfo}
                      <span className="date">{momentKR(parent.createDate).format("YYYY.MM.DD")}</span>
                    </p>
                  </div>
                  {parent.isLeaf === "0" && (
                    <button type="button" className="btn-arrow" onClick={() => handleToggleBoxItem(parent.travelModifyRequestId)} />
                  )}
                  {splitedTravelModifyRequest.childs[parent.travelModifyRequestId]?.map((child) => (
                    <div className="box-bottom" key={child.travelModifyRequestId}>
                      <p className="label">
                        <span>답변</span>
                      </p>
                      <div className="desc-reply">{child.answer}</div>
                      <p className="date">{momentKR(child.createDate).format("YYYY.MM.DD")}</p>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          ) : (
            <div className="box-none">요청하신 내역이 없습니다.</div>
          )}
        </div>
      </div>
      <CancelRequest isOpen={isOpenModal.cancel} onClose={() => setIsOpenModal((prev) => ({ ...prev, cancel: false }))} travel={travel} />
      {isOpenModal.qnaWrite && (
        <QnaWrite
          onClose={() => setIsOpenModal((prev) => ({ ...prev, qnaWrite: false }))}
          onOpenQnaRegistPop={() => setIsOpenModal((prev) => ({ ...prev, qnaRegistPop: true }))}
        />
      )}
      <QnaRegistPop isOpen={isOpenModal.qnaRegistPop} onClose={() => setIsOpenModal((prev) => ({ ...prev, qnaRegistPop: false }))} />
    </>
  );
}

export default ModifyRequest;
