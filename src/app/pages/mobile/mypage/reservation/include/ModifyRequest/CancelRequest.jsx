import { requestWithMobileLoading } from "@/utils/app";
import { get } from "lodash";
import QueryString from "qs";
import { useState } from "react";
import ChangeRequestPop from "@/app/pages/mobile/mypage/reservation/include/ModifyRequest/ChangeRequestPop";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

function CancelRequest(props) {
  const { isOpen, onClose, travel } = props;
  const [requestInfo, setRequestInfo] = useState("");
  const [isDisableSubmit, setIsDisableSubmit] = useState(false);
  const [isOpenChangeRequestPop, setIsOpenChangeRequestPop] = useState(false);

  const modifyRequest = () => {
    if (!requestInfo.trim()) {
      alert("변경/취소 사유를 입력해주세요.");
      return;
    }
    if (requestInfo.trim().length > 500) {
      alert("500자 이하로 입력해주세요.");
      return;
    }
    setIsDisableSubmit(true);
    requestWithMobileLoading({
      url: "/m/reservation/travel/air/modifyRequestProcess",
      method: "POST",
      data: QueryString.stringify({ travelId: travel.id, requestInfo, parentTravelModifyRequestId: 0 }),
      success: (res) => {
        const { resultCode } = get(res, "data", {});
        if (resultCode?.toLocaleUpperCase() !== "SUCCESS") {
          alert(`변경요청 내용 등록 중 에러가 발생하였습니다.\n다시 시도해 주세요.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        handleClose();
        setIsOpenChangeRequestPop(true);
      },
      end: () => setIsDisableSubmit(false),
    });
  };

  const handleClose = () => {
    onClose();
    setRequestInfo("");
  };

  return (
    <>
      <div id="modifyRequestLayer" className="layer-pages cancel-request" style={{ display: isOpen ? "block" : "none" }}>
        <div className="layer-head">
          <p className="tit">변경 / 취소 요청</p>
        </div>
        <div className="layer-cotn">
          <dl className="desc">
            <dt>담당자 확인 후, 예약의 변경/취소가 진행됩니다.</dt>
            <dd>• 항공권 변경/취소 시 발권대행료는 환불되지 않습니다.</dd>
            <dd>
              • 예약에 따라 변경 수수료가 발생 할 수 있으니, 항공권의 요금 규정에 따라 변경 가능 여부를 확인 한 후 진행 합니다. 자세한 사항은
              담당자에게 문의 바랍니다.
            </dd>
          </dl>
          <div className="passenger">
            <dl>
              <dt>탑승객</dt>
              {travel.bookingAir?.bookingAirTravelers?.map((traveler) => (
                <dd key={traveler.id}>
                  {traveler.lastName} <span>{traveler.firstName}</span>
                </dd>
              ))}
            </dl>
          </div>
          <div className="box-textarea">
            <textarea
              className="border-[1px]"
              id="requestInfo"
              placeholder="요청 하실 내용을 입력하세요. (10자-500자)"
              maxLength={500}
              value={requestInfo}
              onChange={(event) => setRequestInfo(event.target.value)}
            />
            <p className="desc">취소 요청 및 환불 금액이 확정되면 이메일을 발송해 드립니다. </p>
          </div>
          <div className="box-btn">
            <button type="button" className="btns-cmm round-basic color-w layer-pages-close" onClick={handleClose}>
              취소
            </button>
            <button disabled={isDisableSubmit} type="button" className="btns-cmm round-basic color-b" id="changeProcessBtn" onClick={modifyRequest}>
              요청
            </button>
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={handleClose}></button>
        </div>
      </div>
      <ChangeRequestPop travelId={travel.id} isOpen={isOpenChangeRequestPop} />
    </>
  );
}

export default CancelRequest;
