function ChangeRequestPop(props) {
  const { travelId, isOpen } = props;

  const modifyRequestReload = () => {
    location.href = "/m/reservation/travel/view/" + travelId + "?includeTabId=includeModifyRequest";
  };

  return (
    <div className="jquery-modal blocker current" style={{ display: isOpen ? "block" : "none" }}>
      <div id="changeRequestPop" className="modal-wrap type-alrt modal !inline-block">
        <div className="modal-cotn">
          <p className="desc">요청이 완료 되었습니다.</p>
          <div className="box-btn">
            <a href="#changeRequestPop" onClick={modifyRequestReload} className="btns-cmm round-basic color-b w-50">
              확인
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ChangeRequestPop;
