import { comma } from "@/utils/common";
import { useState } from "react";
import { momentKR } from "@/utils/date";
import { useAppDispatch, useAppSelector } from "@/store";
import { appMobileLoading } from "@/utils/app";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { actionBookingAirTicket, actionInvoiceList, actionPayInfoList, selectReservationTravelView } from "@/store/travelViewSlice";
import InvoiceAndReceipt from "@/app/pages/mobile/mypage/reservation/include/InvoiceAndReceipt";

function PaymentInfo() {
  const {
    data: { travel = {} },
  } = useAppSelector(selectReservationTravelView);
  const dispatch = useAppDispatch();
  const [isOpenInvoiceAndReceipt, setIsOpenInvoiceAndReceipt] = useState(false);

  const handleOpenInvoiceAndReceipt = () => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    Promise.all([actionInvoiceList, actionPayInfoList, actionBookingAirTicket].map((action) => dispatch(action({ travelId: travel.id })))).finally(
      () => {
        appMobileLoading.off();
        setIsOpenInvoiceAndReceipt(true);
      },
    );
  };

  return (
    <div className="contents reserv-payment-cotn" id="includePaymentInfo">
      {travel.bookingAir?.bookingAirLowest && (
        <div className="sec-bills-info">
          <div className="box-item">
            <div className="clearfix">
              <p className="txt">최저가 요금</p>
              <p className="val">{comma(travel.bookingAir?.bookingAirLowest?.totalAmount)}원</p>
            </div>
          </div>
        </div>
      )}
      <div className="sec-bills-info">
        <div className="box-item">
          <p className="tit">
            성인 <span>{travel.bookingAir?.adultCount}</span>명
          </p>
          <div className="clearfix">
            <span className="txt">항공료</span>
            <span className="val">{comma(travel.bookingAir?.fareAmount)}원</span>
          </div>
          <div className="clearfix">
            <span className="txt">TAX</span>
            <span className="val">{comma(travel.bookingAir?.taxAmount)}원</span>
          </div>
          <div className="clearfix">
            <span className="txt">발권수수료</span>
            <span className="val">{comma(travel.bookingAir?.commissionAmount)}원</span>
          </div>
          <div className="total">
            <p className="txt">총 결제금액</p>
            <p className="val">{comma(travel.bookingAir?.totalAmount)}원</p>
          </div>
          {[396, 397].includes(travel.statusCode?.id) && (
            <p className="desc-date">
              {travel.statusCode?.id === 396 ? "결재일시" : "취소일시"}:{" "}
              {momentKR(travel.bookingAir?.modifyDate).format("YYYY년 MM월 DD일 (ddd) HH:mm")}
            </p>
          )}
        </div>
      </div>
      {/* {travel.statusCode?.id === 396 && ( */}
      <div className="box-btn bil-invoice">
        <a href="#none;" className="btns-cmm round-basic color-b w-75" onClick={handleOpenInvoiceAndReceipt}>
          영수증 / 인보이스
        </a>
      </div>
      {/* )} */}
      <InvoiceAndReceipt isOpen={isOpenInvoiceAndReceipt} onClose={() => setIsOpenInvoiceAndReceipt(false)} />
    </div>
  );
}

export default PaymentInfo;
