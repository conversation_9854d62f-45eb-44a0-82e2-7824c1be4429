import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { cloneDeep, get, isEmpty, range } from "lodash";
import { useEffect, useState } from "react";
import { set } from "lodash";
import { actionGetAccountInfo, selectAccountInfo } from "@/store/mainSlice.mobile";
import { appMobileLoading, requestWithMobileLoading } from "@/utils/app";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import DepartmentSelect from "@/app/components/mobile/common/DepartmentSelect";
import qs from "qs";
import "@/styles/mobile/css/accountInfo.css";
import { useNavigate } from "react-router-dom";
import request from "@/utils/request";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const CELL_PHONE_NUMBER_OPTIONS = [
  { value: "010", label: "010" },
  { value: "011", label: "011" },
  { value: "016", label: "016" },
  { value: "019", label: "019" },
];

const PHONE_NUMBER_OPTIONS = [
  { value: "02", label: "02" },
  { value: "031", label: "031" },
  { value: "032", label: "032" },
  { value: "033", label: "033" },
  { value: "041", label: "041" },
  { value: "042", label: "042" },
  { value: "043", label: "043" },
  { value: "044", label: "044" },
  { value: "051", label: "051" },
  { value: "052", label: "052" },
  { value: "053", label: "053" },
  { value: "054", label: "054" },
  { value: "055", label: "055" },
  { value: "061", label: "061" },
  { value: "062", label: "062" },
  { value: "063", label: "063" },
  { value: "064", label: "064" },
];

const CUSTOMER_PASSPORT_OTG01_OPTIONS = [
  { value: "", label: "선택" },
  { value: "23", label: "한국" },
  { value: "214", label: "미국" },
  { value: "19", label: "일본" },
  { value: "10", label: "중국" },
];

const INIT_FORM_DATA = {
  userId: "",
  email: "",
  name: "",
  departmentId: "",
  birthday: "",
  cellPhoneNumber1: "",
  cellPhoneNumber2: "",
  cellPhoneNumber3: "",
  phoneNumber1: "",
  phoneNumber2: "",
  currentPassword: "",
  password: "",
  passwordCheck: "",
  gender: "",
  mileageInfos: [
    { mileageInfoId: "", airline: "", mileageMemberNo: "" },
    { mileageInfoId: "", airline: "", mileageMemberNo: "" },
    { mileageInfoId: "", airline: "", mileageMemberNo: "" },
    { mileageInfoId: "", airline: "", mileageMemberNo: "" },
    { mileageInfoId: "", airline: "", mileageMemberNo: "" },
  ],
  isEmailReceive: false,
  isSmsReceiveYn: false,
  accountingCode: "",
  position: { id: "" },
  employeeNo: "",
  customerPassport: {
    firstName: "",
    lastName: "",
    passportNumber: "",
    expireYmd: "",
    country: { id: "" },
  },
  workspace: { Id: "" },
  department: { id: "" },
};

const PHONE_DATE_FIELDS = ["birthday", "cellPhoneNumber2", "cellPhoneNumber3", "phoneNumber2", "expireYmd"];
const NAME_FIELDS = ["firstName", "lastName"];
const PASSPORT_FIELDS = ["passportNumber"];
const CHECKBOX_FIELDS = ["isEmailReceive", "isSmsReceiveYn"];
const CELL_PHONE_REGEXP = /^01([0|1|6|7|8|9])([1-9])([0-9]{2,3})([0-9]{4})$/;
const PHONE_REGEXP = /^0(2|3[1-3]|4[1-4]|5[1-5]|6[1-4])([0-9]{3,4})([0-9]{4})$/;

function AccountInfo(props) {
  const { onClose } = props;
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);
  const { customer, workspaceList, positionList, countryList } = useAppSelector(selectAccountInfo);
  const [formData, setFormData] = useState(INIT_FORM_DATA);

  const handleChangeFormData = (event) => {
    let { name, value, checked } = event.target;
    if (PHONE_DATE_FIELDS.some((field) => name.includes(field))) {
      value = value.replace(/[^0-9]/g, "");
    } else if (NAME_FIELDS.some((field) => name.includes(field))) {
      value = value.replace(/[^a-zA-Z]/g, "").toUpperCase();
    } else if (PASSPORT_FIELDS.some((field) => name.includes(field))) {
      value = value.replace(/[^a-zA-Z0-9]/g, "").toUpperCase();
    }
    if (CHECKBOX_FIELDS.some((field) => name.includes(field))) {
      value = checked;
    }
    setFormData((prev) => set(cloneDeep(prev), name, value));
  };

  const modifyProcess = (event) => {
    event.preventDefault();
    const { cellPhoneNumber1, cellPhoneNumber2, cellPhoneNumber3, phoneNumber1, phoneNumber2, department, passwordCheck, ...nest } = formData;
    const payload = {
      ...nest,
      phoneNumber: cellPhoneNumber2 ? `${phoneNumber1}${phoneNumber2}` : "",
      cellPhoneNumber: `${cellPhoneNumber1}${cellPhoneNumber2}${cellPhoneNumber3}`,
      departmentId: department.id,
    };
    if (!isValidationForm(payload, formData)) {
      return;
    }
    if (payload.password || passwordCheck) {
      newPasswordValidCheck(passwordCheck, payload);
      return;
    }
    accountPasswordCheckProcess(payload);
  };

  const isValidationForm = (payload, formData) => {
    if (!payload.birthday) {
      alert("생년월일을 입력해주세요.");
      return false;
    }
    if (!formData.cellPhoneNumber2) {
      alert("휴대전화를 입력해주세요.");
      return false;
    }
    if (!formData.cellPhoneNumber3) {
      alert("휴대전화를 입력해주세요.");
      return false;
    }
    if (!payload.departmentId) {
      alert("부서를 선택해주세요.");
      return false;
    }
    if (payload.cellPhoneNumber && !CELL_PHONE_REGEXP.test(payload.cellPhoneNumber)) {
      alert("휴대전화가 올바르지 않습니다.");
      return false;
    }
    if (payload.phoneNumber && !PHONE_REGEXP.test(payload.phoneNumber)) {
      alert("전화번호가 올바르지 않습니다.");
      return false;
    }
    if (payload.customerPassport.expireYmd && payload.customerPassport.expireYmd.length < 8) {
      alert("여권 유효기간이 올바르지 않습니다.");
      return false;
    }
    if (payload.birthday && payload.birthday.length < 8) {
      alert("생년월일이 올바르지 않습니다.");
      return false;
    }
    if (
      payload.customerPassport.lastName ||
      payload.customerPassport.firstName ||
      payload.customerPassport.passportNumber ||
      payload.customerPassport.expireYmd ||
      payload.customerPassport.country.id
    ) {
      if (
        !payload.customerPassport.lastName ||
        !payload.customerPassport.firstName ||
        !payload.customerPassport.passportNumber ||
        !payload.customerPassport.expireYmd ||
        !payload.customerPassport.country.id
      ) {
        alert("여권정보를 모두 입력해 주세요.");
        return false;
      }
    }
    return true;
  };

  const newPasswordValidCheck = (passwordCheck, payload) => {
    const { password } = payload;
    if (!password) {
      alert("신규 비밀번호를 입력해주세요.");
      return;
    }
    if (!passwordCheck) {
      alert("비밀번호를 재입력해주세요.");
      return;
    }
    if (password !== passwordCheck) {
      alert("비밀번호가 일치하지 않습니다.\n다시 입력해주세요.");
      return;
    }
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    request({
      url: "/customer/v2/join/checkValidPassword",
      data: qs.stringify({ password }),
      method: "POST",
    })
      .then((res) => {
        const resultCode = get(res, "data.resultCode", "");
        if (resultCode == "CONTINUOUS_NUMBER") {
          alert("연속되는 숫자(문자)를 3자 이상 입력하실 수 없습니다.");
          appMobileLoading.off();
          return;
        }
        if (resultCode == "PASSWORD_ONE_CHAR") {
          alert("같은 숫자(문자)를 3번 이상 반복 입력하실 수 없습니다.");
          appMobileLoading.off();
          return;
        }
        if (resultCode != "OK") {
          alert("영문자, 숫자, 특수문자를 조합하여 8~20자로 다시 입력해 주세요.");
          appMobileLoading.off();
          return;
        }
        accountPasswordCheckProcess(payload);
      })
      .catch(() => appMobileLoading.off());
  };

  const accountPasswordCheckProcess = (payload) => {
    if (!payload.currentPassword) {
      alert("현재 비밀번호를 입력해주세요.");
      appMobileLoading.off();
      return;
    }
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    request({
      url: "/customer/v2/account/accountPasswordCheckProcess",
      data: qs.stringify({ password: payload.currentPassword }),
      method: "POST",
    })
      .then((res) => {
        const resultCode = get(res, "data.resultCode", "");
        if (resultCode != "SUCCESS") {
          alert("현재 비밀번호가 일치하지 않습니다. 다시 입력해주세요.");
          setFormData((prev) => ({ ...prev, currentPassword: "" }));
          appMobileLoading.off();
          return;
        }
        modifyInfoProcess(payload);
      })
      .catch(() => appMobileLoading.off());
  };

  const modifyInfoProcess = (payload) => {
    requestWithMobileLoading({
      url: "/customer/m/account/infoProcess",
      data: qs.stringify(payload),
      method: "POST",
      success: (res) => {
        const { resultCode = "", message = "" } = get(res, "data", {});
        if (resultCode == "SUCCESS") {
          alert("수정 되었습니다.");
          navigate("/m/main");
          return;
        }
        if (message == "CONTINUOUS_NUMBER") {
          alert("연속되는 숫자를 3자 이상 입력하실 수 없습니다.");
          return;
        }
        if (message == "PASSWORD_ONE_CHAR") {
          alert("연속되는 문자열 3번이상 반복 입력하실 수 없습니다.");
          return;
        }
        if (message == "PASSWORD_NOT_MATCH") {
          alert("현재 비밀번호가 일치하지 않습니다.");
          return;
        }
        if (message == "NEW_PASSWORD_DUPLICATE_CURRENT_PASSWORD") {
          alert("신규 비밀번호가 현재 비밀번호와 일치합니다.");
          return;
        }
        if (message == "NEW_PASSWORD_DUPLICATE_OLD_PASSWORD") {
          alert("신규 비밀번호가 이전 비밀번호와 일치합니다.");
          return;
        }
        if (message == "") {
          alert("영문자, 숫자, 특수문자를 조합하여 8~20자로 다시 입력해 주세요.");
          return;
        }
        if (message == "PASSWORD_LEN") {
          alert("비밀번호가 너무 짧습니다. \n영문자, 숫자, 특수문자를 조합하여 8~20자로 다시 입력해 주세요.");
          return;
        }
        alert(`정보수정 중 에러가 발생하였습니다.\n다시 시도해 주세요.${CONTACT_ADMIN_ALERT}`);
      },
      fail: (error) => {
        if (error.response?.status === 401) {
          location.href("/loginExpired");
          return;
        }
        alert(`수정중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
      },
    });
  };

  useEffect(() => {
    if (isEmpty(customer)) {
      return;
    }
    const { accountingCode = "", cellPhoneNumber, workspace, phoneNumber, mileageInfos, position, customerPassport } = customer;
    const { firstName = "", lastName = "", passportNumber = "", expireYmd = "", country = {} } = customerPassport ?? {};
    setFormData((prev) => ({
      ...prev,
      ...customer,
      password: "",
      accountingCode,
      position: { id: position?.id ?? "" },
      workspace: { Id: workspace?.id ?? "" },
      cellPhoneNumber1: cellPhoneNumber.substring(0, 3),
      cellPhoneNumber2: cellPhoneNumber.length === 11 ? cellPhoneNumber.substring(3, 7) : cellPhoneNumber.substring(3, 6),
      cellPhoneNumber3: cellPhoneNumber.length === 11 ? cellPhoneNumber.substring(7, 11) : cellPhoneNumber.substring(6, 10),
      phoneNumber1: phoneNumber?.substring(0, 2) == "02" ? phoneNumber?.substring(0, 2) : (phoneNumber?.substring(0, 3) ?? ""),
      phoneNumber2: phoneNumber?.substring(0, 2) == "02" ? phoneNumber?.substring(2) : (phoneNumber?.substring(3) ?? ""),
      customerPassport: { firstName, lastName, passportNumber, expireYmd, country: { id: country.id ?? "" } },
      mileageInfos: !isEmpty(mileageInfos)
        ? mileageInfos.map((item) => ({ mileageInfoId: item.mileageInfoId, airline: item.airline, mileageMemberNo: item.mileageMemberNo }))
        : INIT_FORM_DATA.mileageInfos,
    }));
  }, [customer]);

  useEffect(() => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    dispatch(actionGetAccountInfo());
  }, [dispatch]);

  return (
    <form id="infoForm" name="infoForm">
      <div className="layer-pages edit-profile !block">
        <div className="layer-head">
          <p className="tit">회원정보</p>
        </div>
        <div className="layer-cotn">
          <div className="box-info default">
            <h2 className="title">기본 정보</h2>
            <p className="form-tit">회원 아이디</p>
            <div className="form-cn">
              <span className="box-val" id="email">
                {userInfo.email}
              </span>
            </div>
            <p className="form-tit">이름</p>
            <div className="form-cn">
              <span className="box-val">{userInfo.name}</span>
            </div>
            <p className="form-tit">
              현재 비밀번호<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="password"
                  id="currentPassword"
                  name="currentPassword"
                  maxLength="20"
                  autoComplete="off"
                  value={formData.currentPassword}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">
              신규 비밀번호<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="password"
                  id="password"
                  name="password"
                  maxLength="20"
                  autoComplete="off"
                  value={formData.password}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">
              신규 비밀번호 재입력<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="password"
                  id="passwordCheck"
                  name="passwordCheck"
                  maxLength="20"
                  autoComplete="off"
                  value={formData.passwordCheck}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">
              성별<span>*</span>
            </p>
            <div className="form-cn half-col">
              <label className="item-col form-radio">
                <input type="radio" name="gender" id="genderMale" value="Male" checked={formData.gender === "Male"} onChange={handleChangeFormData} />
                <span>남성</span>
              </label>
              <label className="item-col form-radio">
                <input
                  type="radio"
                  name="gender"
                  id="genderFemale"
                  value="Female"
                  checked={formData.gender === "Female"}
                  onChange={handleChangeFormData}
                />
                <span>여성</span>
              </label>
            </div>
            <p className="form-tit">
              생년월일<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="text"
                  id="birthday"
                  name="birthday"
                  placeholder="예.19900531"
                  maxLength="8"
                  value={formData.birthday}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">
              휴대전화<span>*</span>
            </p>
            <div className="form-cn cellphone">
              <span className="select-cmm">
                <select id="cellPhoneNumber1" name="cellPhoneNumber1" value={formData.cellPhoneNumber1} onChange={handleChangeFormData}>
                  {CELL_PHONE_NUMBER_OPTIONS.map((option) => (
                    <option value={option.value} key={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </span>
              <div className="box-right">
                <span className="input-cmm num-front">
                  <input
                    type="text"
                    id="cellPhoneNumber2"
                    name="cellPhoneNumber2"
                    value={formData.cellPhoneNumber2}
                    maxLength="4"
                    onChange={handleChangeFormData}
                  />
                </span>
                <span className="input-cmm num-end">
                  <input
                    type="text"
                    id="cellPhoneNumber3"
                    name="cellPhoneNumber3"
                    value={formData.cellPhoneNumber3}
                    maxLength="4"
                    onChange={handleChangeFormData}
                  />
                </span>
              </div>
            </div>
            <p className="form-tit">마일리지 회원번호</p>
            <div className="form-cn mileage">
              {range(5).map((item) => (
                <div className="content" key={item}>
                  <div>
                    <span>항공사{item + 1}</span>
                    <input type="hidden" id={`mileageInfoId_${item}`} name={`mileageInfos[${item}].mileageInfoId`} />
                    <input
                      type="text"
                      id={`airline_${item}`}
                      name={`mileageInfos[${item}].airline`}
                      placeholder="한글/영문 입력"
                      value={formData.mileageInfos[item].airline}
                      onChange={handleChangeFormData}
                    />
                  </div>
                  <div>
                    <span>회원번호{item + 1}</span>
                    <input
                      type="text"
                      id={`mileageMemberNo_${item}`}
                      name={`mileageInfos[${item}].mileageMemberNo`}
                      placeholder="숫자/알파벳 입력"
                      value={formData.mileageInfos[item].mileageMemberNo}
                      onChange={handleChangeFormData}
                    />
                  </div>
                </div>
              ))}
            </div>
            <div className="set-agree">
              <dl>
                <dt>마케팅 수신 설정</dt>
                <dd className="txt">회원정보, 예약 및 항공기 운항 정보, 서비스 주요 정책 관련 내용은 수신 동의 여부와 관계없이 발송됩니다.</dd>
                <dd className="slide">
                  <span className="tit">메일 수신동의</span>
                  <label className="btns-toggle">
                    <input
                      type="checkbox"
                      id="isEmailReceive"
                      name="isEmailReceive"
                      checked={formData.isEmailReceive}
                      onChange={handleChangeFormData}
                    />
                    <i></i>
                  </label>
                </dd>
                <dd className="slide">
                  <span className="tit">SMS 수신동의</span>
                  <label className="btns-toggle on">
                    <input
                      type="checkbox"
                      id="isSmsReceiveYn"
                      name="isSmsReceiveYn"
                      checked={formData.isSmsReceiveYn}
                      onChange={handleChangeFormData}
                    />
                    <i></i>
                  </label>
                </dd>
              </dl>
            </div>
          </div>
          <div className="box-info company">
            <h2 className="title">회사 정보</h2>
            <p className="form-tit">회사명</p>
            <div className="form-cn">
              <span className="box-val">{userInfo.workspace.company.name}</span>
            </div>
            <p className="form-tit">
              소속부서<span>*</span>
            </p>
            <div className="form-cn sel-multi-row">
              <span className="select-cmm" id="workspaceList">
                <select name="workspace.Id" id="setWorkspaceId" value={formData.workspace.Id} onChange={handleChangeFormData}>
                  {workspaceList?.map(
                    (item) =>
                      item.id !== 946626 && (
                        <option key={item.id} value={item.id}>
                          {item.name}
                        </option>
                      ),
                  )}
                </select>
              </span>
              {!isEmpty(customer) && (
                <DepartmentSelect
                  id="departmentSelectBoxArea"
                  workspaceId={customer.workspace?.id}
                  departmentId={customer.department?.id}
                  value={formData.department.id}
                  onChange={(value) => handleChangeFormData({ target: { name: "department.id", value } })}
                />
              )}
            </div>
            <p className="form-tit">
              회계코드<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input type="text" id="accountingCode" name="accountingCode" value={formData.accountingCode} onChange={handleChangeFormData} />
              </span>
            </div>
            <p className="form-tit">
              직급<span>*</span>
            </p>
            <div className="form-cn sel-multi-row">
              <span className="select-cmm" id="positionList">
                <select name="position.id" id="setPositionId" value={formData.position.id} onChange={handleChangeFormData}>
                  {positionList?.map((item) => (
                    <option key={item.id} value={item.id}>
                      {item.name}
                    </option>
                  ))}
                </select>
              </span>
            </div>
            <p className="form-tit">사번</p>
            <div className="form-cn">
              <span className="input-cmm">
                <input type="text" id="employeeNo" name="employeeNo" value={formData.employeeNo} onChange={handleChangeFormData} />
              </span>
            </div>
            <p className="form-tit">회사전화</p>
            <div className="form-cn telephone">
              <span className="select-cmm">
                <select id="phoneNumber1" name="phoneNumber1" value={formData.phoneNumber1} onChange={handleChangeFormData}>
                  {PHONE_NUMBER_OPTIONS.map((option) => (
                    <option value={option.value} key={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </span>
              <span className="input-cmm">
                <input
                  type="text"
                  id="phoneNumber2"
                  name="phoneNumber2"
                  maxLength="8"
                  value={formData.phoneNumber2}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
          </div>
          <div className="box-info passport">
            <h2 className="title">여권 정보</h2>
            <p className="form-tit">영문 성</p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="text"
                  id="lastName"
                  name="customerPassport.lastName"
                  placeholder="예) HONG"
                  maxLength="30"
                  value={formData.customerPassport.lastName}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">
              영문 이름<span>*</span>
            </p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="text"
                  id="firstName"
                  name="customerPassport.firstName"
                  placeholder="예) GILDONG"
                  maxLength="30"
                  value={formData.customerPassport.firstName}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">여권번호</p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="text"
                  id="passportNumber"
                  name="customerPassport.passportNumber"
                  placeholder="예) *********"
                  maxLength="9"
                  value={formData.customerPassport.passportNumber}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">여권 만료 기간</p>
            <div className="form-cn">
              <span className="input-cmm">
                <input
                  type="text"
                  id="expireYmd"
                  name="customerPassport.expireYmd"
                  placeholder="예) 20200101"
                  maxLength="8"
                  value={formData.customerPassport.expireYmd}
                  onChange={handleChangeFormData}
                />
              </span>
            </div>
            <p className="form-tit">여권 발행국가</p>
            <div className="form-cn">
              <span className="select-cmm" data-custom-class="passport-nation">
                <select
                  name="customerPassport.country.id"
                  id="ui-id-1"
                  style={{ display: "block" }}
                  value={formData.customerPassport.country.id}
                  onChange={handleChangeFormData}
                >
                  <optgroup label="otg01">
                    {CUSTOMER_PASSPORT_OTG01_OPTIONS.map((option) => (
                      <option value={option.value} key={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="otg02" id="countryList">
                    {countryList?.map((item) => (
                      <option key={item.id} value={item.id}>
                        {item.name}
                      </option>
                    ))}
                  </optgroup>
                </select>
              </span>
            </div>
          </div>
          <div className="sec-bottom-btns">
            <button id="modifyProcessBtn" onClick={modifyProcess} className="btns-cmm round-basic color-b w-90">
              정보 수정
            </button>
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}></button>
        </div>
      </div>
    </form>
  );
}

export default AccountInfo;
