import img_visual_domestic from "@/assets/mobile/images/search/img_visual_domestic.gif";
import "@/styles/mobile/css/search.css";

function AirMain() {
  return (
    <>
      <div id="wrap">
        {/* header */}
        <header className="page-header type-white">
          <a href="/m/main" className="btn-prev-page" />
        </header>
        {/* // header */}
        {/* container */}
        <div id="container" className="pg-search">
          {/* contents */}
          <div className="contents bg-blue sec-domestic">
            <form id="airSearchForm">
              <input type="hidden" id="departSeatTypeCode" name="departSeatTypeCode" defaultValue="M" />
              <input type="hidden" id="sectionType" name="sectionType" defaultValue="RoundTrip" />
              <input type="hidden" id="isOverseas" name="isOverseas" defaultValue={0} />
              <input type="hidden" id="userId" defaultValue="{{userInfo.id}}" />
              <div className="top-visual">
                <p className="tit">국내 항공권</p>
                <img src={img_visual_domestic} alt="해외 항공권 이미지" />
              </div>
              <div className="bg-white ">
                {/* 항공권 검색 */}
                {/* DD :: data-ticket-type ==> type-round , type-oneway  */}
                <div className="search-ticket-cn type-domestic" data-ticket-type="type-round">
                  <div className="box-tab">
                    <ul>
                      <li className="active">
                        <button type="button" data-ticket-value="type-round" onClick="SectionType.select('RoundTrip')">
                          왕복
                        </button>
                      </li>
                      <li>
                        <button type="button" data-ticket-value="type-oneway" onClick="SectionType.select('OneWay')">
                          편도
                        </button>
                      </li>
                    </ul>
                  </div>
                  <div className="box-cotn">
                    {/* 도시명 있을경우 class in-value 추가*/}
                    <div className="sec-city">
                      <button type="button" className="dep" data-page-layer="city-search" onClick="AirportSearch.setDepartPosition(this)">
                        <p className="en">출발</p>
                        <p className="kr">선택</p>
                      </button>
                      <button type="button" className="arr" data-page-layer="city-search" onClick="AirportSearch.setReturnPosition(this)">
                        <p className="en">도착</p>
                        <p className="kr">선택</p>
                      </button>
                      <input type="hidden" id="departureAirportCode_1" name="departureAirportCodes" defaultValue="" />
                      <input type="hidden" id="departureAirportName_1" name="departureAirportNames" defaultValue="" />
                      <input type="hidden" id="arrivalAirportCode_1" name="arrivalAirportCodes" defaultValue="" />
                      <input type="hidden" id="arrivalAirportName_1" name="arrivalAirportNames" defaultValue="" />
                    </div>
                    <div className="sec-day">
                      <button type="button" className="dep" onClick="openCaleandar(this)">
                        {/*in-value */}
                        <p className="txt">
                          <span>가는 날</span>
                        </p>
                        <p className="val" id="dateDepature_1">
                          가는 날 선택
                        </p>
                        {/*10월 16일 (수)*/}
                      </button>
                      <button type="button" className="arr" onClick="openCaleandar(this)">
                        <p className="txt">오는 날</p>
                        <p className="val" id="dateArrival_1">
                          오는 날 선택
                        </p>
                      </button>
                    </div>
                    <div className="sec-class">
                      <button type="button" data-page-layer="saet-class">
                        <span className="passenger">
                          <span className="txt">탑승객</span>
                          <span className="val" id="adultCountText">
                            성인 1명
                          </span>
                        </span>
                      </button>
                    </div>
                  </div>
                  <div className="box-btn">
                    <button type="button" className="btns-cmm round-basic color-b w-75" onClick="AirMain.search()">
                      항공권 검색
                    </button>
                  </div>
                </div>
                {/* // 항공권 검색 */}
                {/* {"{"}
          {"{"}#contains (substring4IndexOf
          userInfo.workspace.company.btmsSetting.url ".") 'mastercard'{"}"}
          {"}"} {"{"}
          {"{"}else{"}"}
          {"}"} */}
                {/* 항공권 예약내역 */}
                <div className="btn-tickt-reserv side-padding">
                  <a href="/m/reservation/travel/list" className="btns-cmm round-sm color-w ">
                    <i className="icn-list" />
                    항공권 예약내역
                    <i className="icn-arrow" />
                  </a>
                </div>
                {/* // 항공권 예약내역 */}
                {"{"}
                {"{"}/contains{"}"}
                {"}"}
                {/* 최근 검색 */}
                <div className="resent-search-sld">
                  <p className="tit-size01 side-padding">최근 검색한 항공권</p>
                  <div className="swiper-container">
                    <ul className="swiper-wrapper" id="recentAir"></ul>
                  </div>
                  <div className="side-padding">
                    <div className="none-item" id="recentAirNoData" style={{ marginTop: 10, display: "none" }}>
                      최근 검색한 스케줄이 없습니다.
                    </div>
                  </div>
                </div>
                {/* // 최근 검색 */}
              </div>
            </form>
          </div>
          {/* // contents */}
        </div>
        {/* // container */}
        {/* footer */}
        {"{"}
        {"{"}&gt; mobile/layout/footer {"}"}
        {"}"}
        {/* // footer */}
      </div>

      <>
        {/* 인원/좌석등급 */}
        <div className="layer-pages saet-class">
          <div className="layer-head">
            <p className="tit">인원/좌석등급</p>
          </div>
          <div className="layer-cotn">
            <div className="member">
              <p className="tit">인원</p>
              <div className="clearfix">
                <dl>
                  <dt>성인</dt>
                  <dd>최대 예약 인원수는 9명입니다.</dd>
                </dl>
                <div className="box-calc">
                  <button
                    type="button"
                    className="btn-calc minus"
                    id="adultCountMinus"
                    onClick="MobileEtcBookingInfo.adultCountMinus()"
                    disabled=""
                  />
                  <input type="number" id="adultCount" defaultValue={1} />
                  <button type="button" className="btn-calc plus" id="adultCountPlus" onClick="MobileEtcBookingInfo.adultCountPlus()" />
                  <input type="hidden" name="adultCount" defaultValue={1} />
                </div>
              </div>
            </div>
            <div className="box-btn">
              <button
                type="button"
                className="btn-confirm btns-cmm round-basic color-b w-75 layer-pages-close"
                onClick="MobileEtcBookingInfo.apply()"
              >
                확인
              </button>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close">
            <button type="button" className="btns-cmm" onClick="MobileEtcBookingInfo.init()">
              닫기
            </button>
          </div>
        </div>
      </>

      <>
        {/* 도시검색 */}
        <div className="layer-pages city-search">
          <div className="layer-head">
            <p className="tit">도착지</p>
          </div>
          <div className="layer-cotn">
            <input
              type="search"
              id="searchAirport"
              placeholder="도시명 또는 공항명을 입력하세요"
              className="search-box"
              autoComplete="off"
              required=""
            />
            {/*<div class="self-position">
      <button type="button" class="btn-pin">현재위치</button>
  </div>*/}
            <div className="box-default">
              {/*<dl class="resently">
          <dt>최근검색 항공편</dt>
          <dd><button type="button">방콕</button><button type="button">바르셀로나</button></dd>
      </dl>*/}
              <div className="sec-list">
                <dl>
                  <dt>국내</dt>
                  {"{"}
                  {"{"}#each airportMains{"}"}
                  {"}"}
                  <dd>
                    <button type="button" onClick="AirportSearch.select('{{name}}','{{code}}')">
                      {"{"}
                      {"{"}name{"}"}
                      {"}"}
                    </button>
                  </dd>
                  {"{"}
                  {"{"}/each{"}"}
                  {"}"}
                </dl>
              </div>
            </div>
            <div className="box-result">
              <ul>
                <li id="airportAutoSearchArea"></li>
              </ul>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close">
            <button type="button" className="btns-cmm">
              닫기
            </button>
          </div>
        </div>
      </>

      <>
        {/* 날짜선택 */}
        <div className="layer-pages select-day">
          <div className="layer-head">
            <p className="tit">날짜선택</p>
          </div>
          <div className="layer-cotn">
            <div className="sec-top">
              <div className="clearfix">
                <div className="dep">
                  <p className="city">
                    가는날 <span />
                  </p>
                  <p className="val">
                    <input id="date-range200" className="input-box date-range200" size={20} defaultValue="" placeholder="날짜 선택 " data-ymd="" />
                  </p>
                </div>
                <div className="arr">
                  <p className="city">
                    오는날 <span />
                  </p>
                  <p className="val">
                    <input id="date-range201" className="input-box date-range201" size={20} defaultValue="" placeholder="날짜 선택" data-ymd="" />
                  </p>
                </div>
              </div>
              <div className="week">
                <p className="sun">일</p>
                <p>월</p>
                <p>화</p>
                <p>수</p>
                <p>목</p>
                <p>금</p>
                <p>토</p>
              </div>
            </div>
            <div className="sec-calendar">
              <div className="fix">
                <input id="dateForm" className="date-input" type="text" name="datefilter" defaultValue="" />
                <div id="two-inputs-container" />
              </div>
              <div className="box-btn">
                <button type="button" onClick="calendarConfirm()" className="btn-confirm btns-cmm round-basic color-b w-75 layer-pages-close">
                  확인
                </button>
              </div>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close">
            <button type="button" className="btns-cmm">
              닫기
            </button>
          </div>
        </div>
      </>
    </>
  );
}
AirMain.displayName = "MDomesticAirMain";
export default AirMain;
