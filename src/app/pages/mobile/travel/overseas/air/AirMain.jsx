import searchImages from "@/assets/mobile/images/search";
import SeatClass from "@/app/pages/mobile/travel/overseas/include/SeatClass";
import { useEffect, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { cloneDeep, has, range } from "lodash";
import CitySearch from "@/app/pages/mobile/travel/overseas/include/CitySearch";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionMain } from "@/store/mainSlice.mobile";
import JqDateRangePicker from "@/app/components/mobile/test/JqDateRangePicker";
import AirSessionUtil from "@/utils/airSessionUtil";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";
import RecentAirSearchSchedule from "@/app/components/mobile/travel/air/RecentAirSearchSchedule";
import { generateMobileDynamicNavigateUrl } from "@/utils/common";
import { selectUserInfo } from "@/store/userSlice";
import { SECTION_TYPE } from "@/constants/app";
import "@/styles/mobile/css/search.css";
import "@/styles/mobile/css/custom.css";

const flightType = [
  {
    type: "type-round",
    name: "왕복",
    value: "RoundTrip",
  },
  {
    type: "type-oneway",
    name: "편도",
    value: "OneWay",
  },
  {
    type: "type-multi",
    name: "다구간",
    value: "MultiCity",
  },
];

const initSeatClassOptions = {
  adultCount: 1,
  departSeatTypeCode: "M",
  departSeatTypeText: "일반석",
};

const initFormData = {
  sectionType: "RoundTrip",
  departureAirportCode_1: "SEL",
  departureAirportName_1: "서울",
  arrivalAirportCode_1: "",
  arrivalAirportName_1: "",
  ...initSeatClassOptions,
};

const rmKeys = ["departureAirportCode_", "departureAirportName_", "arrivalAirportCode_", "arrivalAirportName_", "departureDay_", "departureDayText_"];

function AirMain() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);
  const [ticketType, setTicketType] = useState(flightType[0]);
  const [planItems, setPlanItems] = useState(2);
  const [citySearchData, setCitySearchData] = useState({
    open: false,
    options: {},
  });
  const [dateRangePickerData, setDateRangePickerData] = useState({
    isOpen: false,
    element: null,
  });
  const [isOpenSeatClass, setIsOpenSeatClass] = useState(false);
  const [popupFixedBottom, setPopupFixedBottom] = useState({
    isOpen: false,
    params: {},
  });
  const [formData, setFormData] = useState(cloneDeep(initFormData));

  const handleSelectTicketType = (currentType) => {
    const nextFormData = updateFormData(formData, initFormData, currentType);
    if (currentType.value !== SECTION_TYPE.MULTICITY) {
      setPlanItems(2);
    }
    setFormData(nextFormData);
    setTicketType(currentType);
  };

  const updateFormData = (formData, initFormData, currentType) => {
    const { arrivalAirportCode_1, arrivalAirportName_1, departureAirportCode_1, departureAirportName_1, stopOverType = false } = { ...formData };
    const nextFormData = {
      ...initFormData,
      arrivalAirportCode_1,
      arrivalAirportName_1,
      departureAirportCode_1,
      departureAirportName_1,
      sectionType: currentType.value,
      stopOverType,
    };
    const airportFields = {
      [SECTION_TYPE.ROUNDTRIP]: {
        departureAirportCode_2: arrivalAirportCode_1,
        departureAirportName_2: arrivalAirportName_1,
        arrivalAirportCode_2: departureAirportCode_1,
        arrivalAirportName_2: departureAirportName_1,
      },
      [SECTION_TYPE.MULTICITY]: {
        departureAirportCode_2: "",
        departureAirportName_2: "",
        arrivalAirportCode_2: "",
        arrivalAirportName_2: "",
      },
    };
    return {
      ...nextFormData,
      ...(airportFields[currentType.value] ?? {}),
    };
  };

  const handleAddPlanItemsByTicketMulti = () => {
    if (planItems === 6) return;
    const newPlanItems = planItems + 1;
    setPlanItems(newPlanItems);
    setFormData((prev) => ({
      ...prev,
      [`departureAirportCode_${newPlanItems}`]: "",
      [`departureAirportName_${newPlanItems}`]: "",
      [`arrivalAirportCode_${newPlanItems}`]: "",
      [`arrivalAirportName_${newPlanItems}`]: "",
    }));
  };

  const handleRemovePlanItemsByTicketMulti = (plan) => {
    const newFormData = cloneDeep(formData);
    for (let index = plan; index <= planItems; index++) {
      if (index === planItems) {
        rmKeys.forEach((key) => {
          delete newFormData[`${key}${index}`];
        });
        break;
      }
      rmKeys.forEach((key) => {
        newFormData[`${key}${index}`] = newFormData[`${key}${index + 1}`];
      });
    }
    setPlanItems(planItems - 1);
    setFormData(newFormData);
  };

  const handleCloseCitySearch = () => {
    setCitySearchData((prev) => ({ ...prev, open: false }));
  };

  const handleOpenCitySearch = ({ position = 1, type = "depart" }) => {
    const newCitySearchData = {
      open: true,
      options: {
        type,
        position,
      },
    };

    setCitySearchData(newCitySearchData);
  };

  const handleSelectCity = (event) => {
    const { data, options } = event;
    const nextFormData = { ...formData };
    for (const key in nextFormData) {
      switch (options.type) {
        case "arrive":
          if (key === `arrivalAirportCode_${options.position}`) {
            nextFormData[key] = data.code;
            if (ticketType.type === "type-round") {
              nextFormData[`arrivalAirportCode_${options.position + 1}`] = nextFormData[`departureAirportCode_${options.position}`];
            }
            if (has(nextFormData, `arrivalAirportCode_${options.position + 1}`)) {
              nextFormData[`departureAirportCode_${options.position + 1}`] = data.code;
            }
          }
          if (key === `arrivalAirportName_${options.position}`) {
            if (ticketType.type === "type-round") {
              nextFormData[`arrivalAirportName_${options.position + 1}`] = nextFormData[`departureAirportName_${options.position}`];
            }
            nextFormData[key] = data.name;
            if (has(nextFormData, `arrivalAirportName_${options.position + 1}`)) {
              nextFormData[`departureAirportName_${options.position + 1}`] = data.name;
            }
          }
          break;
        case "depart":
          if (key === `departureAirportCode_${options.position}`) {
            nextFormData[key] = data.code;
            if (ticketType.type === "type-round") {
              nextFormData[`departureAirportCode_${options.position + 1}`] = nextFormData[`arrivalAirportCode_${options.position}`];
              nextFormData[`arrivalAirportCode_${options.position + 1}`] = data.code;
            }
          }
          if (key === `departureAirportName_${options.position}`) {
            nextFormData[key] = data.name;
            if (ticketType.type === "type-round") {
              nextFormData[`departureAirportName_${options.position + 1}`] = nextFormData[`arrivalAirportName_${options.position}`];
              nextFormData[`arrivalAirportName_${options.position + 1}`] = data.name;
            }
          }
          break;
        default:
          break;
      }
    }

    setFormData(nextFormData);
  };

  const handleSearch = () => {
    const params = {
      itineraryType: formData.sectionType,
      departSeatTypeCode: formData.departSeatTypeCode,
      adultCount: formData.adultCount,
      sectionType: formData.sectionType,
    };
    if (formData.stopOverType) {
      params.stopOverType = 0;
    }
    rmKeys.forEach((rmKey) => {
      params[rmKey.replace("_", "s")] = [];
      Object.entries(formData).forEach(([paramKey, value]) => {
        if (paramKey.includes(rmKey)) {
          params[rmKey.replace("_", "s")].push(value);
        }
      });
    });
    params.itineraryCount = params.departureAirportCodes.length;
    if (params.sectionType === "MultiCity") {
      if (params.arrivalAirportCodes.includes("")) {
        alert("도착지를 입력해주세요.");
        return;
      }
      if (params.departureAirportCodes.includes("")) {
        alert("출발지를 입력해주세요.");
        return;
      }
    } else if (!params.departureAirportCodes[0]) {
      alert("출발지를 입력해주세요.");
      return;
    } else if (!params.arrivalAirportCodes[0]) {
      alert("도착지를 입력해주세요.");
      return;
    }

    if (!params.departureDays?.length) {
      alert("날짜를 선택해주세요.");
      return;
    }
    if (params.departureDays?.includes("")) {
      alert("날짜를 선택해주세요.");
      return;
    }
    if (params.sectionType == "MultiCity") {
      setPopupFixedBottom({ isOpen: true, params });
    } else {
      searchProcess(params);
    }
  };

  const searchProcess = (params) => {
    AirSessionUtil.setSession("", params);
    airLocalStorageUtil.set({ ...params, isOverseas: 1 }, userInfo?.id);
    const url = generateMobileDynamicNavigateUrl(params);
    navigate(`/m/overseas/air/search?${url}`);
  };

  useEffect(() => {
    dispatch(actionMain());
  }, [dispatch]);

  useEffect(() => {
    const mainAirportName = sessionStorage.getItem("mainAirportName");
    const mainAirportCode = sessionStorage.getItem("mainAirportCode");
    if (mainAirportName && mainAirportCode) {
      setFormData((prev) => ({ ...prev, arrivalAirportCode_1: mainAirportCode, arrivalAirportName_1: mainAirportName }));
      AirSessionUtil.clear();
    }
  }, []);

  return (
    <>
      {!citySearchData.open && !isOpenSeatClass && (
        <div id="wrap">
          <header className="page-header type-white">
            <Link to="/m/main" className="btn-prev-page" />
          </header>
          <div id="container" className="pg-search">
            <div className="contents bg-blue sec-foregin">
              <form id="airSearchForm">
                <input type="hidden" id="sectionType" name="sectionType" defaultValue="RoundTrip" />
                <input type="hidden" id="isOverseas" name="isOverseas" defaultValue={1} />
                <input type="hidden" id="userId" defaultValue="{{userInfo.id}}" />
                <div className="top-visual">
                  <p className="tit">해외 항공권</p>
                  <img src={searchImages["img_visual_foregin.gif"]} alt="" />
                </div>
                <div className="bg-white ">
                  <div className="search-ticket-cn type-foregin" data-ticket-type={ticketType.type}>
                    <div className="box-tab">
                      <ul>
                        {flightType.map((item) => {
                          return (
                            <li className={`${item.type === ticketType.type ? "active" : ""}`} key={item.type}>
                              <button type="button" data-ticket-value={item.type} onClick={() => handleSelectTicketType(item)}>
                                {item.name}
                              </button>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                    <div className="box-cotn">
                      <div className="sec-city">
                        <button
                          type="button"
                          className={`dep ${formData["departureAirportCode_1"] ? "in-value" : ""}`}
                          data-page-layer="city-search"
                          onClick={() =>
                            handleOpenCitySearch({
                              position: 1,
                              type: "depart",
                            })
                          }
                        >
                          <p className="en">{formData["departureAirportCode_1"] || "출발"}</p>
                          <p className="kr">{formData["departureAirportName_1"] || "선택"}</p>
                        </button>
                        <button
                          type="button"
                          className={`arr ${formData["arrivalAirportCode_1"] ? "in-value" : ""}`}
                          data-page-layer="city-search"
                          onClick={() =>
                            handleOpenCitySearch({
                              position: 1,
                              type: "arrive",
                            })
                          }
                        >
                          <p className="en">{formData[`arrivalAirportCode_1`] || "도착"}</p>
                          <p className="kr">{formData[`arrivalAirportName_1`] || "선택"}</p>
                        </button>
                        <input
                          type="hidden"
                          id="departureAirportCode_1"
                          name="departureAirportCodes"
                          defaultValue={formData["departureAirportCode_1"]}
                        />
                        <input
                          type="hidden"
                          id="departureAirportName_1"
                          name="departureAirportNames"
                          defaultValue={formData["departureAirportName_1"]}
                        />
                        <input type="hidden" id="arrivalAirportCode_1" name="arrivalAirportCodes" defaultValue={formData[`arrivalAirportCode_1`]} />
                        <input type="hidden" id="arrivalAirportName_1" name="arrivalAirportNames" defaultValue={formData[`arrivalAirportName_1`]} />
                      </div>
                      <div className="sec-day">
                        <button
                          type="button"
                          className={`dep ${formData.departureDayText_1 ? "in-value" : ""}`}
                          onClick={(event) => {
                            const currentTarget = event.currentTarget;
                            setDateRangePickerData((prev) => ({ ...prev, isOpen: true, element: currentTarget }));
                          }}
                        >
                          <p className="txt">
                            <span>가는 날</span>
                          </p>
                          <p className="val" id="dateDepature_1" data-ymd={formData.departureDay_1}>
                            {formData.departureDayText_1 || "가는 날 선택"}
                          </p>
                        </button>
                        <button
                          type="button"
                          className={`arr ${formData.departureDayText_2 ? "in-value" : ""}`}
                          onClick={(event) => {
                            const currentTarget = event.currentTarget;
                            setDateRangePickerData((prev) => ({ ...prev, isOpen: true, element: currentTarget }));
                          }}
                        >
                          <p className="txt">오는 날</p>
                          <p className="val" id="dateArrival_1" data-ymd={formData.departureDay_2}>
                            {formData.departureDayText_2 || "오는 날 선택"}
                          </p>
                        </button>
                      </div>

                      <div className="box-multi">
                        {range(2, planItems + 1).map((plan) => {
                          return (
                            <div className="plan-item" key={plan}>
                              <div className="sec-city">
                                <button
                                  type="button"
                                  className={`dep ${formData[`departureAirportCode_${plan}`] ? "in-value" : ""}`}
                                  data-page-layer="city-search"
                                  onClick={() => {
                                    handleOpenCitySearch({
                                      position: plan,
                                      type: "depart",
                                    });
                                  }}
                                >
                                  <p className="en">{formData[`departureAirportCode_${plan}`] || "출발"}</p>
                                  <p className="kr">{formData[`departureAirportName_${plan}`] || "선택"}</p>
                                </button>
                                <button
                                  type="button"
                                  className={`arr ${formData[`arrivalAirportCode_${plan}`] ? "in-value" : ""}`}
                                  data-page-layer="city-search"
                                  onClick={() => {
                                    handleOpenCitySearch({
                                      position: plan,
                                      type: "arrive",
                                    });
                                  }}
                                >
                                  <p className="en">{formData[`arrivalAirportCode_${plan}`] || "도착"}</p>
                                  <p className="kr">{formData[`arrivalAirportName_${plan}`] || "선택"}</p>
                                </button>
                                <input
                                  type="hidden"
                                  id={`departureAirportCode_${plan}`}
                                  name="departureAirportCodes"
                                  defaultValue={formData[`departureAirportCode_${plan}`]}
                                />
                                <input
                                  type="hidden"
                                  id={`departureAirportName_${plan}`}
                                  name="departureAirportNames"
                                  defaultValue={formData[`departureAirportName_${plan}`]}
                                />
                                <input
                                  type="hidden"
                                  id={`arrivalAirportCode_${plan}`}
                                  name="arrivalAirportCodes"
                                  defaultValue={formData[`arrivalAirportCode_${plan}`]}
                                />
                                <input
                                  type="hidden"
                                  id={`arrivalAirportName_${plan}`}
                                  name="arrivalAirportNames"
                                  defaultValue={formData[`arrivalAirportName_${plan}`]}
                                />
                              </div>
                              <div className="sec-day">
                                <button
                                  type="button"
                                  className={`dep ${formData[`departureDayText_${plan}`] ? "in-value" : ""}`}
                                  onClick={(event) => {
                                    const currentTarget = event.currentTarget;
                                    setDateRangePickerData((prev) => ({ ...prev, isOpen: true, element: currentTarget }));
                                  }}
                                >
                                  <p className="txt">
                                    <span>여정{plan}</span>
                                  </p>
                                  <p className="val" id={`dateDepature_${plan}`} data-ymd={formData[`departureDay_${plan}`]}>
                                    {formData[`departureDayText_${plan}`] || "가는날 선택"}
                                  </p>
                                </button>
                              </div>
                              {plan > 2 && (
                                <button
                                  className="btn-remove-item btn-multi-ctrl remove"
                                  id={`itineraryRemove_${plan}`}
                                  onClick={() => handleRemovePlanItemsByTicketMulti(plan)}
                                  type="button"
                                >
                                  여정삭제
                                </button>
                              )}
                            </div>
                          );
                        })}

                        <div className="box-btn">
                          <button type="button" className="btn-add-item btn-multi-ctrl add" onClick={handleAddPlanItemsByTicketMulti} />
                        </div>
                      </div>

                      <div className="sec-class" style={{ marginBottom: 20 }}>
                        <button type="button" data-page-layer="saet-class" onClick={() => setIsOpenSeatClass(true)}>
                          <span className="passenger">
                            <span className="txt">탑승객</span>
                            <span className="val" id="adultCountText">
                              성인 {formData.adultCount}명
                            </span>
                          </span>
                          <span className="seat">
                            <span className="txt">좌석등급</span>
                            <span className="val" id="seatTypeNameText">
                              {formData.departSeatTypeText}
                            </span>
                          </span>
                        </button>
                      </div>
                      <div className="sec-class">
                        <label className="form-chkbox">
                          <input
                            type="checkbox"
                            name="stopOverType"
                            checked={formData.stopOverType}
                            onChange={(event) => setFormData((prev) => ({ ...prev, stopOverType: event.target.checked }))}
                          />
                          <span>직항만</span>
                        </label>
                      </div>
                    </div>
                    <div className="box-btn">
                      <button type="button" className="btns-cmm round-basic color-b w-75" onClick={handleSearch}>
                        항공권 검색
                      </button>
                    </div>
                  </div>
                  <div className="btn-tickt-reserv side-padding">
                    <a href="/m/reservation/travel/list" className="btns-cmm round-sm color-w ">
                      <i className="icn-list" /> 항공권 예약내 <i className="icn-arrow" />
                    </a>
                  </div>
                  <div className="resent-search-sld">
                    <p className="tit-size01 side-padding">최근 검색한 항공권</p>
                    <RecentAirSearchSchedule isOverseas={1} />
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
      <CitySearch open={citySearchData.open} options={citySearchData.options} onClose={handleCloseCitySearch} onSelect={handleSelectCity} />
      <JqDateRangePicker
        open={dateRangePickerData.isOpen}
        dateElem={dateRangePickerData.element}
        onClose={() => setDateRangePickerData((prev) => ({ ...prev, isOpen: false }))}
        ticketType={ticketType.type}
        formData={formData}
        onChangeFormData={setFormData}
      />
      <SeatClass
        open={isOpenSeatClass}
        options={formData}
        onApply={(options) => setFormData((prev) => ({ ...prev, ...options }))}
        onClose={() => setIsOpenSeatClass(false)}
        onReset={() => setFormData((prev) => ({ ...prev, ...initSeatClassOptions }))}
      />
      <div className="popup-fixed-bottom" style={{ display: popupFixedBottom.isOpen ? "block" : "none" }}>
        <div className="modal-wrap last-destination">
          <p className="title">최종 목적지</p>
          <ul id="popupFixedBottom">
            {range(0, planItems).map((plan) => (
              <li key={plan}>
                <label className="form-radio">
                  <input
                    type="radio"
                    name="headDepartAirport"
                    value={plan + 1}
                    checked={formData.headDepartAirport === plan + 1}
                    onChange={() => setFormData((prev) => ({ ...prev, headDepartAirport: plan + 1 }))}
                  />
                  <span>여정 ({plan + 1})</span>
                </label>
                <div className="tit"></div>
                <div className="desc">
                  {formData[`departureAirportName_${plan + 1}`]} → {formData[`arrivalAirportName_${plan + 1}`]}
                </div>
              </li>
            ))}
          </ul>
          <div className="box-btn">
            <button
              className="btns-cmm round-basic color-b w-75"
              onClick={() => searchProcess({ ...popupFixedBottom.params, headDepartAirport: formData.headDepartAirport })}
            >
              선택
            </button>
          </div>
          <div className="tool-tip">
            <button className="btns-cmm" />
            <div className="box-cotn">정확한 출장규정 반영을 위하여 최종 목적지를 포함한 여정을 선택해 주세요.</div>
          </div>
        </div>
      </div>
    </>
  );
}
AirMain.displayName = "MOverseasAirMain";
export default AirMain;
