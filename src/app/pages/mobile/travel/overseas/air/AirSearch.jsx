import { useNavigate } from "react-router-dom";
import searchImages from "@/assets/mobile/images/search";
import { Swiper, SwiperSlide } from "swiper/react";
import { useEffect, useState } from "react";
import { postAirSearch } from "@/service/air";
import FilterAirSearch from "@/app/pages/mobile/travel/overseas/include/FilterAirSearch";
import { convertMinutesToHours, momentKR } from "@/utils/date";
import { comma, generateMobileDynamicNavigateUrl, getJourneyAmount, isIncludedCorporateFare, onErrorImgAirline } from "@/utils/common";
import DetailInfoSearch from "@/app/pages/mobile/travel/overseas/include/DetailInfoSearch";
import TravelPolicy from "@/app/pages/mobile/travel/overseas/include/TravelPolicy";
import { isEmpty, isEqual, cloneDeep } from "lodash";
import useTravelRule from "@/app/hooks/useTravelRule";
import { appMobileLoading, getSortAndCombineJourneyInfos, sortByCondition } from "@/utils/app";
import InfiniteScroll from "@/app/components/user_v2/common/InfinititeScroll";
import { CircularProgress } from "@mui/material";
import moment from "moment";
import useQueryParams from "@/app/hooks/useQueryParams";
import JqDateRangePicker from "@/app/components/mobile/test/JqDateRangePicker";
import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import PopCompareBox from "@/app/pages/mobile/travel/overseas/include/PopCompareBox";
import { SECTION_TYPE, TYPE_MOBILE_LOADING } from "@/constants/app";
import BookingNotice from "@/app/pages/mobile/travel/overseas/include/Booking/BookingNotice";
import { actionSetAirOverseasSessionData, selectTravelRuleBase } from "@/store/airSlice";
import { actionOpenAirCompareSchedule, actionUpdateAirCompareSchedule } from "@/store/mainSlice.mobile";
import AirSessionUtil from "@/utils/airSessionUtil";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";
import { MOBILE_URL } from "@/constants/url";
import AIR_ICONS_PATH from "@/assets/airIcon";
import "@/styles/mobile/css/search.css";
import "swiper/css";

const orderOptions = [
  { value: "recommend", label: "추천 (기업요금 낮은순+직항+총 소요시간)", defaultChecked: true },
  { value: "lowDirectAmount", label: "요금낮은순+직항" },
  { value: "lowBoardingTime", label: "탑승시간 짧은순" },
  { value: "lowAmount", label: "요금 낮은순" },
  { value: "corporateFareLowAmount", label: "기업운임 낮은순" },
  { value: "fastDepartTime", label: "출발시간 빠른 순" },
];

const MAPPED_TICKET_TYPE = {
  RoundTrip: "type-round",
  OneWay: "type-oneway",
  MultiCity: "type-multi",
};

const MAPPED_DEPARTURE_SEAT_TYPE = {
  M: "일반석",
  W: "프리미엄 일반석",
  C: "비즈니스",
  F: "일등석",
};

const MULTIPLE_KEYS = [
  "departureAirportCodes",
  "departureAirportNames",
  "arrivalAirportCodes",
  "arrivalAirportNames",
  "departureDays",
  "departureDayTexts",
];

const getInitFormDateRange = (params) => {
  const initForm = {
    departureDay_1: params.departureDays[0],
    departureDayText_1: params.departureDayTexts[0],
  };
  if (params.sectionType === SECTION_TYPE.ROUNDTRIP) {
    initForm.departureDay_2 = params.departureDays[1];
    initForm.departureDayText_2 = params.departureDayTexts[1];
  }
  return initForm;
};

function AirSearch() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const params = useQueryParams(MULTIPLE_KEYS);
  const [originJourneyInfos, setOriginJourneyInfos] = useState([]);
  const [sortedJourneyInfos, setSortedJourneyInfos] = useState([]);
  const { data: travelRuleBase } = useAppSelector(selectTravelRuleBase);
  const [travelPolicyOpen, setTravelPolicyOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [currentOrderOption, setCurrentOrderOption] = useState(orderOptions[0]);
  const [dateRangePickerData, setDateRangePickerData] = useState({ isOpen: false, element: null });
  const { isViolation } = useTravelRule();
  const { userInfo } = useAppSelector(selectUserInfo);
  const [selectedFares, setSelectedFares] = useState([]);
  const [popCompareBoxData, setPopCompareBoxData] = useState({ isOpen: false, message: "", isOverNum: false });
  const [detailInfoSearchData, setDetailInfoSearchData] = useState({ open: false, journey: {}, isCompare: false });
  const [isOpenModal, setIsOpenModal] = useState({ compareSchedule: false, bookingNotice: false });

  const searchByNewDay = (newDateRange) => {
    const nextParams = cloneDeep(params);
    nextParams.departureDays = [];
    nextParams.departureDayTexts = [];
    Object.entries(newDateRange).forEach(([paramKey, value]) => {
      nextParams[`${paramKey.split("_")[0]}s`].push(value);
    });
    AirSessionUtil.setSession("", nextParams);
    airLocalStorageUtil.set({ ...nextParams, isOverseas: 1 }, userInfo?.id);
    const url = generateMobileDynamicNavigateUrl(nextParams);
    location.href = `${MOBILE_URL.OverseasAirSearch}?${url}`;
  };

  const handleCloseDetailInfoSearch = () => {
    setDetailInfoSearchData({
      open: false,
      journey: {},
      isCompare: false,
    });
  };

  const handleOpenDetailInfoSearch = (option) => {
    setDetailInfoSearchData({
      open: true,
      isCompare: !!option.isCompare,
      ...option,
    });
  };

  const fetchMore = () => {
    if (!isEmpty(sortedJourneyInfos)) {
      setPage((prev) => prev + 1);
    }
  };

  const handleSelectFare = (journey) => {
    let message = "";
    let isOverNum = false;
    if (selectedFares.some((fare) => fare.id === journey.id)) {
      message = "이미 추가된 항공편입니다.";
    } else if (selectedFares.length > 2) {
      message = "선택 가능한 항공편 개수가 초과되었습니다.<br><span>'비교함'</span>에서 삭제 하신 후 추가 하실 수 있습니다.";
    } else {
      if (selectedFares.length === 2) {
        message = "비교견적서 작성이 완료 되었습니다. <br>확인 후 예약을 진행해 주세요.";
        isOverNum = true;
      }
      setSelectedFares((prev) => [...prev, journey]);
    }
    setPopCompareBoxData({ isOpen: true, message, isOverNum });
  };

  const handleOpenAirCompareSchedule = () => {
    dispatch(
      actionOpenAirCompareSchedule({
        selectedFares,
        journeyInfos: sortedJourneyInfos,
        params,
        onOpenDetailInfoSearch: handleOpenDetailInfoSearch,
        onChangeSelectedFares: setSelectedFares,
        onOpenBookingNotice: () => setIsOpenModal((prev) => ({ ...prev, bookingNotice: true })),
        onOpenTravelPolicy: () => setTravelPolicyOpen(true),
      }),
    );
  };

  const handleBooking = (journey) => {
    dispatch(
      actionSetAirOverseasSessionData({
        data: journey,
        filter: params,
        lowestJourney: sortByCondition(sortedJourneyInfos, "lowAmount")?.[0] ?? null,
      }),
    );
    setIsOpenModal((prev) => ({ ...prev, bookingNotice: true }));
  };

  const moveScheduleSearch = () => {
    navigate(`${MOBILE_URL.OverseasAirScheduleSearch}${location.search}`);
  };

  useEffect(() => {
    dispatch(actionUpdateAirCompareSchedule(selectedFares));
  }, [selectedFares, dispatch]);

  useEffect(() => {
    const search = async () => {
      const { departureAirportCodes, arrivalAirportCodes, departSeatTypeCode, departureDays, departureDayTexts, adultCount } = params;
      const journeys = departureAirportCodes.map((code, index) => ({
        deptAirport: code,
        arrAirport: arrivalAirportCodes[index],
        departureDate: momentKR(departureDays[index]).format("YYYY-MM-DD"),
      }));
      try {
        appMobileLoading.on({
          type: TYPE_MOBILE_LOADING.SEARCH,
          data: { departureDayTexts, adultCount, departSeatTypeText: MAPPED_DEPARTURE_SEAT_TYPE[departSeatTypeCode] },
        });
        const res = await postAirSearch({ journeys, adultCount: Number(adultCount), cabinClass: departSeatTypeCode });
        const nextCombinedJourneyInfos = getSortAndCombineJourneyInfos(res.data.data.journeyInfos);
        const filteredJourneyInfos = nextCombinedJourneyInfos.filter((item) => (params.stopOverType == 0 ? item.stops == params.stopOverType : true));
        const sortedFlights = sortByCondition(filteredJourneyInfos, currentOrderOption.value);
        setSortedJourneyInfos(sortedFlights);
        setOriginJourneyInfos(res.data.data.journeyInfos);
      } catch (error) {
        console.log(error);
      } finally {
        appMobileLoading.off();
      }
    };
    search();
  }, []);

  useEffect(() => {
    const getJourneyInfosBy = async () => {
      const sortedFlights = sortByCondition(sortedJourneyInfos, currentOrderOption.value);
      setSortedJourneyInfos(sortedFlights);
    };
    getJourneyInfosBy();
  }, [page, currentOrderOption]);

  return (
    <>
      <div className="head-fixed">
        <header className="page-header type-black">
          <div className="air-plan">
            <div
              className={`city ${params.sectionType == "OneWay" ? "oneway" : ""}`}
              onClick={() => navigate("/m/overseas/air/main")}
              style={{ cursor: "pointer" }}
            >
              <p className="arr">{params.departureAirportCodes[0]}</p>
              <p className="dep">
                {params.sectionType == "MultiCity"
                  ? params.departureAirportCodes[params.departureAirportCodes.length - 1]
                  : params.arrivalAirportCodes[0]}
              </p>
            </div>
            <div
              className="date"
              onClick={(event) => {
                const currentTarget = event.currentTarget;
                setDateRangePickerData((prev) => ({ ...prev, isOpen: true, element: currentTarget }));
              }}
              style={{ cursor: "pointer" }}
            >
              {moment(params.departureDays[0]).format("MM월 DD일")} -{" "}
              {moment(params.departureDays[params.departureDays.length - 1]).format("MM월 DD일")}
            </div>
          </div>
          <a href="/m/overseas/air/main" className="btn-prev-page" />
          <div className="loading-bar">
            <div className="ing" style={{ width: "33%" }} />
          </div>
          <div className="search-result-top">
            <div className="clearfix policy-box">
              <div className="left-col label-type">
                <p className="in">In Policy</p>
                <p className="out">Out of Policy</p>
              </div>
              <div className="right-col">
                <a href="#" className="my-trip-rule" data-page-layer="trip-rule-pop" onClick={() => setTravelPolicyOpen(true)}>
                  나의 출장 규정
                </a>
              </div>
            </div>
            <dl>
              <dt>
                검색결과 <strong id="listCount">{sortedJourneyInfos.length}</strong>개
              </dt>
              <dd>유류할증료와 세금 및 제반요금 포함된 성인 1명 기준 운임입니다.</dd>
            </dl>
            <div className="item-align swiper-container">
              <Swiper slidesPerView={"auto"} spaceBetween={4} effect="coverflow" className="!px-3">
                {orderOptions.map((option, index) => (
                  <SwiperSlide key={index} className="!w-auto">
                    <label onClick={() => setCurrentOrderOption(option)}>
                      <input type="radio" name="orderType" value={option.value} checked={isEqual(currentOrderOption, option)} readOnly />
                      <span className="txt">{option.label}</span>
                    </label>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>
          <div className="btn-open-compare">
            <button type="button" onClick={handleOpenAirCompareSchedule}>
              <i className="ico" /> <span className="txt">비교함</span>
              <span className="num">{selectedFares.length}</span>
            </button>
          </div>
          {userInfo.workspace?.company?.btmsSetting?.isOfflineUse && (
            <div className="f_schedule_md">
              <p className="f_schedule_chs">
                <em>오는편</em>
                <span>을 선택하세요</span>
              </p>
              <ul>
                <li className="f_schedule_info">
                  <span>
                    <img src={searchImages["icn_qm.png"]} alt="물음표 아이콘" />
                  </span>
                  <div className="speech_bubble">
                    원하는 스케줄로 사용자가 선택하여 (구간별선택 가능) 요청할 수 있습니다. 요청시 실제 좌석 가능 여부를 담당자가 확인 후 예약 진행
                    됩니다.
                  </div>
                </li>
                <li>전체 스케줄 모드</li>
                <li className="toggle_btn">
                  <div className="left" onClick={moveScheduleSearch} />
                </li>
              </ul>
            </div>
          )}
        </header>
        <div id="container" className="pg-search mt-4 min-h-screen">
          <div className="contents">
            <div className="saerch-result-wrap" id="result-data" style={{ paddingTop: 265 }}>
              <InfiniteScroll
                className="area-cotn"
                loader={
                  <div className="flex items-center justify-center mt-8">
                    <CircularProgress />
                  </div>
                }
                fetchMore={fetchMore}
                hasMore={sortedJourneyInfos.length > sortedJourneyInfos.slice(0, page * 10).length}
              >
                {sortedJourneyInfos.slice(0, page * 10).map((item, itemIndex) => {
                  const { flights } = item;
                  const { fareAmount, taxAmount, tasfAmount } = getJourneyAmount(item);
                  const isIncludeCorporate = isIncludedCorporateFare(item.flights);
                  const indexSelectedFare = selectedFares.findIndex((fare) => fare.id === item.id);
                  return (
                    // eslint-disable-next-line no-constant-condition
                    <div key={item.journeyKey + itemIndex} className={`box-item pl-${isViolation(item) ? "out" : "in"}`}>
                      <button
                        type="button"
                        className="btn-detail"
                        onClick={() => {
                          handleOpenDetailInfoSearch({ journey: item });
                        }}
                      >
                        {flights.map((journeyMapItem, journeyMapItemIndex) => {
                          const { deptAirport, departureDate, arrivalDate, arrAirport, stops, segments } = journeyMapItem;
                          let elapseFlyingHourMin = 0;
                          segments.forEach((segment) => {
                            elapseFlyingHourMin += Number(segment.waitingTime) + Number(segment.flightTime);
                          });
                          const formattedDeptTime = momentKR(departureDate).format("YYYY-MM-DD");
                          const formattedArrTime = momentKR(arrivalDate).format("YYYY-MM-DD");
                          const dateVariation = momentKR(formattedArrTime).diff(momentKR(formattedDeptTime), "days");

                          return (
                            <div className="air-plan" key={journeyMapItemIndex}>
                              <div className="left-col">
                                <img
                                  className="left-col aircraft"
                                  src={AIR_ICONS_PATH[segments[0].carrierCode] || ""}
                                  onError={onErrorImgAirline}
                                  alt=""
                                />
                                <div className="left-col plan">
                                  <span className="time">{momentKR(departureDate).format("HH:mm")}</span>
                                  <span className="city">{deptAirport}</span>
                                </div>
                                <div className={`left-col way-to n${stops}`} />
                                <div className="left-col plan">
                                  <span className="time" data-more-time={dateVariation ? `+${dateVariation}` : ""}>
                                    {momentKR(arrivalDate).format("HH:mm")}
                                  </span>
                                  <span className="city">{arrAirport}</span>
                                </div>
                                <p className="share">{segments[0].carrierCodeName}</p>
                              </div>
                              <div className="right-col">
                                <p className={`via ${stops > 0 ? "on" : "none"}`}>{stops > 0 ? `${stops}회 경유` : "직항"}</p>
                                <p className="total-time">{convertMinutesToHours(elapseFlyingHourMin)}</p>
                              </div>
                            </div>
                          );
                        })}
                      </button>
                      <div className="sec-bottom">
                        <div className="total-price" style={{ lineHeight: "18px" }}>
                          <div className="fare-detail">항공료 : {comma(fareAmount)} 원</div>
                          <div className="fare-detail">TAX : {comma(taxAmount)} 원</div>
                          <div className="fare-detail">발권수수료 : {comma(tasfAmount)} 원</div>
                          <strong>{comma(fareAmount + taxAmount + tasfAmount)} 원</strong>
                          {isIncludeCorporate && <span style={{ color: "#ff4e50" }}>기업 운임</span>}
                        </div>
                        <div className="box-btn" id={`airCompareScheduleAddBtn_${item.id}`}>
                          {userInfo.workspace?.company?.btmsSetting?.isComparativePrice ? (
                            <>
                              {selectedFares.some((fare) => fare.id === item.id) ? (
                                <button type="button" className="btns-cmm btn-add-compare active" data-value={indexSelectedFare + 1}>
                                  비교함 <span className="txt">추가</span>
                                </button>
                              ) : (
                                <button type="button" className="btns-cmm btn-add-compare" onClick={() => handleSelectFare(item)}>
                                  비교함 <span className="txt">추가</span>
                                </button>
                              )}
                            </>
                          ) : (
                            <button type="button" className="btns-cmm btn-reserv" onClick={() => handleBooking(item)}>
                              예약하기
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </InfiniteScroll>
            </div>
          </div>
        </div>
      </div>
      <div className="btn-page-top">
        <button type="button" onClick={() => scrollTo(0, 0)} className="btns-cmm" />
      </div>
      <JqDateRangePicker
        open={dateRangePickerData.isOpen}
        dateElem={dateRangePickerData.element}
        ticketType={MAPPED_TICKET_TYPE[params.sectionType]}
        formData={getInitFormDateRange(params)}
        onSearch={searchByNewDay}
        onClose={() => setDateRangePickerData((prev) => ({ ...prev, isOpen: false }))}
        isDisable={params.sectionType === SECTION_TYPE.MULTICITY}
      />
      <FilterAirSearch
        params={params}
        journeyInfos={originJourneyInfos}
        currentOrderOption={currentOrderOption}
        onChangeJourneyInfos={setSortedJourneyInfos}
        onChangePage={setPage}
        onResetSelectedFares={() => setSelectedFares([])}
      />
      <PopCompareBox
        selectedFares={selectedFares}
        data={popCompareBoxData}
        onClose={() => setPopCompareBoxData((prev) => ({ ...prev, isOpen: false }))}
        onOpenAirCompareSchedule={handleOpenAirCompareSchedule}
      />
      {!isEmpty(travelRuleBase) && (
        <>
          <DetailInfoSearch
            open={detailInfoSearchData.open}
            journey={detailInfoSearchData.journey}
            isCompare={detailInfoSearchData.isCompare}
            onClose={handleCloseDetailInfoSearch}
            travelRule={travelRuleBase}
            onSelectFare={handleSelectFare}
            onBooking={handleBooking}
          />
          <TravelPolicy open={travelPolicyOpen} travelRule={travelRuleBase} onClose={() => setTravelPolicyOpen(false)} />
        </>
      )}
      <BookingNotice isOpen={isOpenModal.bookingNotice} onClose={() => setIsOpenModal((prev) => ({ ...prev, bookingNotice: false }))} />
    </>
  );
}

AirSearch.displayName = "MOverseasAirSearch";
export default AirSearch;
