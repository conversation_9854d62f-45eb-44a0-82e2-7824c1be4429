function PopCompareBox(props) {
  const { selectedFares, data, onClose, onOpenAirCompareSchedule } = props;
  const num = selectedFares.length;

  return (
    <div className="jquery-modal blocker current flex items-center" style={{ display: data.isOpen ? "flex" : "none" }}>
      <div id="popCompareBox" className="modal-wrap !block bg-white rounded-xl">
        <div className="modal-cotn">
          <div className="box-ico">
            <p className="num">{num}</p>
          </div>
          <p
            className="desc"
            dangerouslySetInnerHTML={{
              __html: data.message || "비교견적서에 추가 되었습니다. <br /> <span>3개의 항공권</span>을 선택해 주세요.",
            }}
          ></p>
          <div className="box-btn">
            {num <= 2 && (
              <button type="button" className="btns-cmm round-basic color-b w-75" rel="modal:close" onClick={onClose}>
                비교 항공권 계속 추가
              </button>
            )}
            <button
              type="button"
              className={`btns-cmm round-basic w-75 ${num === 3 && data.isOverNum ? "color-b" : "color-w"}`}
              id="airCompareScheduleViewBtn"
              onClick={() => {
                onOpenAirCompareSchedule();
                onClose();
              }}
            >
              비교 견적서 보기
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PopCompareBox;
