import { selectUserInfo } from "@/store/userSlice";
import { useAppSelector } from "@/store";
import AirUtil from "@/utils/airUtil";

function TravelPolicy(props) {
  const { open, travelRule, onClose } = props;
  const user = useAppSelector(selectUserInfo);

  return (
    <div className="layer-pages trip-rule-pop !z-[112]" style={{ display: open ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">나의 출장 규정</p>
      </div>
      <div className="layer-cotn">
        <div className="trip-rule">
          <p className="desc">
            <strong>{user.userInfo?.position?.name}</strong>
            님에게 적용되는 해외 항공권 예약 규정입니다.
          </p>
          <div className="box-info">
            <p className="tit">
              해외 항공권 예약은
              <strong>{AirUtil.getExceptTimeType(travelRule?.exceptTimeType)}</strong>을 기준으로 합니다.
            </p>
            <ul>
              <li className="default">
                <dl>
                  <dt>사전예약</dt>
                  <dd>
                    출발일 기준
                    <strong>{AirUtil.getSeatTypeName(travelRule?.baseSeatTypeCode)}</strong>
                  </dd>
                </dl>
              </li>
              <li className="default">
                <dl>
                  <dt>최대 허용 좌석등급</dt>
                  <dd>
                    <strong>{AirUtil.getSeatTypeName(travelRule?.exceptSeatTypeCode)}</strong>
                  </dd>
                </dl>
              </li>
              <li className="default">
                <dl>
                  <dt>좌석 승급 조건</dt>
                  <dd>
                    <strong>{travelRule?.exceptTimeType} 시간 이상 탑승</strong>시 <br />
                    단,
                    <strong>
                      <strong>{travelRule?.exceptCityList?.map((except) => except.cityName).join(", ")}</strong>
                    </strong>
                  </dd>
                </dl>
              </li>
              <li className="etc">
                <dl>
                  <dt>기타사항</dt>
                  <dd>
                    <strong>{travelRule?.etc ?? null}</strong>
                  </dd>
                </dl>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button
          type="button"
          className="btns-cmm"
          onClick={() => {
            onClose && onClose();
          }}
        >
          닫기
        </button>
      </div>
    </div>
  );
}

export default TravelPolicy;
