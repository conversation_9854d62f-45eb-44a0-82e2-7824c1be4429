import AIR_ICONS_PATH from "@/assets/airIcon";
import { MAPPING_UNIT } from "@/constants/common";
import AirUtil from "@/utils/airUtil";
import { getHourMin, onErrorImgAirline } from "@/utils/common";
import { convertMinutesToHours, momentKR } from "@/utils/date";
import DateUtil from "@/utils/dateUtil";
import { isEmpty } from "lodash";
import { Fragment, useState } from "react";

const SecScheduleInfo = (props) => {
  const { data } = props;
  const { segments, airline, freessrs } = data;
  const [showDetail, setShowDetail] = useState(false);
  return (
    <div className="plan">
      {segments.map((segment, segmentIndex) => {
        const {
          carrierCode,
          flightNumber,
          cabinClass,
          departureDate,
          deptAirport,
          arrAirport,
          arrAirportName,
          deptAirportName,
          flightTime,
          legs,
          arrivalDate,
          bookingClass,
          waitingTime,
        } = segment;
        let elapseFlyingHourMin = 0;
        segments.forEach((segment) => {
          elapseFlyingHourMin += Number(segment.waitingTime) + Number(segment.flightTime);
        });
        const departureDay = momentKR(departureDate).format("MM월 DD일");
        const arrivalDay = momentKR(arrivalDate).format("MM월 DD일");
        const departureHour = momentKR(data.departureDate).format("HH:mm");
        const deparDate = departureDay + DateUtil.getDayOfWeekName(momentKR(departureDate).format("YYYY-MM-DD"));
        const arrDate = arrivalDay + DateUtil.getDayOfWeekName(momentKR(arrivalDate).format("YYYY-MM-DD"));
        const arrivalHour = momentKR(arrivalDate).format("HH:mm");

        const baggageAllowance = freessrs.find((fe) => fe.ssrType === "Baggage" && fe.ssrService[0].ssrAmount === 0);
        const arrivalTerminal = isEmpty(legs?.[0]?.arrivalTerminal) ? "" : `T${legs?.[0]?.arrivalTerminal}`;
        const departureTerminal = isEmpty(legs?.[0]?.departureTerminal) ? "" : `T${legs?.[0]?.departureTerminal}`;

        return (
          <Fragment key={segmentIndex}>
            {segmentIndex === 0 && (
              <div className="sec-top">
                <div className="left-col">
                  <div className="label">{data.length > 2 ? "여정" : segmentIndex === 0 ? "가는 편" : "오는 편"}</div>
                  <div className="date">{deparDate}</div>
                </div>
                <div className="right-col">
                  <div className="total-time"> 총 {convertMinutesToHours(elapseFlyingHourMin)}</div>
                  {/* todo: need update */}
                  <p className="status">{waitingTime ? "대기" : "확정"}</p>
                </div>
              </div>
            )}
            <div className={`sec-mid ${showDetail && "active"}`}>
              <div className="aircraft">
                <img src={AIR_ICONS_PATH[airline] || ""} onError={onErrorImgAirline} alt="항공사 로고" />
                <p className="name">{segments[0].carrierCodeName}</p>
                <div className="etc">
                  <span>
                    {carrierCode} {flightNumber}
                  </span>
                  <span>
                    {AirUtil.getSeatTypeName(cabinClass)} ({bookingClass})
                  </span>
                </div>
              </div>
              <div className="schedule">
                <div className="box-group">
                  <p className="date">{deparDate}</p>
                  <p className="time">{departureHour}</p>
                  <div className="sign dep"></div>
                  <p className="city">{deptAirport}</p>
                  <p className="airport">
                    {deptAirportName} {departureTerminal}
                  </p>
                </div>
                <div className="box-etc">
                  비행시간
                  <strong>{getHourMin(flightTime)}</strong>
                  <br />
                  {isEmpty(baggageAllowance)
                    ? "무료 수하물 불포함"
                    : `무료 수하물 ${baggageAllowance.ssrService[0].ssrValue} ${MAPPING_UNIT[baggageAllowance.ssrUnit]}`}
                </div>
                <div className="box-group">
                  <p className="date">{arrDate}</p>
                  <p className="time">{arrivalHour}</p>
                  <div className="sign arr"></div>
                  <p className="city">{arrAirport}</p>
                  <p className="airport">
                    {arrAirportName} {arrivalTerminal}
                  </p>
                </div>
              </div>
              {segments.length > 1 && segments.length > segmentIndex + 1 && <div className="overstop">{convertMinutesToHours(waitingTime)} 대기</div>}
            </div>
            <div className="box-btn">
              <button
                type="button"
                className={`btns-cmm ${showDetail && "active"}`}
                onClick={() => {
                  setShowDetail((prev) => !prev);
                }}
              />
            </div>
          </Fragment>
        );
      })}
    </div>
  );
};

export default SecScheduleInfo;
