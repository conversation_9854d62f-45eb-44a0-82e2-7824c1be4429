import { useAppSelector } from "@/store";
import { selectDataMain } from "@/store/mainSlice.mobile";
import useDebounce from "@/app/hooks/useDebounce";
import { useEffect, useState } from "react";
import { getAirportAutoSearch } from "@/service/mainAirTravelPlaceService";
import { isEmpty } from "lodash";
import { nvl } from "@/common";
import { stripHTMLTagInAirportList } from "@/utils/app";

const title = {
  depart: "출발지",
  arrive: "도착지",
};

const getStorageData = (key, fallback) => (isEmpty(fallback) ? JSON.parse(localStorage.getItem("airMainData"))?.[key] : fallback) ?? [];

function CitySearch(props) {
  const { options, open, onClose, onSelect } = props;
  const { overseas, airportMainSections } = useAppSelector(selectDataMain);
  const storageOverseas = getStorageData("overseas", overseas);
  const storageAirportMainSections = getStorageData("airportMainSections", airportMainSections);
  const [value, setValue] = useState("");
  const [searchResult, setSearchResult] = useState([]);
  const searchKeyword = useDebounce(value, 500);

  const handleChange = (event) => {
    const newValue = event.target.value;
    setValue(newValue);
  };
  useEffect(() => {
    if (isEmpty(value)) {
      setSearchResult([]);
      return;
    }
    const getListCity = async () => {
      try {
        const data = await getAirportAutoSearch({
          keyword: searchKeyword,
          isOverseas: null,
        });
        setSearchResult(stripHTMLTagInAirportList(data));
      } catch (err) {
        setSearchResult([]);
        console.log(err);
      }
    };
    getListCity();
  }, [searchKeyword]);

  return (
    <div className="layer-pages city-search" style={{ display: open ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">{title[options?.type ?? "depart"]}</p>
      </div>
      <div className="layer-cotn">
        <input
          type="search"
          id="searchAirport"
          placeholder="도시명 또는 공항명을 입력하세요"
          className="search-box"
          autoComplete="off"
          value={value}
          onChange={handleChange}
        />

        {isEmpty(searchResult) && (
          <div className="box-default" style={{ display: "block" }}>
            <div className="sec-list">
              {storageAirportMainSections?.map((airportMainSectionsItem) => {
                return (
                  <dl key={airportMainSectionsItem.sectionId}>
                    <dt>{airportMainSectionsItem.airportSection}</dt>
                    {storageOverseas
                      .filter((item) => item.airportSection === airportMainSectionsItem.airportSection)
                      .map((overseasItem, index) => {
                        return (
                          <dd key={index}>
                            <button
                              type="button"
                              onClick={() => {
                                onSelect({ options, data: overseasItem });
                                onClose();
                              }}
                            >
                              {overseasItem.name}
                            </button>
                          </dd>
                        );
                      })}
                  </dl>
                );
              })}
            </div>
          </div>
        )}
        {!isEmpty(searchResult) && (
          <div className="box-result">
            <ul id="airportAutoSearchArea" style={{ display: "block" }}>
              {searchResult.map((item) => {
                return (
                  <li key={item.id}>
                    <button
                      name="airportSelect"
                      id={item.id}
                      data-airportName={nvl(item.originalName, "")}
                      code={item.code}
                      onClick={() => {
                        onSelect({ options, data: item });
                        setValue("");
                        onClose();
                      }}
                    >
                      <p className="name" dangerouslySetInnerHTML={{ __html: `${nvl(item.code === item.cityDTO?.cityCode ? item.cityDTO.name : item.name, "")} (${item.code})` }}></p>
                      <p className="nation" dangerouslySetInnerHTML={{ __html: nvl(item.countryName, "") }} />
                    </button>
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={onClose}>
          닫기
        </button>
      </div>
    </div>
  );
}

export default CitySearch;
