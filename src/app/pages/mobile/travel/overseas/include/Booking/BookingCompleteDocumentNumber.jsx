import { Fragment } from "react";

function BookingCompleteDocumentNumber(props) {
  const { travel, documentNumbers } = props;

  return (
    <>
      {!!documentNumbers?.length && (
        <div className="box-middle fare-info">
          <p className="tit">{travel.company?.btmsSetting?.documentNumberUseTitle || "문서번호"}</p>
          <div className="inner">
            <div className="clearfix">
              {documentNumbers.map((documentNumber, index) => (
                <Fragment key={index}>
                  <span>{documentNumber}</span>
                  {index === documentNumbers.length - 1 ? "" : <>&nbsp;,&nbsp;</>}
                </Fragment>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default BookingCompleteDocumentNumber;
