import { useState } from "react";

function DocumentAttachmentModify(props) {
  const { files, onRemove, onClose } = props;
  const [selectedFiles, setSelectedFiles] = useState([]);

  const handleSelectFile = (event, file) => {
    const isChecked = event.target.checked;
    const nextSelectedFiles = isChecked ? [...selectedFiles, file] : selectedFiles.filter((item) => item.tempFileName !== file.tempFileName);
    setSelectedFiles(nextSelectedFiles);
  };

  const confirmRemoveFile = () => {
    onRemove(selectedFiles);
  };

  const handleClose = () => {
    setSelectedFiles([]);
    onClose();
  };

  return (
    <div className="layer-pages edit-document type-bottom !block">
      <div className="box-wrap active">
        <div className="layer-head">
          <p className="tit">증빙서류 수정</p>
        </div>
        <div className="layer-cotn">
          <div className="document-list-outer">
            <ul className="document-list" id="deleteFileListAreaUl">
              {files.map((file) => (
                <li key={file.tempFileName}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="addDocmentFile"
                      checked={selectedFiles.some((item) => item.tempFileName === file.tempFileName)}
                      onChange={(event) => handleSelectFile(event, file)}
                    />
                    <span>{file.originFileName}</span>
                  </label>
                </li>
              ))}
            </ul>
          </div>
          <div className="box-btn">
            <button type="button" className="btns-cmm round-basic color-w w-100" name="removeFileButton" onClick={confirmRemoveFile}>
              첨부파일 삭제
            </button>
          </div>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={handleClose}>
            닫기
          </button>
        </div>
      </div>
    </div>
  );
}

export default DocumentAttachmentModify;
