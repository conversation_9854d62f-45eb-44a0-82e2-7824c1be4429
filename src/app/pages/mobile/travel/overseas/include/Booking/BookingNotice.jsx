import { MOBILE_URL } from "@/constants/url";
import { useAppDispatch } from "@/store";
import { actionCloseAirCompareSchedule } from "@/store/mainSlice.mobile";
import { momentKR } from "@/utils/date";
import { useNavigate } from "react-router-dom";

function BookingNotice(props) {
  const { isOpen, onClose } = props;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const handleConfirmBooking = () => {
    onClose();
    dispatch(actionCloseAirCompareSchedule());
    navigate(MOBILE_URL.OverseasAirBooking);
  };

  return (
    <div className="jquery-modal blocker current" style={{ display: isOpen ? "block" : "none" }}>
      <div id="popReservNoti" className="modal-wrap modal" style={{ display: "inline-block" }}>
        <div className="modal-cotn">
          <dt>
            항공권 운임은 잔여 좌석에 따라 실시간으로 달라질 수 있습니다. 이후 예약상황 및 가격정책의 변경 등으로 인해 스케쥴 및 운임의 변동이 있을 수
            있습니다.
          </dt>
          <dl>{momentKR().format("YYYY-MM-DD")}기준, 유류할증료와 세금 및 제반요금 포함된 성인 1명 기준 운임입니다.</dl>
          <button id="noti-confirm-btn" onClick={handleConfirmBooking}>
            확인
          </button>
        </div>
      </div>
    </div>
  );
}

export default BookingNotice;
