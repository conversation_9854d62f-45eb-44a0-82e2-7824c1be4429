import { appMobileLoading, requestWithMobileLoading } from "@/utils/app";
import { useState } from "react";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { postFileDelete, postFileMultiUpload } from "@/service/common";
import DocumentAttachmentModify from "@/app/pages/mobile/travel/overseas/include/Booking/DocumentAttachmentModify";
import { postAttachFileDelete, postAttachFileSave } from "@/service/air";

const MAX_FILE_SIZE = 10;
const MAX_FILE_COUNT = 5;

function DocumentAttachment(props) {
  const { files, onChangeFiles, className = "", onDownload, bookingAirId } = props;
  const [isOpenRemoveModal, setIsOpenRemoveModal] = useState(false);

  const handleUploadFile = async (event) => {
    const files = Array.from(event.target.files);
    if (!isValidationFile(files)) {
      return;
    }
    const formData = new FormData();
    files.forEach((file) => {
      formData.append("File", file);
    });
    if (!bookingAirId) {
      uploadBookingFile(formData);
      return;
    }
    uploadViewFile(formData);
  };

  const uploadBookingFile = (formData) => {
    requestWithMobileLoading({
      url: "/common/file/multiUpload",
      method: "POST",
      data: formData,
      success: (res) => onChangeFiles((prev) => [...prev, ...res.data]),
    });
  };

  const uploadViewFile = async (formData) => {
    try {
      appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
      const response = await postFileMultiUpload(formData);
      const responseSave = await postAttachFileSave({
        bookingAirId,
        attachFiles: response.data.map((el) => {
          return {
            originFileName: el.originFileName,
            tempFileName: el.tempFileName,
            fileUploadPath: el.fileUploadPath,
            fileSize: el.fileSize,
            attachFileType: el.attachFileType,
            s3BucketPath: "",
          };
        }),
      });
      onChangeFiles((prev) => [...prev, ...responseSave.data.attachFiles]);
    } catch (error) {
      console.error(error);
    } finally {
      appMobileLoading.off();
    }
  };

  function isValidationFile(files) {
    const isExceedMaxFileSize = files.some((file) => file.size > parseInt(MAX_FILE_SIZE) * 1000000);
    if (isExceedMaxFileSize) {
      alert("파일 용량은 " + MAX_FILE_SIZE + " MB를 초과할 수 없습니다.");
      return false;
    }
    if (MAX_FILE_COUNT != null && parseInt(MAX_FILE_COUNT) > 1) {
      if (files.length > parseInt(MAX_FILE_COUNT)) {
        alert("업로드 파일 개수 " + MAX_FILE_COUNT + "개가 초과되었습니다.");
        return false;
      }
    }
    return true;
  }

  const handleRemoveFile = async (files) => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    for (const file of files) {
      await postFileDelete({ filePath: file.fileUploadPath + file.tempFileName });
      if (bookingAirId) {
        await postAttachFileDelete({ bookingAirId, attachFileId: file.attachFileId });
      }
      onChangeFiles((prev) => prev.filter((prevFile) => prevFile.tempFileName !== file.tempFileName));
    }
    appMobileLoading.off();
  };

  return (
    <>
      <div className={`box-item active ${className}`} id="divAddDocumentFile">
        <dl>
          <dt className="box-dt">
            증빙서류 첨부
            <p className="txt-info">10MB 이하 파일만 등록 가능합니다.</p>
          </dt>
          <dd className="box-dd !block" id="fileListArea">
            <button type="button" className="btn-edit-document" data-page-layer="edit-document" onClick={() => setIsOpenRemoveModal(true)}>
              수정
            </button>
            <ul className="file-list" id="fileListAreaUl">
              {files.map((file) => (
                <li key={file.tempFileName}>
                  <span
                    className="file-name"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDownload && onDownload(file);
                    }}
                  >
                    {file.originFileName}
                  </span>
                </li>
              ))}
            </ul>
            <div className="btn-file-group">
              <label htmlFor="addDocumentFile" style={{ marginBottom: "16px" }}>
                첨부파일 등록
              </label>
              <input type="file" name="File" accept="" className="btn-file" id="addDocumentFile" multiple onChange={handleUploadFile} />
            </div>
          </dd>
        </dl>
      </div>
      {isOpenRemoveModal && <DocumentAttachmentModify files={files} onRemove={handleRemoveFile} onClose={() => setIsOpenRemoveModal(false)} />}
    </>
  );
}

export default DocumentAttachment;
