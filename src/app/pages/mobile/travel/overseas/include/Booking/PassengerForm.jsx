import { useAppSelector } from "@/store";
import { selectCountryList, selectUserInfo } from "@/store/userSlice";
import { momentKR } from "@/utils/date";
import { cloneDeep } from "lodash";
import { useState } from "react";
import { Fragment } from "react/jsx-runtime";
import PassengerSearch from "@/app/pages/mobile/travel/overseas/include/Booking/PassengerSearch";
import { isValidationInput } from "@/utils/common";

function PassengerForm(props) {
  const {
    formItem,
    formIndex,
    formPassengers,
    onChangeFormPassengers,
    params,
    isNameSamePassportRule,
    onChangeIsSameNamePassportRule,
    onResetFormPassengers,
  } = props;
  const countryList = useAppSelector(selectCountryList);
  const { userInfo } = useAppSelector(selectUserInfo);
  const [isActive, setIsActive] = useState(true);
  const [isOpenPassengerSearch, setIsOpenPassengerSearch] = useState(false);

  const handleChangeFormBooking = (event) => {
    const { name, value } = event.target;
    if (!isValidationInput(event)) return;
    const nextFormBooking = cloneDeep(formPassengers);
    nextFormBooking[formIndex] = { ...nextFormBooking[formIndex], [name.split("_")[0]]: value };
    onChangeFormPassengers(nextFormBooking);
  };

  const handleChangeMileageMemberships = (event, index, mileageIndex) => {
    const { name, value } = event.target;
    const nextFormBooking = cloneDeep(formPassengers);
    nextFormBooking[index].mileageMemberships[mileageIndex][name] = value;
    onChangeFormPassengers(nextFormBooking);
  };

  const handleReserverCheck = (event) => {
    const { checked: isChecked } = event.target;
    if (!isChecked) {
      onResetFormPassengers();
      return;
    }
    selectUser(userInfo);
  };

  const selectUser = (user) => {
    const { id, name, email, gender, birthday, nationality, cellPhoneNumber, customerPassport, mileageInfos } = user;
    const { lastName, firstName, passportNumber, expireYmd, countryId } = customerPassport ?? {};
    const nextFormPassengers = cloneDeep(formPassengers);
    nextFormPassengers[formIndex] = {
      userID: id,
      dateOfBirth: momentKR(birthday, "YYYYMMDD").format("YYYY-MM-DD"),
      gender,
      nationality: nationality ?? "KR",
      koreanName: name,
      englishFirstName: firstName ?? "",
      englishLastName: lastName ?? "",
      phoneNumber: cellPhoneNumber,
      email,
      passportDocumentNo: passportNumber ?? "",
      expireYear: expireYmd?.toString()?.substring(0, 4) ?? "",
      expireMonth: expireYmd?.toString()?.substring(4, 6) ?? "",
      expireDay: expireYmd?.toString()?.substring(6, 8) ?? "",
      passportIssuedByCode: countryId ?? "KR",
      mileageMemberships: [
        {
          airline: mileageInfos?.[0]?.airline ?? "",
          memberNo: mileageInfos?.[0]?.mileageMemberNo ?? "",
        },
        {
          airline: mileageInfos?.[1]?.airline ?? "",
          memberNo: mileageInfos?.[1]?.mileageMemberNo ?? "",
        },
      ],
    };
    onChangeFormPassengers(nextFormPassengers);
  };

  return (
    <Fragment>
      <div className={`box-item passenger-info ${isActive ? "active" : ""}`}>
        <dl>
          <dt>탑승객 정보</dt>
          <dd>
            <div className="desc-top">탑승자명은 여권 및 신분증의 내용과 동일하게 입력하여 주시기 바랍니다. 결제 후 탑승자명 변경은 불가합니다.</div>
            <div className="box-gruoop">
              <p className="person">
                성인 <span>{formItem}</span>명
                {formItem === 1 && (
                  <label className="form-chkbox">
                    <input type="checkbox" id="reserverCheck" name="reserverCheck" onClick={handleReserverCheck} />
                    <span>예약자 정</span>
                  </label>
                )}
              </p>
              <p className="form-tit ">
                한글이름<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="koreanName"
                    placeholder="예) 홍길동"
                    data-validateinput="koreanOnly"
                    maxLength="20"
                    value={formPassengers[formIndex].koreanName}
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <p className="form-tit ">
                영문 성<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    className="upper"
                    name="englishLastName"
                    placeholder="예) HONG"
                    data-validateinput="english"
                    maxLength="20"
                    value={formPassengers[formIndex].englishLastName}
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <p className="form-tit ">
                영문 이름<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    className="upper"
                    name="englishFirstName"
                    placeholder="예) GILDONG"
                    data-validateinput="english"
                    maxLength="30"
                    value={formPassengers[formIndex].englishFirstName}
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <p className="form-tit ">
                성별<span>*</span>
              </p>
              <div className="form-cn half-col">
                <label className="item-col form-radio">
                  <input
                    type="radio"
                    name={`gender_${formIndex}`}
                    value="Male"
                    checked={formPassengers[formIndex].gender === "Male"}
                    onChange={handleChangeFormBooking}
                  />
                  <span>남성</span>
                </label>
                <label className="item-col form-radio">
                  <input
                    type="radio"
                    name={`gender_${formIndex}`}
                    value="Female"
                    checked={formPassengers[formIndex].gender === "Female"}
                    onChange={handleChangeFormBooking}
                  />
                  <span>여성</span>
                </label>
              </div>
              <p className="form-tit ">
                생년월일<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="dateOfBirth"
                    placeholder="예) 19900531"
                    data-validateinput="numberOnly"
                    maxLength="8"
                    value={formPassengers[formIndex].dateOfBirth}
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <p className="form-tit ">
                국적<span>*</span>
              </p>
              <div className="form-cn">
                <span className="select-cmm">
                  <select name="nationality" value={formPassengers[formIndex].nationality} onChange={handleChangeFormBooking}>
                    {countryList.map((country) => (
                      <option key={country.code2} value={country.code2}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </span>
              </div>
              <p className="form-tit ">
                휴대전화<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="phoneNumber"
                    value={formPassengers[formIndex].phoneNumber}
                    maxLength="11"
                    placeholder="휴대전화 입력"
                    data-validateinput="numberOnly"
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <p className="form-tit ">
                이메일<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="email"
                    placeholder="<EMAIL>"
                    value={formPassengers[formIndex].email}
                    maxLength="100"
                    onChange={handleChangeFormBooking}
                  />
                </span>
              </div>
              <div className="desc-passport">
                <strong>미주 노선은 여권정보가 반드시 필요합니다. 정확한 여권정보를 입력하세요.</strong> <br />※ 여권 발급 예정자이거나 확인이 어려운
                경우 임의의 값을 입력한 후, 반드시 출발 72시간 전까지 유효한 여권정보를 입력하세요.
                <br />※ 여권 유효기간은 출국일 기준 6개월 이상 남아 있어야 출국이 가능합니다.
              </div>
              <p className="form-tit ">
                여권번호<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="passportDocumentNo"
                    value={formPassengers[formIndex].passportDocumentNo}
                    placeholder="예) *********"
                    maxLength="30"
                    onChange={handleChangeFormBooking}
                  />
                </span>
                <span style={{ color: "red", fontSize: 11 }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</span>
              </div>
              <p className="form-tit ">
                여권 만료 기간<span>*</span>
              </p>
              <div className="form-cn">
                <div className="box-yymmdd">
                  <span className="select-cmm year">
                    <select
                      name="expireYear"
                      value={momentKR(formPassengers[formIndex].expireYear, "YYYY").format("YYYY")}
                      onChange={handleChangeFormBooking}
                    >
                      {Array.from({ length: 20 }, (_, i) => ({
                        value: `${momentKR().year() + i}`,
                        label: `${momentKR().year() + i}년`,
                      })).map(({ value, label }) => (
                        <option key={value} value={value}>
                          {label}
                        </option>
                      ))}
                    </select>
                  </span>
                  <span className="select-cmm month">
                    <select
                      name="expireMonth"
                      value={momentKR(formPassengers[formIndex].expireMonth, "MM").format("MM")}
                      onChange={handleChangeFormBooking}
                    >
                      {Array.from({ length: 12 }, (_, i) => ({
                        value: String(i + 1).padStart(2, "0"),
                        label: `${i + 1}월`,
                      })).map(({ value, label }) => (
                        <option key={value} value={value}>
                          {label}
                        </option>
                      ))}
                    </select>
                  </span>
                  <span className="select-cmm day">
                    <select
                      name="expireDay"
                      value={momentKR(formPassengers[formIndex].expireDay, "DD").format("DD")}
                      onChange={handleChangeFormBooking}
                    >
                      {Array.from({ length: 31 }, (_, i) => ({
                        value: String(i + 1).padStart(2, "0"),
                        label: `${i + 1}일`,
                      })).map(({ value, label }) => (
                        <option key={value} value={value}>
                          {label}
                        </option>
                      ))}
                    </select>
                  </span>
                  <span style={{ color: "red", fontSize: 11 }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</span>
                </div>
              </div>
              <p className="form-tit ">
                여권 발행국가<span>*</span>
              </p>
              <div className="form-cn">
                <span className="select-cmm">
                  <select name="passportIssuedByCode" value={formPassengers[formIndex].passportIssuedByCode} onChange={handleChangeFormBooking}>
                    <option value="">국적</option>
                    {countryList.map((country) => (
                      <option key={country.code2} value={country.code2}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </span>
              </div>
              <p className="form-tit ">마일리지 회원번호</p>
              <div className="form-cn mileage">
                <div className="content">
                  <div>
                    <span>항공사1</span>
                    <input
                      type="text"
                      name="airline"
                      placeholder="한글/영문 입력"
                      value={formPassengers[formIndex].mileageMemberships[0].airline}
                      onChange={(event) => handleChangeMileageMemberships(event, formIndex, 0)}
                    />
                  </div>
                  <div>
                    <span>회원번호1</span>
                    <input
                      type="text"
                      name="memberNo"
                      placeholder="숫자/알파벳 입력"
                      value={formPassengers[formIndex].mileageMemberships[0].memberNo}
                      onChange={(event) => handleChangeMileageMemberships(event, formIndex, 0)}
                    />
                  </div>
                </div>
                <div className="content">
                  <div>
                    <span>항공사2</span>
                    <input
                      type="text"
                      name="airline"
                      placeholder="한글/영문 입력"
                      value={formPassengers[formIndex].mileageMemberships[1].airline}
                      onChange={(event) => handleChangeMileageMemberships(event, formIndex, 1)}
                    />
                  </div>
                  <div>
                    <span>회원번호2</span>
                    <input
                      type="text"
                      name="memberNo"
                      placeholder="숫자/알파벳 입력"
                      value={formPassengers[formIndex].mileageMemberships[1].memberNo}
                      onChange={(event) => handleChangeMileageMemberships(event, formIndex, 1)}
                    />
                  </div>
                </div>
              </div>
              {formItem === Number(params.adultCount) && (
                <div className="box-agree">
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isNameSamePassportRule"
                      name="isNameSamePassportRule"
                      checked={isNameSamePassportRule}
                      onChange={(event) => onChangeIsSameNamePassportRule(event.target.checked)}
                    />
                    <span>탑승객의 영문성명은 여권과 정확히 일치함을 확인하고 동의 합니다.</span>
                  </label>
                </div>
              )}
              <button
                type="button"
                className="btns-cmm round-basic color-w w-100 btn-search-passenger"
                onClick={() => setIsOpenPassengerSearch(true)}
              >
                탑승객 검색
              </button>
            </div>
          </dd>
        </dl>
        <div className="box-btn-arrow">
          <button type="button" className="btns-cmm" onClick={() => setIsActive((prev) => !prev)}></button>
        </div>
      </div>
      <PassengerSearch
        isOpen={isOpenPassengerSearch}
        onClose={() => setIsOpenPassengerSearch(false)}
        companyId=""
        workspaceId={userInfo.workspace?.id ?? ""}
        onSelect={selectUser}
      />
    </Fragment>
  );
}

export default PassengerForm;
