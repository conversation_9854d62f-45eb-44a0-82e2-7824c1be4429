import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

function DocumentNumber(props) {
  const { documentNumberList, onChange } = props;
  const { userInfo } = useAppSelector(selectUserInfo);
  const { documentNumberUseCount, documentNumberUseTitle, isDocumentNumberRequired } = userInfo.workspace?.company?.btmsSetting ?? {};

  const addDocumentList = () => {
    if (documentNumberUseCount === 0) {
      alert("문서번호 허용건수가 설정되지 않았습니다. 관리자에게 문의하여 주세요.");
      return;
    }
    if (documentNumberUseCount <= documentNumberList.length) {
      alert("문서번호는 " + documentNumberUseCount + "개까지만 입력 가능합니다.");
      return;
    }
    onChange((prev) => [...prev, ""]);
  };

  const onChangeDocumentNumber = (index, value) => {
    onChange((prev) => {
      const nextList = [...prev];
      nextList[index] = value;
      return nextList;
    });
  };

  const removeDocumentList = (index) => {
    onChange((prev) => {
      const nextList = [...prev];
      nextList.splice(index, 1);
      return nextList;
    });
  };

  return (
    <div className="box-item box-info active">
      <dl>
        <dt>
          {documentNumberUseTitle ?? "문서번호"}
          {isDocumentNumberRequired && "*"}
        </dt>
        <dd>
          <ul id="documentNumberList" className="form-doc-number">
            {documentNumberList.map((item, index) => (
              <li key={index} name="divDocumentList">
                <span className="input-cmm">
                  <input
                    type="text"
                    name="documentNumber"
                    placeholder="문서번호"
                    style={{ width: "85%" }}
                    value={item}
                    onChange={(event) => onChangeDocumentNumber(index, event.target.value)}
                  />
                  {index !== 0 && (
                    <button
                      type="button"
                      style={{ width: "35px", height: "35px", backgroundColor: "#D3D3D3", marginLeft: "10px", cursor: "pointer" }}
                      onClick={() => removeDocumentList(index)}
                    >
                      -
                    </button>
                  )}
                </span>
              </li>
            ))}
          </ul>
          <button type="button" className="btns-cmm round-basic color-w w-100" style={{ marginBottom: "16px" }} onClick={addDocumentList}>
            문서번호 추가
          </button>
        </dd>
      </dl>
    </div>
  );
}

export default DocumentNumber;
