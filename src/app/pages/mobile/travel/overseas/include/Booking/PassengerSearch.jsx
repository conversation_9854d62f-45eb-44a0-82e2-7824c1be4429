import request from "@/utils/request";
import { useEffect, useState } from "react";
import qs from "qs";
import useDebounce from "@/app/hooks/useDebounce";

function PassengerSearch(props) {
  const { isOpen, onClose, companyId, workspaceId, onSelect } = props;
  const [searchPassengerName, setSearchPassengerName] = useState("");
  const searchKeyword = useDebounce(searchPassengerName, 500);
  const [passengerData, setPassengerData] = useState([]);

  const handleClose = () => {
    setSearchPassengerName("");
    setPassengerData([]);
    onClose();
  };

  useEffect(() => {
    request({
      url: "/common/v2/customer/list",
      method: "POST",
      data: qs.stringify({
        workspaceId: workspaceId,
        companyId: companyId,
        name: searchKeyword,
      }),
    }).then((res) => {
      setPassengerData(res.data ?? []);
    });
  }, [searchKeyword, companyId, workspaceId]);

  return (
    <div className="layer-pages passenger-search" style={{ display: isOpen ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">탑승객 검색</p>
        <div className="box-search">
          <input
            type="search"
            id="searchPassengerName"
            placeholder="임직원 이름을 입력해 주세요. (2자 이상)"
            value={searchPassengerName}
            onChange={(event) => setSearchPassengerName(event.target.value)}
          />
        </div>
      </div>
      <div className="layer-cotn domestic">
        {passengerData.length ? (
          <div className="box-list">
            <ul id="passenger-data">
              {passengerData.map((item, index) => (
                <li
                  key={`${item.id}_${index}`}
                  onClick={() => {
                    onSelect(item);
                    handleClose();
                  }}
                >
                  <div className="profile"></div>
                  <div className="info">
                    <span className="name">{item.name}</span>
                    <span className="code">code</span>
                    <span className="position">{item.position?.name}</span>
                  </div>
                  <div className="email">{item.email}</div>
                  <div className="belong">
                    {item.workspace.name} &gt; {item.department?.name ?? ""}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="box-list none">
            <p>검색하신 회원 정보가 없습니다.</p>
          </div>
        )}
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={handleClose}>
          닫기
        </button>
      </div>
    </div>
  );
}

export default PassengerSearch;
