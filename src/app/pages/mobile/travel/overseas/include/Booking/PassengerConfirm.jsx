import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { MO<PERSON>LE_URL } from "@/constants/url";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { appMobileLoading } from "@/utils/app";
import { useNavigate } from "react-router-dom";
import { compact, isEmpty } from "lodash";
import { selectAirCompareSchedule } from "@/store/mainSlice.mobile";
import { requestV2 } from "@/utils/request";
import useTravelRule from "@/app/hooks/useTravelRule";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

function PassengerConfirm(props) {
  const { isOpen, passengers, onClose, violationReason = "", documentNumbers, attachFileList, checkedJourney } = props;
  const navigate = useNavigate();
  const { isViolation } = useTravelRule();
  const { userInfo } = useAppSelector(selectUserInfo);
  const { selectedFares = [] } = useAppSelector(selectAirCompareSchedule);
  const { lowestJourney = {} } = useAppSelector(selectAirOverseasSessionData);

  const bookingProcess = () => {
    const payloadData = {
      violationReason,
      documentNumbers: compact(documentNumbers),
      attachFiles: convertAttachFiles(),
      journeys: convertJourneys(),
      passengers: convertPassengers(),
    };
    onClose();
    appMobileLoading.on({
      type: TYPE_MOBILE_LOADING.SEARCH,
      message: (
        <dl>
          <dt>예약 진행 중입니다.</dt>
          <dd>
            새로고침 하시면 오류가 발생할 수 있으니
            <br />
            잠시만 기다려주세요.
          </dd>
        </dl>
      ),
    });
    requestV2({
      url: "/user/travel/overseas",
      method: "POST",
      data: payloadData,
    })
      .then((res) => {
        const travelId = res.data?.data?.id;
        if (!travelId) return;
        return postCompareAirSchedule(travelId);
      })
      .catch(() => {
        alert(`항공 예약이 실패하였습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
        appMobileLoading.off();
      })
      .finally(() => appMobileLoading.off());
  };

  const convertAttachFiles = () =>
    attachFileList.map((file) => ({
      originFileName: file.originFileName,
      tempFileName: file.tempFileName,
      fileUploadPath: file.fileUploadPath,
      fileSize: file.fileSize,
      attachFileType: file.attachFileType,
    }));

  const convertJourneys = () =>
    checkedJourney.flights.map((flight) => ({
      airline: flight.airline,
      arrAirport: flight.arrAirport,
      deptAirport: flight.deptAirport,
      arrivalDate: flight.arrivalDate,
      departureDate: flight.departureDate,
      journeyKey: flight.journeyKey,
      journeyType: flight.journeyType,
      pairKey: flight.pairKey,
      fareKey: flight.fares.fareKey,
      airFare: flight.fares.paxTypeFares[0].airFare,
      airTax: flight.fares.paxTypeFares[0].airTax,
      fuelChg: flight.fares.paxTypeFares[0].fuelChg,
      tkFee: flight.fares.paxTypeFares[0].tkFee,
    }));

  const convertPassengers = () =>
    passengers.map((passenger) => ({
      ...passenger,
      mileageMemberships: passenger.mileageMemberships.filter((mileage) => mileage.airline.trim() && mileage.memberNo.trim()),
    }));

  const postCompareAirSchedule = async (travelId) => {
    const isLowestPrice = userInfo?.workspace?.company?.btmsSetting?.isLowestPrice;
    const checkedJourneyIndex = selectedFares.findIndex((fare) => fare.journeyKey === checkedJourney.journeyKey);
    const payload = {
      compareSchedules: !isEmpty(selectedFares)
        ? selectedFares.map((fare) => ({ journeys: fare.flights, isRuleViolation: isViolation(fare) }))
        : null,
      lowestSchedule:
        isLowestPrice && !isEmpty(lowestJourney) ? { journeys: lowestJourney?.flights, isRuleViolation: isViolation(lowestJourney) } : null,
      selectedSchedule: checkedJourneyIndex >= 0 ? checkedJourneyIndex : 0,
    };
    return requestV2({
      url: `/user/travel/overseas/${travelId}/compare-schedule`,
      method: "POST",
      data: payload,
    }).then(() => navigate(MOBILE_URL.OverseasAirBookingComplete.replace(":id", travelId)));
  };

  return (
    <div className="layer-pages passenger-confirm" style={{ display: isOpen ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">탑승객 정보 확인</p>
      </div>
      <div className="layer-cotn domestic">
        <p className="desc">
          탑승객 영문성명 작성 시, 여권상의 이름과 정확히 일치하게 작성해 주세요. <br />
          영문성명이 여권과 다를 경우 탑승이 거절될 수 있으며, 이는 여행사에서 책임지지 않습니다.
        </p>
        <dl className="box-dl">
          <dt className="box-dt">예약자 정보</dt>
          <dd className="box-dd">
            <div className="tbl-cmm">
              <table>
                <colgroup>
                  <col style={{ width: 90 }} />
                  <col />
                </colgroup>
                <tbody>
                  <tr>
                    <th>예약자명</th>
                    <td>{userInfo.name}</td>
                  </tr>
                  <tr>
                    <th>휴대전화</th>
                    <td>{userInfo.cellPhoneNumber?.replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3") ?? ""}</td>
                  </tr>
                  <tr>
                    <th>이메일 주소</th>
                    <td>{userInfo.email}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </dd>
        </dl>
        <dl className="box-dl">
          <dt className="box-dt">탑승객 정보</dt>
          <dd className="box-dd">
            <div className="tbl">
              <table>
                <colgroup>
                  <col style={{ width: "23%" }} />
                  <col />
                  <col style={{ width: 100 }} />
                  <col style={{ width: 60 }} />
                </colgroup>
                <thead>
                  <tr>
                    <th className="left">성</th>
                    <th className="left">이름</th>
                    <th>생년월일</th>
                    <th>성별</th>
                  </tr>
                </thead>
                <tbody id="traveler-confirm-data">
                  {passengers.map((item, index) => (
                    <tr key={`${item.email}_${index}`}>
                      <td className="left">{item.englishLastName.toUpperCase()}</td>
                      <td className="left">{item.englishFirstName.toUpperCase()}</td>
                      <td>{item.dateOfBirth}</td>
                      <td>{item.gender === "Male" ? "남성" : "여성"}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </dd>
        </dl>
        <div className="bottom-btn">
          <a href="#none;" className="btns-cmm round-basic color-w w-100 layer-pages-close" onClick={onClose}>
            다시 입력
          </a>
          <a href="#none;" className="btns-cmm round-basic color-b w-100 layer-pages-close" onClick={bookingProcess}>
            확인
          </a>
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={onClose}>
          닫기
        </button>
      </div>
    </div>
  );
}

export default PassengerConfirm;
