import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { sortByCondition } from "@/utils/app";
import useTravelRule from "@/app/hooks/useTravelRule";
import { convertMinutesToHours, momentKR } from "@/utils/date";
import { useEffect, useMemo, useState } from "react";
import { comma, getJourneyAmount, isIncludedCorporateFare } from "@/utils/common";
import { actionSetAirOverseasSessionData } from "@/store/airSlice";
import { actionCloseAirCompareSchedule, selectAirCompareSchedule } from "@/store/mainSlice.mobile";
import "@/styles/mobile/css/airCompare.css";

function AirCompareSchedule() {
  const {
    isOpen,
    params,
    selectedFares,
    journeyInfos,
    onOpenDetailInfoSearch,
    onChangeSelectedFares,
    onOpenBookingNotice,
    onOpenTravelPolicy,
    isOnlyView,
  } = useAppSelector(selectAirCompareSchedule);
  const dispatch = useAppDispatch();
  const { userInfo } = useAppSelector(selectUserInfo);
  const lowestJourney = useMemo(() => sortByCondition(journeyInfos, "lowAmount")?.[0] ?? null, [journeyInfos]);
  const { isViolation } = useTravelRule();
  const [isOpenDetailAmount, setIsOpenDetailAmount] = useState({});
  const [isActiveTipCompare, setIsActiveTipCompare] = useState(false);
  const [checkedJourney, setCheckedJourney] = useState(null);
  const checkedJourneyAmount = useMemo(() => getJourneyAmount(checkedJourney), [checkedJourney]);
  const lowestJourneyAmount = useMemo(() => getJourneyAmount(lowestJourney), [lowestJourney]);

  const handleRemoveSelectedFare = (fareId) => {
    onChangeSelectedFares((prev) => prev.filter((item) => item.id !== fareId));
    setCheckedJourney(null);
    setIsOpenDetailAmount({});
  };

  const handleClose = () => {
    dispatch(actionCloseAirCompareSchedule());
    setIsOpenDetailAmount({});
  };

  const handleBooking = () => {
    dispatch(actionSetAirOverseasSessionData({ data: checkedJourney, lowestJourney, filter: params }));
    onOpenBookingNotice();
  };

  useEffect(() => {
    setIsActiveTipCompare(!selectedFares.length);
    if (isOpen && !isOnlyView) {
      setCheckedJourney(null);
    }
  }, [isOpen, isOnlyView, selectedFares]);

  return (
    <>
      {selectedFares.length ? (
        <div
          id="search-compare-schedule"
          className="layer-pages compare-ticket"
          style={{ display: isOpen ? "block" : "none", paddingBottom: isOnlyView ? 0 : 60 }}
        >
          <div className="layer-head">
            <p className="tit">항공 스케줄 비교</p>
            <div className="search-result-top" hidden={isOnlyView}>
              <div className="clearfix policy-box">
                <div className="left-col label-type">
                  <p className="in">In Policy</p>
                  <p className="out">Out of Policy</p>
                </div>
                <div className="right-col">
                  <a href="#" className="my-trip-rule" onClick={onOpenTravelPolicy}>
                    나의 출장 규정
                  </a>
                </div>
              </div>
            </div>
            <div className={`btn-tip-compare ${isActiveTipCompare ? "active" : ""}`} onClick={() => setIsActiveTipCompare((prev) => !prev)}>
              <span className="!w-[195px]">비교견적표의 요금은 실제 예약된 금액이 아니며 담당자의 최종 확인이 필요합니다.</span>
            </div>
          </div>
          <div className="layer-cotn !h-[100%]" style={{ paddingTop: isOnlyView ? 58 : 100 }}>
            <div className="saerch-result-wrap" id="airCompareScheduleLayerData">
              {selectedFares.map((item, itemIndex) => {
                const { fareAmount, taxAmount } = getJourneyAmount(item);
                return (
                  <div key={item.id} className="box-item-wrap">
                    <div className="box-top">
                      <p className="tit">
                        <label className="form-radio">
                          <input
                            type="radio"
                            name="airCompareCheckboxSelect"
                            id={`airCompareCheckboxSelect_${itemIndex}`}
                            checked={item.id === checkedJourney?.id}
                            onChange={() => setCheckedJourney(item)}
                            disabled={selectedFares.length !== 3}
                          />
                          <span className="tit">스케줄 {itemIndex + 1}</span>
                        </label>
                      </p>
                      <p className="box-top-delete_btn" hidden={isOnlyView}>
                        <button type="button" onClick={() => handleRemoveSelectedFare(item.id)}>
                          삭제
                        </button>
                      </p>
                    </div>
                    <div key={item.journeyKey + itemIndex} className={`box-item pl-${isViolation(item) ? "out" : "in"}`}>
                      {item.flights.map((journeyMapItem, journeyMapItemIndex) => {
                        const { airline, deptAirport, departureDate, arrivalDate, arrAirport, stops, segments } = journeyMapItem;
                        let elapseFlyingHourMin = 0;
                        segments.forEach((segment) => {
                          elapseFlyingHourMin += Number(segment.waitingTime) + Number(segment.flightTime);
                        });
                        const formattedDeptTime = momentKR(departureDate).format("YYYY-MM-DD");
                        const formattedArrTime = momentKR(arrivalDate).format("YYYY-MM-DD");
                        const dateVariation = momentKR(formattedArrTime).diff(momentKR(formattedDeptTime), "days");

                        return (
                          <div className="air-plan" key={journeyMapItemIndex}>
                            <div className="left-col">
                              <img className="left-col aircraft" src={`https://cdns.tourvis.com/air/dist/images/logo/${airline}.png`} alt="" />
                              <div className="left-col plan">
                                <span className="time">{momentKR(departureDate).format("HH:mm")}</span>
                                <span className="city">{deptAirport}</span>
                              </div>
                              <div className={`left-col way-to n${stops}`} />
                              <div className="left-col plan">
                                <span className="time" data-more-time={dateVariation ? `+${dateVariation}` : ""}>
                                  {momentKR(arrivalDate).format("HH:mm")}
                                </span>
                                <span className="city">{arrAirport}</span>
                              </div>
                              <p className="share">{segments[0].carrierCodeName}</p>
                            </div>
                            <div className="right-col">
                              <p className={`via ${stops > 0 ? "on" : "none"}`}>{stops > 0 ? `${stops}회 경유` : "직항"}</p>
                              <p className="total-time">{convertMinutesToHours(elapseFlyingHourMin)}</p>
                            </div>
                          </div>
                        );
                      })}
                      {isOnlyView ? (
                        <div className="sec-bottom">
                          <div className="total-price">
                            <strong className="!pr-0 after:!w-0">721,200원</strong> 기업 운임
                          </div>
                        </div>
                      ) : (
                        <div className="sec-bottom">
                          <div className="total-price" onClick={() => setIsOpenDetailAmount((prev) => ({ ...prev, [itemIndex]: !prev[itemIndex] }))}>
                            <p>기업운임</p>
                            <strong>{comma(fareAmount + taxAmount)}원</strong>
                          </div>
                          <ul className="total-price_info" style={{ display: isOpenDetailAmount[itemIndex] ? "block" : "none" }}>
                            <li>
                              <span>항공료: {comma(fareAmount + taxAmount)}원</span>
                            </li>
                            <li>
                              <span>TAX: {comma(taxAmount)}원</span>
                            </li>
                            <li>
                              <span>발권 수수료: {comma(fareAmount)}원</span>
                            </li>
                          </ul>
                          <div className="box-btn">
                            <a href="#" className="btns-detail" onClick={() => onOpenDetailInfoSearch({ journey: item, isCompare: true })}>
                              상세보기
                            </a>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className="saerch-result-wrap lowest-price-wrap" hidden={isOnlyView}>
              {userInfo.workspace?.company?.btmsSetting?.isLowestPrice && (
                <>
                  <p className="tit">최저가 요금 스케줄</p>
                  <div className="box-item-wrap">
                    <div className={`box-item pl-${isViolation(lowestJourney) ? "out" : "in"}`}>
                      {lowestJourney?.flights?.map((journeyMapItem, journeyMapItemIndex) => {
                        const { airline, deptAirport, departureDate, arrivalDate, arrAirport, stops, segments } = journeyMapItem;
                        let elapseFlyingHourMin = 0;
                        segments.forEach((segment) => {
                          elapseFlyingHourMin += Number(segment.waitingTime) + Number(segment.flightTime);
                        });
                        const formattedDeptTime = momentKR(departureDate).format("YYYY-MM-DD");
                        const formattedArrTime = momentKR(arrivalDate).format("YYYY-MM-DD");
                        const dateVariation = momentKR(formattedArrTime).diff(momentKR(formattedDeptTime), "days");

                        return (
                          <div className="air-plan" key={journeyMapItemIndex}>
                            <div className="left-col">
                              <img className="left-col aircraft" src={`https://cdns.tourvis.com/air/dist/images/logo/${airline}.png`} alt="" />
                              <div className="left-col plan">
                                <span className="time">{momentKR(departureDate).format("HH:mm")}</span>
                                <span className="city">{deptAirport}</span>
                              </div>
                              <div className={`left-col way-to n${stops}`} />
                              <div className="left-col plan">
                                <span className="time" data-more-time={dateVariation ? `+${dateVariation}` : ""}>
                                  {momentKR(arrivalDate).format("HH:mm")}
                                </span>
                                <span className="city">{arrAirport}</span>
                              </div>
                              <p className="share">{segments[0].carrierCodeName}</p>
                            </div>
                            <div className="right-col">
                              <p className={`via ${stops > 0 ? "on" : "none"}`}>{stops > 0 ? `${stops}회 경유` : "직항"}</p>
                              <p className="total-time">{convertMinutesToHours(elapseFlyingHourMin)}</p>
                            </div>
                          </div>
                        );
                      })}
                      <div className="sec-bottom">
                        <div className="total-price" onClick={() => setIsOpenDetailAmount((prev) => ({ ...prev, lowest: !prev.lowest }))}>
                          <p>기업운임</p>
                          <strong>{comma(lowestJourneyAmount.fareAmount + lowestJourneyAmount.taxAmount)}원</strong>
                        </div>
                        <ul className="total-price_info" style={{ display: isOpenDetailAmount.lowest ? "block" : "none" }}>
                          <li>
                            <span>항공료: {comma(lowestJourneyAmount.fareAmount + lowestJourneyAmount.taxAmount)}원</span>
                          </li>
                          <li>
                            <span>TAX: {comma(lowestJourneyAmount.taxAmount)}원</span>
                          </li>
                          <li>
                            <span>발권 수수료: {comma(lowestJourneyAmount.fareAmount)}원</span>
                          </li>
                        </ul>
                        <div className="box-btn">
                          <a href="#" className="btns-detail" onClick={() => onOpenDetailInfoSearch({ journey: lowestJourney, isCompare: true })}>
                            상세보기
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="sec-bottom-price" hidden={isOnlyView}>
            <div className="total-price">
              <p className="tit">총 결제요금</p>
              <p className="fare">{comma(checkedJourneyAmount.fareAmount + checkedJourneyAmount.taxAmount)}원</p>
              <p className="type">{isIncludedCorporateFare(checkedJourney?.flights) ? "기업요금" : ""}</p>
            </div>
            <div className="right-col" id="airCompareScheduleBtnArea">
              <button
                type="button"
                className="btns-cmm round-basic color-b w-75"
                style={{ cursor: "default" }}
                disabled={!checkedJourney}
                onClick={handleBooking}
              >
                이 항공편으로 예약 요청
              </button>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close type-arrow">
            <button type="button" className="btns-cmm" onClick={handleClose}>
              닫기
            </button>
          </div>
        </div>
      ) : (
        <div className="layer-pages compare-ticket none-compare" style={{ display: isOpen ? "block" : "none" }}>
          <div className="layer-head">
            <p className="tit">항공 스케줄 비교</p>
            <div className={`btn-tip-compare ${isActiveTipCompare ? "active" : ""}`} onClick={() => setIsActiveTipCompare((prev) => !prev)}>
              <span className="!w-[195px]">비교견적표의 요금은 실제 예약된 금액이 아니며 담당자의 최종 확인이 필요합니다.</span>
            </div>
          </div>
          <div className="layer-cotn  none-compare-cotn">
            <div className="innner">
              <dl>
                <dt>비교할 항공권이 없습니다.</dt>
                <dd>
                  비교견적서에 항공편을 담아 <br />
                  상세 정보를 비교하신 후 예약하세요.
                </dd>
              </dl>
              <a href="#none;" className="btns-cmm round-basic color-b w-50" onClick={handleClose}>
                돌아가기
              </a>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close type-arrow">
            <button type="button" className="btns-cmm" onClick={handleClose}>
              닫기
            </button>
          </div>
        </div>
      )}
    </>
  );
}

export default AirCompareSchedule;
