import { useEffect, useState } from "react";
import AirUtil from "@/utils/airUtil";
import { comma, isIncludedCorporateFare, replaceCarriageReturns } from "@/utils/common";
import { isEmpty, isEqual } from "lodash";
import useTravelRule from "@/app/hooks/useTravelRule";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { getFareRule } from "@/service/air";
import { appMobileLoading } from "@/utils/app";
import { COP_CODE, TYPE_MOBILE_LOADING } from "@/constants/app";
import { selectAppLoading } from "@/store/loadingSlice.mobile";
import SecScheduleInfo from "@/app/pages/mobile/travel/overseas/include/DetailInfoSearch/SecScheduleInfo";

const secTab = [
  {
    type: "FlightDetailView",
    name: "여정정보",
  },
  {
    type: "FareRuleView",
    name: "운임규정",
  },
  {
    type: "MobileTravelRule",
    name: "출장규정",
  },
];

const TripRule = (props) => {
  const { data, travelRule } = props;
  const { isTravelLimitDayViolation, isBaseSeatTypeCodeViolation, isExceptSeatTypeCodeViolation } = useTravelRule();
  const user = useAppSelector(selectUserInfo);
  const isTravelLimitDayViolationFlag = isTravelLimitDayViolation(data);
  const isBaseSeatTypeCodeViolationFlag = isBaseSeatTypeCodeViolation(data);
  const isExceptSeatTypeCodeViolationFlag = isExceptSeatTypeCodeViolation(data);

  return (
    <>
      {isTravelLimitDayViolationFlag || isBaseSeatTypeCodeViolationFlag || isExceptSeatTypeCodeViolationFlag ? (
        <p className="result fail">예약 규정에 적합하지 않은 항공권입니다. 기준 외 발권 시 반드시 담당임원의 결제가 필요합니다.</p>
      ) : (
        <p className="result success">예약 규정에 적합한 항공권입니다.</p>
      )}
      <p className="desc">
        <strong>{user.userInfo.position.name}</strong> 님에게 적용되는 해외 항공권 예약 규정입니다.
      </p>
      <div className="box-info">
        <p className="tit">
          해외 항공권 예약은 <strong>{AirUtil.getExceptTimeType(travelRule?.exceptTimeType)}</strong>을 기준으로 합니다.
        </p>
        <ul>
          <li className={`${isTravelLimitDayViolationFlag ? "fail" : isTravelLimitDayViolationFlag == null ? "default" : "success"}`}>
            <dl>
              <dt>사전예약</dt>
              <dd>
                출발일 기준 <strong>{travelRule?.travelLimitDay}일 전</strong>
              </dd>
            </dl>
          </li>
          <li className={`${isBaseSeatTypeCodeViolationFlag ? "fail" : isBaseSeatTypeCodeViolationFlag == null ? "default" : "success"}`}>
            <dl>
              <dt>기본 좌석등급</dt>
              <dd>
                <strong>{AirUtil.getSeatTypeName(travelRule?.baseSeatTypeCode)}</strong>
              </dd>
            </dl>
          </li>
          <li className="default">
            <dl>
              <dt>최대 허용 좌석등급</dt>
              <dd>
                <strong>{AirUtil.getSeatTypeName(travelRule?.exceptSeatTypeCode)}</strong>
              </dd>
            </dl>
          </li>
          <li className={`${isExceptSeatTypeCodeViolationFlag ? "fail" : isExceptSeatTypeCodeViolationFlag == null ? "default" : "success"}`}>
            <dl>
              <dt>좌석 승급 조건</dt>
              <dd>
                <strong>{travelRule?.exceptTimeType} 시간 이상 탑승</strong>시 <br />
                단,
                <strong>
                  <strong>{travelRule?.exceptCityList.map((except) => except.cityName).join(", ")}</strong>
                </strong>
              </dd>
            </dl>
          </li>
          <li className="etc">
            <dl>
              <dt>기타사항</dt>
              <dd>
                <strong>{travelRule?.etc ?? null}</strong>
              </dd>
            </dl>
          </li>
        </ul>
      </div>
    </>
  );
};

const FareCondition = (props) => {
  const { data } = props;
  const [journey, setJourney] = useState({});
  const [fareBasis, setFareBasis] = useState({});
  const { open: isLoading } = useAppSelector(selectAppLoading);

  useEffect(() => {
    const getData = async () => {
      try {
        appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
        const journeys = data.map(({ journeyKey, pairKey, fares, airline, deptAirport, arrAirport, departureDate }) => {
          return {
            journeyKey,
            fareKey: fares.fareKey,
            pairKey,
            airline,
            deptAirport,
            arrAirport,
            departureDate,
            promotionId: "",
            fopPromotionId: "",
          };
        });
        const res = await getFareRule({
          journeys,
          ssCode: "BTMS",
        });
        setJourney(res.data.data?.journeys?.[0]);
        setFareBasis(res.data.data?.journeys?.[0]?.fareBasisRules?.[0]);
      } catch (error) {
        console.log(error);
        setJourney({});
      } finally {
        appMobileLoading.off();
      }
    };
    getData();
  }, []);
  return (
    <>
      {!isLoading && !isEmpty(journey) && (
        <>
          <ul className="tab-btm-fare">
            {journey.fareBasisRules.map((fareBasisRulesItem, index) => {
              return (
                <li key={index} className={`${isEqual(fareBasisRulesItem, fareBasis) ? "active" : ""}`}>
                  <a
                    href="#none;"
                    className="btn-default"
                    onClick={() => {
                      setFareBasis(fareBasisRulesItem);
                    }}
                  >
                    운임규정 {index + 1}
                  </a>
                </li>
              );
            })}
          </ul>

          <div className="tbl-cmm" id="fareRuleView_">
            <table className="table-fixed">
              <colgroup>
                <col style={{ width: "101px" }} />
                <col />
              </colgroup>
              <tbody>
                {[...(fareBasis?.items ?? []), ...journey.agencyItems]?.map((item, itemIndex) => {
                  return (
                    <tr key={itemIndex}>
                      <th>{item.name}</th>
                      <td dangerouslySetInnerHTML={{ __html: replaceCarriageReturns(item.description) }}></td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </>
      )}
    </>
  );
};

const getAmount = (data) => {
  let fareAmount = 0;
  let taxAmount = 0;

  data.forEach(({ fares }) => {
    fareAmount += fares.paxTypeFares[0].airFare;
    taxAmount += fares.paxTypeFares[0].airTax + fares.paxTypeFares[0].fuelChg;
  });

  return {
    fareAmount,
    taxAmount,
  };
};

const getCorporateFare = (data) => {
  const isInclude = isIncludedCorporateFare(data);
  return isInclude ? <span style={{ color: "#ff4e50" }}>기업 운임</span> : null;
};

function DetailInfoSearch(props) {
  const { open, onClose, journey, travelRule, isCompare, onSelectFare, onBooking } = props;
  const data = journey?.flights ?? [];
  const [tabType, setTabType] = useState("FlightDetailView");
  const { userInfo } = useAppSelector(selectUserInfo);

  const handleSelectTab = (currentTabType) => {
    setTabType(currentTabType);
  };

  return (
    <div className="layer-pages detail-info z-[111]" id="detailInfo" style={{ display: open ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">상세정보</p>
        <div className="sec-tab-top n03" hidden={isCompare}>
          <ul>
            {secTab.map(({ type, name }) => {
              return (
                <li key={type} className={`${tabType === type && "active"}`}>
                  <button type="button" onClick={() => handleSelectTab(type)}>
                    {name}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
      <div className={`layer-cotn tab-cotn ${isCompare ? "!pt-[58px]" : ""}`}>
        {tabType === "FlightDetailView" && (
          <div className="box-tab sec-schedule-info  active">
            {data.map((item, index) => {
              return <SecScheduleInfo data={item} key={index} />;
            })}
          </div>
        )}
        {tabType === "FareRuleView" && (
          <div className="box-tab fare-condition active">
            <FareCondition data={data} />
          </div>
        )}
        {tabType === "MobileTravelRule" && (
          <div className="box-tab trip-rule active">
            <TripRule data={{ ...data[0], flights: data }} travelRule={travelRule} />
          </div>
        )}
      </div>
      <div className="sec-bottom-price" hidden={isCompare}>
        <div className="total-price">
          <p className="tit">총 결제요금</p>
          <p className="fare">{comma(getAmount(data).fareAmount + getAmount(data).taxAmount)} 원</p>
          <p className="type">{getCorporateFare(data)}</p>
        </div>
        <div className="right-col">
          {userInfo.workspace?.company?.btmsSetting?.isComparativePrice ? (
            <button
              className="btns-cmm round-basic color-b w-75"
              onClick={() => {
                onClose();
                onSelectFare && onSelectFare(journey);
                setTabType("FlightDetailView");
              }}
            >
              비교함 추가
            </button>
          ) : (
            <button
              className="btns-cmm round-basic color-b w-75"
              onClick={() => {
                onClose();
                onBooking && onBooking(journey);
                setTabType("FlightDetailView");
              }}
            >
              예약하기
            </button>
          )}
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button
          type="button"
          className="btns-cmm"
          onClick={() => {
            onClose();
            setTabType("FlightDetailView");
          }}
        >
          닫기
        </button>
      </div>
    </div>
  );
}

export default DetailInfoSearch;
