import MIOSSlider from "@/app/components/mobile/common/IOSSlider";
import { useEffect, useMemo, useState } from "react";
import { isEmpty, uniq, cloneDeep, intersection } from "lodash";
import { COMPANY_SITECODE, COP_CODE } from "@/constants/app";
import DateUtil from "@/utils/dateUtil";
import { getSortAndCombineJourneyInfos, sortByCondition } from "@/utils/app";
import { momentKR } from "@/utils/date";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

const INIT_FILTER_DATA = {
  arrayCorporateFare: ["기업운임", "일반운임"],
  airlines: [],
  arrayStopOverCount: ["0", "1", "2"],
  alliances: [],
  departureTime: [],
  arrivalTime: [],
  leadTime: {},
  stopOverTime: {},
};

const VIA = [
  { id: 1, value: "0", label: "직항" },
  { id: 2, value: "1", label: "1회" },
  { id: 3, value: "2", label: "2회 이상" },
];

const FARE_TYPES = ["기업운임", "일반운임"];

const MAPPED_ALLIANCES = {
  SkyTeam: "스카이팀",
  Star: "스타얼라이언스",
  OneWorld: "원월드",
};

const SPECIFIC_KORTEK_AIRLINES = ["KE", "OZ", "VN"];

function FilterAirSearch(props) {
  const { params, journeyInfos, currentOrderOption, onChangeJourneyInfos, onChangePage, onResetSelectedFares } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [sliceAirlineMenu, setSliceAirlineMenu] = useState(3);
  const [filterData, setFilterData] = useState(INIT_FILTER_DATA);
  const [baseFilterData, setBaseFilterData] = useState(INIT_FILTER_DATA);
  const [uniqueList, setUniqueList] = useState({ airlines: [], fareTypes: [], alliances: [] });
  const [rangeTime, setRangeTime] = useState("startTime");
  const keyRangeTime = useMemo(() => (rangeTime === "startTime" ? "departureTime" : "arrivalTime"), [rangeTime]);
  const { userInfo } = useAppSelector(selectUserInfo);

  const getUniqueList = () => {
    const flattened = journeyInfos.flatMap((obj) => Object.values(obj).flat());
    const airlines = flattened.map((obj) => {
      return { airline: obj.airline, name: obj.airlineName };
    });
    const uniqueAirlines = [...new Map(airlines.map((item) => [item["airline"], item])).values()];
    const alliances = flattened.map((obj) => obj.alliances);
    return { airlines: uniqueAirlines, alliances: uniq(alliances.flat()) };
  };

  const handleChangeSlider = (name, value) => {
    const newTime = filterData[keyRangeTime].map((el) => {
      if (el.name === name) {
        return { ...el, value };
      }
      return el;
    });
    setFilterData((prev) => ({ ...prev, [keyRangeTime]: newTime }));
  };

  const formatTime = (decimal) => {
    const hours = Math.floor(decimal);
    const minutes = Math.round((decimal - hours) * 60);
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  };

  const handleChangeSliderDuration = (name, value) => {
    const newTime = filterData[name];
    newTime.value = value;
    setFilterData((prev) => ({ ...prev, [name]: newTime }));
  };

  const convertDecimalHoursToKoreanTime = (decimalHours) => {
    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);
    return `${hours}시간 ${minutes}분`;
  };

  const convertDecimalToTime = (decimal) => {
    const hours = Math.floor(decimal);
    const minutes = Math.round((decimal - hours) * 60);
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    return `${formattedHours}${formattedMinutes}`;
  };

  const handleChangeCondition = (e) => {
    const { name, value, checked } = e.target;
    setFilterData((prev) => ({
      ...prev,
      [name]: checked ? [...prev[name], value] : prev[name].filter((el) => el !== value),
    }));
  };

  const handleApplyFilter = () => {
    setIsOpen(false);
  };

  const handleResetFilter = () => {
    setIsOpen(false);
    setFilterData({ ...baseFilterData, leadTime: { name: params.departureAirportCodes[0], value: [0.5, 100] }, stopOverTime: { value: [0, 56] } });
  };

  const filterProcess = async () => {
    const isDirect = params?.stopOverType == 0;
    const { arrayCorporateFare, airlines, arrayStopOverCount, alliances, departureTime, arrivalTime, leadTime, stopOverTime } = filterData;
    const rawData = getSortAndCombineJourneyInfos(journeyInfos).filter((flight) => {
      if (isDirect) return flight.flights.every((el) => el.stops === 0);
      return true;
    });
    const isCorporateFareValid = (fareType) => {
      const hasCopCode = fareType.some((el) => el.includes(COP_CODE));
      if (arrayCorporateFare.length > 1) return true;
      if (arrayCorporateFare.length === 1 && arrayCorporateFare[0] === COP_CODE) return hasCopCode;
      if (arrayCorporateFare.length === 1 && arrayCorporateFare[0] !== COP_CODE) return !hasCopCode;
      return false;
    };
    const isStopsValid = (stops) => intersection(arrayStopOverCount, stops).length > 0;
    const isAlliancesValid = (alliance) => {
      if (isEmpty(alliance) && isEmpty(alliances)) return true;
      return intersection(alliances, alliance).length > 0;
    };
    const isAirlineValid = (flight) => {
      return airlines.includes(flight.airline);
    };
    const isTimeInRange = (time, range) => {
      const formattedTime = momentKR(time).format("HHmm");
      return formattedTime >= convertDecimalToTime(range[0]) && formattedTime <= convertDecimalToTime(range[1]);
    };
    const isDepartureTimeValid = (flights) => {
      return departureTime.every((timeRange, index) => isTimeInRange(flights[index].segments[0].departureDate, timeRange.value));
    };
    const isArrivalTimeValid = (flights) => {
      return arrivalTime.every((timeRange, index) => isTimeInRange(flights[index].segments.slice(-1)[0].arrivalDate, timeRange.value));
    };
    const isDurationValid = (segments) => {
      const totalTime = segments.reduce((total, segment) => total + Number(segment.waitingTime) + Number(segment.flightTime), 0);
      return totalTime >= leadTime.value[0] * 60 && totalTime <= leadTime.value[1] * 60;
    };
    const isStopOverDurationValid = (flights) => {
      const stopOverDurations = flights.map((flight) => flight.segments.map((seg) => seg.waitingTime));
      return stopOverDurations.flat().some((duration) => duration >= stopOverTime.value[0] * 60 && duration <= stopOverTime.value[1] * 60);
    };
    const filteredData = rawData.filter((el) => {
      const { flights, alliances } = el;
      const fareTypes = el.flights.map((flight) => flight.fares.fareTypes).flat();
      const stops = flights.map((flight) => JSON.stringify(flight.stops));
      const firstSegments = flights[0].segments;
      return (
        isCorporateFareValid(fareTypes) &&
        isStopsValid(stops) &&
        isAlliancesValid(alliances) &&
        isAirlineValid(el) &&
        (!isEmpty(departureTime) ? isDepartureTimeValid(flights) : true) &&
        (!isEmpty(arrivalTime) ? isArrivalTimeValid(flights) : true) &&
        (!isEmpty(leadTime) ? isDurationValid(firstSegments) : true) &&
        (!isEmpty(stopOverTime) ? isStopOverDurationValid(flights) : true)
      );
    });
    const sortedFlights = sortByCondition(filteredData, currentOrderOption.value);
    onResetSelectedFares();
    onChangeJourneyInfos(sortedFlights);
    onChangePage(1);
  };

  useEffect(() => {
    if (!isOpen && !isEmpty(journeyInfos)) {
      filterProcess();
    }
  }, [isOpen, filterData]);

  useEffect(() => {
    if (!isEmpty(journeyInfos)) {
      const hasIcn = params.arrivalAirportCodes.some((code) => code === "ICN") || params.departureAirportCodes.some((code) => code === "ICN");
      const hasSel = params.arrivalAirportCodes.some((code) => code === "SEL") || params.departureAirportCodes.some((code) => code === "SEL");
      const hasHan = params.arrivalAirportCodes.some((code) => code === "HAN") || params.departureAirportCodes.some((code) => code === "HAN");
      const isKortek = userInfo?.workspace?.company?.siteCode === COMPANY_SITECODE.KORTEK_COMPANY_SITECODE;
      const isFilterFlag = (hasIcn || hasSel) && hasHan && isKortek;

      const { airlines, alliances } = getUniqueList();
      const nextCondition = {
        ...cloneDeep(filterData),
        airlines: airlines.map((el) => el.airline).filter((el) => (isFilterFlag ? SPECIFIC_KORTEK_AIRLINES.includes(el) : true)),
        alliances,
      };
      setFilterData(nextCondition);
      setBaseFilterData(nextCondition);
      setUniqueList({ airlines, alliances });
    }
  }, [journeyInfos]);

  useEffect(() => {
    if (isEmpty(params)) return;
    const { departureAirportCodes, arrivalAirportCodes } = params;
    const mappedDepartureAirportCodes = departureAirportCodes.map((el) => ({ name: el, value: [0, 24] }));
    const mappedArrivalAirportCodes = arrivalAirportCodes.map((el) => ({ name: el, value: [0, 24] }));
    const updateCondition = {
      departureTime: mappedDepartureAirportCodes,
      arrivalTime: mappedArrivalAirportCodes,
      leadTime: { name: departureAirportCodes[0], value: [0.5, 100] },
      stopOverTime: { value: [0, 56] },
    };
    setFilterData((prev) => ({ ...prev, ...updateCondition }));
  }, [params]);

  return (
    <>
      <div className="btn-filter z-[100]">
        <button className="btns-cmm" onClick={() => setIsOpen(true)}>
          <span>필터</span>
        </button>
      </div>
      <div className="layer-pages filter" style={{ display: isOpen ? "block" : "none" }}>
        <div className="layer-head">
          <p className="tit">필터</p>
        </div>
        <div className="layer-cotn">
          <form>
            <dl>
              <dt>운임 조건</dt>
              {FARE_TYPES.map((item, index) => (
                <dd className="mb-0" key={item + index}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="arrayCorporateFare"
                      defaultValue={item}
                      checked={filterData.arrayCorporateFare.includes(item)}
                      onChange={handleChangeCondition}
                    />
                    <span>{item}</span>
                  </label>
                </dd>
              ))}
            </dl>
            <dl>
              <dt>경유</dt>
              {VIA.map((item) => (
                <dd className="mb-0" key={item.id}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="arrayStopOverCount"
                      defaultValue={item.value}
                      className="item"
                      checked={filterData.arrayStopOverCount.includes(item.value)}
                      onChange={handleChangeCondition}
                    />
                    <span>{item.label}</span>
                  </label>
                </dd>
              ))}
            </dl>
            <dl>
              <dt>얼라이언스</dt>
              {uniqueList.alliances?.map((item) => (
                <dd className="mb-0" key={item}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="alliances"
                      defaultValue={item}
                      checked={filterData.alliances.includes(item)}
                      onChange={handleChangeCondition}
                    />
                    <span>{MAPPED_ALLIANCES[item] ?? item}</span>
                  </label>
                </dd>
              ))}
            </dl>
            <div className="box-aircraft">
              <dl id="airlineSearchFilter">
                <dt>항공사</dt>
                {uniqueList.airlines.slice(0, sliceAirlineMenu).map((item, index) => (
                  <dd key={item.airline + index}>
                    <label className="form-chkbox">
                      <input
                        type="checkbox"
                        name="airlines"
                        defaultValue={item.airline}
                        checked={filterData.airlines.includes(item.airline)}
                        onChange={handleChangeCondition}
                      />
                      <span>{item.name}</span>
                    </label>
                  </dd>
                ))}
              </dl>
              {sliceAirlineMenu !== uniqueList.airlines.length && (
                <button id="airlineButton" type="button" className="btn-view-all" onClick={() => setSliceAirlineMenu(uniqueList.airlines.length)}>
                  모든 항공사 보기
                </button>
              )}
            </div>
            <dl id="departReturnTimeArea">
              <dt>시간대</dt>
              <dd className="type-time">
                <label>
                  <input
                    type="radio"
                    name="timeOption"
                    defaultValue="startTime"
                    checked={rangeTime === "startTime"}
                    onChange={() => setRangeTime("startTime")}
                  />
                  <span className="txt">출발 시간</span>
                </label>
                <label>
                  <input
                    type="radio"
                    name="timeOption"
                    defaultValue="arrivalTime"
                    checked={rangeTime === "arrivalTime"}
                    onChange={() => setRangeTime("arrivalTime")}
                  />
                  <span className="txt">도착 시간</span>
                </label>
              </dd>
              {filterData[keyRangeTime]?.map((item, index) => (
                <dd key={item?.name + index} className="set-time !pb-0" id={`departReturnTime_${index}`}>
                  <div className="tit">
                    {item?.name} {keyRangeTime === "departureTime" ? "출발" : "도착"}
                  </div>
                  <p className="val">
                    {DateUtil.getDayOfWeekName(params.departureDays[index])} <span className="start">{formatTime(item?.value[0])}</span> -{" "}
                    <span className="end">{formatTime(item?.value[1])}</span>
                  </p>
                  <div className="w-[97%] my-0 mx-auto">
                    <MIOSSlider
                      valueLabelDisplay="off"
                      disableSwap
                      value={item?.value}
                      max={24}
                      step={0.5}
                      onChange={(e, value) => handleChangeSlider(item.name, value)}
                    />
                  </div>
                  <input type="hidden" defaultValue="0000" /> <input type="hidden" defaultValue={2400} />
                </dd>
              ))}
            </dl>
            <dl id="leadTime">
              {filterData.leadTime?.value && (
                <>
                  <dt>소요시간</dt>
                  <dd className="set-time !pb-0">
                    <div className="tit">
                      {params.departureAirportNames?.[0]}({params.departureAirportCodes?.[0]}) 출발
                    </div>
                    <p className="val">
                      <span className="start">{convertDecimalHoursToKoreanTime(filterData.leadTime?.value[0])}</span> -{" "}
                      <span className="end">{convertDecimalHoursToKoreanTime(filterData.leadTime?.value[1])}</span>
                    </p>
                    <div className="w-[97%] my-0 mx-auto">
                      <MIOSSlider
                        valueLabelDisplay="off"
                        disableSwap
                        min={0.5}
                        value={filterData.leadTime?.value}
                        step={0.5}
                        onChange={(e, value) => {
                          handleChangeSliderDuration("leadTime", value);
                        }}
                      />
                    </div>
                    <input type="hidden" id="leadStartTime" />
                    <input type="hidden" id="leadEndTime" />
                  </dd>
                </>
              )}
            </dl>
            <dl id="stopWaitTime">
              {filterData.stopOverTime?.value && (
                <>
                  <dt>대기시간</dt>
                  <dd className="set-time !pb-0">
                    <p className="val">
                      <span className="start">{convertDecimalHoursToKoreanTime(filterData.stopOverTime?.value[0])}</span> -{" "}
                      <span className="end">{convertDecimalHoursToKoreanTime(filterData.stopOverTime?.value[1])}</span>
                    </p>
                    <div className="w-[97%] my-0 mx-auto">
                      <MIOSSlider
                        valueLabelDisplay="off"
                        disableSwap
                        value={filterData.stopOverTime?.value}
                        max={56}
                        step={0.5}
                        onChange={(e, value) => {
                          handleChangeSliderDuration("stopOverTime", value);
                        }}
                      />
                    </div>
                    <input type="hidden" id="stopWaitStartTime" />
                    <input type="hidden" id="stopWaitEndTime" />
                  </dd>
                </>
              )}
            </dl>
            <div className="box-btn">
              <button type="button" className="btns-cmm round-basic color-b w-75" onClick={handleApplyFilter}>
                적용
              </button>
            </div>
            <div className="btn-reset">
              <button type="reset" className="btns-cmm" onClick={handleResetFilter}>
                초기화
              </button>
            </div>
          </form>
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={handleResetFilter}>
            닫기
          </button>
        </div>
      </div>
    </>
  );
}

export default FilterAirSearch;
