import searchImages from "@/assets/mobile/images/search";
import { useState } from "react";

const seatTypes = [
  {
    className: "box-grade normal",
    value: "M",
    name: "departSeatType",
    images: {
      on: searchImages["ico_seat_01_on.png"],
      off: searchImages["ico_seat_01_off.png"],
    },
    text: "일반석",
  },
  {
    className: "box-grade normal-pr",
    value: "W",
    name: "departSeatType",
    images: {
      on: searchImages["ico_seat_02_on.png"],
      off: searchImages["ico_seat_02_off.png"],
    },
    text: "프리미엄 일반석",
  },
  {
    className: "box-grade bussiness",
    value: "C",
    name: "departSeatType",
    images: {
      on: searchImages["ico_seat_03_on.png"],
      off: searchImages["ico_seat_03_off.png"],
    },
    text: "비즈니스",
  },
  {
    className: "box-grade first",
    value: "F",
    name: "departSeatType",
    images: {
      on: searchImages["ico_seat_04_on.png"],
      off: searchImages["ico_seat_04_off.png"],
    },
    text: "일등석",
  },
];

function SeatClass(props) {
  const { open, onClose, options, onApply, onReset } = props;
  const [adultCount, setAdultCount] = useState(options.adultCount);
  const [departSeatType, setDepartSeatType] = useState({ value: options.departSeatTypeCode, text: options.departSeatTypeText });
  const handleAdultCountMinus = () => {
    if (adultCount === 1) return;
    setAdultCount(adultCount - 1);
  };
  const handleAdultCountPlus = () => {
    if (adultCount === 9) return;
    setAdultCount(adultCount + 1);
  };

  return (
    <div className="layer-pages saet-class" style={{ display: open ? "block" : "none" }}>
      <div className="layer-head">
        <p className="tit">인원/좌석등급</p>
      </div>
      <div className="layer-cotn">
        <div className="member">
          <p className="tit">인원</p>
          <div className="clearfix">
            <dl>
              <dt>성인</dt>
              <dd>최대 예약 인원수는 9명입니다.</dd>
            </dl>
            <div className="box-calc">
              <button type="button" className="btn-calc minus" id="adultCountMinus" onClick={handleAdultCountMinus} disabled={adultCount === 1} />
              <input type="number" id="adultCount" value={adultCount} readOnly />
              <button type="button" className="btn-calc plus" id="adultCountPlus" onClick={handleAdultCountPlus} disabled={adultCount === 9} />
              <input type="hidden" name="adultCount" defaultValue={1} />
            </div>
          </div>
        </div>
        <div className="seat">
          <p className="tit">좌석등급</p>
          <div className="sel">
            {seatTypes.map((seat, index) => (
              <label key={index} className={seat.className} onClick={() => setDepartSeatType({ value: seat.value, text: seat.text })}>
                <input type="radio" defaultValue={seat.value} name={seat.name} defaultChecked={seat.value === departSeatType.value} />
                <span className="img">
                  <img src={seat.images.on} alt="이미지" className="on" />
                  <img src={seat.images.off} alt="이미지" className="off" />
                </span>
                <span className="txt">{seat.text}</span>
              </label>
            ))}
          </div>
        </div>
        <div className="box-btn">
          <button
            type="button"
            className="btn-confirm btns-cmm round-basic color-b w-75 layer-pages-close"
            onClick={() => {
              onApply({ adultCount, departSeatTypeCode: departSeatType.value, departSeatTypeText: departSeatType.text });
              onClose();
            }}
          >
            확인
          </button>
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button
          type="button"
          className="btns-cmm"
          onClick={() => {
            setDepartSeatType({ value: seatTypes[0].value, text: seatTypes[0].text });
            setAdultCount(1);
            onReset();
            onClose();
          }}
        >
          닫기
        </button>
      </div>
    </div>
  );
}

export default SeatClass;
