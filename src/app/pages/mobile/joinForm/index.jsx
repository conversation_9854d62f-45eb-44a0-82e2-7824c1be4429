import { useEffect, useRef, useState } from "react";
import MJoinAgreement from "../MJoinAgreement";
import useGeneralVersion from "@/app/hooks/useGeneralVersion";
import { getCompanyByDomain, getCompanyBySitecode } from "@/service/login";
import { getCompanyList, getDepartmentList, getPositionList, getWorkspaceList } from "@/service/company";
import { MAP_PASSWORD_CHECK_RESULT } from "@/constants/app";
import { validPassword } from "@/utils/common";

import { duplicateCheckLoginId } from "@/service/join";
import { confirmVerificationCode } from "@/service/common";
import { registerComplete } from "@/service/user";
import MRegisterSuccessModal from "@/app/components/user_v2/modal/MRegisterSuccess.modal";

import "@/styles/mobile/css/reset.css";
import "@/styles/mobile/css/common.css";
import "@/styles/mobile/css/search.css";
import "@/styles/mobile/css/login.css";

const FIVE_MINUTES = 300;

export default function JoinForm() {
  const { isGeneralVersion } = useGeneralVersion();

  const [openJoinAgreement, setOpenJoinAgreement] = useState(true);
  const [checkAll, setCheckAll] = useState({
    isAgreeService: false,
    isAgreePrivacy: false,
    isAgreePrivacyId: false,
    isAgreePrivacyProvide: false,
    isAgreePrivacyMarketing: false,
  });

  const [formState, setFormState] = useState({
    companySitecode: "",
    email: "",
    verificationCode: "",
    name: "",
    password: "",
    passwordCheck: "",
    gender: "Male",
    birthY: new Date().getFullYear(),
    birthM: 1,
    birthD: 1,
    cellPhoneNumber: "",
    workspaceId: "",
    departmentId: "",
    positionId: "",
    phoneNumber: "",
    employeeNo: "",
    companyId: "",
  });

  const [company, setCompany] = useState({});
  const [companyList, setCompanyList] = useState([]);
  const [workspaceList, setWorkspaceList] = useState([]);
  const [positionList, setPositionList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);

  const [isPasswordMatch, setIsPasswordMatch] = useState(true);
  const [passwordCheckResult, setPasswordCheckResult] = useState(MAP_PASSWORD_CHECK_RESULT.DEFAULT);
  const [isVerifyCodeSuccess, setIsVerifyCodeSuccess] = useState(false);
  const [isValidEmail, setIsValidEmail] = useState(true);
  const [errorEmailMessage, setErrorEmailMessage] = useState("");
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [isShowEmailAuthBtn, setIsShowEmailAuthBtn] = useState(true);
  const [countdown, setCountdown] = useState(FIVE_MINUTES); // 5 minutes
  const [verificationCodeMessage, setVerificationCodeMessage] = useState("");
  const [isVerifyCodeFailed, setIsVerifyCodeFailed] = useState(false);
  const [emailConfirmBtnText, setEmailConfirmBtnText] = useState("인증확인");
  const [departmentStructure, setDepartmentStructure] = useState([]);
  const [isRegisterSuccess, setIsRegisterSuccess] = useState(false);

  const formRef = useRef(null);

  const confirmPassword = async () => {
    if (formState.password !== formState.passwordCheck) {
      setIsPasswordMatch(false);
    } else {
      setIsPasswordMatch(true);
      const res = validPassword(formState.password);
      setPasswordCheckResult(MAP_PASSWORD_CHECK_RESULT[res]);
      if (res == "OK") {
        setIsPasswordMatch(true);
      } else {
        setIsPasswordMatch(false);
      }
    }
  };

  const checkCompanySitecode = async () => {
    const res = await getCompanyBySitecode(formState.companySitecode);

    return res.data;
  };

  const isValidEmailCheck = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const duplicateCheckEmail = async () => {
    try {
      setIsVerifyCodeSuccess(false);
      let emailDomainList = [];
      if (company?.isGroup) {
        companyList.map((item) => {
          emailDomainList.push(...item.emailDomains);
        });
      } else {
        emailDomainList = company.emailDomains;
      }

      if (formState.email.trim() == "") {
        setIsValidEmail(false);
        setErrorEmailMessage("이메일 주소를 입력해주세요.");
        return;
      }

      if (!isValidEmailCheck(formState.email)) {
        setIsValidEmail(false);
        setErrorEmailMessage("이메일 형식이 올바르지 않습니다.");
        return;
      }

      if (isGeneralVersion) {
        const res = await checkCompanySitecode();
        if (res.emailDomains.includes(formState.email.split("@")[1])) {
          await duplicateCheckLoginId({
            email: formState.email,
          });
          setCompany(res);
          setFormState({ ...formState, companyId: res.id });
        } else {
          alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
          formRef.current.elements.email.focus();
          return;
        }
      } else {
        if (emailDomainList?.includes(formState.email.split("@")[1])) {
          await duplicateCheckLoginId({
            email: formState.email,
          });
        } else {
          alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
          formRef.current.elements.email.focus();
          return;
        }
      }
      setIsValidEmail(true);
      setIsCountingDown(true);
      setIsShowEmailAuthBtn(false);
    } catch (error) {
      if (error.response.status == 400) {
        setIsValidEmail(false);
        setIsCountingDown(false);
        setIsShowEmailAuthBtn(true);
        setErrorEmailMessage("이미 가입된 회원입니다.");
      } else {
        alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
      }
    }
  };

  const handleConfirmVerificationCode = async () => {
    try {
      const res = await confirmVerificationCode({ email: formState.email, verificationCode: formState.verificationCode });
      setEmailConfirmBtnText("인증확인");
      setFormState({ ...formState, emailVerified: formState.email, emailConfirm: "Y" });
      setIsShowEmailAuthBtn(false);
      setIsVerifyCodeSuccess(res.status === 200);
      setIsVerifyCodeFailed(res.status !== 200);
      formRef.current.elements.email.readOnly = true;
    } catch (error) {
      if (error.response.data.error_code === "E400_INVALID_OTP") {
        setEmailConfirmBtnText("다시 인증하기");
        setFormState({ ...formState, emailConfirm: "N" });
        setVerificationCodeMessage("인증번호가 올바르지 않습니다. 다시 진행하여 주십시요.");
      } else if (error.response.data.error_code === "E400_OTP_EXPIRED") {
        setEmailConfirmBtnText("다시 인증하기");
        setFormState({ ...formState, emailConfirm: "N" });
        setVerificationCodeMessage("인증시간이 만료 되었습니다. 다시 진행하여 주십시요.");
      }
      setIsVerifyCodeSuccess(false);
      setIsVerifyCodeFailed(true);
    }
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
  };

  const joinValidCheck = () => {
    const cellRegExp = /^01([0|1|6|7|8|9])([1-9])([0-9]{2,3})([0-9]{4})$/;
    const phoneRegExp = /^0(2|3[1-3]|4[1-4]|5[1-5]|6[1-4])([0-9]{3,4})([0-9]{4})$/;

    if (formState.email.trim() == "") {
      alert("이메일 아이디를 입력해주세요.");
      formRef.current.elements.email.focus();
      return false;
    } else if (formState.emailConfirm != "Y") {
      alert("이메일 아이디 인증하기를 해주세요.");
      formRef.current.elements.verificationCode.focus();
      return false;
    } else if (formState.name.trim() == "") {
      alert("이름을 입력해주세요.");
      formRef.current.elements.name.focus();
      return false;
    } else if (formState.password.trim() == "") {
      alert("비밀번호를 입력해주세요.");
      formRef.current.elements.password.focus();
      return false;
    } else if (formState.passwordCheck.trim() == "") {
      alert("비밀번호 확인을 입력해주세요.");
      formRef.current.elements.passwordCheck.focus();
      return false;
    } else if (formState.birthY.toString().trim() == "") {
      alert("생년월일 년을 선택해주세요.");
      formRef.current.elements.birthY.focus();
      return false;
    } else if (formState.birthM.toString().trim() == "") {
      alert("생년월일 월을 선택해주세요.");
      formRef.current.elements.birthM.focus();
      return false;
    } else if (formState.birthD.toString().trim() == "") {
      alert("생년월일 일을 선택해주세요.");
      formRef.current.elements.birthD.focus();
      return false;
    } else if (formState.cellPhoneNumber.trim() == "") {
      alert("휴대전화를 입력해주세요.");
      formRef.current.elements.cellPhoneNumber.focus();
      return false;
    } else if (formState.cellPhoneNumber.trim() != "" && !cellRegExp.test(formState.cellPhoneNumber.trim())) {
      alert("휴대전화가 올바르지 않습니다.");
      formRef.current.elements.cellPhoneNumber.focus();
      return false;
    } else if (formState.phoneNumber.trim() != "" && !phoneRegExp.test(formState.phoneNumber)) {
      alert("전화번호가 올바르지 않습니다.");
      formRef.current.elements.phoneNumber.focus();
      return false;
    } else if (
      !isGeneralVersion &&
      formState.workspaceId == "" &&
      !["sksiltron.tourvis.com"].includes(window.location.hostname) &&
      company?.isGroup == false
    ) {
      alert("사업장을 선택해주세요.");
      return false;
    } else if (
      !isGeneralVersion &&
      formState.departmentId == "" &&
      !["sksiltron.tourvis.com"].includes(window.location.hostname) &&
      company?.isGroup == false
    ) {
      alert("소속부서를 선택해주세요.");
      return false;
    } else if (formState.companyId?.toString().trim() == "" && company?.isGroup == true) {
      alert("회사 를 선택해주세요.");
      return false;
    } else {
      return true;
    }
  };

  const handleRegister = async () => {
    try {
      if (!joinValidCheck()) return;

      const data = {
        email: formState.email,
        verificationCode: formState.verificationCode,
        password: formState.password,
        name: formState.name,
        gender: formState.gender,
        birthday: `${formState.birthY}${formState.birthM < 10 ? `0${formState.birthM}` : formState.birthM}${formState.birthD < 10 ? `0${formState.birthD}` : formState.birthD}`,
        cellPhoneNumber: formState.cellPhoneNumber,
        companyId: company?.isGroup ? +formState.companyId : company.id,
        workspaceId: +formState.workspaceId || null,
        departmentId: departmentStructure.length > 0 ? +departmentStructure[departmentStructure.length - 1].id : "",
        positionId: formState.positionId || positionList.find((i) => i.name === "사원")?.id,
        employeeNo: formState.employeeNo || null,
        phoneNumber: formState.phoneNumber || null,
        isAgreePrivacyMarketing: checkAll.isAgreePrivacyMarketing,
      };

      await registerComplete(data);
      setIsRegisterSuccess(true);
    } catch (error) {
      setIsRegisterSuccess(false);
      alert(
        "회원가입 중 오류가 발생하였습니다.\n다시 시도해 주세요.\n지속적으로 시스템 오류가 발생하면 <EMAIL> 해당 메일로 접수해주시기 바랍니다.",
      );
    }
  };

  const handleChangeWorkspace = async (e) => {
    setFormState({ ...formState, workspaceId: e.target.value });
    setDepartmentStructure([]);
    if (e.target.value !== "-1") {
      const res = await getDepartmentList(e.target.value);
      setDepartmentList(res.data);
    } else {
      setDepartmentList([]);
    }
  };

  const handleChangeDepartment = async (value, level) => {
    setFormState({ ...formState, departmentId: value });

    if (!value) {
      setDepartmentStructure([]);
      return;
    } else {
      const res = await getDepartmentList(formState.workspaceId, value);

      setDepartmentStructure((prev) => {
        const newStructure = [...prev];
        newStructure.splice(level);
        newStructure[level] = {
          id: value,
          children: res.data,
        };
        return newStructure;
      });
    }
  };

  useEffect(() => {
    if (isGeneralVersion) {
      return;
    } else {
      const data = {
        domain: window.location.hostname == "localhost" ? "hnbtms.tourvis.com" : window.location.hostname,
      };
      const fetchData = async () => {
        try {
          const res = await getCompanyByDomain(data);
          setCompany(res.data);
          const [resCompanyList, resPositionList, resWorkspaceList] = await Promise.all([
            getCompanyList(res.data.id),
            getPositionList(res.data.id),
            getWorkspaceList(res.data.id),
          ]);
          setCompanyList(resCompanyList.data);
          setPositionList(resPositionList.data);
          setWorkspaceList(resWorkspaceList.data);
        } catch (error) {
          console.log(error);
        }
      };
      fetchData();
    }
  }, [isGeneralVersion]);

  useEffect(() => {
    let timer;
    if (isCountingDown && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      setIsCountingDown(false);
      setIsShowEmailAuthBtn(true);
      setCountdown(FIVE_MINUTES);
    }
    return () => clearInterval(timer);
  }, [isCountingDown, countdown]);

  return (
    <div id="wrap" className="head-fixed">
      <header className="page-header type-black">
        <h1 className="pg-title">회원가입</h1>
        <a href="/login" className="btn-prev-page" />
      </header>
      <form name="joinForm" ref={formRef}>
        <div id="contaier" className="pg-login">
          <div className="contents member-join-cotn">
            <div className="box-info default">
              <h2 className="title">기본 정보</h2>
              {isGeneralVersion && (
                <>
                  <p className="form-tit ">
                    거래처코드<span>*</span>
                  </p>
                  <div className="box-certify">
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="text"
                          id="companySitecode"
                          name="companySitecode"
                          maxLength={100}
                          autoComplete="off"
                          value={formState.companySitecode}
                          onChange={(e) => setFormState({ ...formState, companySitecode: e.target.value })}
                        />
                      </span>
                    </div>
                  </div>
                </>
              )}
              <p className="form-tit ">
                이메일 아이디<span>*</span>
              </p>
              <div className="box-certify">
                <div className="form-cn">
                  <span className="input-cmm">
                    <input
                      type="text"
                      id="email"
                      name="email"
                      maxLength={100}
                      autoComplete="off"
                      value={formState.email}
                      onChange={(e) => setFormState({ ...formState, email: e.target.value })}
                      readOnly={isVerifyCodeSuccess}
                    />
                  </span>
                  <span id="idMessage" className="validate" style={{ display: !isValidEmail ? "block" : "none" }}>
                    {errorEmailMessage}
                  </span>
                  <span id="retryEmailTime" className="return-time" style={{ display: isShowEmailAuthBtn || isVerifyCodeSuccess ? "none" : "block" }}>
                    재발송 {formatTime(countdown)}
                  </span>
                </div>
                <button
                  type="button"
                  id="emailAuthBtn"
                  style={{ display: isShowEmailAuthBtn ? "block" : "none" }}
                  className="btns-cmm round-basic color-w w-50"
                  onClick={duplicateCheckEmail}
                >
                  인증 확인
                </button>
                <button
                  type="button"
                  style={{ display: isShowEmailAuthBtn || isVerifyCodeSuccess ? "none" : "block" }}
                  id="resendEmailAuthBtn"
                  className="btns-cmm round-basic color-w w-50"
                  onClick={() => {
                    if (!isCountingDown) {
                      duplicateCheckEmail();
                    }
                  }}
                >
                  인증메일 재발송
                </button>
              </div>
              <p className="form-tit ">
                인증번호<span>*</span>
              </p>
              <div className="box-certify">
                <div className="form-cn">
                  <span className="input-cmm">
                    <input
                      type="num"
                      id="verificationCode"
                      className={`${isVerifyCodeSuccess && "val"}`}
                      value={formState.verificationCode}
                      onChange={(e) => {
                        setFormState({ ...formState, verificationCode: e.target.value });
                        setIsVerifyCodeFailed(false);
                      }}
                      disabled={isVerifyCodeSuccess}
                    />
                  </span>
                  <span id="confirmMessage" className="validate" style={{ display: isVerifyCodeFailed ? "block" : "none", fontSize: 12 }}>
                    {verificationCodeMessage}
                  </span>
                </div>
                <button
                  type="button"
                  id="emailConfirmBtn"
                  className="btns-cmm round-basic color-w w-50"
                  style={{
                    display: isVerifyCodeSuccess ? "none" : "block",
                  }}
                  onClick={handleConfirmVerificationCode}
                >
                  {emailConfirmBtnText}
                </button>
                <button type="button" id="emailCompleteBtn" className="btns-cmm round-basic color-w w-50" disabled="" style={{ display: "none" }}>
                  인증 완료
                </button>
              </div>
              <p className="form-tit ">
                이름<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    id="name"
                    name="name"
                    maxLength={30}
                    value={formState.name}
                    onChange={(e) => setFormState({ ...formState, name: e.target.value })}
                  />
                </span>
              </div>
              <p className="form-tit ">
                비밀번호<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="password"
                    id="password"
                    name="password"
                    maxLength={20}
                    autoComplete="off"
                    value={formState.password}
                    onChange={(e) => {
                      setFormState({ ...formState, password: e.target.value });
                      setIsPasswordMatch(true);
                    }}
                  />
                </span>
              </div>
              <p className="form-tit ">
                비밀번호 확인<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="password"
                    id="passwordCheck"
                    name="passwordCheck"
                    maxLength={20}
                    autoComplete="off"
                    value={formState.passwordCheck}
                    onChange={(e) => {
                      setFormState({ ...formState, passwordCheck: e.target.value });
                      setIsPasswordMatch(true);
                    }}
                    onBlur={confirmPassword}
                  />
                </span>
                <span id="passwordConfirmMsg" className="validate" style={{ display: isPasswordMatch ? "none" : "block" }}>
                  {passwordCheckResult}
                </span>
              </div>
              <p className="form-tit ">
                성별<span>*</span>
              </p>
              <div className="form-cn half-col">
                <label className="item-col form-radio">
                  <input
                    type="radio"
                    id="genderM"
                    name="gender"
                    defaultValue="Male"
                    checked={formState.gender === "Male"}
                    onChange={(e) => setFormState({ ...formState, gender: e.target.value })}
                  />
                  <span>남성</span>
                </label>
                <label className="item-col form-radio">
                  <input
                    type="radio"
                    id="genderF"
                    name="gender"
                    defaultValue="Female"
                    checked={formState.gender === "Female"}
                    onChange={(e) => setFormState({ ...formState, gender: e.target.value })}
                  />
                  <span>여성</span>
                </label>
              </div>
              <p className="form-tit ">
                생년월일<span>*</span>
              </p>
              <div className="form-cn cellphone">
                <span className="select-cmm yy" style={{ float: "left", width: 100 }}>
                  <select id="birthY" name="birthY" value={formState.birthY} onChange={(e) => setFormState({ ...formState, birthY: e.target.value })}>
                    <option value="">생년</option>
                    {Array.from({ length: 100 }, (_, i) => (
                      <option key={i} value={new Date().getFullYear() - i}>
                        {new Date().getFullYear() - i}
                      </option>
                    ))}
                  </select>
                </span>
                <div
                  className="box-right"
                  style={{
                    position: "relative",
                    float: "right",
                    width: "calc( 100% - 100px)",
                  }}
                >
                  <span className="select-cmm mm" style={{ float: "left", width: "50%" }}>
                    <select
                      id="birthM"
                      name="birthM"
                      value={formState.birthM}
                      onChange={(e) => setFormState({ ...formState, birthM: e.target.value })}
                      style={{ textAlign: "center" }}
                    >
                      {Array.from({ length: 12 }, (_, i) => (
                        <option key={i} value={i + 1}>
                          {i + 1}월
                        </option>
                      ))}
                    </select>
                  </span>
                  <span className="select-cmm dd" style={{ float: "left", width: "50%" }}>
                    <select
                      id="birthD"
                      name="birthD"
                      value={formState.birthD}
                      onChange={(e) => setFormState({ ...formState, birthD: e.target.value })}
                      style={{ textAlign: "center" }}
                    >
                      +
                      {Array.from({ length: 31 }, (_, i) => (
                        <option key={i} value={i + 1}>
                          {i + 1}일
                        </option>
                      ))}
                    </select>
                  </span>
                </div>
              </div>
              <p className="form-tit ">
                휴대전화<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input
                    type="text"
                    id="cellPhoneNumber"
                    name="cellPhoneNumber"
                    maxLength={11}
                    value={formState.cellPhoneNumber}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      setFormState({ ...formState, cellPhoneNumber: value });
                    }}
                    placeholder="예.01012345678"
                  />
                </span>
                <span className="validate" style={{ display: "none" }}>
                  숫자만 입력 하세요.
                </span>
              </div>
            </div>

            <div className="box-info company">
              <h2 className="title">회사 정보</h2>
              <p className="form-tit ">회사명{company.isGroup && <span>*</span>}</p>
              {company?.isGroup ? (
                <div className="form-cn sel-multi-row">
                  <span className="select-cmm">
                    <select
                      id="setCompanyId"
                      name="companyId"
                      value={formState.companyId}
                      onChange={(e) => setFormState({ ...formState, companyId: e.target.value })}
                    >
                      <option value="">선택하세요.</option>
                      {companyList.map((i) => {
                        return (
                          <option key={i.id} value={i.id}>
                            {i.name}
                          </option>
                        );
                      })}
                    </select>
                  </span>
                </div>
              ) : (
                <div className="form-cn">
                  <span className="input-cmm">
                    <input type="text" readOnly value={company.name} />
                  </span>
                </div>
              )}

              {!isGeneralVersion && company?.btmsSetting?.url != "sksiltron.tourvis.com" && !company?.isGroup && (
                <>
                  <p className="form-tit">
                    사업장<span>*</span>
                  </p>
                  <div className="form-cn sel-multi-row">
                    <span className="select-cmm">
                      <select id="setWorkspaceId" name="workspaceId" value={formState.workspaceId} onChange={handleChangeWorkspace}>
                        <option value={-1}>선택하세요.</option>
                        {workspaceList.map((i) => {
                          return (
                            !(i.id == 946626) && (
                              <option key={i.id} value={i.id}>
                                {i.name}
                              </option>
                            )
                          );
                        })}
                      </select>
                    </span>
                  </div>

                  <p className="form-tit ">
                    소속부서<span>*</span>
                  </p>
                  <div id="departmentSelectBoxArea" className="form-cn sel-multi-row">
                    <span className="select-cmm">
                      <select id="setDepartmentId" name="departmentId" onChange={(e) => handleChangeDepartment(e.target.value, 0)}>
                        <option value="">선택하세요.</option>
                        {departmentList.map((item) => (
                          <option key={item.id} value={item.id}>
                            {item.name}
                          </option>
                        ))}
                      </select>
                    </span>
                  </div>

                  {departmentStructure.map(
                    (dept, index) =>
                      dept.children &&
                      dept.children.length > 0 && (
                        <div className="box-col form-cn" key={dept.id}>
                          <span className="select-cmm">
                            <select onChange={(e) => handleChangeDepartment(e.target.value, index + 1)}>
                              <option value="">선택하세요.</option>
                              {dept.children.map((item) => (
                                <option key={item.id} value={item.id}>
                                  {item.name}
                                </option>
                              ))}
                            </select>
                          </span>
                        </div>
                      ),
                  )}

                  <p className="form-tit ">
                    직급<span>*</span>
                  </p>
                  <div className="form-cn sel-multi-row">
                    <span className="select-cmm">
                      <select
                        id="positionId"
                        name="positionId"
                        value={formState.positionId}
                        onChange={(e) => setFormState({ ...formState, positionId: e.target.value })}
                      >
                        {positionList.map((i) => {
                          return (
                            <option key={i.id} value={i.id}>
                              {i.name}
                            </option>
                          );
                        })}
                      </select>
                    </span>
                  </div>
                </>
              )}
              <>
                <p className="form-tit ">사번</p>
                <div className="form-cn">
                  <span className="input-cmm">
                    <input
                      type="text"
                      placeholder="예.TS0001"
                      id="employeeNo"
                      name="employeeNo"
                      maxLength={20}
                      value={formState.employeeNo}
                      onChange={(e) => setFormState({ ...formState, employeeNo: e.target.value })}
                    />
                  </span>
                </div>
                <p className="form-tit ">회사전화</p>
                <div className="form-cn">
                  <span className="input-cmm">
                    <input
                      type="text"
                      id="phoneNumber"
                      name="phoneNumber"
                      maxLength={11}
                      defaultValue=""
                      placeholder="예.0212345678"
                      value={formState.phoneNumber}
                      onChange={(e) => {
                        const value = e.target.value.replace(/[^0-9]/g, "");
                        setFormState({ ...formState, phoneNumber: value });
                      }}
                    />
                  </span>
                  <span className="validate" style={{ display: "none" }}>
                    숫자만 입력 하세요.
                  </span>
                </div>
              </>
            </div>
            <div className="box-btn">
              <button type="button" className="btns-cmm round-basic color-b w-75" id="joinProcessBtn" onClick={handleRegister}>
                확인
              </button>
            </div>
          </div>
        </div>
      </form>

      <MJoinAgreement open={openJoinAgreement} setOpen={setOpenJoinAgreement} checkAll={checkAll} setCheckAll={setCheckAll} />
      <MRegisterSuccessModal open={isRegisterSuccess} onClose={() => setIsRegisterSuccess(false)} />
    </div>
  );
}
