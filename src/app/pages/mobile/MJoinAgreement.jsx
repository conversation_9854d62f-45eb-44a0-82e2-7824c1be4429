export default function MJoinAgreement({ open, setOpen, checkAll, setCheckAll }) {
  const handleCheckAll = () => {
    Object.values(checkAll).every((v) => v)
      ? setCheckAll({
          isAgreeService: false,
          isAgreePrivacy: false,
          isAgreePrivacyId: false,
          isAgreePrivacyProvide: false,
          isAgreePrivacyMarketing: false,
        })
      : setCheckAll({
          isAgreeService: true,
          isAgreePrivacy: true,
          isAgreePrivacyId: true,
          isAgreePrivacyProvide: true,
          isAgreePrivacyMarketing: true,
        });
  };

  const validate = () => {
    if (!checkAll.isAgreeService) {
      alert("BTMS계정 이용 약관에 동의하지 않으셨습니다.\n다시 확인해 주세요!");
      return false;
    }
    if (!checkAll.isAgreePrivacy) {
      alert("개인정보 수집 및 이용에 대한 안내에\n동의하지 않으셨습니다. 다시 확인해 주세요!");
      return false;
    }
    if (!checkAll.isAgreePrivacyId) {
      alert("고유식별정보 처리에 대한 안내에\n동의하지 않으셨습니다. 다시 확인해 주세요!");
      return false;
    }
    if (!checkAll.isAgreePrivacyProvide) {
      alert("개인정보 제3자 제공 안내에 동의하지 않으셨습니다.\n다시 확인해 주세요!");
      return false;
    }

    return true;
  };

  const handleSubmit = () => {
    if (!validate()) return;

    setOpen(false);
  };

  return (
    <form id="agreementForm" name="agreementForm" style={{ display: open ? "block" : "none" }}>
      <div className="layer-pages policy-agree" style={{ display: "block" }}>
        <div className="layer-cotn">
          <p className="title">
            BTMS계정 <br />
            서비스약관에 동의해주세요.
          </p>
          <div className="box-agree">
            <div className="all-agree">
              <label className="form-chkbox">
                <input type="checkbox" id="agreeCheckAll" onChange={handleCheckAll} checked={Object.values(checkAll).every((v) => v)} />
                <span>모두 동의합니다.</span>
              </label>
              <p className="desc">
                전체동의는 필수 및 선택정보에 대한 동의도 포함되어 있으며, 개별적으로도 동의를 선택하실 수 있습니다. 선택항목에 대한 동의를 거부하시는
                경우에도 서비스는 이용이 가능합니다.
              </p>
            </div>
            <div className="box-cell active">
              <dl>
                <dt>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isAgreeService"
                      name="isAgreeService"
                      checked={checkAll.isAgreeService}
                      onChange={() => {
                        setCheckAll((prev) => ({
                          ...prev,
                          isAgreeService: !prev.isAgreeService,
                        }));
                      }}
                    />
                    <span>[필수] BTMS계정 약관</span>
                  </label>
                </dt>
              </dl>
            </div>
            <div className="box-cell">
              <dl>
                <dt>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isAgreePrivacy"
                      name="isAgreePrivacy"
                      checked={checkAll.isAgreePrivacy}
                      onChange={() => {
                        setCheckAll((prev) => ({
                          ...prev,
                          isAgreePrivacy: !prev.isAgreePrivacy,
                        }));
                      }}
                    />
                    <span>[필수] 개인정보 수집 및 이용 동의</span>
                  </label>
                </dt>
              </dl>
            </div>
            <div className="box-cell">
              <dl>
                <dt>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isAgreePrivacyId"
                      name="isAgreePrivacyId"
                      checked={checkAll.isAgreePrivacyId}
                      onChange={() => {
                        setCheckAll((prev) => ({
                          ...prev,
                          isAgreePrivacyId: !prev.isAgreePrivacyId,
                        }));
                      }}
                    />
                    <span>[필수] 고유식별정보 처리에 대한 안내</span>
                  </label>
                </dt>
              </dl>
            </div>
            <div className="box-cell">
              <dl>
                <dt>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isAgreePrivacyProvide"
                      name="isAgreePrivacyProvide"
                      checked={checkAll.isAgreePrivacyProvide}
                      onChange={() => {
                        setCheckAll((prev) => ({
                          ...prev,
                          isAgreePrivacyProvide: !prev.isAgreePrivacyProvide,
                        }));
                      }}
                    />
                    <span>[필수] 개인정보 제3자 제공 안내</span>
                  </label>
                </dt>
              </dl>
            </div>
            <div className="box-cell">
              <dl>
                <dt>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      id="isAgreePrivacyMarketing"
                      name="isAgreePrivacyMarketing"
                      checked={checkAll.isAgreePrivacyMarketing}
                      onChange={() => {
                        setCheckAll((prev) => ({
                          ...prev,
                          isAgreePrivacyMarketing: !prev.isAgreePrivacyMarketing,
                        }));
                      }}
                    />
                    <span>[선택] 마케팅 및 광고에 활용</span>
                  </label>
                </dt>
              </dl>
            </div>
          </div>
          <div className="box-btn">
            <button type="button" className="btns-cmm round-basic color-b w-75 layer-pages-close btn-regist" id="agreeBtn" onClick={handleSubmit}>
              동의
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
