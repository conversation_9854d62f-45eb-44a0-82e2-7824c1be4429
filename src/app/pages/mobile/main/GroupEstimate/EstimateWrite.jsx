import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { isValidationInput } from "@/utils/common";
import { appMobileLoading, requestWithMobileLoading } from "@/utils/app";
import { useNavigate } from "react-router-dom";
import { actionCodeList, selectReservationTravelView } from "@/store/travelViewSlice";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { AREAS, PURPOSES, SERVICE_TYPES } from "@/constants/estimateGroup";
import EstimateRegistPop from "@/app/pages/mobile/main/GroupEstimate/EstimateRegistPop";
import useDebounce from "@/app/hooks/useDebounce";
import qs from "qs";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const INIT_FORM_DATA = {
  serviceType: SERVICE_TYPES[0].key,
  purpose: PURPOSES[0].key,
  area: AREAS[0].key,
  areaDetail: "",
  departYmd: "",
  returnYmd: "",
  personCount: "",
  airlineName: "",
  airlineId: "",
  title: "",
  contents: "",
  addRequest: [],
};

function EstimateWrite(props) {
  const { onClose, onRetrieve } = props;
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { userInfo } = useAppSelector(selectUserInfo);
  const { codeList: addRequestList } = useAppSelector(selectReservationTravelView);
  const [formData, setFormData] = useState(INIT_FORM_DATA);
  const [isOpenRegistPop, setIsOpenRegistPop] = useState(false);
  const debouncedAirlineName = useDebounce(formData.airlineName, 500);
  const [airlines, setAirlines] = useState([]);
  const [isSelectAction, setIsSelectAction] = useState(false);

  const handleClose = () => {
    onClose();
    setFormData(INIT_FORM_DATA);
    setAirlines([]);
  };

  const handleSelectAirline = (selectedAirline) => {
    setFormData((prev) => ({ ...prev, airlineName: selectedAirline.name, airlineId: selectedAirline.id }));
    setAirlines([]);
    setIsSelectAction([true]);
  };

  const handleChangeForm = (event) => {
    const { name, value } = event.target;
    if (!isValidationInput(event)) return;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleChangeAddRequestCheckbox = (event) => {
    const { checked, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      addRequest: checked ? [...prev.addRequest, Number(value)] : prev.addRequest.filter((item) => item !== Number(value)),
    }));
  };

  const addProcess = () => {
    if (!isValidationForm()) {
      return;
    }
    const { addRequest, ...nest } = formData;
    const payload = { ...nest, creatorUserId: userInfo.id, companyId: userInfo.workspace?.company?.id };
    addRequest.forEach((item, index) => {
      payload[`addRequestDTOs[${index}].code`] = item;
    });
    requestWithMobileLoading({
      url: "/m/group/estimate/addProcess",
      data: qs.stringify(payload),
      method: "POST",
      success: () => setIsOpenRegistPop(true),
      fail: (error) => {
        if (error.response?.status === 401) {
          navigate("/loginExpired");
          return;
        }
        alert(`등록중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
      },
    });
  };

  const isValidationForm = () => {
    const { areaDetail, departYmd, returnYmd, personCount, airlineName, airlineId, title, contents } = formData;
    if (!areaDetail) {
      alert("상세지역을 입력하세요.");
      return false;
    }
    if (!departYmd.trim()) {
      alert("출발일을 입력하세요.");
      return false;
    }
    if (!returnYmd.trim()) {
      alert("도착일을 입력하세요.");
      return false;
    }
    if (!personCount.trim()) {
      alert("인원수를 입력하세요.");
      return false;
    }
    if (!airlineName.trim() || !airlineId) {
      alert("항공사를 입력하세요.");
      return false;
    }
    if (!title.trim()) {
      alert("제목을 입력하세요.");
      return false;
    }
    if (!contents.trim()) {
      alert("내용을 입력하세요.");
      return false;
    }
    return true;
  };

  useEffect(() => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    Promise.all([actionCodeList({ groupCode: "GROUP_ADD_REQUEST" })].map((action) => dispatch(action))).finally(() => appMobileLoading.off());
  }, [dispatch]);

  useEffect(() => {
    if (!debouncedAirlineName.trim() || isSelectAction) {
      return;
    }
    requestWithMobileLoading({
      url: "/common/airline/autoSearch",
      data: qs.stringify({ name: debouncedAirlineName }),
      method: "POST",
      success: (res) => setAirlines(res.data ?? []),
    });
  }, [debouncedAirlineName, isSelectAction]);

  return (
    <>
      <div className="layer-pages group-estimate estimate-write !block">
        <div className="layer-head">
          <p className="tit">법인/단체 견적 문의</p>
        </div>
        <div className="layer-cotn">
          <div className="sec-view sec-write">
            <div className="box-item">
              <p className="tit">회원 정보</p>
              <dl>
                <dt>회사명</dt>
                <dd>{userInfo.workspace?.company?.name}</dd>
                <dt>이름</dt>
                <dd>{userInfo.name}</dd>
                <dt>이메일 주소</dt>
                <dd>{userInfo.email}</dd>
                <dt>휴대전화</dt>
                <dd>{userInfo.cellPhoneNumber?.replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3")}</dd>
              </dl>
            </div>
            <form id="groupForm">
              <input type="hidden" id="userId" name="creatorUserId" value="{userInfo.id}" />
              <input type="hidden" id="companyId" name="companyId" value="{userInfo.workspace.company.id}" />

              <div className="box-item">
                <p className="tit">문의 내용</p>
                <dl>
                  <dt className="form-tit ">
                    서비스 구분 <span>*</span>
                  </dt>
                  <dd>
                    <div className="select-cmm">
                      <select name="serviceType" id="serviceType" value={formData.serviceType} onChange={handleChangeForm}>
                        {SERVICE_TYPES.map((item) => (
                          <option key={item.key} value={item.key}>
                            {item.value}
                          </option>
                        ))}
                      </select>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    목적 <span>*</span>
                  </dt>
                  <dd>
                    <div className="select-cmm">
                      <select name="purpose" id="purpose" value={formData.purpose} onChange={handleChangeForm}>
                        {PURPOSES.map((item) => (
                          <option key={item.key} value={item.key}>
                            {item.value}
                          </option>
                        ))}
                      </select>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    지역 <span>*</span>
                  </dt>
                  <dd>
                    <div className="select-cmm">
                      <select name="area" id="area" value={formData.area} onChange={handleChangeForm}>
                        {AREAS.map((item) => (
                          <option key={item.key} value={item.key}>
                            {item.value}
                          </option>
                        ))}
                      </select>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    상세 지역 <span>*</span>
                  </dt>
                  <dd>
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="text"
                          id="areaDetail"
                          name="areaDetail"
                          placeholder="상세 지역을 입력해주세요."
                          autoComplete="off"
                          value={formData.areaDetail}
                          onChange={handleChangeForm}
                        />
                      </span>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    출발일 <span>*</span>
                  </dt>
                  <dd>
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="num"
                          id="departYmd"
                          name="departYmd"
                          placeholder="예) 20200101"
                          data-validateinput="numberOnly"
                          maxLength="8"
                          autoComplete="off"
                          value={formData.departYmd}
                          onChange={handleChangeForm}
                        />
                      </span>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    도착일 <span>*</span>
                  </dt>
                  <dd>
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="num"
                          id="returnYmd"
                          name="returnYmd"
                          placeholder="예) 20200113"
                          data-validateinput="numberOnly"
                          maxLength="8"
                          autoComplete="off"
                          value={formData.returnYmd}
                          onChange={handleChangeForm}
                        />
                      </span>
                    </div>
                  </dd>
                  <dt className="form-tit ">
                    인원수 <span>*</span>
                  </dt>
                  <dd>
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="num"
                          id="personCount"
                          name="personCount"
                          placeholder="예상 인원수를 입력해주세요"
                          data-validateinput="numberOnly"
                          maxLength="3"
                          autoComplete="off"
                          value={formData.personCount}
                          onChange={handleChangeForm}
                        />
                      </span>
                    </div>
                  </dd>
                  <dt className="form-tit ">항공사 </dt>
                  <dd className="saerch-auto">
                    <span className="input-cmm search-auto">
                      <input
                        type="search"
                        id="airlineName"
                        name="airlineName"
                        placeholder="선호하는 항공사를 입력해주세요"
                        maxLength="20"
                        autoComplete="off"
                        required
                        value={formData.airlineName}
                        onChange={(event) => {
                          setAirlines([]);
                          handleChangeForm(event);
                          setIsSelectAction(false);
                        }}
                      />
                      <input type="hidden" id="airlineId" name="airlineId" value={formData.airlineId} />
                      <div className="box-nation" id="airlineAutoSearchArea">
                        <ul>
                          {airlines.map((item, index) => (
                            <li key={`${item.id}_${index}`}>
                              <a href="#none;" name="airlineSelect" onClick={() => handleSelectAirline(item)}>
                                {item.name}
                              </a>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </span>
                  </dd>
                  <dt className="form-tit">추가사항 </dt>
                  <dd className="add-property">
                    <div className="box-chk">
                      {addRequestList.map((item) => (
                        <label key={item.id} className="form-chkbox">
                          <input
                            type="checkbox"
                            id={`addRequest_${item.id}`}
                            name="addRequestDTOs"
                            value={item.id}
                            checked={formData.addRequest.includes(item.id)}
                            onChange={handleChangeAddRequestCheckbox}
                          />
                          <span>{item.name}</span>
                        </label>
                      ))}
                    </div>
                    <div className="form-cn">
                      <span className="input-cmm">
                        <input
                          type="text"
                          id="title"
                          name="title"
                          placeholder="제목"
                          maxLength="50"
                          autoComplete="off"
                          value={formData.title}
                          onChange={handleChangeForm}
                        />
                      </span>
                    </div>
                    <div className="form-cn">
                      <textarea
                        id="contents"
                        name="contents"
                        placeholder="문의내용을 입력해주세요. (2000자 이내)"
                        maxLength="2000"
                        value={formData.contents}
                        onChange={handleChangeForm}
                      ></textarea>
                    </div>
                    <div className="desc">
                      <p>• 문의 주신 내용은 확인 후 빠른 시간 내 답변 드리겠습니다.</p>
                      <p>• 기재된 연락처 또는 이메일로 안내해 드리니 정보 변경을 진행해주세요. MY페이지 &gt; 회원정보</p>
                      <p>• 주말 및 공휴일에는 휴무인 관계로 답변이 지연될 수 있으니 양해 바랍니다.</p>
                      <p>• 한번 등록한 문의 내용은 수정이 불가능합니다.</p>
                    </div>
                  </dd>
                </dl>
                <p className="essential">
                  <span>*</span> 필수입력
                </p>
              </div>
            </form>
            <div className="box-btn">
              <button type="button" className="btns-cmm round-basic color-w layer-pages-close">
                취소
              </button>
              <button type="button" className="btns-cmm round-basic color-b" onClick={addProcess}>
                견적 신청
              </button>
            </div>
          </div>
        </div>
        <div className="btn-prev-page layer-pages-close">
          <button type="button" className="btns-cmm" onClick={handleClose}></button>
        </div>
      </div>
      {isOpenRegistPop && (
        <EstimateRegistPop
          onClose={() => {
            setIsOpenRegistPop(false);
            onRetrieve();
          }}
          onCloseEstimateWrite={handleClose}
        />
      )}
    </>
  );
}

export default EstimateWrite;
