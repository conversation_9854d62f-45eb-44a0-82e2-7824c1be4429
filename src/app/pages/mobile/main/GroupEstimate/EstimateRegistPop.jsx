function EstimateRegistPop(props) {
  const { onClose, onCloseEstimateWrite } = props;

  const handleClose = () => {
    onClose();
    onCloseEstimateWrite();
  };

  return (
    <div id="groupEstimateRegistPop" className="modal-wrap type-alrt !block">
      <div className="modal-cotn">
        <p className="desc">등록이 완료 되었습니다.</p>
        <div className="box-btn">
          <button className="btns-cmm round-basic color-b w-50" onClick={handleClose}>
            확인
          </button>
        </div>
      </div>
    </div>
  );
}

export default EstimateRegistPop;
