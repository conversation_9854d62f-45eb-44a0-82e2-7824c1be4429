import { useEffect, useState } from "react";
import { requestWithMobileLoading } from "@/utils/app";
import { momentKR } from "@/utils/date";
import EstimateWrite from "@/app/pages/mobile/main/GroupEstimate/EstimateWrite";
import EstimateView from "@/app/pages/mobile/main/GroupEstimate/EstimateView";
import { MAPPED_AREA, MAPPED_SERVICE } from "@/constants/estimateGroup";

function EstimateList(props) {
  const { onClose } = props;
  const [estimateData, setEstimateData] = useState([]);
  const [viewData, setViewData] = useState({});
  const [isOpenModal, setIsOpenModal] = useState({ estimateWrite: false, estimateView: false });

  const viewEstimate = (estimateId) => {
    requestWithMobileLoading({
      url: "/m/group/estimate/getAjax",
      params: { estimateId },
      success: (res) => !res.data.accessLog && setViewData(res.data ?? {}),
      end: () => setIsOpenModal({ ...isOpenModal, estimateView: true }),
    });
  };

  const handleRetrieve = () => {
    requestWithMobileLoading({
      url: "/m/group/estimate/listAjax",
      method: "POST",
      success: (res) => setEstimateData(res.data?.list ?? []),
    });
  };

  useEffect(() => {
    handleRetrieve();
  }, []);

  return (
    <>
      <div className="layer-pages group-estimate estimate-list !block">
        <div className="layer-head">
          <p className="tit">법인/단체 견적 문의 내역</p>
        </div>
        <div className="layer-cotn">
          <div className="cs-time">
            <dl>
              <dt>상담시간</dt>
              <dd>
                평일(월 ~ 금) 09:00 ~ 17:00 <br />
                (Off-time 11:30 ~ 13:00, 토/일/공휴일 휴무)
              </dd>
            </dl>
          </div>
          <div className="box-btn ta-c btn-request">
            <button
              type="button"
              className="btns-cmm round-basic color-b w-75"
              onClick={() => setIsOpenModal({ ...isOpenModal, estimateWrite: true })}
            >
              견적 요청
            </button>
          </div>
          {estimateData.length ? (
            <div className="sec-list has-item" id="estimate-data">
              {estimateData.map((item, index) => (
                <div key={`${item.estimateId}_${index}`} className="box-item">
                  <p className="num">
                    신청번호 <strong>{item.estimateId}</strong>
                  </p>
                  <p className="tit">
                    <button type="button" onClick={() => viewEstimate(item.estimateId)}>
                      {item.title}
                    </button>
                  </p>
                  <div className="etc">
                    <span>{MAPPED_SERVICE[item.serviceType] ?? ""}</span>
                    <span>{MAPPED_AREA[item.area] ?? ""}</span>
                    <span>작성일: {momentKR(item.createDate).format("YYYY.MM.DD (ddd)")}</span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="sec-list has-none" id="estimate-none-data">
              <p className="none-item">요청 내역이 없습니다.</p>
            </div>
          )}
        </div>
        <div className="box-btn-close layer-pages-close">
          <button type="button" className="btns-cmm" onClick={onClose}>
            닫기
          </button>
        </div>
      </div>
      {isOpenModal.estimateWrite && (
        <EstimateWrite onClose={() => setIsOpenModal({ ...isOpenModal, estimateWrite: false })} onRetrieve={handleRetrieve} />
      )}
      {isOpenModal.estimateView && <EstimateView data={viewData} onClose={() => setIsOpenModal({ ...isOpenModal, estimateView: false })} />}
    </>
  );
}

export default EstimateList;
