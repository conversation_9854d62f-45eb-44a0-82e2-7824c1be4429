import { MAPPED_AREA, MAPPED_PURPOSE, MAPPED_SERVICE } from "@/constants/estimateGroup";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { momentKR } from "@/utils/date";
import { isEmpty } from "lodash";

function EstimateView(props) {
  const { data, onClose } = props;
  const { userInfo } = useAppSelector(selectUserInfo);

  return (
    <div className="layer-pages group-estimate estimate-view !block">
      <div className="layer-head">
        <p className="tit">법인/단체 견적 문의</p>
      </div>
      <div className="layer-cotn">
        <div className="sec-view">
          <div className="box-item">
            <p className="tit">회원 정보</p>
            <dl>
              <dt>회사명</dt>
              <dd>{userInfo.workspace.company.name}</dd>
              <dt>이름</dt>
              <dd>{userInfo.name}</dd>
              <dt>이메일 주소</dt>
              <dd>{userInfo.email}</dd>
              <dt>휴대전화</dt>
              <dd>{userInfo.cellPhoneNumber.replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3")}</dd>
            </dl>
          </div>
          <div className="box-item">
            <p className="tit">문의 내용</p>
            <dl id="estimate-view-data">
              {!isEmpty(data) && (
                <>
                  <dt>서비스 구분</dt>
                  <dd>{MAPPED_SERVICE[data.serviceType]}</dd>
                  <dt>목적</dt>
                  <dd>{MAPPED_PURPOSE[data.purpose]}</dd>
                  <dt>지역</dt>
                  <dd>{MAPPED_AREA[data.area]}</dd>
                  <dt>상세 지역</dt>
                  <dd>{data.areaDetail}</dd>
                  <dt>출발/도착일</dt>
                  <dd>
                    {momentKR(data.departYmd).format("MM월 DD일")} ~ {momentKR(data.returnYmd).format("MM월 DD일")}
                  </dd>
                  <dt>인원수</dt>
                  <dd>{data.personCount}</dd>
                </>
              )}
            </dl>
          </div>
          <div className="box-cotn">
            <dl id="estimate-addRequest-view-data">
              {!isEmpty(data) && (
                <>
                  <dt>{data.title}</dt>
                  <dd>
                    {data.content
                      ?.replace(/<br>/gi, "\n")
                      ?.replace(/<(\/)?([a-zA-Z]*)(\s[a-zA-Z]*=[^>]*)?(\s)*(\/)?>/gi, "")
                      ?.replace(/\r?\n/g, "<br />")}
                  </dd>
                </>
              )}
            </dl>
          </div>
          <div className="box-btn ta-c">
            <button type="button" className="btns-cmm round-basic color-b w-75 layer-pages-close" onClick={onClose}>
              목록으로
            </button>
          </div>
        </div>
      </div>
      <div className="btn-prev-page layer-pages-close">
        <button type="button" className="btns-cmm" onClick={onClose}></button>
      </div>
    </div>
  );
}

export default EstimateView;
