import { Fragment, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "@/store";
import { Link, useNavigate } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import { actionMain, selectDataMain } from "@/store/mainSlice.mobile";
import { selectUserInfo } from "@/store/userSlice";
import { returnPhoneNumber, substring4IndexOf } from "@/utils/common";
import { MOBILE_URL } from "@/constants/url";
import IMG_URL from "@/assets/images/travelplace";
import MobileLayoutGnb from "@/app/components/mobile/Gnb";
import mainImages from "@/assets/mobile/images/main";
import RecentAirSearchSchedule from "@/app/components/mobile/travel/air/RecentAirSearchSchedule";
import AirSessionUtil from "@/utils/airSessionUtil";
import EstimateList from "@/app/pages/mobile/main/GroupEstimate/EstimateList";
import "@/styles/mobile/css/main.css";
import "swiper/css";

function Main() {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [mobileLayoutGnbActive, setMobileLayoutGnbActive] = useState(false);
  const { mainAirTravelPlaces } = useAppSelector(selectDataMain);
  const { userInfo, gnbUserInfo } = useAppSelector(selectUserInfo);
  const [isOpenGroupEstimate, setIsOpenGroupEstimate] = useState(false);

  const handleBtnSideMenu = () => {
    setMobileLayoutGnbActive(true);
  };

  const handleMobileLayoutGnbUnActive = () => {
    setMobileLayoutGnbActive(false);
  };

  const handleSelectFavorTripCity = (selectedCity) => {
    const { replaceAirportName, airportCode, isOverseas } = selectedCity;
    AirSessionUtil.setMainAirTravelPlaceSession(replaceAirportName, airportCode);
    if (!isOverseas) {
      navigate(MOBILE_URL.DomesticAirMain);
      return;
    }
    navigate(MOBILE_URL.OverseasAirMain);
  };

  useEffect(() => {
    dispatch(actionMain());
  }, [dispatch]);

  return (
    <>
      <div id="container" className="pg-main">
        <div className="box-top">
          <button
            type="button"
            className="btn-sidemenu"
            style={{ backgroundImage: `url(${mainImages["btn_sidemenu.png"]})` }}
            onClick={handleBtnSideMenu}
          />
        </div>
        <div className="contents">
          <p className="side-padding copy">
            항공권부터 <br />
            예약 해볼까요?
          </p>
          <div className="airticket-type side-padding">
            <Link to="/m/overseas/air/main">
              <img src={mainImages["ticket_tab_01.png"]} />
              <span className="txt">
                해외 <br />
                항공권
              </span>
            </Link>
            <Link to="/m/domestic/air/main">
              <img src={mainImages["ticket_tab_02.png"]} />
              <span className="txt">
                국내 <br />
                항공권
              </span>
            </Link>
          </div>
          <div className="business-manage side-padding">
            <dl>
              <dt>더 완벽한 출장을 준비하세요.</dt>
              <dd>언제 어디서든 편리하게 출장을 관리할 수 있습니다.</dd>
            </dl>
            <ul className="cate">
              <li className="hotel">
                <Link to="/m/hotel/main">
                  <i className="spr-booking hotel" style={{ backgroundImage: `url(${mainImages["spr_booking.png"]})` }} />
                  <span className="txt">호텔</span>
                </Link>
              </li>
              <li className="car" onClick={() => alert("서비스 준비중입니다.")}>
                <i className="spr-booking car" style={{ backgroundImage: `url(${mainImages["spr_booking.png"]})` }} />
                <span className="txt">렌터카</span>
              </li>
              <li className="visa" onClick={() => alert("서비스 준비중입니다.")}>
                <i className="spr-booking visa" style={{ backgroundImage: `url(${mainImages["spr_booking.png"]})` }} />
                <span className="txt">VISA</span>
              </li>
            </ul>
            {!substring4IndexOf(userInfo.workspace?.company?.btmsSetting?.url, ".").includes("mastercard") && (
              <div className="group">
                <a href="#none;" onClick={() => setIsOpenGroupEstimate(true)} style={{ backgroundImage: `url(${mainImages["img_mice.png"]})` }}>
                  <span className="txt" style={{ backgroundImage: `url(${mainImages["icn_arrow_l.png"]})` }}>
                    단체 행사를 <br />
                    계획 중이신가요?
                  </span>
                </a>
              </div>
            )}
          </div>
          <div className="resent-search-sld">
            <p className="tit-size01 side-padding">최근 검색</p>
            <RecentAirSearchSchedule isMain={true} />
          </div>
          <div className="favor-trip-city">
            <p className="tit-size01 side-padding">자주 떠나는 출장지</p>
            <div className="swiper-container">
              <Swiper
                slidesPerView={"2.1"}
                spaceBetween={10}
                className="swiper-wrapper"
                effect="coverflow"
                coverflowEffect={{
                  rotate: 50,
                  stretch: 0,
                  depth: 100,
                  modifier: 1,
                  slideShadows: true,
                }}
              >
                {mainAirTravelPlaces?.map((item) => {
                  const { mainAirTravelPlaceId, filePath, replaceAirportName, airportCode } = item;
                  return (
                    <SwiperSlide key={mainAirTravelPlaceId} className="swiper-slide">
                      <a className="item" href="#none;" onClick={() => handleSelectFavorTripCity(item)}>
                        <img src={IMG_URL[filePath]} />
                        <span className="txt">
                          <span className="kr">{replaceAirportName}</span>
                          <span className="en">{airportCode}</span>
                        </span>
                      </a>
                    </SwiperSlide>
                  );
                })}
              </Swiper>
            </div>
          </div>
          <div className="inquiry-service side-padding">
            <p className="tit-size01 side-padding pl-0">서비스별 문의</p>
            <div className="box-item">
              <i className="spr-service air" style={{ backgroundImage: `url(${mainImages["spr_service.png"]})` }} />
              <p className="txt">항공 서비스 문의</p>
              <p className="tel">
                {gnbUserInfo.workspace?.company?.btmsManagers?.map(
                  (manager, index) =>
                    manager.travelAgencyUser &&
                    manager.isOverseas &&
                    manager.managerType === "Air" &&
                    manager.displayOrder == 1 &&
                    manager.travelAgencyUser?.phoneNumber && (
                      <Fragment key={index}>
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "FIRST")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "MIDDLE")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "END")}
                      </Fragment>
                    ),
                )}
              </p>
            </div>
            <div className="box-item">
              <i className="spr-service hotel" style={{ backgroundImage: `url(${mainImages["spr_service.png"]})` }} />
              <p className="txt">호텔 서비스 문의</p>
              <p className="tel">
                {gnbUserInfo.workspace?.company?.btmsManagers?.map(
                  (manager, index) =>
                    manager.travelAgencyUser &&
                    manager.isOverseas &&
                    manager.managerType === "Hotel_RentCar" &&
                    manager.displayOrder == 1 &&
                    manager.travelAgencyUser?.phoneNumber && (
                      <Fragment key={index}>
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "FIRST")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "MIDDLE")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "END")}
                      </Fragment>
                    ),
                )}
              </p>
            </div>
            <div className="box-item">
              <i className="spr-service visa" style={{ backgroundImage: `url(${mainImages["icn-service-visa-mobile.png"]})` }} />
              <p className="txt">비자 신청 문의</p>
              <p className="tel">
                {gnbUserInfo.workspace?.company?.btmsManagers?.map(
                  (manager, index) =>
                    manager.travelAgencyUser &&
                    manager.isOverseas &&
                    manager.managerType === "Passport_Visa" &&
                    manager.displayOrder == 1 &&
                    manager.travelAgencyUser?.phoneNumber && (
                      <Fragment key={index}>
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "FIRST")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "MIDDLE")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "END")}
                      </Fragment>
                    ),
                )}
              </p>
            </div>
            <div className="box-item">
              <i className="spr-service mice" style={{ backgroundImage: `url(${mainImages["spr_service.png"]})` }} />
              <p className="txt">단체행사 견적 문의</p>
              <p className="tel">
                {gnbUserInfo.workspace?.company?.btmsManagers?.map(
                  (manager, index) =>
                    manager.travelAgencyUser &&
                    manager.isOverseas &&
                    manager.managerType === "BizTraining" &&
                    manager.displayOrder == 1 &&
                    manager.travelAgencyUser?.phoneNumber && (
                      <Fragment key={index}>
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "FIRST")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "MIDDLE")}-
                        {returnPhoneNumber(manager.travelAgencyUser.phoneNumber, "END")}
                      </Fragment>
                    ),
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
      <MobileLayoutGnb active={mobileLayoutGnbActive} onUnActive={handleMobileLayoutGnbUnActive} />
      {isOpenGroupEstimate && <EstimateList onClose={() => setIsOpenGroupEstimate(false)} />}
    </>
  );
}

Main.displayName = "MMain";
export default Main;
