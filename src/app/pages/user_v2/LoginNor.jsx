import Footer from "@/app/components/user_v2/Footer";

import { Link } from "react-router-dom";

import logo from "@/assets/images/cmm/logo.png";

import "@/styles/user_v2/reset.css";
import "@/styles/user_v2/common.css";
import "@/styles/user_v2/login.css";
import "@/styles/user_v2/style.bundle.css";
import SKLogin from "@/app/components/user_v2/modal/SKLogin.modal";

export default function LoginNor({ data, setDataLogin, onLogin, errorLoginMessage, setErrorLoginMessage, company, isSaveLoginId, setIsSaveLoginId, isGeneralVersion }) {
  return (
    <form id="loginForm" name="loginForm" action="/login/authenticate" method="post">
      <div className="wrap">
        <div id="header">
          <div className="clearfix">
            <div className="left-col">
              <h1>
                <Link to="/login">
                  <img
                    src={
                      isGeneralVersion || !company || !company.homepageSetting || company.homepageSetting.isUseDefaultLogo
                        ? logo
                        : import.meta.env.VITE_STORAGE_URL +
                        "/" +
                        company.homepageSetting?.loginLogoAttachFile?.fileUploadPath +
                        company.homepageSetting?.loginLogoAttachFile?.tempFileName
                    }
                    alt="LOGO"
                    style={{ width: "auto" }}
                  />
                </Link>
              </h1>
            </div>
          </div>
        </div>
        <div id="container" className="clearfix default-wd pg-login">
          <div className="contents login-cotn">
            <h2 className="title">로그인</h2>
            <div className="id">
              <div className="box-col">
                <input
                  type="text"
                  id="email"
                  name="email"
                  maxLength={100}
                  placeholder="이메일(아이디)"
                  value={data.email}
                  onChange={(e) => {
                    setDataLogin({ ...data, email: e.target.value });
                    setErrorLoginMessage("");
                  }}
                />
              </div>
            </div>
            <div className="pw">
              <div className="box-col">
                <input
                  type="password"
                  id="password"
                  name="password"
                  placeholder="비밀번호"
                  autoComplete="off"
                  value={data?.password}
                  onChange={(e) => {
                    setDataLogin({ ...data, password: e.target.value });
                    setErrorLoginMessage("");
                  }}
                />
              </div>
            </div>
            <p id="loginMessage" className="validate" style={{ display: errorLoginMessage ? "block" : "none" }}>
              {errorLoginMessage}
            </p>
            <div className="clearfix py-3">
              <div className="float-left">
                <label className="m-checkbox">
                  <input type="checkbox" id="saveId" name="saveId" checked={isSaveLoginId} onChange={(e) => setIsSaveLoginId(e.target.checked)} />{" "}
                  아이디 저장
                  <span />
                </label>
              </div>
            </div>
            <div
              className="etc-col"
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "flex-start",
              }}
            >
              <div className="btns">
                <Link to="/customer/v2/join/joinForm">회원가입</Link>
                <a href="/customer/v2/find/emailIdentity" style={{ width: 86 }}>
                  비밀번호 찾기
                </a>
              </div>
              <button type="button" className="btn-default btn-login" id="loginBtn" onClick={onLogin}>
                로그인
              </button>
            </div>
          </div>
          <SKLogin />
        </div>
        <Footer />
      </div>
    </form>
  );
}
