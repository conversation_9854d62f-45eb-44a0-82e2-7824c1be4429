import { useEffect, useState } from "react";
import { isEmpty } from "lodash";
import { useDropzone } from "react-dropzone";
import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { postFileDelete, postFileMultiUpload } from "@/service/common";
import { actionDecreaseLoadingCount, actionIncreaseLoadingCount } from "@/store/loadingUserSlice";
import { postAttachFileDelete, postAttachFileSave } from "@/service/air";
import { selectReservationTravelView } from "@/store/travelViewSlice";

export default function DocumentFileUpload(props) {
  const { bookingAirId } = props;
  const { userInfo } = useAppSelector(selectUserInfo);
  const { viewFile } = useAppSelector(selectReservationTravelView);

  const [files, setFiles] = useState([]);
  const dispatch = useAppDispatch();

  const onDrop = async (acceptedFiles) => {
    const formData = new FormData();
    const isValid = validateFile(acceptedFiles);
    if (!isValid) return;
    acceptedFiles.forEach((file) => {
      formData.append("File", file);
    });
    try {
      dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
      const response = await postFileMultiUpload(formData);
      const responseSave = await postAttachFileSave({
        bookingAirId,
        attachFiles: [...response.data].map((el) => {
          return {
            originFileName: el.originFileName,
            tempFileName: el.tempFileName,
            fileUploadPath: el.fileUploadPath,
            fileSize: el.fileSize,
            attachFileType: el.attachFileType,
            s3BucketPath: "",
          };
        }),
      });
      const mergedFiles = [...files, ...responseSave.data.attachFiles];
      setFiles(mergedFiles.map((file, index) => ({ ...file, id: index })));
    } catch (error) {
      console.error(error);
    } finally {
      dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
    }
  };

  function validateFile(files) {
    const maxFileSize = 10;
    const maxFileCount = 5;

    const isExceedMaxFileSize = files.some((file) => file.size > parseInt(maxFileSize) * 1000000);
    if (isExceedMaxFileSize) {
      alert("파일 용량은 " + maxFileSize + " MB를 초과할 수 없습니다.");
      return false;
    }

    if (maxFileCount != null && parseInt(maxFileCount) > 1) {
      if (files.length > parseInt(maxFileCount)) {
        alert("업로드 파일 개수 " + maxFileCount + "개가 초과되었습니다.");
        return false;
      }
    }

    return true;
  }

  async function handleRemoveFile(file) {
    await postFileDelete({ filePath: file.fileUploadPath + file.tempFileName });
    await postAttachFileDelete({ bookingAirId, attachFileId: file.attachFileId });
    setFiles((prev) => prev.filter((f) => f.attachFileId !== file.attachFileId));
  }

  function downloadFile(file) {
    var hiddenIFrameId = "hiddenDownloader";
    var iframe = document.getElementById(hiddenIFrameId);
    if (iframe === null) {
      iframe = document.createElement("iframe");
      iframe.id = hiddenIFrameId;
      iframe.style.display = "none";
      document.body.appendChild(iframe);
    }
    iframe.src = "/common/file/custom/download?attachFileId=" + file.id;
  }

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  useEffect(() => {
    if (viewFile.attachFiles.length > 0) {
      setFiles(viewFile.attachFiles);
    }
  }, [viewFile.attachFiles]);

  return userInfo?.workspace?.company?.btmsSetting?.isDocumentEvidenceFile ? (
    <div className="box-line box-item document-add" id="divAddDocumentFile">
      <h3 className="tit">증빙서류 첨부</h3>
      <div className="btn-file-group absolute top-[20px] right-[20px]">
        <label htmlFor="inputAddDocumentFile">파일선택</label>
        <input type="file" name="File" accept="" className="btn-file" id="inputAddDocumentFile" multiple {...getInputProps()} />
      </div>
      <div className="upload-cotn" {...getRootProps()}>
        <div className="box-default" id="fileDropArea" style={{ display: isEmpty(files) ? "flex" : "none" }}>
          <p>
            <strong>등록하실 파일을 여기에 올려주세요.</strong>
          </p>
          <p>10MB 이하 파일만 업로드 가능합니다.</p>
        </div>
        <div className="box-upload" id="fileListArea">
          <div className="slimScrollDiv" style={{ position: "relative", overflow: "hidden", width: "auto" }}>
            <ul className="file-list scrollbar-inner overflow-y-auto overflow-x-hidden" id="fileListAreaUl">
              {files.map((file) => (
                <li key={file.id} className="box-content">
                  <span
                    className="file-name cursor-pointer"
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadFile(file);
                    }}
                  >
                    {file.originFileName}
                  </span>
                  <button
                    type="button"
                    className="btn-del-file"
                    name="removeFileButton"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(file);
                    }}
                  >
                    삭제
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  ) : null;
}
