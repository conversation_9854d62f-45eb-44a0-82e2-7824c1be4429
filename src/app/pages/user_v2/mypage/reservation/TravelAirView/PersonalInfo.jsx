import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { useState } from "react";

export default function PersonalInfo() {
  const [active, setActive] = useState(true);
  const {
    data: { travel },
  } = useAppSelector(selectReservationTravelView);

  return (
    <div className={`box-item ${active ? "active" : ""} personal-info`}>
      <h3 className="tit">예약자 정보</h3>
      {travel?.reserver && (
        <div className="box-cotn">
          <dl className="box-col name">
            <dt>예약자 이름</dt>
            <dd className="desc">{travel?.reserver?.name}</dd>
          </dl>
          <dl className="box-col num">
            <dt>휴대전화 번호</dt>
            <dd className="desc">{travel?.reserver?.cellPhoneNumber}</dd>
          </dl>
          <dl className="box-col email">
            <dt>이메일 주소</dt>
            <dd className="desc">{travel?.reserver?.email}</dd>
          </dl>
        </div>
      )}

      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
    </div>
  );
}
