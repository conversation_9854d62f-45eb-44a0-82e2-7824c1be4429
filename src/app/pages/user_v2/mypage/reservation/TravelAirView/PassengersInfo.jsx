import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import PassengerInfo from "@/app/pages/user_v2/mypage/reservation/TravelAirView/PassengerInfo";

export default function PassengersInfo() {
  const [active, setActive] = useState(true);
  const {
    data: { travel },
  } = useAppSelector(selectReservationTravelView);

  return (
    <div className={`box-item ${active ? "active" : ""} passanger-info`}>
      <h3 className="tit">탑승객 정보</h3>
      <div className="box-cotn ">
        <p className="caution-america">
          미주 노선은 여권정보가 반드시 필요합니다. 정확한 여권정보를 입력하세요. <br />※ 여권 발급 예정자이거나 확인이 어려운 경우 임의의 값을 입력한
          후, 반드시 출발 72시간 전까지 유효한 여권정보를 입력하세요.
          <br /> ※ 여권 유효기간은 출국일 기준 6개월 이상 남아 있어야 출국이 가능합니다.
        </p>
        {travel?.bookingAir?.bookingAirTravelers?.map((travel, index) => {
          return <PassengerInfo key={travel?.id} index={index} passenger={travel} />;
        })}

        {/* 탑승객 수정 폼 */}
      </div>
      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
      <style
        dangerouslySetInnerHTML={{
          __html:
            "\n        .box-col {\n            padding: 16px 16px 16px 16px !important;\n        }\n        .box-border {\n            border: 1px solid #e2e4e8;\n            border-radius: 5px;\n            line-height: 34px !important;\n            height: 34px;\n            padding-left: 5px;\n        }\n\n        .mileage {\n            width: 100%;\n            padding-top: 22px;\n            padding-bottom: 22px;\n            display: block;\n\n            line-height: 19px;\n            color: #757f92;\n        }\n\n        .mileage .title {\n            width: 120px;\n            display: block;\n            float: left;\n            line-height: 35px;\n        }\n\n        .mileage .content {\n            display: block;\n            float: left;\n            margin-left: 22px;\n        }\n\n        .mileage button {\n            float: right;\n            width: 107px;\n            font-size: 13px;\n            font-weight: 500;\n            text-align: center;\n            line-height: 34px;\n            color: #fff;\n            background-color: #34446e;\n            border-radius: 5px;\n        }\n\n        .mileage .content span {\n            margin-right : 11px;\n            margin-left : 11px;\n        }\n\n        .mileage .content div {\n            padding-bottom: 10px;\n        }\n\n        .mileage .content div input[type=text] {\n            height: 34px;\n            border: 0 none;\n            border-bottom: 1px solid #e2e4e8;\n            outline: none;\n            padding-left: 5px;\n            margin-right: 10px;\n        }\n        .mileage .content div p {\n            display: inline-block;\n            height: 34px;\n            border: 0 none;\n            outline: none;\n            padding-left: 5px;\n            margin-right: 10px;\n            line-height: 34px;\n            color: black;\n        }\n    ",
        }}
      />
    </div>
  );
}
