import { useMemo } from "react";
import { momentKR } from "@/utils/date";
import { carriageReturn } from "@/utils/common";
import ImgNew from "@/assets/images/cmm/new.png";

export default function ReservChangeItem(props) {
  const {
    index,
    requestInfo,
    creatorUserName,
    answer,
    createDate,
    isLeaf,
    travelModifyRequestId,
    parentTravelModifyRequestId,
    travelModifyRequestRes,
    showAnswerId,
    setShowAnswerId,
  } = props;

  const needShowNewImg = useMemo(() => {
    return travelModifyRequestRes.find((el) => el.parentTravelModifyRequestId === travelModifyRequestId)?.isNew;
  }, [travelModifyRequestRes]);

  if (parentTravelModifyRequestId === 0) {
    return (
      <tr key={createDate + index} className="question">
        <td>
          {needShowNewImg && <img className="inline-block" src={ImgNew} alt="new" style={{ width: 30 }} />}
          {isLeaf === "0" ? "답변완료" : "답변대기"}
        </td>
        <td>변경</td>
        <td className="txt-short !text-left p-3">
          <a
            onClick={() => {
              setShowAnswerId(showAnswerId === travelModifyRequestId ? -1 : travelModifyRequestId);
            }}
            dangerouslySetInnerHTML={{ __html: carriageReturn(requestInfo) }}
          />
        </td>
        <td>{creatorUserName}</td>
        <td>{momentKR(createDate).format("yyyy.MM.DD HH:mm")}</td>
      </tr>
    );
  }
  return (
    <tr key={createDate + index} className={`answer ${showAnswerId === parentTravelModifyRequestId ? "active" : ""}`}>
      <td colSpan={2}></td>
      <td>
        <div className="box-answer box-content">
          <span className="tag">답변</span>
          <span dangerouslySetInnerHTML={{ __html: carriageReturn(answer) }} />
        </div>
      </td>
      <td>{creatorUserName}</td>
      <td>{momentKR(createDate).format("yyyy.MM.DD HH:mm")}</td>
    </tr>
  );
}
