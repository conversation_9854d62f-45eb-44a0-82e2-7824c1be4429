import { Fragment, useState } from "react";
import { isEmpty } from "lodash";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { momentKR } from "@/utils/date";
import AIR_ICONS_PATH from "@/assets/airIcon";
import { onErrorImgAirline } from "@/utils/common";
import useBookingAirScheduleMap from "@/app/hooks/useBookingAirScheduleMap";
import { MAPPED_SEAT_TYPE } from "@/constants/app";

export default function FlightInfoBooking() {
  const [active, setActive] = useState(true);
  const {
    data: { travel },
  } = useAppSelector(selectReservationTravelView);
  const { bookingAirScheduleMap } = useBookingAirScheduleMap({ bookingAirSchedule: travel.bookingAir?.bookingAirSchedules });

  return (
    <div className={`box-item ${active ? "active" : ""}`}>
      <h3 className="tit">여정정보</h3>
      <div className="box-cotn info-booking">
        <dl className="plan">
          {!isEmpty(bookingAirScheduleMap) &&
            Object.entries(bookingAirScheduleMap)?.map(([key, value]) => {
              const { fromAirport, totalTime } = value[0];
              const { sectionType } = travel.bookingAir;
              return (
                <Fragment key={value[0].id}>
                  <dt>
                    <div className="type">
                      {sectionType === "OneWay" && "편도"}
                      {sectionType === "RoundTrip" ? (Number(key) === 1 ? "오는편" : "가는편") : ""}
                      {sectionType === "MultiCity" && "편도 " + key}
                    </div>
                    <div className="city">
                      <span className="dep">
                        {fromAirport?.name}({fromAirport?.code})
                      </span>
                      <span className="arr">
                        {value.slice(-1)[0].toAirport?.name}({value.slice(-1)[0].toAirport?.code})
                      </span>
                    </div>
                    <div className="time">
                      총 {totalTime?.substring(0, 2)}시간 {totalTime?.substring(2, 4)}분
                    </div>
                  </dt>
                  <dd>
                    {value.map((item, index) => {
                      const {
                        airline,
                        opAirline,
                        fromAirport,
                        toAirport,
                        departureTerminal,
                        arrivalTerminal,
                        fromDate,
                        leadTime,
                        toDate,
                        airlineFlightNo,
                        seatType,
                        bookingClassCode,
                        baggageAllow,
                      } = item;

                      return (
                        <Fragment key={item.id}>
                          <div className="plane clearfix">
                            <span>
                              <img
                                src={AIR_ICONS_PATH[airline?.code] || ""}
                                onError={onErrorImgAirline}
                                className="w-[30px] h-[20px] object-cover inline-block"
                                alt="항공사로고"
                              />
                              {airline?.name}
                              {!isEmpty(opAirline) && airline?.code !== opAirline?.code && (
                                <Fragment>
                                  <br /> [실제운항] {opAirline?.name}
                                </Fragment>
                              )}
                            </span>
                            {/*<span>AC0064</span>*/}
                            <span>
                              {airline?.code || ""}
                              {airlineFlightNo}
                            </span>
                            <span>
                              {MAPPED_SEAT_TYPE[seatType]}({bookingClassCode})
                            </span>
                          </div>
                          <div className="schdule">
                            <div className="clearfix">
                              <p className="date">{momentKR.utc(fromDate).format("MM월 DD일 (ddd)")}</p>
                              <p className="time">{momentKR.utc(fromDate).format("HH:mm")}</p>
                              <div className="sign point" />
                              <p className="city">
                                <span className="en">{fromAirport?.code}</span>
                                <span className="kr">
                                  {fromAirport?.name} {departureTerminal ? `T${departureTerminal}` : ""}
                                </span>
                              </p>
                            </div>
                            <p className="time-plan">
                              {leadTime?.substring(0, 2)}시간 {leadTime?.substring(2, 4)}분
                            </p>
                            <div className="clearfix">
                              <p className="date">{momentKR.utc(toDate).format("MM월 DD일 (ddd)")}</p>
                              <p className="time">{momentKR.utc(toDate).format("HH:mm")}</p>
                              <div className="sign ing" />
                              <p className="city">
                                <span className="en">{toAirport?.code}</span>
                                <span className="kr">
                                  {toAirport?.name} {arrivalTerminal ? `T${arrivalTerminal}` : ""}
                                </span>
                              </p>
                            </div>
                            {index !== value.length - 1 && (
                              <Fragment>
                                {index === 0 && (
                                  <p className="overstop">
                                    {value?.[1]?.groundTime?.substring(0, 2) + "시간 " + value?.[1]?.groundTime?.substring(2, 4) + "분 대기"}
                                  </p>
                                )}
                                {index === 1 && (
                                  <p className="overstop">
                                    {value?.[2]?.groundTime?.substring(0, 2) + "시간 " + value?.[2]?.groundTime?.substring(2, 4) + "분 대기"}
                                  </p>
                                )}
                                {index === 2 && (
                                  <p className="overstop">
                                    {value?.[3]?.groundTime?.substring(0, 2) + "시간 " + value?.[2]?.groundTime?.substring(2, 4) + "분 대기"}
                                  </p>
                                )}
                              </Fragment>
                            )}
                            <div className="etc">무료 수하물 {baggageAllow || "불포함"}</div>
                          </div>
                        </Fragment>
                      );
                    })}
                  </dd>
                </Fragment>
              );
            })}
        </dl>
      </div>
      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
    </div>
  );
}
