import { MAPPED_STATUS_CODE } from "@/constants/app";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { momentKR } from "@/utils/date";
import { isEmpty } from "lodash";
import { useState } from "react";

export default function ReservStatus() {
  const [active, setActive] = useState(false);
  const { travelStatusHistories } = useAppSelector(selectReservationTravelView);

  return (
    <div className={`box-item ${active ? "active" : ""} reserv-status`}>
      <h3 className="tit">예약 진행 내역</h3>
      <div className="box-cotn">
        <div className="table-cmm type-fare">
          <table>
            <colgroup>
              <col style={{ width: 94 }} />
              <col style={{ width: 140 }} />
              <col style={{ width: 388 }} />
              <col style={{ width: "*" }} />
            </colgroup>
            <thead>
              <tr>
                <th>진행상태</th>
                <th>이름</th>
                <th>내용</th>
                <th>요청일시</th>
              </tr>
            </thead>
            <tbody>
              {!isEmpty(travelStatusHistories) ? (
                travelStatusHistories.map((history, index) => {
                  const { modifyDate, status, modifier, modifyInfo } = history;
                  return (
                    <tr key={modifyDate + index}>
                      <td>{MAPPED_STATUS_CODE[status].name}</td>
                      <td>{modifier?.name}</td>
                      <td className="left">{modifyInfo}</td>
                      <td>{momentKR(modifyDate).format("yyyy.MM.DD HH:mm")}</td>
                    </tr>
                  );
                })
              ) : (
                <tr>
                  <td colSpan={4} className="none">
                    예약 진행 내역이 없습니다.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
    </div>
  );
}
