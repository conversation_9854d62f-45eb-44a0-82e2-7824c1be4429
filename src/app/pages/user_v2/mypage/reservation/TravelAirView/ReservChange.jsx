import { useEffect, useMemo, useState } from "react";
import ImgNew from "@/assets/images/cmm/new.png";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { cloneDeep, isEmpty } from "lodash";
import ReservChangeItem from "@/app/pages/user_v2/mypage/reservation/TravelAirView/ReservChangeItem";
import { postRequestReadProcess } from "@/service/air";

export default function ReservChange() {
  const [active, setActive] = useState(false);
  const {
    data: { travelModifyRequestRes },
  } = useAppSelector(selectReservationTravelView);
  const [viewData, setViewData] = useState([]);
  const [showAnswerId, setShowAnswerId] = useState(-1);

  const needShowNewImg = useMemo(() => {
    return viewData.some((item) => item?.isNew && item.parentTravelModifyRequestId !== 0);
  }, [viewData]);

  useEffect(() => {
    if (!isEmpty(travelModifyRequestRes)) {
      setViewData(travelModifyRequestRes);
    }
  }, [travelModifyRequestRes]);

  useEffect(() => {
    async function viewModifyRequestAnswer(id) {
      const response = await postRequestReadProcess({ travelModifyRequestId: id });
      const clonedViewData = cloneDeep(viewData);
      setViewData(
        clonedViewData.map((el) => {
          const item = el;
          if (el.parentTravelModifyRequestId === id) item.isNew = false;
          return item;
        }),
      );
    }

    if (showAnswerId !== -1) {
      viewModifyRequestAnswer(showAnswerId);
    }
  }, [showAnswerId]);

  return (
    <div className={`box-item ${active ? "active" : ""} reserv-change`}>
      <h3 className="tit relative">
        예약 변경 / 취소 내역{" "}
        <img id="overseasNewHistory" src={ImgNew} alt="new" style={{ width: 30, display: needShowNewImg ? "inline-block" : "none" }} />
      </h3>
      <div className="box-cotn">
        <p className="desc">
          해당 예약 이외 궁금하신 점은{" "}
          <span className="etc" onClick={() => (location.href = "/user/v2/board/qna/add")} style={{ cursor: "pointer" }}>
            1:1 문의
          </span>
          를 이용해 주시기 바랍니다.
        </p>
        <div className="table-cmm type-fare">
          <table>
            <colgroup>
              <col style={{ width: 102 }} />
              <col style={{ width: 64 }} />
              <col style={{ width: "*" }} />
              <col style={{ width: 78 }} />
              <col style={{ width: 105 }} />
            </colgroup>
            <thead>
              <tr>
                <th>상태</th>
                <th>종류</th>
                <th>내용</th>
                <th>작성자</th>
                <th>작성일</th>
              </tr>
            </thead>
            <tbody>
              {isEmpty(viewData) ? (
                <tr className="question active">
                  <td colSpan={5}>예약 변경/취소 내역이 없습니다.</td>
                </tr>
              ) : (
                viewData.map((travelModifyRequest, index) => {
                  return (
                    <ReservChangeItem
                      {...travelModifyRequest}
                      travelModifyRequestRes={viewData}
                      key={index}
                      index={index}
                      showAnswerId={showAnswerId}
                      setShowAnswerId={setShowAnswerId}
                    />
                  );
                })
              )}
            </tbody>
          </table>
        </div>
      </div>
      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
    </div>
  );
}
