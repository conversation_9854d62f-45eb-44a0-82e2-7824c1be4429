import { useState } from "react";
import { isEmpty } from "lodash";
import { useParams } from "react-router-dom";
import AirCompareSchedule from "@/app/components/user_v2/modal/AirCompareSchedule.modal";
import ReservationFareRule from "@/app/components/user_v2/modal/ReservationFareRule.modal";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionSavedFareRule, selectTravelRuleBase } from "@/store/airSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { comma, getSeatTypeName } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { EXECPT_TIME_TYPE, MAPPED_SEAT_TYPE } from "@/constants/app";
import { selectUserInfo } from "@/store/userSlice";
import { MAPPED_STATUS_CODE } from "@/constants/app";

export default function RightSide() {
  const {
    data: { travel },
    compareAirSchedules,
  } = useAppSelector(selectReservationTravelView);
  const { data: travelRuleBaseDTO } = useAppSelector(selectTravelRuleBase);
  const { userInfo } = useAppSelector(selectUserInfo);

  const { travelId } = useParams();
  const dispatch = useAppDispatch();
  const apiWithLoading = useApiWithLoading();

  const [openAirBookingCompareScheduleModal, setOpenAirBookingCompareScheduleModal] = useState(false);
  const [openFareRuleModal, setOpenFareRuleModal] = useState(false);

  async function handleOpenAirBookingCompareScheduleModal() {
    setOpenAirBookingCompareScheduleModal(true);
  }

  async function handleOpenFareRuleModal() {
    await apiWithLoading(
      () => dispatch(actionSavedFareRule({ travelId })).unwrap(),
      () => {
        setOpenFareRuleModal(true);
      },
    );
  }

  return (
    <div className="right-side sec-pay-to !sticky top-0 right-0">
      {/* S: 총 결제요금 */}
      <div className="box-line">
        <div className="txt-big clearfix">
          <p className="fl-l">총 결제요금</p>
          <p className="fl-r c-price">
            <strong>{comma(travel?.bookingAir?.totalAmount)}</strong>원
          </p>
        </div>
        <dl className="fare-detail">
          <dt>성인 {travel?.bookingAir?.adultCount}명</dt>
          <dd>
            <span className="tit">항공료</span>
            <span className="sum">
              {comma(travel?.bookingAir?.fareAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
          <dd>
            <span className="tit">TAX</span>
            <span className="sum">
              {comma(travel?.bookingAir?.taxAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
          <dd>
            <span className="tit">발권수수료</span>
            <span className="sum">
              {comma(travel?.bookingAir?.commissionAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
        </dl>
      </div>
      <div className="box-line">
        <p className="txt-big">출장자 규정</p>
        <ul className="brakedown">
          <li>
            결재 요청 의견 출발일 기준
            <span>{travelRuleBaseDTO?.travelLimitDay === 0 ? "제한없음" : "일 전"}</span>
          </li>
          <li>
            기본 좌석등급
            <span>
              {travelRuleBaseDTO?.baseSeatTypeCode} {MAPPED_SEAT_TYPE[travelRuleBaseDTO?.baseSeatTypeCode]}
            </span>
          </li>
          {travelRuleBaseDTO?.isExceptUse && (
            <li>
              {EXECPT_TIME_TYPE[travelRuleBaseDTO?.exceptTimeType]}
              <span>{travelRuleBaseDTO?.exceptBordingTime}</span>
              시간 이상 일 경우
              <br />
              <span>{getSeatTypeName(travelRuleBaseDTO?.exceptSeatTypeCode)}</span>
              허용
            </li>
          )}
        </ul>
      </div>
      <div className="box-line">
        <p className="txt-big">결재</p>
        <dl
          className={`pay-status ${travel?.statusCode?.id === MAPPED_STATUS_CODE.Completed.id ? "complete" : [MAPPED_STATUS_CODE.Rejected.id, MAPPED_STATUS_CODE.Cancelled.id].includes(travel?.statusCode?.id) ? "reject" : "ready"}`}
        >
          <dt>결재 상태</dt>
          <dd>{travel?.statusCode?.name}</dd>
        </dl>

        {[MAPPED_STATUS_CODE.Approved.id, MAPPED_STATUS_CODE.Completed.id, MAPPED_STATUS_CODE.Rejected.id, MAPPED_STATUS_CODE.Cancelled.id].includes(
          travel?.statusCode?.id,
        ) && <div className="paid-date">결재일 {momentKR(travel?.modifyDate, 0).format("yyyy년 MM월 DD일(ddd) HH:mm")}</div>}
        <div>
          <p className="txt-medium clearfix">결재 요청 의견</p>
          <div className="opinion-insert">
            <textarea name="violationReason" id="violationReason" readOnly defaultValue={travel?.violationReason} />
          </div>
        </div>
        <div>
          <p className="txt-medium clearfix">결재 의견</p>
          <dd>{travel?.approvalMemo}</dd>
        </div>
      </div>

      {!isEmpty(travel?.documentNumbers) ? (
        <div className="box-line">
          <p className="txt-big">
            {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
              ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
              : "문서번호"}
          </p>
          <ul>
            {travel?.documentNumbers?.map((item, index) => (
              <li key={index + item}>{item?.documentNo}</li>
            ))}
          </ul>
        </div>
      ) : (
        userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberUse && (
          <div className="box-line">
            <p className="txt-big">
              {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                : "문서번호"}
            </p>
            <ul></ul>
          </div>
        )
      )}
      <div className="box-applys">
        {!isEmpty(compareAirSchedules) && (
          <a onClick={handleOpenAirBookingCompareScheduleModal} className="btn-default btn-compare">
            비교견적서
          </a>
        )}
      </div>
      <div className="box-applys">
        <a className="btn-default btn-compare" onClick={handleOpenFareRuleModal}>
          요금 규정
        </a>
      </div>

      <AirCompareSchedule travel={travel} open={openAirBookingCompareScheduleModal} setOpen={setOpenAirBookingCompareScheduleModal} />
      <ReservationFareRule open={openFareRuleModal} setOpen={setOpenFareRuleModal} />
    </div>
  );
}
