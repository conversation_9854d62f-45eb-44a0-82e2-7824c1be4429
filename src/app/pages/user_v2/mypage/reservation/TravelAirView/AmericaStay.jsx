import { useEffect, useState } from "react";
import { cloneDeep, get, isEmpty, set } from "lodash";
import { useParams } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  actionSearchUsaState,
  actionSearchUsaStayCity,
  selectReservationTravelView,
  selectUsaCities,
  selectUsaStates,
  selectUsaStayLoading,
} from "@/store/travelViewSlice";
import { hanldeInputChangeValidation } from "@/utils/common";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import { postAddAmericaStayProcess } from "@/service/air";
import { nvl } from "@/common";
import { MAPPED_STATUS_CODE } from "@/constants/app";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";
import { debounce } from "lodash";
import StayInfoAutoComplete from "@/app/components/user_v2/common/StayInfoAutoComplete";

const debouncedSearch = debounce((callback) => callback(), 500);

export default function AmericaStay() {
  const dispatch = useAppDispatch();
  const [active, setActive] = useState(false);
  const [isUpdated, setIsUpdated] = useState(false);
  const { travelId } = useParams();
  const {
    data: { travel, americaStayDTO },
  } = useAppSelector(selectReservationTravelView);
  const isLoadingUsaStay = useAppSelector(selectUsaStayLoading);
  const usaCities = useAppSelector(selectUsaCities);
  const usaStates = useAppSelector(selectUsaStates);

  const apiWithLoading = useApiWithLoading();
  const [data, setData] = useState({
    state: { display: "", name: "", code: "" },
    city: { display: "", name: "", code: "" },
    zipcode: "",
    address: "",
  });

  function onInput(event) {
    const isValid = hanldeInputChangeValidation(event);
    if (isValid) {
      handleChange(event);
    }
  }

  function handleChange(event) {
    const { name, value } = event.target;
    setData((prev) => {
      return set(cloneDeep(prev), name, value);
    });
  }

  const handleSearchUsaState = (event) => {
    const { value } = event.target;
    setData((prev) => ({ ...prev, state: { display: value, name: "", code: "" } }));
    debouncedSearch(() => {
      if (!value) return;
      dispatch(actionSearchUsaState({ keyword: value }));
    });
  };

  const handleSearchUsaCity = (event) => {
    const { value } = event.target;
    setData((prev) => ({ ...prev, city: { display: value, name: "", code: "" } }));
    debouncedSearch(() => {
      if (!value) return;
      dispatch(actionSearchUsaStayCity({ keyword: value }));
    });
  };

  async function handleSubmit() {
    if (isEmpty(data.state.display)) {
      alert("주를 입력해주세요.");
      return;
    }
    if (isEmpty(data.city.display)) {
      alert("도시를 입력해주세요.");
      return;
    }
    if (isEmpty(data.address)) {
      alert("주소를 입력해주세요.");
      return;
    }

    const payload = {
      travelId,
      stateCode: data.state.code,
      stateName: data.state.name || data.state.display,
      cityCode: data.city.code,
      cityName: data.city.name || data.city.display,
      zipcode: data.zipcode,
      address: data.address,
    };

    await apiWithLoading(
      () => postAddAmericaStayProcess(payload),
      () => {
        setIsUpdated(true);
        if (isEmpty(americaStayDTO) && !isUpdated) {
          alert("등록되었습니다.");
          return;
        }
        alert("수정되었습니다.");
      },
      () => {
        alert(`처리 중 에러가 발생하였습니다.\n다시 시도해 주세요.${CONTACT_ADMIN_ALERT}`);
      },
    );
  }

  useEffect(() => {
    const { stateCode = "", stateName = "", cityCode = "", cityName = "", zipcode = "", address = "" } = americaStayDTO ?? {};
    setData({
      state: { display: stateCode ? `${stateName} (${stateCode})` : stateName, name: stateName, code: stateCode },
      city: { display: cityCode ? `${cityName} (${cityCode})` : cityName, name: cityName, code: cityCode },
      zipcode,
      address,
    });
  }, [americaStayDTO]);

  return (
    <div className={`box-item stay-adress ${active ? "active" : ""} `}>
      <h3 className="tit">미국 내 체류지 정보</h3>
      <div className="box-cotn">
        <div className="caution-america flex flex-col !items-start gap-1">
          <span>• 출발 72시간 전까지 정확한 체류지 정보를 등록 및 수정하시기 바랍니다.</span>
          <span>• 체류 도시, 주소에 현금과 특수문자는 입력이 불가하며, 영문과 숫자만 합력하시기 바랍니다.</span>
          <span>• 잘못된 체류지 정보 입력으로 인한 탑승 및 입국 거절은 투어비스에서 책임지지 않습니다.</span>
        </div>
        <div className="clearfix">
          <dl>
            <dt>
              체류 주<span className="essential">*</span>
            </dt>
            <dd>
              <StayInfoAutoComplete
                value={data.state.display}
                options={usaStates}
                debounceSearch={handleSearchUsaState}
                isLoading={isLoadingUsaStay}
                labelText="name"
                valueText="code"
                onChange={(value) =>
                  setData((prev) => ({ ...prev, state: { display: `${value.name} (${value.code})`, name: value.name, code: value.code } }))
                }
                onClear={() => setData((prev) => ({ ...prev, state: { display: "", name: "", code: "" } }))}
              />
            </dd>
          </dl>
          <dl>
            <dt>
              체류 도시<span className="essential">*</span>
            </dt>
            <dd>
              <StayInfoAutoComplete
                value={data.city.display}
                options={usaCities}
                debounceSearch={handleSearchUsaCity}
                isLoading={isLoadingUsaStay}
                labelText="name"
                valueText="cityCode"
                onChange={(value) =>
                  setData((prev) => ({ ...prev, city: { display: `${value.name} (${value.cityCode})`, name: value.name, code: value.cityCode } }))
                }
                onClear={() => setData((prev) => ({ ...prev, city: { display: "", name: "", code: "" } }))}
              />
            </dd>
          </dl>
        </div>
        <div className="clearfix">
          <dl>
            <dt>
              체류 주소<span className="essential">*</span>
            </dt>
            <dd>
              <input
                className="w-full h-[34px] outline-none border-b-[1px]"
                type="text"
                id="americaAddress"
                name="address"
                value={data.address}
                onInput={onInput}
                data-validateinput="englishAndNumberAndSpecialOnly"
              />
            </dd>
          </dl>
          <dl>
            <dt>
              우편번호<span className="essential">*</span>
            </dt>
            <dd>
              <input
                className="w-full h-[34px] outline-none border-b-[1px]"
                type="text"
                id="americaZipcode"
                name="zipcode"
                value={data.zipcode}
                onInput={onInput}
                data-validateinput="numberAndDotAndMinusOnly"
              />
            </dd>
          </dl>
        </div>

        {![MAPPED_STATUS_CODE.Completed.id, MAPPED_STATUS_CODE.Cancelled.id].includes(travel?.statusCode?.id) && (
          <div className="center">
            {!isEmpty(americaStayDTO) || isUpdated ? (
              <button type="button" id="addAmericaStayBtn" onClick={handleSubmit} className="btn-default btn-regist mr-4">
                수정
              </button>
            ) : (
              <button type="button" id="addAmericaStayBtn" onClick={handleSubmit} className="btn-default btn-regist">
                등록
              </button>
            )}
          </div>
        )}
      </div>
      <div className="btn-arrow">
        <button type="button" className="btn-default" onClick={() => setActive(!active)} />
      </div>
    </div>
  );
}
