import { useEffect, useState } from "react";
import qs from "qs";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { momentKR } from "@/utils/date";
import hotelLocalStorageUtil from "@/utils/hotelLocalStorgeUtil";

const RecentTripHome = () => {
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);
  const [data, setData] = useState([]);

  function search(data) {
    const params = {
      htlMasterId: data.regionId,
      searchType: "00000001",
      regionId: data.regionId,
      regionUpperId: data.regionUpperId,
      regionNameLong: data.regionNameLong,
      checkIn: data.checkIn,
      checkInText: data.checkInText,
      checkOut: data.checkOut,
      checkOutText: data.checkOutText,
      roomInfo: data.roomInfo,
      roomCount: data.roomCount,
      googleMapPlaceId: data?.googleMapPlaceId || "",
    };
    const queryString = qs.stringify(params, { arrayFormat: "repeat", skipNulls: true, encode: false });
    navigate(`/user/v2/hotel/search?${queryString}`);
  }

  useEffect(() => {
    if (hotelLocalStorageUtil.get(userInfo?.id) != null) {
      const hotelRecentSearchList = hotelLocalStorageUtil.get(userInfo?.id).reverse();
      setData(hotelRecentSearchList || []);
    }
  }, []);

  return (
    <div className="default-wd recent-trip-home type-hotel">
      <h2 className="tit">최근 검색</h2>
      {data.length > 0 ? (
        <div className="box-col" id="recentHotel">
          {data.slice(0, 2).map((hotel, index) => {
            const { id, checkIn, checkOut, nightCount, roomInfo, regionNameLong } = hotel;

            return (
              <div className={`${index === 0 ? "left-col" : "right-col"} round`} key={id}>
                <div className="inner">
                  <a onClick={() => search(hotel)}>
                    <div className="city">
                      <p>{regionNameLong}</p>
                    </div>
                    <p className="period">
                      {momentKR(checkIn, "YYYYMMDD").format("YYYY. MM. DD. ddd")} - {momentKR(checkOut, "YYYYMMDD").format("YYYY. MM. DD. ddd")} (
                      {nightCount}박)
                    </p>
                    <p className="info">
                      성인 {roomInfo.split(",").reduce((acc, item) => acc + Number(item), 0)}명, 객실 {roomInfo.split(",").length}개
                    </p>
                  </a>
                  <a className="btn-delete" onClick={() => hotelLocalStorageUtil.remove(id, userInfo.id)}></a>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="none" id="recentHotelNoData">
          <div className="inner">최근 검색한 호텔이 없습니다.</div>
        </div>
      )}
    </div>
  );
};

export default RecentTripHome;
