import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import qs from "qs";
import { debounce, isEmpty } from "lodash";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionHotelAutoSearch } from "@/store/hotelSlice";
import KrDateRangePicker from "@/app/components/user_v2/common/KrDateRangePicker";
import { momentKR } from "@/utils/date";
import SearchCity from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition/SearchCity";
import { HOTEL_AUTOSEARCH_TYPE } from "@/constants/app";
import { stripHTML } from "@/utils/common";
import ClassMember from "@/app/pages/user_v2/mainHotel/ClassMember";
import { useNavigate } from "react-router-dom";
import { selectIsKrReward, selectUserInfo } from "@/store/userSlice";
import hotelLocalStorageUtil from "@/utils/hotelLocalStorgeUtil";

const HotelSearchForm = (props) => {
  const { filter, setFilter, setOpenModal } = props;

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);
  const isKrReward = useAppSelector(selectIsKrReward);

  const searchCityRef = useRef(null);
  const inputRef = useRef(null);
  const [isAutoSearch, setIsAutoSearch] = useState(false);
  const [isLoadingAutoSearch, setIsLoadingAutoSearch] = useState(false);

  const dateCount = useMemo(() => {
    const { checkIn, checkOut } = filter;
    if (checkIn && checkOut) {
      const diffDate = momentKR(checkOut, "YYYYMMDD").diff(momentKR(checkIn, "YYYYMMDD"), "days");
      return diffDate >= 1 ? `${diffDate}박` : "";
    }
    return "";
  }, [filter.checkIn, filter.checkOut]);

  function onSelectDate(item) {
    const startDate = momentKR(item.startDate);
    const endDate = momentKR(item.endDate);

    setFilter((prev) => ({
      ...prev,
      checkIn: startDate.format("YYYYMMDD"),
      checkInText: startDate.format("MM월 DD일(ddd)"),
      checkOut: endDate.format("YYYYMMDD"),
      checkOutText: endDate.format("MM월 DD일(ddd)"),
    }));
  }

  function search(e) {
    e.preventDefault();
    const isValid = handleValidate(filter);
    if (!isValid) return;

    const queryString = qs.stringify(filter, { arrayFormat: "repeat", skipNulls: true, encode: false });

    hotelLocalStorageUtil.set(filter, userInfo.id);
    navigate(`/user/v2/hotel/search?${queryString}`);
  }

  function handleValidate(filter) {
    const { checkIn, checkOut } = filter;

    const keyword = inputRef.current.value;
    if (isEmpty(keyword)) {
      alert("출장지를 입력해주세요.");
      return false;
    }

    if (isEmpty(checkIn) || isEmpty(checkOut)) {
      alert("숙박날짜를 선택해주세요.");
      return false;
    }

    if (checkIn === checkOut) {
      alert("숙박날짜를 1박 이상 선택해주세요.");
      return false;
    }

    return true;
  }

  function onSelectSearchCity(region) {
    const { regionId, name, countryName, type } = region;

    const newFilter = {
      htlMasterId: "",
      regionId: "",
      regionUpperId: "",
      regionNameLong: name,
    };

    if (countryName) newFilter.regionNameLong += ", " + countryName;
    if (isAutoSearch) newFilter.regionNameLong = region.regionNameLong;

    if (type === HOTEL_AUTOSEARCH_TYPE.HOTEL) {
      newFilter.htlMasterId = region.regionId;
    } else if (type === HOTEL_AUTOSEARCH_TYPE.GOOGLEMAP) {
      newFilter.googleMapPlaceId = regionId;
      newFilter.regionId = "";
      newFilter.regionUpperId = "";
      newFilter.htlMasterId = "";
    } else {
      newFilter.regionId = region.regionId || "";
      newFilter.regionUpperId = region.regionUpperId == "0" ? region.upperCityId : region.regionUpperId;
    }

    newFilter.regionNameLong = stripHTML(newFilter.regionNameLong);

    setIsLoadingAutoSearch(true);
    setFilter((prev) => ({ ...prev, ...newFilter }));
  }

  function onChangeSearchCity(e) {
    const { value } = e.target;
    if (searchCityRef.current) {
      searchCityRef.current.setIsOpen(true);
      searchCityRef.current.calculatePosition();
    }
    setIsLoadingAutoSearch(true);
    setIsAutoSearch(true);
    autoSearchByKeyword(value);
  }

  const autoSearchByKeyword = useCallback(
    debounce(async (val) => {
      setFilter((prev) => ({ ...prev, regionNameLong: val, keyword: val, hotelId: 0, cityId: 0, upperCityId: 0 }));
      await dispatch(actionHotelAutoSearch({ keyword: val })).unwrap();
      setIsLoadingAutoSearch(false);
    }, 200),
    [],
  );

  useEffect(() => {
    if (filter && inputRef.current) {
      inputRef.current.value = filter.regionNameLong || "";
    }
  }, [filter, inputRef]);

  return (
    <form id="hotelSearchForm" onSubmit={search}>
      <div id="searchTicketBox" className="default-wd search-trip-home type-foregin ">
        <p className="copy">어디로 출장가세요? </p>
        <div id="ticketSearch" className="type-round">
          <div className="mid-area">
            <SearchCity
              ref={searchCityRef}
              isAutoSearch={isAutoSearch}
              isLoadingAutoSearch={isLoadingAutoSearch}
              setIsAutoSearch={setIsAutoSearch}
              onSelect={onSelectSearchCity}
            >
              <div className="box-line city">
                <input
                  ref={inputRef}
                  onChange={onChangeSearchCity}
                  placeholder="도시, 지역, 호텔이름, 랜드마크"
                  className="focus-visible:outline-none !w-[318px] placeholder:!text-[#C9CFD8]"
                />
              </div>
            </SearchCity>
            <KrDateRangePicker
              startDate={filter.checkIn ? momentKR(filter.checkIn, "YYYYMMDD") : undefined}
              endDate={filter.checkOut ? momentKR(filter.checkOut, "YYYYMMDD") : undefined}
              isHotelSearch
              onSelectDate={(item) => onSelectDate(item)}
            >
              <div className="box-line period w-[344px] !pr-0">
                <a className={`start btn-datepicker ${filter.checkIn !== "" ? "in" : ""} after:!left-[100px] !box-content`}>
                  <span className="default">숙박 날짜</span>
                  <span id="dateDepature" className="val">
                    {filter.checkInText}
                  </span>
                </a>
                <a className={`end btn-datepicker ${filter.checkOut !== "" ? "in" : ""} !flex items-center gap-[8px]`} style={{ width: 144 }}>
                  <span id="dateArrival" className="val relative">
                    {filter.checkOutText}
                  </span>
                  <span className="text-[#9da9be]">{dateCount}</span>
                </a>
              </div>
            </KrDateRangePicker>

            <ClassMember filter={filter} setFilter={setFilter} />
            <div className="search">
              <button type="submit" className="btn-default">
                검색
              </button>
            </div>
          </div>
          {isKrReward && (
            <div className="main-notice">
              <a onClick={() => setOpenModal("true")}>
                숙소 예약시 대한항공 마일리지 최대 <span className="c-blue">1,500마일리지</span> 적립
              </a>
            </div>
          )}
        </div>
      </div>
    </form>
  );
};

export default HotelSearchForm;
