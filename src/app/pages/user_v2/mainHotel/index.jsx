import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import HotelSearchForm from "@/app/pages/user_v2/mainHotel/HotelSearchForm";
import PopMileage from "@/app/components/user_v2/modal/PopMileage.modal";
import { actionHotelCities } from "@/store/hotelSlice";
import { useAppDispatch } from "@/store";
import BoardHome from "@/app/pages/user_v2/main/boardHome";
import ServiceCsHome from "@/app/pages/user_v2/main/serviceCsHome";
import RecentTripHome from "@/app/pages/user_v2/mainHotel/RecentTripHome";
import MajorTripHome from "@/app/pages/user_v2/mainHotel/MajorTripHome";
import { useMainUrl } from "@/app/hooks/useMainUrl";
import { MENU_USE } from "@/constants/app";
import { useNavigate } from "react-router-dom";
import { URL } from "@/constants/url";
import { actionGetMainAirTravelPlaces } from "@/store/userSlice";

const DEFAULT_FILTER = {
  htlMasterId: "",
  searchType: "00000001",
  regionId: "",
  regionUpperId: "",
  regionNameLong: "",
  checkIn: "",
  checkInText: "",
  checkOut: "",
  checkOutText: "",
  roomInfo: "1",
  roomCount: "1",
  sectionType: "",
  adultCount: "",
};

const MainHotel = () => {
  const dispatch = useAppDispatch();

  const [openModal, setOpenModal] = useState(Cookies.get("noShowPopup") || "true");
  const [filter, setFilter] = useState(DEFAULT_FILTER);
  const { menuUse } = useMainUrl();
  const navigate = useNavigate();

  useEffect(() => {
    dispatch(actionGetMainAirTravelPlaces());
    dispatch(actionHotelCities());
  }, []);

  useEffect(() => {
    if (menuUse === MENU_USE.ONLY_AIR) {
      navigate(URL.AirMain);
    }
  }, [menuUse, navigate]);

  return (
    <div className="pg-main type-hotel" id="container">
      <div className="contents">
        <HotelSearchForm filter={filter} setFilter={setFilter} setOpenModal={setOpenModal} />
        <MajorTripHome setFilter={setFilter} />
        <RecentTripHome />
        <BoardHome />
        <ServiceCsHome />
      </div>

      <PopMileage open={openModal === "true"} isHotel openSetOpenModal={setOpenModal} />
    </div>
  );
};

export default MainHotel;
