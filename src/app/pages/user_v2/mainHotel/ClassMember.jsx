import { useState, useRef, useEffect, useMemo } from "react";
import { useClickOutside } from "@/app/hooks/useClickOutside";
import RoomItem from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition/RoomItem";
import { MAX_HOTEL_CLASS_MEMBER } from "@/constants/app";

const ClassMember = (props) => {
  const { filter, setFilter } = props;

  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const [openLayerClassMember, setOpenLayerClassMember] = useState(false);
  const [roomList, setRoomList] = useState([]);

  const totalPeopleInAllRooms = useMemo(() => {
    return roomList.reduce((acc, room) => acc + Number(room.adultCnt), 0);
  }, [roomList]);

  function toggleOpenLayerClassMember() {
    setOpenLayerClassMember(!openLayerClassMember);
  }

  function addNewRoom() {
    if (roomList.length === MAX_HOTEL_CLASS_MEMBER) return;
    setRoomList([...roomList, { id: roomList.length, adultCnt: 1 }]);
  }

  useEffect(() => {
    const roomInfoValue = filter?.roomInfo ? filter?.roomInfo : "1";
    const roomsWithPeople = roomInfoValue.split(",").map((el, index) => ({ id: index, adultCnt: Number(el) }));
    setRoomList(roomsWithPeople);
  }, [filter.roomInfo]);

  useEffect(() => {
    if (!openLayerClassMember && roomList.length > 0) {
      const newRoomCount = roomList.length;
      const newRoomInfo = roomList.map((room) => room.adultCnt).join(",");
      setFilter((prev) => ({ ...prev, roomCount: newRoomCount, roomInfo: newRoomInfo }));
    }
  }, [openLayerClassMember]);

  useClickOutside(menuRef, buttonRef, () => setOpenLayerClassMember(false));

  return (
    <div className="box-line passenger !box-content">
      <p className="tit txt-short" onClick={toggleOpenLayerClassMember}>
        <a href="javascript:void(0)" className="btn-class-member" id="etcBookingInfoTxt">
          성인 {totalPeopleInAllRooms}명, 객실 {filter?.roomCount || roomList.length}개 <i className="ico" />
        </a>
      </p>

      <div className={`layer-class-member type-hotel ${openLayerClassMember ? "!block" : "!hidden"}`} ref={menuRef}>
        {roomList.map((room) => (
          <RoomItem key={room.id} {...room} setRoomList={setRoomList} />
        ))}
        {roomList.length !== MAX_HOTEL_CLASS_MEMBER && (
          <div className="btn-add-room">
            <button type="button" className="btn-default" onClick={addNewRoom}>
              객실추가
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClassMember;
