import { useEffect, useRef, useState } from "react";
import { Link } from "react-router-dom";

import { getCompanyByDomain, getCompanyBySitecode } from "@/service/login";

import logo from "@/assets/images/cmm/logo.png";
import { duplicateCheckLoginId } from "@/service/join";
import { confirmVerificationCode } from "@/service/common";
import JoinAgreementModal from "@/app/components/user_v2/modal/JoinAgreement.modal";
import { getCompanyList, getDepartmentList, getPositionList, getWorkspaceList } from "@/service/company";
import { validPassword } from "@/utils/common";
import { registerComplete } from "@/service/user";
import RegisterSuccessModal from "@/app/components/user_v2/modal/RegisterSuccess.modal";
import useGeneralVersion from "@/app/hooks/useGeneralVersion";
import { MAP_PASSWORD_CHECK_RESULT } from "@/constants/app";

const FIVE_MINUTES = 300;

export default function JoinForm() {
  const { isGeneralVersion } = useGeneralVersion();
  const [company, setCompany] = useState({});
  const [formData, setFormData] = useState({
    companySitecode: "",
    email: "",
    emailConfirm: "N",
    verificationCode: "",
    name: "",
    password: "",
    passwordCheck: "",
    gender: "Male",
    cellPhoneNumber: "",
    employeeNo: "",
    phoneNumber: "",
    emailVerified: "",
    setPositionId: "",
    isGroup: false,
    positionId: "",
    companyId: "",
  });

  const [isValidEmail, setIsValidEmail] = useState(true);
  const [errorEmailMessage, setErrorEmailMessage] = useState("");

  const [isShowEmailAuthBtn, setIsShowEmailAuthBtn] = useState(true);
  const [countdown, setCountdown] = useState(FIVE_MINUTES); // 5 minutes
  const [isCountingDown, setIsCountingDown] = useState(false);
  const [isVerifyCodeSuccess, setIsVerifyCodeSuccess] = useState(false);
  const [isVerifyCodeFailed, setIsVerifyCodeFailed] = useState(false);
  const [verificationCodeMessage, setVerificationCodeMessage] = useState("");
  const [openJoinAgreementModal, setOpenJoinAgreementModal] = useState(true);
  const [isPasswordMatch, setIsPasswordMatch] = useState(true);
  const [passwordCheckResult, setPasswordCheckResult] = useState(MAP_PASSWORD_CHECK_RESULT.DEFAULT);
  const [isCommon, setIsCommon] = useState(false);
  const [emailConfirmBtnText, setEmailConfirmBtnText] = useState("인증확인");
  const [companyList, setCompanyList] = useState([]);
  const [workspaceList, setWorkspaceList] = useState([]);
  const [positionList, setPositionList] = useState([]);
  const [departmentList, setDepartmentList] = useState([]);
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState(null);
  const [isRegisterSuccess, setIsRegisterSuccess] = useState(false);

  const [checkData, setCheckData] = useState({
    isAgreeService: false,
    isAgreePrivacy: false,
    isAgreePrivacyId: false,
    isAgreePrivacyProvide: false,
    isAgreePrivacyMarketing: false,
  });

  const [birthDate, setBirthDate] = useState({
    year: new Date().getFullYear(),
    month: 1,
    day: 1,
  });

  const [departmentStructure, setDepartmentStructure] = useState([]);

  const formRef = useRef(null);

  const checkCompanySitecode = async () => {
    const res = await getCompanyBySitecode(formData.companySitecode);

    return res.data;
  };

  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${String(minutes).padStart(2, "0")}:${String(remainingSeconds).padStart(2, "0")}`;
  };

  const isValidEmailCheck = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const duplicateCheckEmail = async () => {
    try {
      setIsVerifyCodeSuccess(false);
      setIsValidEmail(true);
      setErrorEmailMessage("");
      let emailDomainList = [];
      if (company?.isGroup) {
        companyList.map((item) => {
          emailDomainList.push(...item.emailDomains);
        });
      } else {
        emailDomainList = company.emailDomains;
      }

      if (formData.email.trim() == "") {
        setIsValidEmail(false);
        setErrorEmailMessage("이메일 주소를 입력해주세요.");
        return;
      }

      if (!isValidEmailCheck(formData.email)) {
        setIsValidEmail(false);
        setErrorEmailMessage("이메일 형식이 올바르지 않습니다.");
        return;
      }

      if (isGeneralVersion) {
        const res = await checkCompanySitecode();
        if (res.emailDomains.includes(formData.email.split("@")[1])) {
          await duplicateCheckLoginId({
            email: formData.email,
          });
          setCompany(res);
          setFormData({ ...formData, companyId: res.id });
        } else {
          alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
          formRef.current.elements.email.focus();
          return;
        }
      } else {
        if (emailDomainList?.includes(formData.email.split("@")[1])) {
          await duplicateCheckLoginId({
            email: formData.email,
          });
        } else {
          alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
          formRef.current.elements.email.focus();
          return;
        }
      }
      setIsValidEmail(true);
      setIsCountingDown(true);
      setIsShowEmailAuthBtn(false);
    } catch (error) {
      if (error.response.status == 400) {
        setIsValidEmail(false);
        setIsCountingDown(false);
        setIsShowEmailAuthBtn(true);
        setErrorEmailMessage("이미 가입된 회원입니다.");
      } else {
        alert("가입할 수 없는 이메일 도메인입니다.\n계약된 거래처 이메일로만 회원가입이 가능합니다.");
      }
    }
  };

  const handleConfirmVerificationCode = async () => {
    try {
      const res = await confirmVerificationCode({ email: formData.email, verificationCode: formData.verificationCode });
      setEmailConfirmBtnText("인증확인");
      setFormData({ ...formData, emailVerified: formData.email, emailConfirm: "Y" });
      setIsShowEmailAuthBtn(false);
      setIsVerifyCodeSuccess(res.status === 200);
      setIsVerifyCodeFailed(res.status !== 200);
      formRef.current.elements.email.readOnly = true;
    } catch (error) {
      if (error.response.data.error_code === "E400_INVALID_OTP") {
        setEmailConfirmBtnText("다시 인증하기");
        setFormData({ ...formData, emailConfirm: "N" });
        setVerificationCodeMessage("인증번호가 올바르지 않습니다. 다시 진행하여 주십시요.");
      } else if (error.response.data.error_code === "E400_OTP_EXPIRED") {
        setEmailConfirmBtnText("다시 인증하기");
        setFormData({ ...formData, emailConfirm: "N" });
        setVerificationCodeMessage("인증시간이 만료 되었습니다. 다시 진행하여 주십시요.");
      }
      setIsVerifyCodeSuccess(false);
      setIsVerifyCodeFailed(true);
    }
  };

  const confirmPassword = async () => {
    if (formData.password !== formData.passwordCheck) {
      setIsPasswordMatch(false);
    } else {
      setIsPasswordMatch(true);
      const res = validPassword(formData.password);
      setPasswordCheckResult(MAP_PASSWORD_CHECK_RESULT[res]);
      if (res == "OK") {
        setIsPasswordMatch(true);
      } else {
        setIsPasswordMatch(false);
      }
    }
  };

  const handleChangeWorkspace = async (e) => {
    setSelectedWorkspaceId(e.target.value);
    setDepartmentStructure([]);
    if (e.target.value !== "-1") {
      const res = await getDepartmentList(e.target.value);
      setDepartmentList(res.data);
    } else {
      setDepartmentList([]);
    }
  };

  const handleChangeDepartment = async (value, level) => {
    if (!value) return;

    const res = await getDepartmentList(selectedWorkspaceId, value);

    setDepartmentStructure((prev) => {
      const newStructure = [...prev];
      newStructure.splice(level);
      newStructure[level] = {
        id: value,
        children: res.data,
      };
      return newStructure;
    });
  };

  const joinValidCheck = () => {
    const cellRegExp = /^01([0|1|6|7|8|9])([1-9])([0-9]{2,3})([0-9]{4})$/;
    const phoneRegExp = /^0(2|3[1-3]|4[1-4]|5[1-5]|6[1-4])([0-9]{3,4})([0-9]{4})$/;
    const departmentId = departmentStructure[departmentStructure.length - 1]?.id || "";
    if (formData.email.trim() == "") {
      alert("이메일 아이디를 입력해주세요.");
      formRef.current.elements.email.focus();
      return false;
    } else if (formData.emailConfirm != "Y") {
      alert("이메일 아이디 인증하기를 해주세요.");
      formRef.current.elements.verificationCode.focus();
      return false;
    } else if (formData.name.trim() == "") {
      alert("이름을 입력해주세요.");
      formRef.current.elements.name.focus();
      return false;
    } else if (formData.password.trim() == "") {
      alert("비밀번호를 입력해주세요.");
      formRef.current.elements.password.focus();
      return false;
    } else if (formData.passwordCheck.trim() == "") {
      alert("비밀번호 확인을 입력해주세요.");
      formRef.current.elements.passwordCheck.focus();
      return false;
    } else if (birthDate.year.toString().trim() == "") {
      alert("생년월일 년을 선택해주세요.");
      formRef.current.elements.birthY.focus();
      return false;
    } else if (birthDate.month.toString().trim() == "") {
      alert("생년월일 월을 선택해주세요.");
      formRef.current.elements.birthM.focus();
      return false;
    } else if (birthDate.day.toString().trim() == "") {
      alert("생년월일 일을 선택해주세요.");
      formRef.current.elements.birthD.focus();
      return false;
    } else if (formData.cellPhoneNumber.trim() == "") {
      alert("휴대전화를 입력해주세요.");
      formRef.current.elements.cellPhoneNumber.focus();
      return false;
    } else if (formData.cellPhoneNumber.trim() != "" && !cellRegExp.test(formData.cellPhoneNumber.trim())) {
      alert("휴대전화가 올바르지 않습니다.");
      formRef.current.elements.cellPhoneNumber.focus();
      return false;
    } else if (formData.phoneNumber.trim() != "" && !phoneRegExp.test(formData.phoneNumber)) {
      alert("전화번호가 올바르지 않습니다.");
      formRef.current.elements.phoneNumber.focus();
      return false;
    } else if (!isCommon && selectedWorkspaceId == "" && !["sksiltron.tourvis.com"].includes(window.location.hostname) && company?.isGroup == false) {
      alert("사업장을 선택해주세요.");
      return false;
    } else if (!isCommon && departmentId == "" && !["sksiltron.tourvis.com"].includes(window.location.hostname) && company?.isGroup == false) {
      alert("소속부서를 선택해주세요.");
      formRef.current.elements.departmentId.focus();
      return false;
    } else if (formData.companyId.toString().trim() == "" && company?.isGroup == true) {
      alert("회사 를 선택해주세요.");
      return false;
    } else {
      return true;
    }
  };

  const handleRegister = async () => {
    try {
      if (!joinValidCheck()) return;

      const data = {
        email: formData.email,
        verificationCode: formData.verificationCode,
        password: formData.password,
        name: formData.name,
        gender: formData.gender,
        birthday: `${birthDate.year}${birthDate.month < 10 ? `0${birthDate.month}` : birthDate.month}${birthDate.day < 10 ? `0${birthDate.day}` : birthDate.day}`,
        cellPhoneNumber: formData.cellPhoneNumber,
        companyId: company?.isGroup ? +formData.companyId : company.id,
        workspaceId: +selectedWorkspaceId || null,
        departmentId: departmentStructure.length > 0 ? +departmentStructure[departmentStructure.length - 1].id : "",
        positionId: formData.positionId || null,
        employeeNo: formData.employeeNo || null,
        phoneNumber: formData.phoneNumber || null,
        isAgreePrivacyMarketing: checkData.isAgreePrivacyMarketing,
      };

      await registerComplete(data);
      setIsRegisterSuccess(true);
    } catch (error) {
      setIsRegisterSuccess(false);
      alert(
        "회원가입 중 오류가 발생하였습니다.\n다시 시도해 주세요.\n지속적으로 시스템 오류가 발생하면 <EMAIL> 해당 메일로 접수해주시기 바랍니다.",
      );
    }
  };

  useEffect(() => {
    let timer;
    if (isCountingDown && countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => prev - 1);
      }, 1000);
    } else if (countdown === 0) {
      setIsCountingDown(false);
      setIsShowEmailAuthBtn(true);
      setCountdown(FIVE_MINUTES);
    }
    return () => clearInterval(timer);
  }, [isCountingDown, countdown]);

  useEffect(() => {
    if (isGeneralVersion) {
      setIsCommon(true);
      return;
    }

    const data = {
      domain: window.location.hostname == "localhost" ? "hnbtms.tourvis.com" : window.location.hostname,
    };
    const fetchData = async () => {
      try {
        const res = await getCompanyByDomain(data);
        setCompany(res.data);
        const [resCompanyList, resPositionList, resWorkspaceList] = await Promise.all([
          getCompanyList(res.data.id),
          getPositionList(res.data.id),
          getWorkspaceList(res.data.id),
        ]);
        setCompanyList(resCompanyList.data);
        setPositionList(resPositionList.data);
        setWorkspaceList(resWorkspaceList.data);
      } catch (error) {
        console.log(error);
      }
    };
    fetchData();
  }, [isGeneralVersion]);

  return (
    <form id="joinForm" name="joinForm" ref={formRef}>
      <div id="warp">
        <div id="header">
          <div className="clearfix">
            <div className="left-col">
              <h1>
                <Link to={"/#"} onClick={() => (window.location.href = "/login")}>
                  <img
                    src={
                      isGeneralVersion || !company || !company.homepageSetting || company.homepageSetting.isUseDefaultLogo
                        ? logo
                        : import.meta.env.VITE_STORAGE_URL +
                          "/" +
                          company.homepageSetting?.loginLogoAttachFile?.fileUploadPath +
                          company.homepageSetting?.loginLogoAttachFile?.tempFileName
                    }
                    alt="LOGO"
                    style={{ width: "auto" }}
                  />
                </Link>
              </h1>
            </div>
          </div>
        </div>
        <div id="container" className="clearfix default-wd pg-login">
          <div className="contents join-cotn">
            <h2 className="title">회원가입</h2>
            <div className="box-form">
              <div className="box-division">
                <p className="tit">기본 정보</p>
                <dl>
                  {isCommon && (
                    <>
                      <dt>
                        거래처코드<span className="essential">*</span>
                      </dt>
                      <dd>
                        <div className="box-col">
                          <input
                            type="text"
                            name="companySitecode"
                            value={formData.companySitecode}
                            onChange={(e) => setFormData({ ...formData, companySitecode: e.target.value })}
                          />
                        </div>
                      </dd>
                    </>
                  )}
                  <dt>
                    이메일 아이디<span className="essential">*</span>
                  </dt>
                  <dd className="mail">
                    <div className="box-col">
                      <input
                        type="text"
                        id="email"
                        name="email"
                        maxLength={100}
                        autoComplete="off"
                        value={formData.email}
                        onChange={(e) => {
                          setFormData({ ...formData, email: e.target.value });
                          setIsValidEmail(true);
                        }}
                      />
                    </div>
                    <button
                      type="button"
                      id="emailAuthBtn"
                      style={{ display: isShowEmailAuthBtn ? "block" : "none" }}
                      className="btn-default btn-white"
                      onClick={duplicateCheckEmail}
                    >
                      인증메일 발송
                    </button>
                    <button
                      type="button"
                      id="resendEmailAuthBtn"
                      style={{ display: isShowEmailAuthBtn || isVerifyCodeSuccess ? "none" : "block" }}
                      className="btn-default btn-gray"
                      onClick={() => {
                        if (!isCountingDown) {
                          duplicateCheckEmail();
                        }
                      }}
                    >
                      인증메일 재발송
                    </button>
                    <span id="retryEmailTime" className="time" style={{ display: isShowEmailAuthBtn || isVerifyCodeSuccess ? "none" : "block" }}>
                      재발송 {formatTime(countdown)}
                    </span>
                    <p id="idMessage" className="validate" style={{ display: !isValidEmail ? "block" : "none" }}>
                      {errorEmailMessage}
                    </p>
                  </dd>
                  <dt>
                    인증번호<span className="essential">*</span>
                  </dt>
                  <dd className="code">
                    <div className="box-col">
                      <input
                        type="text"
                        id="verificationCode"
                        value={formData.verificationCode}
                        name="verificationCode"
                        onChange={(e) => {
                          setFormData({ ...formData, verificationCode: e.target.value });
                          setIsVerifyCodeFailed(false);
                        }}
                      />
                    </div>
                    <button
                      type="button"
                      id="emailConfirmBtn"
                      name="emailConfirmBtn"
                      style={{
                        display: isVerifyCodeSuccess ? "none" : "block",
                      }}
                      className="btn-default btn-white"
                      onClick={handleConfirmVerificationCode}
                    >
                      {emailConfirmBtnText}
                    </button>
                    <button
                      type="button"
                      id="emailCompleteBtn"
                      className="btn-default btn-gray"
                      style={{ display: isVerifyCodeSuccess ? "block" : "none" }}
                    >
                      인증완료
                    </button>
                    <p id="confirmMessage" className="validate" style={{ display: isVerifyCodeFailed ? "block" : "none", fontSize: 12 }}>
                      {verificationCodeMessage}
                    </p>
                  </dd>
                  <dt>
                    이름<span className="essential">*</span>
                  </dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="text"
                        id="name"
                        name="name"
                        maxLength={30}
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      />
                    </div>
                  </dd>
                  <dt>
                    비밀번호<span className="essential">*</span>
                  </dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="password"
                        id="password"
                        name="password"
                        maxLength={20}
                        autoComplete="off"
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      />
                    </div>
                    <p id="passwordCheckMsg" className="validate" style={{ display: "none", fontSize: 12 }}>
                      영문자, 숫자, 특수문자를 조합하여 8~20자로 입력하세요.
                    </p>
                  </dd>
                  <dt>
                    비밀번호 확인<span className="essential">*</span>
                  </dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="password"
                        id="passwordCheck"
                        name="passwordCheck"
                        maxLength={20}
                        autoComplete="off"
                        value={formData.passwordCheck}
                        onChange={(e) => setFormData({ ...formData, passwordCheck: e.target.value })}
                        onBlur={confirmPassword}
                      />
                    </div>
                    <p
                      id="passwordConfirmMsg"
                      className="validate"
                      style={{
                        display: isPasswordMatch ? "none" : "block",
                        fontSize: 12,
                      }}
                    >
                      {passwordCheckResult}
                    </p>
                  </dd>
                  <dt>
                    성별<span className="essential">*</span>
                  </dt>
                  <dd className="gender">
                    <div className="box-col">
                      <label className="form-radio">
                        <input
                          type="radio"
                          id="genderM"
                          name="gender"
                          defaultValue="Male"
                          checked={formData.gender === "Male"}
                          onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                        />
                        <span>남성</span>
                      </label>
                      <label className="form-radio">
                        <input
                          type="radio"
                          id="genderF"
                          name="gender"
                          defaultValue="Female"
                          checked={formData.gender === "Female"}
                          onChange={(e) => setFormData({ ...formData, gender: e.target.value })}
                        />
                        <span>여성</span>
                      </label>
                    </div>
                  </dd>
                  <dt>
                    생년월일<span className="essential">*</span>
                  </dt>
                  <dd>
                    <div className="box-col box-ymd">
                      <span className="form-select yy">
                        <select id="birthY" onChange={(e) => setBirthDate({ ...birthDate, year: e.target.value })} value={birthDate.year}>
                          <option value="">생년</option>
                          {Array.from({ length: 100 }, (_, i) => (
                            <option key={i} value={new Date().getFullYear() - i}>
                              {new Date().getFullYear() - i}
                            </option>
                          ))}
                        </select>
                      </span>
                      <span className="form-select mm">
                        <select id="birthM" onChange={(e) => setBirthDate({ ...birthDate, month: e.target.value })} value={birthDate.month}>
                          {Array.from({ length: 12 }, (_, i) => (
                            <option key={i} value={i + 1}>
                              {i + 1}월
                            </option>
                          ))}
                        </select>
                      </span>
                      <span className="form-select dd" onChange={(e) => setBirthDate({ ...birthDate, day: e.target.value })} value={birthDate.day}>
                        <select id="birthD">
                          {Array.from({ length: new Date(birthDate.year, birthDate.month, 0).getDate() }, (_, i) => (
                            <option key={i} value={i + 1}>
                              {i + 1}일
                            </option>
                          ))}
                        </select>
                      </span>
                    </div>
                  </dd>
                  <dt>
                    휴대전화<span className="essential">*</span>
                  </dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="text"
                        id="cellPhoneNumber"
                        name="cellPhoneNumber"
                        maxLength={11}
                        value={formData.cellPhoneNumber}
                        onChange={(e) => {
                          const value = e.target.value.replace(/[^0-9]/g, "");
                          setFormData({ ...formData, cellPhoneNumber: value });
                        }}
                      />
                      <input type="hidden" id="isSmsReceiveY" name="isSmsReceiveYn" defaultValue="true" />
                    </div>
                    <p className="validate" style={{ fontSize: 12 }}>
                      숫자만 입력 하세요.
                    </p>
                  </dd>
                </dl>
                <p className="desc-essential">
                  <span>*</span> 표시는 필수입력 항목 이오니 반드시 입력해 주세요.
                </p>
              </div>
              <div className="box-division">
                <p className="tit">회사 정보</p>
                <dl>
                  <dt>
                    회사
                    {company?.isGroup && <span className="essential">*</span>}
                  </dt>
                  <dd>
                    {company?.isGroup ? (
                      <div className="box-col">
                        <span className="form-select w100">
                          <select name="" id="setCompanyId" onChange={(e) => setFormData({ ...formData, companyId: e.target.value })}>
                            <option value="">선택하세요.</option>
                            {companyList.map((item) => (
                              <option key={item.id} value={item.id}>
                                {item.name}
                              </option>
                            ))}
                          </select>
                        </span>
                      </div>
                    ) : (
                      <div className="box-col">
                        <span className="form-select w100">{company.name}</span>
                      </div>
                    )}
                  </dd>
                  {!isCommon && (
                    <>
                      {company.btmsSetting?.url !== "sksiltron.tourvis.com" && !company?.isGroup && (
                        <>
                          <dt>
                            사업장<span className="essential">*</span>
                          </dt>
                          <dd>
                            <div className="box-col">
                              <span className="form-select w100">
                                <select id="setWorkspaceId" onChange={handleChangeWorkspace}>
                                  <option value="-1">선택하세요.</option>
                                  {workspaceList.map(
                                    (item) =>
                                      item.id !== 946626 && (
                                        <option key={item.id} value={item.id}>
                                          {item.name}
                                        </option>
                                      ),
                                  )}
                                </select>
                              </span>
                            </div>
                          </dd>
                        </>
                      )}
                      {company.btmsSetting?.url !== "sksiltron.tourvis.com" && !company?.isGroup && (
                        <>
                          <dt>
                            소속부서<span className="essential">*</span>
                          </dt>
                          <dd id="departmentSelectBoxArea">
                            <div className="box-col">
                              <span className="form-select w100">
                                <select onChange={(e) => handleChangeDepartment(e.target.value, 0)}>
                                  <option value="">선택하세요.</option>
                                  {departmentList.map((item) => (
                                    <option key={item.id} value={item.id}>
                                      {item.name}
                                    </option>
                                  ))}
                                </select>
                              </span>
                            </div>

                            {departmentStructure.map(
                              (dept, index) =>
                                dept.children &&
                                dept.children.length > 0 && (
                                  <div className="box-col" key={dept.id}>
                                    <span className="form-select w100">
                                      <select onChange={(e) => handleChangeDepartment(e.target.value, index + 1)}>
                                        <option value="">선택하세요.</option>
                                        {dept.children.map((item) => (
                                          <option key={item.id} value={item.id}>
                                            {item.name}
                                          </option>
                                        ))}
                                      </select>
                                    </span>
                                  </div>
                                ),
                            )}
                          </dd>
                        </>
                      )}
                      {company.btmsSetting?.url !== "sksiltron.tourvis.com" && !company?.isGroup && (
                        <>
                          <dt>
                            직급<span className="essential">*</span>
                          </dt>
                          <dd>
                            <div className="box-col">
                              <span className="form-select w100">
                                <select id="positionId" name="position.id" onChange={(e) => setFormData({ ...formData, positionId: e.target.value })}>
                                  {positionList.map((i) => {
                                    return (
                                      <option key={i.id} value={i.id}>
                                        {i.name}
                                      </option>
                                    );
                                  })}
                                </select>
                              </span>
                            </div>
                          </dd>
                        </>
                      )}
                    </>
                  )}
                  <dt>사번</dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="text"
                        id="employeeNo"
                        name="employeeNo"
                        maxLength={20}
                        defaultValue=""
                        value={formData.employeeNo}
                        onChange={(e) => setFormData({ ...formData, employeeNo: e.target.value })}
                      />
                    </div>
                  </dd>
                  <dt>회사전화</dt>
                  <dd>
                    <div className="box-col">
                      <input
                        type="text"
                        id="phoneNumber"
                        name="phoneNumber"
                        maxLength={11}
                        defaultValue=""
                        value={formData.phoneNumber}
                        onChange={(e) => {
                          const value = e.target.value.replace(/[^0-9]/g, "");
                          setFormData({ ...formData, phoneNumber: value });
                        }}
                      />
                    </div>
                    <p className="validate" style={{ fontSize: 12 }}>
                      숫자만 입력 하세요.
                    </p>
                  </dd>
                </dl>
                <p className="desc-essential">
                  <span>*</span> 표시는 필수입력 항목 이오니 반드시 입력해 주세요.
                </p>
              </div>
            </div>

            <div className="btns-bottom">
              <button type="button" id="joinProcessBtn" onClick={handleRegister} className="btn-default btn-blue">
                확인
              </button>
            </div>
          </div>
        </div>
      </div>

      <JoinAgreementModal
        open={openJoinAgreementModal}
        onCLose={() => setOpenJoinAgreementModal(false)}
        checkData={checkData}
        setCheckData={setCheckData}
        isGeneralVersion={isGeneralVersion}
        company={company}
      />

      <RegisterSuccessModal
        open={isRegisterSuccess}
        onClose={() => setIsRegisterSuccess(false)}
        isGeneralVersion={isGeneralVersion}
        company={company}
      />
    </form>
  );
}
