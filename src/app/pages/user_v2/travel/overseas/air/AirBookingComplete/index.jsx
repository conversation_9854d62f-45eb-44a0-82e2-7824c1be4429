import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { comma, getSeatTypeName, nvl, returnPhoneNumber } from "@/utils/common";
import { useAppSelector, useAppDispatch } from "@/store";
import { selectTravelRuleBase, actionTravelRuleBase } from "@/store/airSlice";
import { BTMS_MANAGER_CODE, EXECPT_TIME_TYPE } from "@/constants/app";
import { isEmpty } from "lodash";
import {
  actionBtmsManager,
  actionDocumentNumbers,
  actionReservationTravelView,
  selectBtmsManager4Visa,
  selectReservationTravelView,
} from "@/store/travelViewSlice";
import PopMileage from "@/app/components/user_v2/modal/PopMileage.modal";
import PopViewTicketDateWarning from "@/app/components/user_v2/modal/PopViewTicketDateWarning.modal";
import { selectUserInfo } from "@/store/userSlice";
import { getTravelCities } from "@/utils/app";

export default function AirBookingComplete() {
  const { travelId } = useParams();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const travelRuleBase = useAppSelector(selectTravelRuleBase);
  const {
    data: { travel },
    documentNumbers,
  } = useAppSelector(selectReservationTravelView);
  const btmsManager4Visa = useAppSelector(selectBtmsManager4Visa);
  const { userInfo } = useAppSelector(selectUserInfo);

  const [openPopMileageModal, setOpenPopMileageModal] = useState(true);
  const [openPopViewTicketDateWarning, setOpenPopViewTicketDateWarning] = useState(true);

  function viewReservation() {
    navigate("/user/v2/reservation/travel/view/" + travelId);
  }

  useEffect(() => {
    if (travel?.bookingAir?.isViewTicketDateWarning) {
      setOpenPopViewTicketDateWarning(true);
    }
  }, [travel]);

  useEffect(() => {
    dispatch(actionTravelRuleBase());
    dispatch(actionReservationTravelView({ travelId }));
    dispatch(actionDocumentNumbers({ travelId }));
    dispatch(actionBtmsManager({ managerType: BTMS_MANAGER_CODE.PASSPORT_VISA }));
  }, [travelId]);

  return (
    <div id="container" className="pg-reserv ">
      <div className="step-reservation">
        <ol>
          <li>
            <div className="num">1</div>
            <p className="tit">예약 요청</p>
          </li>
          <li className="active">
            <div className="num">2</div>
            <p className="tit">예약 완료</p>
          </li>
        </ol>
      </div>
      <div className="contents default-wd clearfix !block">
        {/* left-side */}
        <div className="left-side ">
          <div className="left-side sec-resev-request">
            <dl>
              <dt>{getTravelCities(travel).join(" - ")} 예약을 요청했습니다.</dt>
              <dd>출장관리자와 여행사 예약 담당자의 담당자 확인 후 발권이 진행 됩니다.</dd>
            </dl>
            <p className="code">
              BTMS 예약번호 <strong>{travelId}</strong>
            </p>
          </div>
          <div className="left-side" style={{ marginTop: 50, textAlign: "center" }}>
            <a
              href="https://www.0404.go.kr/dev/notice_view.mofa?id=ATC0000000009020&pagenum=1&mst_id=MST0000000000045&st=title&stext="
              target="_blank"
            >
              외교부 해외안전여행 사이트 (각국의 입국허가 요건) 바로가기
            </a>
          </div>
          {!isEmpty(btmsManager4Visa) && (
            <div className="left-side" style={{ marginTop: 50, textAlign: "center" }}>
              비자 신청 문의 : {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "FIRST")} -{" "}
              {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "MIDDLE")} -{" "}
              {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "END")}
            </div>
          )}
        </div>
        <div className="right-side sec-pay-to">
          <div className="box-line">
            <div className="txt-big clearfix">
              <p className="fl-l">총 결제요금</p>
              <p className="fl-r c-price">
                <strong>{comma(travel?.bookingAir?.totalAmount)}</strong>원
              </p>
            </div>
            <dl className="fare-detail">
              <dt>성인 {travel?.bookingAir?.adultCount}명</dt>
              <dd>
                <span className="tit">항공료</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.fareAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
              <dd>
                <span className="tit">TAX</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.taxAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
              <dd>
                <span className="tit">발권수수료</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.commissionAmount / (travel?.bookingAir?.adultCount || 1))} X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
            </dl>
          </div>
          <div className="box-line">
            <p className="txt-big">출장자 규정</p>
            <ul className="brakedown">
              <li>
                결재 요청 의견 출발일 기준
                <span>{travelRuleBase?.data?.travelLimitDay === 0 ? "제한없음" : `${nvl(travelRuleBase?.data?.travelLimitDay, "")}일 전`}</span>
              </li>
              <li>
                기본 좌석등급
                <span>
                  {travelRuleBase?.data?.baseSeatTypeCode} {getSeatTypeName(travelRuleBase?.data?.baseSeatTypeCode)}
                </span>
              </li>
              {travelRuleBase?.data?.isExceptUse && (
                <li>
                  {EXECPT_TIME_TYPE[travelRuleBase?.data?.exceptTimeType]}
                  <span>{travelRuleBase?.data?.exceptBordingTime}</span>
                  시간 이상 일 경우
                  <br />
                  <span>{getSeatTypeName(travelRuleBase?.data?.exceptSeatTypeCode)}</span>
                  허용
                </li>
              )}
            </ul>
            <div className="opinion-insert">
              <textarea name="violationReason" id="violationReason" disabled>
                {travel?.violationReason}
              </textarea>
            </div>
          </div>

          {!isEmpty(documentNumbers) && (
            <div className="box-line">
              <p className="txt-big">
                {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  : "문서번호"}
                <span className="emp">*</span>
              </p>
              <div className="form-doc-number">
                <ul className="doc-list">
                  {documentNumbers?.map((documentNumber, index) => (
                    <li key={index + documentNumber}>{documentNumber}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}
          <div className="box-applys">
            <button type="button" className="btn-default btn-request" onClick={viewReservation}>
              예약 내역 보기
            </button>
          </div>
        </div>
      </div>

      <PopMileage open={openPopMileageModal} openSetOpenModal={() => setOpenPopMileageModal(false)} />
      <PopViewTicketDateWarning open={openPopViewTicketDateWarning} openSetOpenModal={() => setOpenPopViewTicketDateWarning(false)} />
    </div>
  );
}
