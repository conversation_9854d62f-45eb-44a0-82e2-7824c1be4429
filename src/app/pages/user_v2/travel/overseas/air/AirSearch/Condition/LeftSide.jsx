import { useState, useMemo, Fragment } from "react";
import { useSelector } from "react-redux";
import IOSSlider from "@/app/components/user_v2/common/IOSSlider.jsx";
import { selectAirSearch, selectTravelRuleBase } from "@/store/airSlice.js";
import AirUtil from "@/utils/airUtil.js";
import { nvl } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { ALLIANCE_KR } from "@/constants/app";
import { selectUserInfo } from "@/store/userSlice";

const VIA = [
  { id: 1, value: "0", checked: true, label: "직항" },
  { id: 2, value: "1", checked: true, label: "1회" },
  { id: 3, value: "2", checked: true, label: "2회 이상" },
];

const FARE_TYPES = ["기업운임", "일반운임"];

const LeftSide = (props) => {
  const { filter, condition, conditionTime, setCondition, setConditionTime, uniqueAirlines, uniqueAlliances, resetFilter } = props;
  const { userInfo } = useSelector(selectUserInfo);
  const airSearch = useSelector(selectAirSearch);

  const travelRuleBase = useSelector(selectTravelRuleBase);
  const [sliceAirlineMenu, setSliceAirlineMenu] = useState(3);
  const [rangeTime, setRangeTime] = useState("startTime");

  const keyRangeTime = useMemo(() => (rangeTime === "startTime" ? "departureTime" : "arrivalTime"), [rangeTime]);

  function handleChangeCondition(e) {
    const { name, value, checked } = e.target;
    setCondition((prev) => ({
      ...prev,
      [name]: checked ? [...prev[name], value] : prev[name].filter((el) => el !== value),
    }));
  }

  function handleChangeSlider(item, value, type) {
    const newTime = conditionTime[keyRangeTime].map((el) => {
      if (el.id === item.id) {
        return { ...el, value };
      }
      return el;
    });
    if (type === "CHANGE") {
      setConditionTime((prev) => ({ ...prev, [keyRangeTime]: newTime }));
    } else {
      setCondition((prev) => ({ ...prev, [keyRangeTime]: newTime }));
    }
  }

  function handleChangeSliderDuration(name, value, type) {
    const newTime = conditionTime[name];
    newTime.value = value;
    if (type === "CHANGE") {
      setConditionTime((prev) => ({ ...prev, [name]: newTime }));
    } else {
      setCondition((prev) => ({ ...prev, [name]: newTime }));
    }
  }

  function formatTime(decimal) {
    const hours = Math.floor(decimal);
    const minutes = Math.round((decimal - hours) * 60);
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  }

  function convertDecimalHoursToKoreanTime(decimalHours) {
    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);
    return `${hours}시간 ${minutes}분`;
  }

  function getCityText(travelRuleBaseDTO) {
    let cityText = "";
    for (let i = 0; i < travelRuleBaseDTO?.exceptCityList.length; i++) {
      cityText += nvl(travelRuleBaseDTO?.exceptCityList[i].cityName, "");

      if (i < travelRuleBaseDTO.exceptCityList.length - 1) {
        cityText += ", ";
      }
    }
    return cityText;
  }

  function deselectAllAirline() {
    setCondition((prev) => ({ ...prev, airlines: [] }));
  }

  function selectAllAirline() {
    const flattened = airSearch.data.journeyInfos.flatMap((obj) => Object.values(obj).flat());
    const airlines = flattened.map((obj) => ({ airline: obj.airline, name: obj.airlineName }));
    const uniqueAirlines = [...new Map(airlines.map((item) => [item["airline"], item])).values()];
    setCondition((prev) => ({ ...prev, airlines: uniqueAirlines.map((el) => el.airline) }));
  }

  return (
    <div className="left-side">
      {!userInfo?.workspace?.company?.parent && (
        <div className="sec-regulation">
          <h2>나의 출장 규정</h2>
          {travelRuleBase?.data?.isExceptUse && (
            <dl className="time">
              <dt>예약 규정</dt>
              <dd>
                <strong>{AirUtil.getExceptTimeType(travelRuleBase?.data?.exceptTimeType)}</strong> 기준
              </dd>
            </dl>
          )}
          <dl className="caleandar">
            <dt>사전 예약</dt>
            <dd className="mb-0">
              출발일 기준 <strong>{typeof travelRuleBase?.data == "undefined" ? 0 : travelRuleBase?.data?.travelLimitDay}일 전</strong>
            </dd>
          </dl>
          <dl className="seat-df">
            <dt>기본 좌석등급 </dt>
            <dd className="mb-0">
              <strong>{typeof travelRuleBase?.data === "undefined" ? "" : AirUtil.getSeatTypeName(travelRuleBase?.data?.baseSeatTypeCode)}</strong>
            </dd>
          </dl>
          {travelRuleBase?.data?.isExceptUse && (
            <dl className="seat-mx">
              <dt>최대 허용 좌석등급</dt>
              <dd>
                <strong>{AirUtil.getSeatTypeName(travelRuleBase?.data?.exceptSeatTypeCode)}</strong>
              </dd>
            </dl>
          )}
          {travelRuleBase?.data?.isExceptUse && (
            <Fragment>
              <dl className="condition">
                <dt>승급 허용 조건</dt>
                <dd>
                  <strong>{travelRuleBase?.data?.exceptBordingTime} 시간 이상 탑승</strong>
                  {getCityText(travelRuleBase?.data) !== "" ? (
                    <Fragment>
                      <br /> 단, + <strong>{getCityText(travelRuleBase?.data)} 는 조건 충족 없이 예약 가능</strong>
                    </Fragment>
                  ) : (
                    ""
                  )}
                </dd>
              </dl>
              <dl className="etc">
                <dt>기타사항 선호</dt>
                <dd>{nvl(travelRuleBase?.data?.etc, "-")}</dd>
              </dl>
            </Fragment>
          )}
        </div>
      )}
      {/* // 나의 출장 규정 */}
      {/* 검색결과 내 검색 */}
      <div className="sec-in-search">
        <h2>검색결과 내 검색</h2>
        <form>
          <dl>
            <dt>운임 조건</dt>
            {FARE_TYPES.map((item, index) => (
              <dd className="mb-0" key={item + index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="arrayCorporateFare"
                    defaultValue={item}
                    checked={condition.arrayCorporateFare.includes(item)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">
                    {item}
                    {index === 0 && (
                      <div className="tooltip-custom">
                        <div className="tooltip-custom-icon" />
                        <div className="tooltip-custom-text !w-[390px]">
                          기업 고객을 대상으로 항공사가 제공하는 할인된 운임으로 변경 <br /> 및 취소가 유연하며 수수료 감면 또는 면제 될 수 있는
                          요금입니다.
                        </div>
                      </div>
                    )}
                  </span>
                </label>
              </dd>
            ))}
          </dl>
          <dl>
            <dt>경유</dt>
            {VIA.map((item, index) => (
              <dd className="mb-0" key={item.id + index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="arrayStopOverCount"
                    defaultValue={item.value}
                    className="item"
                    checked={condition.arrayStopOverCount.includes(item.value)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">{item.label}</span>
                </label>
              </dd>
            ))}
          </dl>
          <dl>
            <dt>얼라이언스</dt>
            {uniqueAlliances.map((item, index) => (
              <dd className="mb-0" key={index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="alliances"
                    defaultValue={item}
                    checked={condition.alliances.includes(item)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">{ALLIANCE_KR[item] || ""}</span>
                </label>
              </dd>
            ))}
          </dl>
          <div className="airline">
            <dl id="airlineSearchFilter">
              <dt>항공사</dt>
              {uniqueAirlines.slice(0, sliceAirlineMenu).map((item, index) => (
                <dd key={item.airline + index}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="airlines"
                      defaultValue={item.airline}
                      checked={condition.airlines.includes(item.airline)}
                      onChange={handleChangeCondition}
                    />
                    <span className="after:box-content">{item.name}</span>
                  </label>
                </dd>
              ))}
            </dl>
            <div className="btn-regi-all flex gap-1">
              <button type="button" className="btn-default sel" onClick={() => selectAllAirline()}>
                모두 선택
              </button>
              <span className="after:box-content">|</span>
              <button type="button" className="btn-default clear active" onClick={() => deselectAllAirline()}>
                모두 해제
              </button>
            </div>
            <div id="airlineButton" className="btn-view-all" style={{ display: "block" }}>
              <button type="button" className="btn-default" onClick={() => setSliceAirlineMenu(uniqueAirlines.length)}>
                모든 항공사 보기
              </button>
            </div>
          </div>
          <dl className="range-time" id="departReturnTimeArea">
            <dt>시간대</dt>
            <dd className="btn-time flex gap-2">
              <label>
                <input
                  type="radio"
                  name="timeOption"
                  defaultValue="startTime"
                  checked={rangeTime === "startTime"}
                  onChange={() => setRangeTime("startTime")}
                />
                <span className="after:box-content">출발시간</span>
              </label>
              <label>
                <input
                  type="radio"
                  name="timeOption"
                  defaultValue="arrivalTime"
                  checked={rangeTime === "arrivalTime"}
                  onChange={() => setRangeTime("arrivalTime")}
                />
                <span className="after:box-content">도착시간</span>
              </label>
            </dd>
            {conditionTime[keyRangeTime]?.map((item, index) => (
              <dd key={item?.name + index} className="in-slide" id="departReturnTime_0">
                <p className="tit">
                  <strong>
                    {item?.name} {keyRangeTime === "departureTime" ? "출발" : "도착"}
                  </strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {momentKR(filter.departureDays[0], "YYYYMMDD").format("ddd")} {formatTime(item?.value[0])} - {formatTime(item?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  value={item?.value}
                  max={24}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSlider(item, value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSlider(item, value, "COMMIT");
                  }}
                />
                <input type="hidden" defaultValue="0000" /> <input type="hidden" defaultValue={2400} />
              </dd>
            ))}
          </dl>
          <dl className="range-time">
            <dt>소요시간</dt>
            {conditionTime.leadTime?.value && (
              <dd className="in-slide" id="leadTime">
                <p className="tit">
                  <strong>
                    {filter.departureAirportNames[0]}({filter.departureAirportCodes[0]}) 출발
                  </strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {convertDecimalHoursToKoreanTime(conditionTime.leadTime?.value[0])} -{" "}
                    {convertDecimalHoursToKoreanTime(conditionTime.leadTime?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  min={0.5}
                  value={conditionTime.leadTime?.value}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSliderDuration("leadTime", value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSliderDuration("leadTime", value, "COMMIT");
                  }}
                />
                <input type="hidden" id="leadStartTime" />
                <input type="hidden" id="leadEndTime" />
              </dd>
            )}
            {conditionTime.stopOverTime?.value && (
              <dd className="in-slide" id="stopWaitTime">
                <p className="tit">
                  <strong>경유지 대기시간</strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {convertDecimalHoursToKoreanTime(conditionTime.stopOverTime?.value[0])} -{" "}
                    {convertDecimalHoursToKoreanTime(conditionTime.stopOverTime?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  value={conditionTime.stopOverTime?.value}
                  max={56}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSliderDuration("stopOverTime", value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSliderDuration("stopOverTime", value, "COMMIT");
                  }}
                />
                <input type="hidden" id="stopWaitStartTime" />
                <input type="hidden" id="stopWaitEndTime" />
              </dd>
            )}
          </dl>
          <button type="reset" className="btn-default btn-init" onClick={resetFilter}>
            초기화
          </button>
        </form>
      </div>
      {/* //검색결과 내 검색 */}
    </div>
  );
};

export default LeftSide;
