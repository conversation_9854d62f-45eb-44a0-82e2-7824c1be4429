import { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { isEmpty, uniq, cloneDeep, sortBy, get } from "lodash";
import {
  selectAirportCity,
  selectAirSearch,
  actionSetAirOverseasSessionData,
  actionClearAirOverseasSessionData,
  actionAirportCity,
  actionClearAirSearch,
} from "@/store/airSlice.js";
import LeftSide from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/LeftSide.jsx";
import RightSide from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/RightSide.jsx";
import { COMPANY_SITECODE, SECTION_TYPE } from "@/constants/app";
import AirBookingCompareSchedule from "@/app/components/user_v2/modal/AirBookingCompareSchedule.modal";
import LayerCompareBox from "@/app/pages/user_v2/travel/overseas/air/AirSearch/LayerCompareBox";
import { actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { filterFlights, getUniqueList, sortByCondition } from "@/utils/app";
import { selectUserInfo } from "@/store/userSlice";

const initialState = {
  arrayCorporateFare: ["기업운임", "일반운임"],
  airlines: [],
  arrayStopOverCount: ["0", "1", "2"],
  alliances: [],
  departureTime: [],
  arrivalTime: [],
  leadTime: {},
  stopOverTime: {},
};

const initialStateTime = {
  departureTime: [],
  arrivalTime: [],
  leadTime: {},
  stopOverTime: {},
};

const SearchResults = (props) => {
  const { filter, selectedCompareFares, setSelectedCompareFares } = props;

  const dispatch = useDispatch();
  const arrivalCityCodeMap = useSelector(selectAirportCity);
  const airSearch = useSelector(selectAirSearch);
  const { userInfo } = useSelector(selectUserInfo);
  const [condition, setCondition] = useState(initialState);
  const [baseCondition, setBaseCondition] = useState(initialState);
  const [totalTicket, setTotalTicket] = useState(0);
  const [page, setPage] = useState(1);

  const [baseConditionTime, setBaseConditionTime] = useState(initialStateTime);
  const [conditionTime, setConditionTime] = useState(initialStateTime);
  const [uniqueList, setUniqueList] = useState({ airlines: [], fareTypes: [], alliances: [] });
  const [reset, setReset] = useState(false);
  const [journeyInfos, setJourneyInfos] = useState([]);
  const [selectedMenuSort, setSelectedMenuSort] = useState("recommend");
  const [selectedFare, setSelectedFare] = useState({});
  const [openAirCompareSchedule, setOpenAirCompareSchedule] = useState(false);

  function getSortAndCombineJourneyInfos(journeyInfos) {
    return journeyInfos[0].journeys.map((journey, index) => {
      const { pairKey, journeyKey } = journey;
      const id = journeyKey + index;
      const flights = [journey];
      journeyInfos.slice(1).forEach((item) => {
        item.journeys.forEach((el) => {
          if (el.pairKey === pairKey) {
            flights.push(el);
          }
        });
      });
      return { ...journey, flights, id };
    });
  }

  function resetFilter() {
    setCondition(baseCondition);
    setConditionTime(baseConditionTime);
    setReset(!reset);
  }

  async function fetchMore() {
    if (!isEmpty(journeyInfos)) {
      setPage((prev) => prev + 1);
    }
  }

  useEffect(() => {
    if (!isEmpty(airSearch.data)) {
      const containsIcn = filter.arrivalAirportCodes.includes("ICN") || filter.departureAirportCodes.includes("ICN");
      const containsSel = filter.arrivalAirportCodes.includes("SEL") || filter.departureAirportCodes.includes("SEL");
      const containsHan = filter.arrivalAirportCodes.includes("HAN") || filter.departureAirportCodes.includes("HAN");
      const isKortek = userInfo?.workspace?.company?.siteCode === COMPANY_SITECODE.KORTEK_COMPANY_SITECODE;
      const isAirlineFilterFlag = (containsIcn || containsSel) && containsHan && isKortek;

      const { airlines, alliances } = getUniqueList(airSearch.data.journeyInfos);
      const matchingAirlines = isAirlineFilterFlag ? ["KE", "OZ", "VN"] : airlines.map((el) => el.airline);
      const sortedAirlines = isAirlineFilterFlag
        ? sortBy(cloneDeep(airlines), (airline) => {
            const index = matchingAirlines.indexOf(airline.airline);
            return index === -1 ? Number.MAX_SAFE_INTEGER : index;
          })
        : airlines;
      setUniqueList({ airlines: sortedAirlines, alliances });
      setCondition((prev) => ({ ...prev, airlines: matchingAirlines, alliances }));
      setBaseCondition((prev) => ({ ...prev, airlines: matchingAirlines, alliances }));
    }
  }, [airSearch]);

  useEffect(() => {
    if (isEmpty(filter)) return;
    const { sectionType, departureAirportCodes, arrivalAirportCodes } = filter;
    const mappedDepartureAirportCodes = departureAirportCodes.map((el, index) => ({ id: index, name: el, value: [0, 24] }));
    const mappedArrivalAirportCodes = arrivalAirportCodes.map((el, index) => ({ id: index, name: el, value: [0, 24] }));
    const updateCondition = {
      departureTime: sectionType === SECTION_TYPE.MULTICITY ? mappedDepartureAirportCodes : [mappedDepartureAirportCodes?.[0]],
      arrivalTime: sectionType === SECTION_TYPE.MULTICITY ? mappedArrivalAirportCodes : [mappedArrivalAirportCodes?.[0]],
      leadTime: { name: departureAirportCodes[0], value: [0.5, 100] },
      stopOverTime: { value: [0, 56] },
    };
    setConditionTime((prev) => ({ ...prev, ...updateCondition }));
    setBaseConditionTime((prev) => ({ ...prev, ...updateCondition }));
  }, [filter]);

  async function filterAndCalcCommission(isResetCompare = true) {
    const filteredFlights = filterFlights(filter, airSearch, condition);
    const sortedFlights = sortByCondition(filteredFlights, selectedMenuSort);
    setTotalTicket(sortedFlights.length);
    setSelectedFare({});

    if (isResetCompare) {
      setSelectedCompareFares([]);
    }

    setJourneyInfos(sortedFlights);
    dispatch(
      actionSetAirOverseasSessionData({
        lowestFareAndSchedule: sortByCondition(sortedFlights, "lowAmount")[0],
        filter,
      }),
    );
    setPage(1);
  }

  useEffect(() => {
    if (!isEmpty(airSearch.data) && !isEmpty(arrivalCityCodeMap)) {
      filterAndCalcCommission();
    }
  }, [reset, airSearch, arrivalCityCodeMap]);

  useEffect(() => {
    if (!isEmpty(condition)) {
      filterAndCalcCommission(false);
    }
  }, [condition]);

  useEffect(() => {
    if (!isEmpty(airSearch.data)) {
      const rawData = getSortAndCombineJourneyInfos(airSearch.data.journeyInfos);
      const uniqueAirportCode = uniq(
        rawData.map((el) => el.flights.map((el) => el.segments.map((seg) => [seg.deptAirport, seg.arrAirport]))).flat(3),
      );
      dispatch(actionAirportCity({ codes: uniqueAirportCode.join(","), fields: "city" })).then((_) => {
        dispatch(actionDecreaseLoadingCount("loadingSearchBgCount"));
      });
    } else {
      setJourneyInfos([]);
      setTotalTicket(0);
    }
  }, [airSearch]);

  useEffect(() => {
    async function fetchCommission() {
      const filteredFlights = filterFlights(filter, airSearch, condition);
      const sortedFlights = sortByCondition(filteredFlights, selectedMenuSort);
      setJourneyInfos(sortedFlights);
    }
    setPage(1);
    if (!isEmpty(airSearch.data)) {
      fetchCommission();
    }
  }, [airSearch, selectedMenuSort]);

  useEffect(() => {
    dispatch(actionClearAirOverseasSessionData());

    return () => {
      dispatch(actionClearAirSearch());
    };
  }, []);

  useEffect(() => {
    if (!isEmpty(userInfo)) {
      const airSearchOrderBy = get(userInfo, "workspace.company.btmsSetting.airSearchOrderBy", "recommend");
      setSelectedMenuSort(airSearchOrderBy);
    }
  }, [userInfo]);

  return (
    <div className="contents !block">
      <div className="default-wd clearfix">
        <LeftSide
          filter={filter}
          condition={condition}
          conditionTime={conditionTime}
          setCondition={setCondition}
          setConditionTime={setConditionTime}
          uniqueAirlines={uniqueList.airlines}
          uniqueFareType={uniqueList.fareTypes}
          uniqueAlliances={uniqueList.alliances}
          resetFilter={resetFilter}
        />
        <RightSide
          filter={filter}
          totalTicket={totalTicket}
          selectedCompareFares={selectedCompareFares}
          journeyInfos={journeyInfos.slice(0, page * 10)}
          selectedFare={selectedFare}
          setSelectedFare={setSelectedFare}
          selectedMenuSort={selectedMenuSort}
          setSelectedMenuSort={setSelectedMenuSort}
          setSelectedCompareFares={setSelectedCompareFares}
          fetchMore={fetchMore}
        />
      </div>
      <LayerCompareBox selectedCompareFares={selectedCompareFares} setOpenAirCompareSchedule={setOpenAirCompareSchedule} />
      <AirBookingCompareSchedule
        open={openAirCompareSchedule}
        selectedCompareFares={selectedCompareFares}
        setSelectedCompareFares={setSelectedCompareFares}
        setOpen={setOpenAirCompareSchedule}
      />
    </div>
  );
};

export default SearchResults;
