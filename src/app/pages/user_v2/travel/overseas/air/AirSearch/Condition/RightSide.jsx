import { useSelector } from "react-redux";
import CircularProgress from "@mui/material/CircularProgress";
import ImgInpolicy from "@/assets/images/cmm/img_inpolicy.png";
import ImgOutofpolicy from "@/assets/images/cmm/img_outofpolicy.png";
import { selectUserInfo, selectBtmsSetting } from "@/store/userSlice.js";
import FareItem from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/FareItem.jsx";
import MenuSort from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/MenuSort.jsx";
import InfiniteScroll from "@/app/components/user_v2/common/InfinititeScroll";
import { AIR_SEARCH_ORDER_BY } from "@/constants/common.js";
import { COP_CODE, SECTION_TYPE } from "@/constants/app";
import AirSessionUtil from "@/utils/airSessionUtil";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";

const RightSide = (props) => {
  const {
    filter,
    totalTicket,
    selectedCompareFares,
    setSelectedCompareFares,
    journeyInfos,
    selectedFare,
    setSelectedFare,
    selectedMenuSort,
    setSelectedMenuSort,
    fetchMore,
  } = props;
  const btmsSetting = useSelector(selectBtmsSetting);
  const { userInfo } = useSelector(selectUserInfo);

  function getLabelOfSelectedMenu(name) {
    let label = "";
    for (let [key, value] of Object.entries(AIR_SEARCH_ORDER_BY)) {
      if (key === name) {
        label = value;
        break;
      }
    }
    return label;
  }

  function navigateScheduleSearch() {
    const { sectionType, departureAirportCodes, arrivalAirportCodes, departureDays } = filter;
    const isOneWay = sectionType === SECTION_TYPE.ONEWAY;
    const clonedFilter = {
      ...filter,
      arrivalAirportCodes: isOneWay ? [arrivalAirportCodes[0]] : arrivalAirportCodes,
      departureAirportCodes: isOneWay ? [departureAirportCodes[0]] : departureAirportCodes,
      departureDays: isOneWay ? [departureDays[0]] : departureDays,
    };
    const isMissingArrivalCode = clonedFilter.arrivalAirportCodes.some((code) => code === "");
    const isMissingDepartureCode = clonedFilter.departureAirportCodes.some((code) => code === "");
    const isMissingDepartureDate = clonedFilter.departureDays.some((date) => date === "");
    if (isMissingArrivalCode) {
      alert("도착지를 입력해주세요.");
      return;
    }

    if (isMissingDepartureCode) {
      alert("출발지를 입력해주세요.");
      return;
    }

    if (isMissingDepartureDate) {
      alert("날짜를 선택해주세요.");
      return;
    }
    AirSessionUtil.setSession("", { ...filter, positionName: userInfo?.position?.name });
    airLocalStorageUtil.set(filter);
    location.href = `/user/v2/overseas/air/scheduleSearch${location.search}`;
  }

  return (
    <div className="right-side">
      {btmsSetting?.btmsSetting?.isOfflineUse !== false && (
        <div className="schedule-inquiry-mode-box">
          <div className="left">
            <p className="tit">전체 스케줄 조회 모드</p>
            <p className="text">
              원하는 스케줄로 사용자가 선택 (구간별 선택 가능) 요청할 수 있습니다.
              <br />
              요청시 바로 예약진행이 되지 않으며, 실제 좌석 가능 여부를 담당자가 확인 후 예약 진행 됩니다.
            </p>
          </div>
          <div className="right">
            <button className="btn-mode-view" onClick={navigateScheduleSearch}>
              전체 스케줄 모드 보기
            </button>
          </div>
        </div>
      )}
      <p className="desc-price">
        항공권 운임은 잔여 좌석에 따라 실시간으로 달라질 수 있습니다.
        <strong>이후 예약상황 및 가격정책의 변경 등으로 인해 스케줄 및 운임의 변동이 있을 수 있습니다.</strong> <br />
        2024-08-01 기준, 유류할증료와 세금 및 제반요금 포함된 성인 1명 기준 운임입니다.
      </p>
      <div className="clearfix area-top">
        <p className="col-left result">
          검색결과 <strong id="listCount">{totalTicket}</strong>개
        </p>
        <div className="col-right">
          {!userInfo?.workspace?.company?.parent && (
            <div className="col-left box-mark ">
              <p className="flex">
                <img src={ImgInpolicy} alt="Policy" />
                In Policy
              </p>
              <p className="flex">
                <img src={ImgOutofpolicy} alt="Policy" />
                Out of Policy
              </p>
            </div>
          )}
          <MenuSort setSelectedMenuSort={setSelectedMenuSort}>
            <div className="col-left select-align">
              {/* DD : select-menu에 innerHtml 사용으로 class =>  custom-ui추가 및 data-in-html에 텍스트 적용 */}
              <select className="select-menu custom-ui" id="listAlign" style={{ display: "none" }}>
                {Object.entries(AIR_SEARCH_ORDER_BY).map(([key, value]) => (
                  <option key={key} data-in-html={value} value={key} />
                ))}
              </select>
              <span
                tabIndex={0}
                id="listAlign-button"
                role="combobox"
                aria-expanded="false"
                aria-autocomplete="list"
                aria-owns="listAlign-menu"
                aria-haspopup="true"
                className="ui-selectmenu-button ui-corner-all ui-button ui-widget box-content"
              >
                <span className="ui-selectmenu-icon ui-icon ui-icon-triangle-1-s" />
                <span className="ui-selectmenu-text">{getLabelOfSelectedMenu(selectedMenuSort)}</span>
              </span>
            </div>
          </MenuSort>
        </div>
      </div>
      <InfiniteScroll
        className="area-cotn"
        loader={
          <div className="flex items-center justify-center mt-8">
            <CircularProgress />
          </div>
        }
        fetchMore={fetchMore}
        hasMore={totalTicket > journeyInfos.length}
      >
        {journeyInfos.map((journey) => {
          const { flights, id } = journey;
          const paxTypeFaresDisplay = journey.fares.paxTypeFares[0];
          const airFare = Number(paxTypeFaresDisplay.airFare);
          const tax = Number(paxTypeFaresDisplay.airTax) + Number(paxTypeFaresDisplay.fuelChg);
          const ticketFare = Number(paxTypeFaresDisplay?.tasfAmount ?? 0); // Update later!

          const fareTypes = journey.flights.map((flight) => flight.fares.fareTypes).flat();
          const includedCorporateFare = fareTypes.some((type) => type.includes(COP_CODE));
          return (
            <FareItem
              id={id}
              filter={filter}
              key={id}
              flights={flights}
              journey={journey}
              airFare={airFare}
              tax={tax}
              ticketFare={ticketFare}
              includedCorporateFare={includedCorporateFare}
              selectedFare={selectedFare}
              setSelectedFare={setSelectedFare}
              selectedCompareFares={selectedCompareFares}
              setSelectedCompareFares={setSelectedCompareFares}
            />
          );
        })}
      </InfiniteScroll>
    </div>
  );
};

export default RightSide;
