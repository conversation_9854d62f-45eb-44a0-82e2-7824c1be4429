import { useState, useMemo } from "react";
import { replaceCarriageReturns } from "@/utils/common";

const FareRegulationsTab = (props) => {
  const { fareRules, className = "", type = "DEFAULT", onConfirm } = props;

  const isView = type === "VIEW";
  const [selectedTab, setSelectedTab] = useState(0);

  const fareBasisRules = useMemo(() => fareRules[0].fareBasisRules.map((rule, index) => ({ ...rule, id: index })), [fareRules]);
  const selectedRule = useMemo(() => fareBasisRules?.[selectedTab] || {}, [fareBasisRules, selectedTab]);
  const combinedRules = useMemo(() => {
    const agencyItems = fareRules[0]?.agencyItems || [];
    const items = selectedRule?.items || [];
    return [...items, ...agencyItems];
  }, [selectedRule, fareRules]);

  return (
    <div className={`fare active ${isView ? className : "!p-0"}`}>
      <div className="tab-box tab-wrap">
        <ul className="tab-btm">
          {fareBasisRules.map((rule, index) => {
            return (
              <li key={rule.id + index} className={`${selectedTab === rule.id ? "active" : ""}`}>
                <a className="btn-default" onClick={() => setSelectedTab(index)}>
                  운임규정{index + 1}
                </a>
              </li>
            );
          })}
        </ul>
        <div className="tbl-cmm active">
          <div className="slimScrollDiv" style={{ position: "relative", overflow: "hidden", width: "auto" }}>
            <div className="scrollbar-inner custom-set !mr-0" name="fareRuleHtmlScroll" style={{ overflow: "scroll", width: "auto" }}>
              <table className="table-fixed">
                <colgroup>
                  <col style={{ width: 131 }} /> <col />
                </colgroup>
                <tbody>
                  {combinedRules?.map((rule, index) => {
                    const { name, description } = rule;
                    return (
                      <tr key={rule.name + index}>
                        <th dangerouslySetInnerHTML={{ __html: name }} />
                        <td dangerouslySetInnerHTML={{ __html: replaceCarriageReturns(description) }} />
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {!isView && <p className="desc">※ 전체 여정의 규정 중 가장 제한적인 규정이 적용됩니다.</p>}
      {isView && (
        <div className="btn-confirm">
          <a className="btn-default" onClick={onConfirm}>
            확인
          </a>
        </div>
      )}
    </div>
  );
};

export default FareRegulationsTab;
