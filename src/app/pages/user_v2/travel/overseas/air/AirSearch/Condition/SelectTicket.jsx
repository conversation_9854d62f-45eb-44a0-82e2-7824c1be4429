import { useState, useRef } from "react";
import { useClickOutside } from "@/app/hooks/useClickOutside.js";

const SELECT_TICKET_OPTIONS = [
  { id: 1, label: "왕복", value: "RoundTrip" },
  { id: 2, label: "편도", value: "OneWay" },
  { id: 3, label: "다구간", value: "MultiCity" },
];

const SelectTicket = (props) => {
  const { filter, setFilter, setOpenMultiRoute } = props;
  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const [openSelectTicket, setOpenSelectTicket] = useState(false);

  useClickOutside(menuRef, buttonRef, () => setOpenSelectTicket(false));

  return (
    <div className={`type select-ticket box-content ${openSelectTicket ? "active" : ""}`}>
      <p className="sel mb-0" ref={buttonRef}>
        <button type="button" className="btn-default" onClick={() => setOpenSelectTicket(!openSelectTicket)}>
          {SELECT_TICKET_OPTIONS.find((item) => item.value === filter?.sectionType)?.label || SELECT_TICKET_OPTIONS[0].label}
        </button>
      </p>
      <ul className="list" ref={menuRef}>
        {SELECT_TICKET_OPTIONS.map((item) => (
          <li key={item.id}>
            <button
              type="button"
              className="btn-default"
              data-ticket-val={item.value}
              onClick={() => {
                setFilter((prev) => ({ ...prev, sectionType: item.value }));
                setOpenMultiRoute(false);
                setOpenSelectTicket(false);
              }}
            >
              {item.label}
            </button>
          </li>
        ))}
      </ul>
      <i className="ico" />
    </div>
  );
};

export default SelectTicket;
