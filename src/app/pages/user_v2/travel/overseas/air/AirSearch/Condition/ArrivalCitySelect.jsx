import { Fragment, useRef } from "react";
import useOpenComponent from "@/app/hooks/useOpenComponent.js";
import SearchCity from "@/app/components/user_v2/common/SearchCity.jsx";

const ArrivalCitySelect = (props) => {
  const { children, code, onChooseArrival } = props;
  const dropdownRef = useRef(null);
  const { isOpen, dropdownPosition, clonedChild, setIsOpen } = useOpenComponent({ children, dropdownRef });

  const handleChooseCity = (item) => {
    onChooseArrival(item);
    setIsOpen(false);
  };

  return (
    <Fragment>
      {clonedChild}
      <SearchCity code={code} ref={dropdownRef} dropdownPosition={dropdownPosition} open={isOpen} onChooseCity={handleChooseCity} />
    </Fragment>
  );
};

export default ArrivalCitySelect;
