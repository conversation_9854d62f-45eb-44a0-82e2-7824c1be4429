import { useRef, Fragment } from "react";
import { createPortal } from "react-dom";
import useOpenComponent from "@/app/hooks/useOpenComponent.js";
import { AIR_SEARCH_ORDER_BY } from "@/constants/common.js";

const MenuSort = (props) => {
  const { children, setSelectedMenuSort } = props;
  const dropdownRef = useRef(null);
  const { isOpen, setIsOpen, dropdownPosition, clonedChild } = useOpenComponent({ children, dropdownRef });

  return (
    <Fragment>
      {clonedChild}
      {isOpen &&
        createPortal(
          <div
            ref={dropdownRef}
            className="ui-selectmenu-menu ui-front ui-selectmenu-open"
            style={{ top: dropdownPosition.top, left: dropdownPosition.left }}
          >
            <ul
              aria-hidden="false"
              aria-labelledby="listAlign-button"
              id="listAlign-menu"
              role="listbox"
              tabIndex={0}
              className="ui-menu ui-corner-bottom ui-widget ui-widget-content"
              aria-activedescendant="ui-id-5"
              aria-disabled="false"
              style={{ width: 300 }}
            >
              {Object.entries(AIR_SEARCH_ORDER_BY).map(([key, value]) => (
                <li
                  className="ui-menu-item"
                  key={key}
                  onClick={() => {
                    setSelectedMenuSort(key);
                    setIsOpen(false);
                  }}
                >
                  <div id="ui-id-1" tabIndex={-1} role="option" className="ui-menu-item-wrapper">
                    {key.includes("recommend") ? (
                      <Fragment>
                        {value.split("(")[0]}
                        <strong>({value.split("(")[1]}</strong>
                      </Fragment>
                    ) : (
                      value
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>,
          document.body,
        )}
    </Fragment>
  );
};

export default MenuSort;
