import { Fragment } from "react";
import { useSelector } from "react-redux";
import { selectTravelRuleBase } from "@/store/airSlice.js";
import { selectUserInfo } from "@/store/userSlice";
import AirUtil from "@/utils/airUtil.js";
import { nvl } from "@/utils/common";
import useTravelRule from "@/app/hooks/useTravelRule";

const TravelRuleTab = (props) => {
  const { journey } = props;
  const travelRuleBase = useSelector(selectTravelRuleBase);
  const { userInfo } = useSelector(selectUserInfo);

  const travelRule = useTravelRule();

  const isTravelLimitDayViolationFlag = travelRule.isTravelLimitDayViolation(journey);
  const isBaseSeatTypeCodeViolationFlag = travelRule.isBaseSeatTypeCodeViolation(journey);
  const isExceptSeatTypeCodeViolationFlag = travelRule.isExceptSeatTypeCodeViolation(journey);

  function getCityText(travelRuleBaseDTO) {
    let cityText = "";
    for (let i = 0; i < travelRuleBaseDTO?.exceptCityList.length; i++) {
      cityText += nvl(travelRuleBaseDTO?.exceptCityList[i].cityName, "");

      if (i < travelRuleBaseDTO.exceptCityList.length - 1) {
        cityText += ", ";
      }
    }
    return cityText;
  }

  return (
    <div className="business active">
      {isTravelLimitDayViolationFlag || isBaseSeatTypeCodeViolationFlag || isExceptSeatTypeCodeViolationFlag ? (
        <p className="access fail">예약 규정에 적합하지 않은 항공권입니다. 기준 외 발권 시 반드시 담당임원의 결제가 필요합니다.</p>
      ) : (
        <p className="access pass">예약 규정에 적합한 항공권입니다.</p>
      )}
      <p className="tit">
        <strong>{userInfo?.position?.name}</strong>님에게 적용되는 해외 항공권 예약 규정입니다.
      </p>
      <div className="inner">
        {travelRuleBase?.data?.isExceptUse && (
          <p className="mb-4 text-[15px] leading-[22px] text-[#293036]">
            해외 항공권 예약은 <strong className="text-[#4e81ff]">{AirUtil.getExceptTimeType(travelRuleBase?.data?.exceptTimeType)}</strong>을
            기준으로 합니다.
          </p>
        )}

        <dl className={`${isTravelLimitDayViolationFlag ? "fail" : "pass"} flex items-center justify-between`}>
          <dt className="!bg-transparent !p-0 !mb-0 !pl-5">사전 예약 규정</dt>
          <dd className="!mb-0">
            출발일 기준 <strong>{typeof travelRuleBase?.data == "undefined" ? 0 : travelRuleBase?.data?.travelLimitDay}일 전</strong>
          </dd>
        </dl>
        <dl className={`${isBaseSeatTypeCodeViolationFlag ? "fail" : "pass"} flex items-center justify-between`}>
          <dt className="!bg-transparent !p-0 !mb-0 !pl-5">기본 좌석등급</dt>
          <dd className="!mb-0">
            <strong>{typeof travelRuleBase?.data === "undefined" ? "" : AirUtil.getSeatTypeName(travelRuleBase?.data?.baseSeatTypeCode)}</strong>
          </dd>
        </dl>

        {travelRuleBase?.data?.isExceptUse && (
          <Fragment>
            <dl className={`normal flex items-center justify-between`}>
              <dt className="!bg-transparent !p-0 !mb-0 !pl-5">최대 허용 좌석등급</dt>
              <dd className="!mb-0">
                <strong>{AirUtil.getSeatTypeName(travelRuleBase?.data?.exceptSeatTypeCode)}</strong>
              </dd>
            </dl>
            <dl className={`${isExceptSeatTypeCodeViolationFlag ? "fail" : "pass"} flex justify-between items-start`}>
              <dt className="!bg-transparent !p-0 !mb-0 !pl-5">좌석 승급 조건</dt>
              <dd className="!mb-0">
                <strong>{travelRuleBase?.data?.exceptBordingTime} 시간 이상 탑승</strong>
                {getCityText(travelRuleBase?.data) !== "" ? (
                  <Fragment>
                    <br /> 단, <strong>{getCityText(travelRuleBase?.data)}</strong> 는 조건 충족 없이 예약 가능
                  </Fragment>
                ) : (
                  ""
                )}
              </dd>
            </dl>
            <dl className="etc flex items-center justify-between">
              <dt className="!bg-transparent !p-0 !mb-0 !pl-5">기타사항 선호</dt>
              <dd className="!mb-0">{nvl(travelRuleBase?.data?.etc, "-")}</dd>
            </dl>
          </Fragment>
        )}
      </div>
    </div>
  );
};

export default TravelRuleTab;
