import { isEmpty } from "lodash";
import AIR_ICONS_PATH from "@/assets/airIcon";
import { MAPPING_UNIT } from "@/constants/common";
import AirUtil from "@/utils/airUtil.js";
import { getHourMin, nvl } from "@/utils/common";
import { momentKR } from "@/utils/date";
import DateUtil from "@/utils/dateUtil";

export default function ItineraryInformation(props) {
  const { data, filter, index } = props;
  const elapseFlyingTime = data.segments.reduce((acc, res) => {
    return acc + parseInt(res.flightTime || 0) + parseInt(res.waitingTime || 0);
  }, 0);
  const totalHour = Math.floor(elapseFlyingTime / 60);
  const totalMin = elapseFlyingTime % 60;
  const totalFlightTime = (totalHour > 0 ? totalHour + "시간" : "") + "" + (totalMin > 0 ? totalMin + "분" : "");
  const sectionType = filter.sectionType;
  const handleError = (event) => {
    event.target.src = AIR_ICONS_PATH.default_airline;
  };

  const getSsrService = (freessrs) => {
    if (freessrs?.length === 0 || isEmpty(freessrs)) return null;
    let validSsrService;
    for (let i = 0; i < freessrs.length; i++) {
      if (freessrs[i].ssrType === "Baggage" && freessrs[i].ssrService.filter((ssr) => ssr.ssrAmount == 0)) {
        validSsrService = freessrs[i];
        break;
      }
    }
    return validSsrService;
  };

  return (
    <>
      <dt>
        <div className="type">{sectionType === "MultiCity" ? `여정 ${index + 1}` : index == 0 ? "가는 편" : "오는 편"}</div>
        <div className="city">
          <span className="dep">{`${data.deptAirportName}(${data.deptAirport})`}</span>
          <span className="arr">{`${data.arrAirportName}(${data.arrAirport})`}</span>
        </div>
        <div className="time">총 {totalFlightTime}</div>
      </dt>
      {data.segments.map((seg, index) => {
        return (
          <dd key={index}>
            <div className="plane clearfix">
              <span className="flex">
                <img src={AIR_ICONS_PATH[seg.carrierCode] || ""} alt="항공사로고" onError={handleError} />
                {seg.carrierCodeName}
              </span>
              <span>{`${seg.carrierCode}${seg.flightNumber}`}</span>
              <span>{`${AirUtil.getSeatTypeName(seg.cabinClass)}(${seg.bookingClass})`}</span>
            </div>
            {seg.legs[0].operatingCarrier != null && seg.carrierCode != seg.legs[0].operatingCarrier ? (
              <div className="boarding">
                <span>{`실제탑승 - ${nvl(seg.legs[0].operatingCarrierName, "")}(${seg.legs[0].operatingCarrier})`}</span>
              </div>
            ) : null}
            <div className="schdule">
              <div className="clearfix">
                <p className="date">{`${momentKR(seg.departureDate).format("MM월 DD일 dd")}`}</p>
                <p className="time">{`${momentKR(seg.departureDate).format("HH:mm")}`}</p>
                <div className="sign point"></div>
                <p className="city">
                  <span className="en">{seg.deptAirport}</span>
                  <span className="kr">{seg.deptAirportName}</span>
                </p>
              </div>
              <p className="time-plan">{DateUtil.getHourMin(seg.flightTime)}</p>
              <div className="clearfix">
                <p className="date">{`${momentKR(seg.arrivalDate).format("MM월 DD일 dd")}`}</p>
                <p className="time">{`${momentKR(seg.arrivalDate).format("HH:mm")}`}</p>
                <div className="sign ing"></div>
                <p className="city">
                  <span className="en">{seg.arrAirport}</span>
                  <span className="kr">{seg.arrAirportName}</span>
                  <span></span>
                </p>
              </div>
              {seg.waitingTime !== 0 && <p className="overstop">{getHourMin(seg.waitingTime)}</p>}
              <div className="etc">
                {getSsrService(data.freessrs) == null
                  ? "무료 수하물 불포함"
                  : `무료 수하물 ${getSsrService(data.freessrs).ssrService[0].ssrValue} ${MAPPING_UNIT[getSsrService(data.freessrs).ssrUnit]}`}
                <br /> {AirUtil.getSeatWaitingName(seg.legs[0].waitingTime)} <strong>{data.fares.availableCount}</strong>석 남음
              </div>
            </div>
          </dd>
        );
      })}
    </>
  );
}
