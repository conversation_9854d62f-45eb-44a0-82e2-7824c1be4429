import { useCallback } from "react";
import { isEmpty, cloneDeep, set } from "lodash";
import { useDropzone } from "react-dropzone";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { postFileDelete, postFileMultiUpload } from "@/service/common";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";

export default function DocumentFileUpload(props) {
  const { formState, setFormState } = props;
  const { userInfo } = useAppSelector(selectUserInfo);
  const apiWithLoading = useApiWithLoading();

  const onDrop = useCallback(async (acceptedFiles) => {
    const formData = new FormData();
    const isValid = validateFile(acceptedFiles);
    if (!isValid) return;
    acceptedFiles.forEach((file) => {
      formData.append("File", file);
    });
    await apiWithLoading(
      () => postFileMultiUpload(formData),
      (response) => {
        const mergedFiles = [...formState.attachFiles, ...response.data];
        setFormState((prev) => set(cloneDeep(prev), "attachFiles", mergedFiles));
      },
      () => {},
    );
  }, []);

  function validateFile(files) {
    const maxFileSize = 10;
    const maxFileCount = 5;

    const isExceedMaxFileSize = files.some((file) => file.size > parseInt(maxFileSize) * 1000000);
    if (isExceedMaxFileSize) {
      alert("파일 용량은 " + maxFileSize + " MB를 초과할 수 없습니다.");
      return false;
    }

    if (maxFileCount != null && parseInt(maxFileCount) > 1) {
      if (files.length > parseInt(maxFileCount)) {
        alert("업로드 파일 개수 " + maxFileCount + "개가 초과되었습니다.");
        return false;
      }
    }

    return true;
  }

  async function handleRemoveFile(file) {
    await postFileDelete({ filePath: file.fileUploadPath + file.tempFileName });
    setFormState((prev) =>
      set(
        cloneDeep(prev),
        "attachFiles",
        prev.attachFiles.filter((f) => f.id !== file.id),
      ),
    );
  }

  const { getRootProps, getInputProps } = useDropzone({ onDrop });

  return userInfo?.workspace?.company?.btmsSetting?.isDocumentEvidenceFile ? (
    <div className="box-line box-item document-add" id="divAddDocumentFile">
      <h3 className="tit">증빙서류 첨부</h3>
      <div className="btn-file-group">
        <label htmlFor="inputAddDocumentFile">파일선택</label>
        <input type="file" name="File" accept="" className="btn-file" id="inputAddDocumentFile" multiple {...getInputProps()} />
      </div>
      <div className="upload-cotn" {...getRootProps()}>
        <div className="box-default" id="fileDropArea" style={{ display: isEmpty(formState.attachFiles) ? "flex" : "none" }}>
          <p>
            <strong>등록하실 파일을 여기에 올려주세요.</strong>
          </p>
          <p>10MB 이하 파일만 업로드 가능합니다.</p>
        </div>
        <div className="box-upload" id="fileListArea">
          <div className="slimScrollDiv" style={{ position: "relative", overflow: "hidden", width: "auto" }}>
            <ul className="file-list scrollbar-inner overflow-y-auto overflow-x-hidden" id="fileListAreaUl">
              {formState.attachFiles.map((file) => (
                <li key={file.id} className="box-content">
                  <span className="file-name">{file.originFileName}</span>
                  <button
                    type="button"
                    className="btn-del-file"
                    name="removeFileButton"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRemoveFile(file);
                    }}
                  >
                    삭제
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  ) : null;
}
