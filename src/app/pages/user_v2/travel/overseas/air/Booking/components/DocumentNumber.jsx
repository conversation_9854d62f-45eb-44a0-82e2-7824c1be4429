import { cloneDeep } from "lodash";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

export default function DocumentNumber(props) {
  const { formState, setFormState } = props;
  const { userInfo } = useAppSelector(selectUserInfo);

  function documentNumberPlus() {
    const documentNumberCount = userInfo?.workspace?.company?.btmsSetting?.documentNumberUseCount || 0;
    const existingGroups = formState.documentNumber.length;
    if (existingGroups >= documentNumberCount) {
      alert("최대 " + documentNumberCount + "개까지 입력 가능합니다.");
      return;
    }
    setFormState((prev) => {
      const clonedPrev = cloneDeep(prev);
      clonedPrev.documentNumber.push({ id: clonedPrev.documentNumber.length, value: "" });
      return clonedPrev;
    });
  }

  function documentNumberMinus(id) {
    setFormState((prev) => {
      const clonedPrev = cloneDeep(prev);
      clonedPrev.documentNumber = clonedPrev.documentNumber.filter((group) => group.id !== id);
      return clonedPrev;
    });
  }

  function handleChange(e, id) {
    setFormState((prev) => {
      const clonedPrev = cloneDeep(prev);
      clonedPrev.documentNumber = clonedPrev.documentNumber.map((group) => (group.id === id ? { ...group, value: e.target.value } : group));
      return clonedPrev;
    });
  }

  return (
    <div className="box-line">
      <p className="txt-big">
        {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
          ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
          : "문서번호"}
        {userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberRequired && <span className="emp">*</span>}
      </p>
      <div className="form-doc-number flex flex-col gap-2" name="document-number-area">
        <div className="group-input">
          <input
            type="text"
            name="documentNumber"
            placeholder={
              userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                : "문서번호"
            }
            value={formState?.documentNumber[0].value}
            onChange={(e) => handleChange(e, 0)}
          />
          <button type="button" className="btn-document plus" onClick={documentNumberPlus}>
            +
          </button>
        </div>
        {formState?.documentNumber.slice(1, formState?.documentNumber.length).map((group) => (
          <div className="group-input" key={group.id}>
            <input
              type="text"
              name="documentNumber"
              placeholder={
                userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  : "문서번호"
              }
              value={group.value}
              onChange={(e) => handleChange(e, group.id)}
            />
            <button type="button" className="btn-document minus" onClick={() => documentNumberMinus(group.id)}>
              -
            </button>
          </div>
        ))}
      </div>
    </div>
  );
}
