import { useState } from "react";
import { set,get, cloneDeep } from "lodash";

export default function PersionalInfoShort(props) {
  const { formState, setFormState } = props;
  const [active, setActive] = useState(true);

  function toggleAgreeReserverInfo(e) {
    const {  name } = e.target;
    const valBool = get(formState, name);
    setFormState((prev) => {
      return set(cloneDeep(prev), name, !valBool);
    });
  }

  return (
    <div className={`box-item personal-info ${active ? "active" : ""}`}>
      <h3 className="tit">예약자 정보</h3>
      <div className="box-cotn">
        <dl className="box-col name">
          <dt>예약자 이름</dt>
          <dd className="desc">
            <input type="text" id="name" name="travelers[0].name" defaultValue={formState.travelers?.[0]?.name} maxLength={20} readOnly={true} />
          </dd>
          <dd className="validate" style={{ display: "none" }}>
            한국 이름이 없는 경우 영문으로 입력하거나 영문 발음대로 한글로 기입해주시길 바랍니다.
          </dd>
        </dl>
        <dl className="box-col num">
          <dt>휴대전화 번호</dt>
          <dd className="desc">
            <input
              type="text"
              id="reservationCellPhoneNumber"
              name="travelers[0].cellPhoneNumber"
              defaultValue={formState.travelers?.[0]?.cellPhoneNumber}
              placeholder="- 없이 입력 해 주세요"
              maxLength={11}
              readOnly={true}
            />
          </dd>
          <dd className="validate" style={{ display: "none" }}>
            휴대전화 번호는 숫자만 입력 가능합니다.
          </dd>
        </dl>
        <dl className="box-col email">
          <dt>이메일 주소</dt>
          <dd className="desc">
            <input
              type="text"
              id="email"
              name="travelers[0].email"
              placeholder="<EMAIL>"
              defaultValue={formState.travelers?.[0]?.email}
              maxLength={100}
              readOnly={true}
            />
          </dd>
          <dd className="validate" style={{ display: "none" }}>
            이메일 주소 형식을 확인하세요. (a@b.c)
          </dd>
        </dl>
      </div>
      <div className="btn-arrow" onClick={() => setActive(!active)}>
        <button type="button" className="btn-default" />
      </div>
      <div className="box-chk-info">
        <label className="form-chkbox">
          <input
            type="checkbox"
            id="isAgreeReserverInfo"
            name="reservationRuleAgreementDTO.isAgreeReserverInfo"
            checked={formState.reservationRuleAgreementDTO.isAgreeReserverInfo}
            onChange={toggleAgreeReserverInfo}
          />
          <span style={{ fontSize: 15 }}>예약진행상황 및 정보전달을 위하여 정확한 연락처 및 이메일 정보를 입력했음에 동의합니다.</span>
        </label>
      </div>
    </div>
  );
}
