import { useMemo, useEffect } from "react";
import { uniq, compact, isEmpty } from "lodash";
import { useNavigate } from "react-router-dom";
import { useAppSelector } from "@/store";
import AgreeRule from "@/app/pages/user_v2/travel/overseas/air/Booking/components/AgreeRule";
import PersionalInfoFull from "@/app/pages/user_v2/travel/overseas/air/Booking/components/PersionalInfoFull";
import PersionalInfoShort from "@/app/pages/user_v2/travel/overseas/air/Booking/components/PersionalInfoShort";
import ResultItem from "@/app/pages/user_v2/travel/overseas/air/Booking/components/ResultItem";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import DocumentFileUpload from "@/app/pages/user_v2/travel/overseas/air/Booking/components/DocumentFileUpload";
import { useMainUrl } from "@/app/hooks/useMainUrl";

export default function LeftSide(props) {
  const navigate = useNavigate();
  const { filter, data } = useAppSelector(selectAirOverseasSessionData);
  const { mainUrl } = useMainUrl();
  const scheduleCityList = useMemo(() => {
    if (isEmpty(filter)) return [];
    const { departureAirportNames, arrivalAirportNames } = filter;
    return uniq(compact([...departureAirportNames, ...arrivalAirportNames]));
  }, [filter]);

  useEffect(() => {
    if (isEmpty(data)) navigate(mainUrl);
  }, [data, navigate, mainUrl]);

  return (
    <div className="left-side sec-info-user">
      <dl className="box-tit">
        <dt>{scheduleCityList.join(" - ")}</dt>
        <dd>출장관리자와 여행사 예약 담당자의 담당자 확인 후 발권이 진행 됩니다.</dd>
      </dl>
      <div className="box-accodion">
        <ResultItem />
        <PersionalInfoShort {...props} />
        <PersionalInfoFull {...props} />
        <DocumentFileUpload {...props} />
        <AgreeRule {...props} />
      </div>
    </div>
  );
}
