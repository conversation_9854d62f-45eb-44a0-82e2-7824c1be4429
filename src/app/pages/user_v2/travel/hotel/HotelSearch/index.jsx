import { useEffect, useRef, useState } from "react";
import { cloneDeep, isEmpty } from "lodash";
import { useMap } from "@vis.gl/react-google-maps";
import { useSearchParams } from "react-router-dom";
import Condition from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition";
import FilterArea from "@/app/pages/user_v2/travel/hotel/HotelSearch/FilterArea";
import SearchList from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList";
import HotelDetail from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelDetail";
import Map from "@/app/pages/user_v2/travel/hotel/HotelSearch/Map";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  actionClearExchangeRate,
  actionClearHotelCompare,
  actionClearSortBy,
  actionHotelCities,
  actionHotelDetailCodes,
  actionHotelTravelRule,
  actionSelectHotel,
  actionSetHotelCondition,
  actionSetHotelFilter,
  actionSetHotelList,
  actionSetHotelSearchType,
  selectConditionHotel,
  selectFilterHotel,
  selectHotelDetailCodes,
  selectHotelList,
  selectHotelSearch,
  selectSortBy,
} from "@/store/hotelSlice";
import { convertMapIntoSelectList, getAdditionalParams, getFilterFromSearchParams, getFirstValidValue } from "@/utils/common";
import { filterHotelByCondition, getMapBounds, getPayloadHotelSearch, setQueryParams, sortHotelByCondition } from "@/utils/app";
import HotelCompareFloatButton from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelCompareFloatButton";
import RefreshSearchFloatButton from "@/app/pages/user_v2/travel/hotel/HotelSearch/RefreshSearchFloatButton";
import { DEFAULT_ROOM_INFO, HOTEL_AUTOSEARCH_TYPE, HOTEL_DEFAULT_CENTER_MAP } from "@/constants/app";
import useHotelApi from "@/app/hooks/useHotelApi";

const HotelSearch = () => {
  const dispatch = useAppDispatch();

  const map = useMap();
  const hotelApi = useHotelApi();

  const [searchParams] = useSearchParams();
  const sortBy = useAppSelector(selectSortBy);
  const hotelList = useAppSelector(selectHotelList);
  const hotelDetailCodes = useAppSelector(selectHotelDetailCodes);
  const condition = useAppSelector(selectConditionHotel);

  const filter = useAppSelector(selectFilterHotel);
  const hotelSearch = useAppSelector(selectHotelSearch);

  const hotelItemRef = useRef({});
  const [isConditionChange, setConditionChange] = useState(false);
  const [isOpenSearchList, setIsOpenSearchList] = useState(true);
  const [initCondition, setInitCondition] = useState({});
  const [center, setCenter] = useState(HOTEL_DEFAULT_CENTER_MAP);
  const [isChangeMapCenter, setIsChangeMapCenter] = useState(false);

  function resetCondition() {
    setConditionChange(false);
    dispatch(actionSetHotelCondition(initCondition));
  }

  async function refreshSearch() {
    const isValid = handleValidate(filter);
    if (!isValid) return;

    const mapBounds = getMapBounds(map);
    const searchParams = cloneDeep(filter);
    const refreshedSearchParams = {
      ...searchParams,
      ...mapBounds,
      htlMasterId: 0,
      regionId: "",
      regionNameLong: "",
      regionUpperId: "",
      googleMapPlaceId: "",
    };

    setQueryParams(refreshedSearchParams);
    setIsChangeMapCenter(false);
    dispatch(actionSetHotelSearchType(HOTEL_AUTOSEARCH_TYPE.MAP));
    dispatch(actionSelectHotel(null));
    dispatch(actionClearHotelCompare());
    dispatch(actionClearSortBy());
    dispatch(actionClearExchangeRate());
    dispatch(actionSetHotelFilter({ ...searchParams, ...mapBounds }));
    await hotelApi.searchHotel(getPayloadHotelSearch(filter, mapBounds), map);
  }

  function handleValidate(filter) {
    const { checkIn, checkOut } = filter;

    if (isEmpty(checkIn) || isEmpty(checkOut)) {
      alert("숙박날짜를 선택해주세요.");
      return false;
    }

    if (checkIn === checkOut) {
      alert("숙박날짜를 1박 이상 선택해주세요.");
      return false;
    }

    return true;
  }

  function isBackFromBooking(filter) {
    // Note: Special case when back from booking
    const { htlMasterId, regionId, regionUpperId } = filter;
    return htlMasterId && (regionId || regionUpperId);
  }

  useEffect(() => {
    if (hotelSearch && hotelDetailCodes) {
      const additionalParams = getAdditionalParams(filter, hotelSearch, hotelDetailCodes);
      const { minAveragePrice, maxAveragePrice, gradeCodeMap, facilityCodeMap } = additionalParams;

      const newCondition = {
        ...condition,
        arrayGradeCode: convertMapIntoSelectList(gradeCodeMap).map((el) => el.value),
        arrayFacilityCode: convertMapIntoSelectList(facilityCodeMap).map((el) => el.value),
        fareRange: [minAveragePrice, maxAveragePrice],
        ...additionalParams,
      };

      dispatch(actionSetHotelCondition(newCondition));
      setInitCondition(newCondition);
    }
  }, [hotelSearch, hotelDetailCodes]);

  useEffect(() => {
    if (condition && hotelSearch) {
      if (JSON.stringify(condition) !== JSON.stringify(initCondition) && hotelList.length > 0) {
        setConditionChange(true);
      }
      const originalHotelFareList = cloneDeep(hotelSearch)?.hotelFareList || [];
      const filteredHotelList = filterHotelByCondition(originalHotelFareList, condition);
      dispatch(actionSetHotelList(sortHotelByCondition(filteredHotelList, sortBy)));
    }
  }, [condition, hotelSearch, sortBy]);

  useEffect(() => {
    async function searchHotel() {
      const newFilter = getFilterFromSearchParams(searchParams);
      const {
        htlMasterId,
        roomInfo,
        regionId,
        checkIn,
        checkOut,
        regionUpperId,
        regionNameLong,
        leftBottomLatitude,
        leftBottomLongitude,
        rightTopLatitude,
        rightTopLongitude,
        googleMapPlaceId,
      } = newFilter;
      const additionalParams = {
        hotelId: htlMasterId && !regionId && !regionUpperId ? htlMasterId : 0,
        keyword: getFirstValidValue(htlMasterId, regionId, regionUpperId) ? "" : regionNameLong,
        cityId: regionId || 0,
        upperCityId: regionUpperId || 0,
        roomInfo: roomInfo || DEFAULT_ROOM_INFO,
        googleMapPlaceId: googleMapPlaceId || 0,
      };

      if (isBackFromBooking(newFilter)) {
        additionalParams.hotelId = newFilter.htlMasterId = htlMasterId;
        additionalParams.cityId = newFilter.regionId = 0;
        additionalParams.upperCityId = newFilter.regionUpperId = 0;
      }

      dispatch(actionSetHotelFilter({ ...newFilter, roomInfo: roomInfo || DEFAULT_ROOM_INFO }));
      if ((checkIn, checkOut)) {
        dispatch(actionSetHotelSearchType(HOTEL_AUTOSEARCH_TYPE.CITY));
        await hotelApi.searchHotel(
          getPayloadHotelSearch(
            newFilter,
            leftBottomLatitude && leftBottomLongitude && rightTopLatitude && rightTopLongitude
              ? { leftBottomLatitude, leftBottomLongitude, rightTopLatitude, rightTopLongitude, roomInfo: additionalParams.roomInfo }
              : additionalParams,
          ),
          map,
        );
      }
    }
    if (map) searchHotel();
  }, [searchParams, map]);

  useEffect(() => {
    async function autoSelectHotelWhenSearchByHotelId() {
      const {
        checkIn,
        checkOut,
        roomInfo,
        htlMasterId,
        regionId,
        regionUpperId,
        leftBottomLatitude,
        leftBottomLongitude,
        rightTopLatitude,
        rightTopLongitude,
      } = filter;
      if (
        (htlMasterId &&
          !regionId &&
          !regionUpperId &&
          !getFirstValidValue(leftBottomLatitude, leftBottomLongitude, rightTopLatitude, rightTopLongitude)) ||
        isBackFromBooking(filter)
      ) {
        const selectedHotelId = htlMasterId;
        const payload = {
          checkIn,
          checkOut,
          roomInfo,
          htlMasterId: selectedHotelId,
        };
        const founedHotel = hotelSearch.hotelFareList.find((el) => el.htlMasterId == selectedHotelId);
        setIsOpenSearchList(true);
        if (selectedHotelId) hotelItemRef.current[selectedHotelId].scrollIntoView({ behavior: "smooth" });

        await hotelApi.selectHotel(founedHotel, selectedHotelId, payload);
      }
    }

    if (hotelSearch.hotelFareList && hotelItemRef.current) {
      setTimeout(() => {
        autoSelectHotelWhenSearchByHotelId();
      }, 0);
    }
  }, [hotelSearch, hotelItemRef]);

  useEffect(() => {
    dispatch(actionHotelCities());
    dispatch(actionHotelTravelRule());
    dispatch(actionHotelDetailCodes());
  }, []);

  return (
    <div id="container" className="pg-search custom-hotel-map-search">
      <Condition />
      <FilterArea isConditionChange={isConditionChange} resetCondition={resetCondition} />
      <SearchList isOpenSearchList={isOpenSearchList} setIsOpenSearchList={setIsOpenSearchList} hotelItemRef={hotelItemRef} />
      <Map
        center={center}
        setCenter={setCenter}
        setIsChangeMapCenter={setIsChangeMapCenter}
        hotelItemRef={hotelItemRef}
        setIsOpenSearchList={setIsOpenSearchList}
      />

      <HotelDetail />

      {/* FloatButton */}
      <RefreshSearchFloatButton isChangeMapCenter={isChangeMapCenter} refreshSearch={refreshSearch} isOpenSearchList={isOpenSearchList} />
      <HotelCompareFloatButton hotelItemRef={hotelItemRef} setIsOpenSearchList={setIsOpenSearchList} />
    </div>
  );
};

export default HotelSearch;
