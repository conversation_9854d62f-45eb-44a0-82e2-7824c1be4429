import { useEffect, useState } from "react";
import { cloneDeep, get } from "lodash";
import {
  HOTEL_DETAIL_TABS_VALUE,
  HOTEL_MAX_SLICE_ROOM_ITEM,
  HOTEL_SELECT_ACTION_TYPE,
  HOTEL_SORT_CONDITIONS,
  ROOM_HOTEL_FILTER,
  ROOM_HOTEL_FILTER_MAP,
  Y_N,
} from "@/constants/app";
import SortCondition from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList/SortCondition";
import IconSearchCategory from "@/assets/images/svg/ic-search-category.svg";
import IconChevronDown from "@/assets/images/svg/ic-chevron-down.svg";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  actionHotelCancelRes,
  actionHotelNotice,
  selectConditionHotel,
  selectFilterHotel,
  selectHotelRoomView,
  selectHotelTravelRule,
  selectHotelView,
} from "@/store/hotelSlice";
import RoomItem from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelDetail/RoomItem";
import PopSlideRoomImages from "@/app/components/user_v2/modal/PopSlideRoomImage.modal";
import PopCancelResRule from "@/app/components/user_v2/modal/PopCancelResRule.modal";
import PopNotice from "@/app/components/user_v2/modal/PopNotice.modal";
import Button from "@/app/components/user_v2/common/Button";
import { momentKR } from "@/utils/date";
import { getTextDemension, reverseMap } from "@/utils/common";
import { getHotelViolation } from "@/utils/app";
import InfiniteScroll from "@/app/components/user_v2/common/InfinititeScroll";
import { CircularProgress } from "@mui/material";

const RoomList = (props) => {
  const { selectedTab } = props;

  const dispatch = useAppDispatch();
  const hotelRoomView = useAppSelector(selectHotelRoomView);
  const { hotelContent } = useAppSelector(selectHotelView);
  const { nightCount } = useAppSelector(selectConditionHotel);
  const travelRuleBaseDTO = useAppSelector(selectHotelTravelRule);
  const [roomList, setRoomList] = useState([]);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const hotelFilter = useAppSelector(selectFilterHotel);

  const [roomCondition, setRoomCondition] = useState({
    sortBy: "lowAmount",
    filter: ["all"],
  });

  const [page, setPage] = useState(0);
  const [openPopSlideRoomImage, setOpenPopSlideRoomImage] = useState(false);
  const [openPopCancelResRule, setOpenPopCancelResRule] = useState(false);
  const [isBooking, setIsBooking] = useState(false);
  const [openPopNotice, setOpenPopNotice] = useState(false);

  function selectedSortCondtion(value) {
    setRoomCondition((prev) => ({ ...prev, sortBy: value }));
  }

  async function selectRoom(room, type) {
    setSelectedRoom(room);
    if (type === HOTEL_SELECT_ACTION_TYPE.PICTURES) setOpenPopSlideRoomImage(true);
    if (type === HOTEL_SELECT_ACTION_TYPE.RULE || type === HOTEL_SELECT_ACTION_TYPE.BOOKING) {
      await dispatch(actionHotelCancelRes({ roomInfo: JSON.stringify(room), htlMasterId: hotelContent.htlDetailInfo.htlMasterId })).unwrap();
      setIsBooking(type === HOTEL_SELECT_ACTION_TYPE.RULE ? false : true);
      setOpenPopCancelResRule(true);
    }
    if (type === HOTEL_SELECT_ACTION_TYPE.NOTICE) {
      await dispatch(actionHotelNotice({ roomInfo: JSON.stringify(room), htlMasterId: hotelContent.htlDetailInfo.htlMasterId })).unwrap();
      setOpenPopNotice(true);
    }
  }

  useEffect(() => {
    if (hotelRoomView) {
      setRoomList(get(hotelRoomView, "roomFareList", []));
    }
  }, [hotelRoomView]);

  useEffect(() => {
    function filterRoomByCondition(filter) {
      const originalRoomList = get(hotelRoomView, "roomFareList", []);
      if (filter[0] === ROOM_HOTEL_FILTER_MAP.all) {
        return originalRoomList;
      }

      const today = momentKR().format("YYYYMMDD");
      return originalRoomList.filter((roomFare) => {
        for (const item of filter) {
          switch (item) {
            case ROOM_HOTEL_FILTER_MAP.breakfastN:
              if (roomFare.breakfastYn !== Y_N.N) {
                return false;
              }
              break;
            case ROOM_HOTEL_FILTER_MAP.breakfastY:
              if (roomFare.breakfastYn !== Y_N.Y) {
                return false;
              }
              break;
            case ROOM_HOTEL_FILTER_MAP.freeCancleYn:
              if (roomFare.cancelDeadLine == null || roomFare.cancelDeadLine == "" || (roomFare.cancelDeadLine.length > 0 && roomFare.cancelDeadLine <= today)) {
                return false;
              }
              break;
            case ROOM_HOTEL_FILTER_MAP.fareRule:
              if (getHotelViolation(roomFare, travelRuleBaseDTO, nightCount, hotelFilter)) {
                return false;
              }
              break;
          }
        }
        return true;
      });
    }

    function sortRoomByCondition(roomList, sortBy) {
      const clonedRoomList = cloneDeep(roomList);
      const roomSortReverse = reverseMap(HOTEL_SORT_CONDITIONS);

      clonedRoomList.sort((a, b) => {
        switch (sortBy) {
          case roomSortReverse.recommend:
            return a.seqNo - b.seqNo;
          case roomSortReverse.lowAmount:
            return a.salePrice - b.salePrice;
          case roomSortReverse.highAmount:
            return b.salePrice - a.salePrice;
        }
        return true;
      });
      return clonedRoomList;
    }

    if (hotelRoomView) {
      setRoomList(sortRoomByCondition(filterRoomByCondition(roomCondition.filter), roomCondition.sortBy));
    } else {
      setRoomList([]);
    }
  }, [roomCondition.filter, hotelRoomView, roomCondition.sortBy]);

  return (
    <div className={`${selectedTab === HOTEL_DETAIL_TABS_VALUE.ROOM_LIST ? "block" : "!hidden"} transition-[opacity] duration-500`}>
      <div
        className="sticky flex items-center justify-between w-full bg-white  top-0 z-[21] p-[16px] h-[72px]"
        style={{ top: 30 * Math.ceil(getTextDemension(hotelContent?.htlDetailInfo?.htlNameKr).width / 448) + 32 + 48 }}
      >
        <p className="text-custom-gray-600 font-medium">총 {roomList.length}개</p>
        <SortCondition isRoomSort selected={roomCondition.sortBy} onSelect={selectedSortCondtion}>
          <Button className="custom-hotel-select btn-text cursor-pointer flex items-center gap-[2px] text-custom-gray-500 font-bold [&_path]:!fill-custom-gray-500">
            {HOTEL_SORT_CONDITIONS[roomCondition.sortBy]}
            <IconChevronDown />
          </Button>
        </SortCondition>
      </div>
      <div
        className="px-[16px] py-[8px] flex sticky bg-white top-0 z-[21] gap-[4px] !justify-normal"
        style={{ top: 30 * Math.ceil(getTextDemension(hotelContent?.htlDetailInfo?.htlNameKr).width / 448) + 32 + 48 + 72 }}
      >
        {ROOM_HOTEL_FILTER.map((filter) => {
          const { id, label, value } = filter;
          return (
            <Button
              key={id}
              className={`border btn-border !px-[12px] h-[40px] rounded-[8px] flex items-center cursor-pointer font-medium ${roomCondition.filter.includes(value) ? "!border-custom-blue-100 text-custom-blue-100" : ""}`}
              onClick={() => {
                if (value === ROOM_HOTEL_FILTER_MAP.all) {
                  setRoomCondition({ ...roomCondition, filter: [ROOM_HOTEL_FILTER_MAP.all] });
                  return;
                }
                if (roomCondition.filter[0] === ROOM_HOTEL_FILTER_MAP.all) {
                  setRoomCondition({ ...roomCondition, filter: [value] });
                  return;
                }
                if (roomCondition.filter.includes(value)) {
                  const newFilter = roomCondition.filter.filter((el) => el !== value)
                  setRoomCondition({ ...roomCondition, filter: newFilter.length === 0 ? [ROOM_HOTEL_FILTER_MAP.all] : newFilter });
                  return;
                }
                setRoomCondition({ ...roomCondition, filter: [...roomCondition.filter, value] });
              }}
            >
              {label}
            </Button>
          );
        })}
      </div>

      <div className="min-h-[240px] flex flex-col !items-baseline">
        {roomList.length === 0 ? (
          <div className="flex flex-col gap-[16px] my-auto w-full">
            <IconSearchCategory />
            <p className="text-custom-gray-1000 font-bold text-[16px] leading-[24px]">조건에 맞는 객실이 없습니다.</p>
          </div>
        ) : (
          <>
            {roomList.slice(0, HOTEL_MAX_SLICE_ROOM_ITEM).map((room, index) => {
              return <RoomItem key={index} room={room} selectRoom={selectRoom} htlMasterId={hotelContent.htlDetailInfo.htlMasterId} />;
            })}
            {page ? <InfiniteScroll
              className="w-full"
              loader={<div className="flex items-center justify-center mt-8"><CircularProgress /></div>}
              hasMore={roomList.length > HOTEL_MAX_SLICE_ROOM_ITEM + page * 10}
              fetchMore={() => setPage(page + 1)}
            >
              {roomList.slice(HOTEL_MAX_SLICE_ROOM_ITEM, HOTEL_MAX_SLICE_ROOM_ITEM + page * 10).map((room, index) => {
                return <RoomItem key={index} room={room} selectRoom={selectRoom} htlMasterId={hotelContent.htlDetailInfo.htlMasterId} />;
              })}
            </InfiniteScroll> : null}
          </>
        )}
        {HOTEL_MAX_SLICE_ROOM_ITEM < roomList.length && !page && (
          <div className="p-[16px] w-full">
            <Button
              className="w-full !bg-custom-bg-300 btn-secondary !font-bold !justify-center !h-[48px] !border-none !text-custom-blue-100"
              onClick={() => setPage(1)}
            >
              모든 객실 보기 ({roomList.length - HOTEL_MAX_SLICE_ROOM_ITEM}개)
            </Button>
          </div>
        )}
      </div>

      <PopSlideRoomImages
        open={openPopSlideRoomImage}
        setOpen={setOpenPopSlideRoomImage}
        title={selectedRoom?.roomGradeName}
        imageList={selectedRoom?.roomImageList || []}
      />
      <PopCancelResRule
        open={openPopCancelResRule}
        setOpen={setOpenPopCancelResRule}
        selectedRoom={selectedRoom}
        isBooking={isBooking}
      />
      <PopNotice open={openPopNotice} setOpen={setOpenPopNotice} selectedRoom={selectedRoom} />
    </div>
  );
};

export default RoomList;
