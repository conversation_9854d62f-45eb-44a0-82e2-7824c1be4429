import { Fragment, useEffect, useMemo, useRef, useState } from "react";
import { Tab, Tabs } from "@mui/material";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import KoreanAir from "@/assets/images/svg/koreanair_symbol.svg";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  actionAddHotelCompare,
  actionRemoveHotelCompareById,
  actionSelectHotel,
  selectConditionHotel,
  selectHotelCompareList,
  selectHotelExchangeRate,
  selectHotelRoomView,
  selectHotelView,
  selectSelectedHotel,
} from "@/store/hotelSlice";
import Button from "@/app/components/user_v2/common/Button";
import IconClose from "@/assets/images/svg/ic-close.svg";
import IconStar from "@/assets/images/svg/ic-star.svg";
import IconCopy from "@/assets/images/svg/ic-copy.svg";
import IconAdd from "@/assets/images/svg/ic-add.svg";
import IconCheck from "@/assets/images/svg/ic-check.svg";
import PopSlideRoomImages from "@/app/components/user_v2/modal/PopSlideRoomImage.modal";
import Divider from "@/app/components/user_v2/common/Divider";
import { DIVIDER_DIRECTION, HOTEL_DETAIL_TABS, HOTEL_DETAIL_TABS_VALUE, MAX_HOTEL_COMPARISION, SNACK_BAR_TYPE } from "@/constants/app";
import RoomList from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelDetail/RoomList";
import DetailInfo from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelDetail/DetailInfo";
import { comma, time, unescapeHtml, getTextDemension } from "@/utils/common";
import SnackBar from "@/app/components/user_v2/common/SnackBar";
import { selectLoadingUser } from "@/store/loadingUserSlice";
import { getPriceByExchangeRate, getRoomMinSalePrice } from "@/utils/app";
import HotelDetailLoading from "@/app/pages/user_v2/travel/hotel/HotelSearch/HotelDetail/HotelDetailLoading";
import Image from "@/app/components/user_v2/common/Image";
import useRewardMileSettings from "@/app/hooks/useRewardMileSettings";
import "@/styles/user_v2/hotelSearch.css";

const HotelDetail = () => {
  const dispatch = useAppDispatch();

  const lastScrollTop = useRef(0);
  const containerRef = useRef(null);
  const hotelNameRef = useRef(null);

  const { isDisplayMileageKorea } = useRewardMileSettings();
  const { nightCount } = useAppSelector(selectConditionHotel);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const hotelRoomView = useAppSelector(selectHotelRoomView);
  const selectedHotel = useAppSelector(selectSelectedHotel);
  const { loadingNormalCount } = useAppSelector(selectLoadingUser);
  const hotelCompareList = useAppSelector(selectHotelCompareList);
  const { hotelContent } = useAppSelector(selectHotelView);

  const [selectedTab, setSelectedTab] = useState(HOTEL_DETAIL_TABS_VALUE.ROOM_LIST);
  const [openModalSlideImages, setOpenModalSlideImages] = useState(false);
  const [atTop, setAtTop] = useState(true);
  const [optionSnackBar, setOptionSnackBar] = useState({
    type: SNACK_BAR_TYPE.SUCCESS,
    title: "비교함에 추가되었습니다.",
  });
  const [showSnackBar, setShowSnackBar] = useState(false);

  const isSelectedCompare = useMemo(() => {
    return hotelCompareList.some((hotel) => hotel.htlDetailInfo.htlMasterId === hotelContent.htlDetailInfo.htlMasterId);
  }, [hotelCompareList, hotelContent]);

  const mileage = useMemo(() => {
    return getRoomMinSalePrice(hotelRoomView);
  }, [hotelRoomView]);

  function closeHotelDetail() {
    dispatch(actionSelectHotel(null));
  }

  function openMoreImage() {
    if (hotelContent?.htlImageList?.length > 0) setOpenModalSlideImages(true);
  }

  function copyHotelAddress() {
    navigator.clipboard.writeText(hotelContent.htlDetailInfo.addr);
    setShowSnackBar(true);
    setOptionSnackBar({
      type: SNACK_BAR_TYPE.SUCCESS,
      title: "복사되었습니다",
    });
  }

  function addCompareHotel() {
    const isSelectedCompare = hotelCompareList.some((hotel) => hotel.htlDetailInfo.htlMasterId === hotelContent.htlDetailInfo.htlMasterId);
    const isMaxSelectCompare = hotelCompareList.length >= MAX_HOTEL_COMPARISION;
    if (isSelectedCompare) {
      dispatch(actionRemoveHotelCompareById(hotelContent.htlDetailInfo.htlMasterId));
      return;
    }

    if (isMaxSelectCompare) {
      setShowSnackBar(true);
      setOptionSnackBar({
        type: SNACK_BAR_TYPE.DANGER,
        title: "비교담기는 최대 3개까지만 가능합니다.",
      });
      return;
    }

    dispatch(actionAddHotelCompare({ ...hotelContent, roomView: hotelRoomView }));
    setShowSnackBar(true);
    setOptionSnackBar({
      type: SNACK_BAR_TYPE.SUCCESS,
      title: "비교함에 추가되었습니다.",
    });
  }

  useEffect(() => {
    const handleScroll = () => {
      const div = containerRef.current;
      if (!div) return;
      const currentScrollTop = div.scrollTop;
      setAtTop(currentScrollTop === 0);
      lastScrollTop.current = currentScrollTop;
    };
    const div = containerRef.current;
    if (div) {
      div.addEventListener("scroll", handleScroll);
    }
    return () => {
      if (div) {
        div.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  useEffect(() => {
    setSelectedTab(HOTEL_DETAIL_TABS_VALUE.ROOM_LIST);
  }, [selectedHotel]);

  return (
    <Fragment>
      <PortalComponent>
        <SnackBar
          className="w-[448px] !bottom-[24px] !top-auto !left-[calc(360px+32px)] !h-fit"
          type={optionSnackBar.type}
          open={showSnackBar}
          setOpen={setShowSnackBar}
          title={optionSnackBar.title}
        />

        <div
          ref={containerRef}
          className={`${selectedHotel !== null ? "visible opacity-100 translate-x-0" : "invisible opacity-0 -translate-x-[360px]"} hidden-scrollbar z-[15] fixed top-[241px] left-[376px] w-[480px] shadow-custom-200 transition-all rounded-[10px] duration-300 linear h-[calc(100vh-258px)] overflow-y-auto bg-white border-custom-gray-200`}
        >
          {loadingNormalCount > 0 ? (
            <HotelDetailLoading />
          ) : (
            <Fragment>
              <div
                className={`${atTop ? "opacity-0 invisible" : "opacity-100 visible"} sticky  w-full trasition-[opacity, visibility] duration-300  p-[16px] top-0 bg-white z-[22]`}
                style={{
                  marginTop: `-${30 * Math.ceil(getTextDemension(hotelContent?.htlDetailInfo?.htlNameKr).width / 448) + 32}px`,
                }}
              >
                <p className="hotel-name-kr font-bold text-[20px] text-custom-gray-600 leading-[30px]">
                  {unescapeHtml(hotelContent?.htlDetailInfo?.htlNameKr)}
                </p>
              </div>
              <div className="detail-hotel">
                <div className="hotel-images-container !grid-cols-2 cursor-pointer" onClick={openMoreImage}>
                  <div className="main-image">
                    <Image className="!h-[200px] object-cover" src={hotelContent?.htlImageList?.[0]?.imgUrl} alt="Large" />
                  </div>
                  <div className="thumbnail-images !grid-cols-2 !grid-rows-2">
                    <Image className="!h-[100px] object-cover" src={hotelContent?.htlImageList?.[1]?.imgUrl} alt={`Small 1`} />
                    <Image className="!h-[100px] object-cover" src={hotelContent?.htlImageList?.[2]?.imgUrl} alt={`Small 2`} />
                    <Image className="!h-[100px] object-cover" src={hotelContent?.htlImageList?.[3]?.imgUrl} alt={`Small 3`} />
                    <div className="blur-overlay">
                      <Image className="!h-[100px] object-cover" src={hotelContent?.htlImageList?.[4]?.imgUrl} alt="More" />
                      <div className="overlay-text">
                        {hotelContent?.htlImageList?.length - 4 > 0 ? `+ ${hotelContent?.htlImageList?.length - 4}` : ""}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="hotel-info flex flex-col p-[16px] pb-[8px] gap-[16px] !items-baseline">
                  <div className="flex flex-col gap-[8px] !items-baseline w-full">
                    <div>
                      <p ref={hotelNameRef} className="hotel-name-kr font-bold text-[20px] text-custom-gray-600 leading-[30px]">
                        {unescapeHtml(hotelContent?.htlDetailInfo?.htlNameKr)}
                      </p>
                      <p className="text-custom-gray-500 leading-[21px]">{unescapeHtml(hotelContent?.htlDetailInfo?.htlNameEn)}</p>
                    </div>
                    <div className="flex !justify-normal gap-[4px]">
                      <span className="hotel-grade text-custom-gray-300">
                        <p>{hotelContent?.htlDetailInfo?.htlGrade >= 1 ? `${hotelContent?.htlDetailInfo?.htlGrade}성급` : "성급 없음"}</p>
                      </span>
                      <span className="hotel-star flex">
                        {hotelContent?.htlDetailInfo?.htlGrade >= 1 &&
                          Array.from({ length: Math.floor(Number(hotelContent?.htlDetailInfo?.htlGrade || 1)) }).map((_, index) => (
                            <IconStar key={index} />
                          ))}
                      </span>
                    </div>
                  </div>

                  <div className="flex rounded-[8px] border border-custom-gray-100 border-solid gap-[8px] py-[16px] w-full">
                    <div className="flex flex-col flex-1 gap-[8px]">
                      <p className="text-custom-gray-500 font-medium">체크인</p>
                      <p className="text-custom-gray-600 font-bold text-[16px]">
                        {hotelContent?.htlDetailInfo?.checkinTime ? `${time(hotelContent?.htlDetailInfo?.checkinTime)} 이후` : "-"}
                      </p>
                    </div>
                    <Divider className="!h-[53px]" />
                    <div className="flex flex-col flex-1 gap-[8px]">
                      <p className="text-custom-gray-500 font-medium">체크아웃</p>
                      <p className="text-custom-gray-600 font-bold text-[16px]">
                        {hotelContent?.htlDetailInfo?.checkoutTime ? `${time(hotelContent?.htlDetailInfo?.checkoutTime)} 이전` : "-"}
                      </p>
                    </div>
                  </div>

                  <div>
                    <div className="flex !justify-normal !items-center">
                      <span className="text-custom-gray-300 font-bold leading-[21px] shrink-0">위치</span>
                      <span className="text-custom-gray-300 mx-[8px] flex items-center leading-[21px]">
                        <span className="truncate max-w-[320px]">{hotelContent?.htlDetailInfo?.addr}</span>
                        <Button
                          onClick={copyHotelAddress}
                          className="!border-none shrink-0 !ml-[8px] !text-custom-blue-100 font-medium !gap-[2px] !h-[32px] !inline-flex translate-y-[2px] btn-text !px-[6px]"
                        >
                          <IconCopy className="shrink-0" /> 복사
                        </Button>
                      </span>
                    </div>

                    {hotelContent?.htlDetailInfo?.telno && (
                      <div className="">
                        <span className="text-custom-gray-300 font-bold leading-[32px] mr-[8px]">연락처</span>
                        <span className="text-custom-gray-300">{hotelContent?.htlDetailInfo?.telno}</span>
                      </div>
                    )}
                    {hotelContent?.htlDetailInfo?.faxno && (
                      <div className="">
                        <span className="text-custom-gray-300 font-bold leading-[32px] mr-[8px]">Fax</span>
                        <span className="text-custom-gray-300">{hotelContent?.htlDetailInfo?.faxno}</span>
                      </div>
                    )}
                    {hotelContent?.htlFacilityList?.length > 0 && hotelContent?.htlFacilityList && (
                      <div className="flex !items-start mt-[6px] !justify-normal">
                        <span className="text-custom-gray-300 font-bold mr-[8px] leading-[21px] shrink-0">편의시설</span>
                        <span className="text-custom-gray-300 leading-[21px]">
                          {hotelContent?.htlFacilityList?.map((el) => el?.facilityName).join(", ")}
                        </span>
                      </div>
                    )}
                  </div>

                  <Divider className="w-full" direction={DIVIDER_DIRECTION.HORIZONTAL} />

                  <div className="flex w-full pb-[16px] relative before:absolute before:w-[calc(100%+32px)] before:h-[8px] before:bg-custom-gray-700 before:bottom-[-8px] before:left-[-16px]">
                    <Button
                      className={`btn-border ${isSelectedCompare ? "active" : ""} ${hotelCompareList.length >= MAX_HOTEL_COMPARISION && !isSelectedCompare ? "opacity-50" : ""} font-medium`}
                      onClick={addCompareHotel}
                    >
                      {isSelectedCompare ? <IconCheck /> : <IconAdd />}
                      비교담기
                    </Button>
                    <div>
                      <p className="text-custom-gray-600 font-bold leading-[36px] text-[24px] text-end">
                        {exchangeRate > 1
                          ? `$${comma(getPriceByExchangeRate(hotelRoomView?.minSalePrice, exchangeRate))}`
                          : `${comma(hotelRoomView?.minSalePrice)}원`}
                        ~
                      </p>
                      <p className="flex !items-end !justify-end text-end text-[12px] gap-[4px] leading-[20px] text-custom-gray-500">
                        {mileage > 0 && isDisplayMileageKorea && (
                          <Fragment>
                            <span className="flex gap-[4px]">
                              <KoreanAir />
                              최대 {mileage}마일 적립
                            </span>
                            <span>∙</span>
                          </Fragment>
                        )}
                        {nightCount}박 평균가
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              {selectedHotel !== null && (
                <div className="custom-tab">
                  <div
                    className="sticky top-0 z-[22] bg-white mt-[8px]"
                    style={{ top: 30 * Math.ceil(getTextDemension(hotelContent?.htlDetailInfo?.htlNameKr).width / 448) + 32 }}
                  >
                    <Tabs value={selectedTab} onChange={(_, value) => setSelectedTab(value)}>
                      {HOTEL_DETAIL_TABS.map((tab) => (
                        <Tab key={tab.id} value={tab.value} label={tab.label} />
                      ))}
                    </Tabs>
                  </div>
                  <RoomList selectedTab={selectedTab} />
                  <DetailInfo selectedTab={selectedTab} copyHotelAddress={copyHotelAddress} />
                </div>
              )}
            </Fragment>
          )}
        </div>

        <Button
          className={`btn-icon ${selectedHotel !== null ? "visible opacity-100" : "invisible opacity-0 transition-none"} fixed z-[21] top-[241px] !px-[10px] !min-w-[40px] left-[calc(840px+16px+8px)] shadow-custom-100 !h-[40px] transition-all duration-[0.44s] linear !justify-center !gap-0 !border-none bg-white hover:bg-custom-gray-1100`}
          onClick={closeHotelDetail}
        >
          <IconClose />
        </Button>
      </PortalComponent>

      <PopSlideRoomImages
        open={openModalSlideImages}
        setOpen={setOpenModalSlideImages}
        title={hotelContent?.htlDetailInfo?.htlNameKr}
        imageList={hotelContent?.htlImageList}
      />
    </Fragment>
  );
};

export default HotelDetail;
