import { Fragment, useEffect, useState } from "react";
import Button from "@/app/components/user_v2/common/Button";
import Image from "@/app/components/user_v2/common/Image";
import Divider from "@/app/components/user_v2/common/Divider";
import IconChevronRight from "@/assets/images/svg/ic-chevron-right.svg";
import IconCaution from "@/assets/images/svg/ic-caution.svg";
import KoreanAir from "@/assets/images/svg/koreanair_symbol.svg";
import { momentKR } from "@/utils/date";
import { BADGE_TYPE, HOTEL_SELECT_ACTION_TYPE, Y_N } from "@/constants/app";
import { currencyFormat } from "@/utils/common";
import { useAppSelector } from "@/store";
import { selectConditionHotel, selectFilterHotel, selectHotelExchangeRate, selectHotelTravelRule } from "@/store/hotelSlice";
import { getHotelViolation, getPriceByExchangeRate } from "@/utils/app";
import Badge from "@/app/components/user_v2/common/Badge";
import useRewardMileSettings from "@/app/hooks/useRewardMileSettings";
import { postCancelCharges } from "@/service/hotel";

const RoomItem = (props) => {
  const { room, selectRoom, htlMasterId } = props;
  const {
    roomImageList = [],
    cancelDeadLine = "",
    displayCancelDate = "",
    roomNotiYn,
    breakfastYn,
    salePrice,
    mileage = 0,
    roomGradeName,
    roomAdditionalInfo,
    standardPerson,
    maxPerson,
  } = room;

  const [cancelCharges, setCancelCharges] = useState(null);

  useEffect(() => {
    const checkCancelCharges = async () => {
      const hotelCancelRes = await postCancelCharges({ roomInfo: JSON.stringify(room) }, htlMasterId);
      if (hotelCancelRes?.data?.roomJson) {
        const xRoomJson = JSON.parse(hotelCancelRes.data.roomJson);
        const cancelDeadLine = xRoomJson.cancelDeadLine;
        const displayCancelDate = xRoomJson.displayCancelDate;
        const roomCancelChargeList = xRoomJson.roomCancelChargeList;
        const roomSalePrice = xRoomJson.roomSalePrice;
        let cancelDeadLineYes = false;
        let year, month, day;
        const today = momentKR().format("YYYYMMDD");

        if (displayCancelDate.length > 0 && displayCancelDate >= today) {
          cancelDeadLineYes = true;
          year = displayCancelDate.slice(0, 4);
          month = displayCancelDate.slice(4, 6);
          day = displayCancelDate.slice(6, 8);
        } else if (cancelDeadLine.length > 0 && cancelDeadLine >= today) {
          cancelDeadLineYes = true;
          year = cancelDeadLine.slice(0, 4);
          month = cancelDeadLine.slice(4, 6);
          day = cancelDeadLine.slice(6, 8);
        }

        if (cancelDeadLineYes) {
          if (roomCancelChargeList !== null) {
            const cancelFirstItem = roomCancelChargeList[0];
            if (cancelFirstItem.cancelAvailableYn == "Y") {
              const displayCancelDate = cancelFirstItem.toDateText;
              year = displayCancelDate.slice(0, 4);
              month = displayCancelDate.slice(4, 6);
              day = displayCancelDate.slice(6, 8);
              setCancelCharges({
                allow: true,
                text: `무료취소 ${year}.${month}.${day} 17:00 까지`,
              })
            } else {
              setCancelCharges({
                allow: true,
                text: `무료취소 ${year}.${month}.${day} 17:00 까지`,
              })
            }
          } else {
            setCancelCharges({
              allow: true,
              text: `무료취소 ${year}.${month}.${day} 17:00 까지`,
            })
          }
        } else {
          setCancelCharges({
            allow: false,
            text: "환불불가",
          })
        }
      }
    };

    checkCancelCharges();
  }, []);

  const { isDisplayMileageKorea } = useRewardMileSettings();
  const { nightCount } = useAppSelector(selectConditionHotel);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const travelRuleBaseDTO = useAppSelector(selectHotelTravelRule);
  const filter = useAppSelector(selectFilterHotel);

  const isRoomViolation = getHotelViolation(room, travelRuleBaseDTO, nightCount, filter);

  return (
    <div className="p-[16px] w-full flex flex-col gap-[8px] !items-baseline">
      <div className="flex gap-[12px] w-full !justify-normal !items-start">
        {roomImageList.length > 0 && (
          <div className="relative shrink-0">
            <Fragment>
              <Image className="!w-[120px] !h-[120px] object-cover rounded-[8px]" src={roomImageList[0].imgUrl} />
              <span
                onClick={() => selectRoom(room, HOTEL_SELECT_ACTION_TYPE.PICTURES)}
                className="min-w-[24px] h-[24px] cursor-pointer absolute right-[4px] bottom-[4px] px-[6px] py-[4px] bg-custom-gray-300 font-medium rounded-[8px] text-[12px] text-white flex !items-center !justify-center"
              >
                {roomImageList.length}
              </span>
            </Fragment>
          </div>
        )}
        <div className="h-full flex flex-col !items-baseline gap-[8px]">
          <Badge
            type={isRoomViolation ? BADGE_TYPE.CORLOR.SECONDARY : BADGE_TYPE.CORLOR.SUCCESS}
            text={isRoomViolation ? "출장규정에 부적합" : "출장규정에 적합"}
          />

          <p className="text-[16px] font-bold text-custom-gray-600 leading-[24px]">{roomGradeName}</p>
          <div>
            <div className="flex gap-[2px] h-[32px] !justify-normal">
              <div className="flex gap-[8px] !justify-normal">
                <p className="text-custom-gray-300">즉시확정</p>
                <Divider className="w-[1px] !h-[15px]" />
              </div>
              {!cancelCharges && cancelDeadLine === "" && displayCancelDate === "" && (
                <div>
                  <style>
                    {`@keyframes c4 {to{clip-path: inset(0 -7.1px 0 0)}}`}
                  </style>
                  <p style={{ clipPath: "inset(0 35.5px 0 0)", animation: "c4 4s steps(6) infinite", fontWeight: "bold" }}>. . . . . </p>
                </div>
              )}
              <p
                className={`flex cursor-pointer`}
                onClick={() => {
                  selectRoom(room, HOTEL_SELECT_ACTION_TYPE.RULE);
                }}
              >
                {cancelCharges && (
                  <Button className={cancelCharges.allow ? "btn-text flex !text-custom-blue-100 [&_path]:fill-custom-blue-100 !gap-[2px]" : "btn-text flex !text-custom-red-100 [&_path]:fill-custom-red-100 !gap-[2px]"}>
                    <p>{cancelCharges.text}</p> <IconChevronRight className="!w-[16px] !h-[16px]" />
                  </Button>
                )}
                {!cancelCharges && ((cancelDeadLine.length > 0 && cancelDeadLine >= momentKR().format("YYYYMMDD")) ||
                  (displayCancelDate.length > 0 && displayCancelDate >= momentKR().format("YYYYMMDD"))) && (
                    <Button className="btn-text flex !text-custom-blue-100 [&_path]:fill-custom-blue-100 !gap-[2px]">
                      <p>무료취소 {momentKR(cancelDeadLine, "YYYYMMDD").format("YYYY. MM. DD까지")}</p>{" "}
                      <IconChevronRight className="!w-[16px] !h-[16px]" />
                    </Button>
                  )}
                {!cancelCharges && ((cancelDeadLine.length > 0 && cancelDeadLine < momentKR().format("YYYYMMDD")) ||
                  (displayCancelDate.length > 0 && displayCancelDate < momentKR().format("YYYYMMDD"))) && (
                    <Button className="btn-text flex !text-custom-red-100 [&_path]:fill-custom-red-100 !gap-[2px]">
                      <p>환불불가</p> <IconChevronRight className="!w-[16px] !h-[16px]" />
                    </Button>
                  )}
              </p>
            </div>
            <p className="text-custom-gray-300  ">
              {breakfastYn === Y_N.Y ? "조식포함" : "조식불포함"}
              {roomAdditionalInfo?.bedTypeInfo?.bedTypeList && `, ${roomAdditionalInfo?.bedTypeInfo?.bedTypeList.map((bed) => bed.desc).join(", ")}`}
              {standardPerson && `, 기준${standardPerson}인 ${maxPerson && `(최대${maxPerson}인`})`}
            </p>
          </div>
        </div>
      </div>

      <div className="flex w-full items-end relative before:absolute before:w-full before:h-[1px] before:bottom-[-16px] before:left-0 before:bg-custom-gray-100">
        {roomNotiYn === Y_N.Y && (
          <Button onClick={() => selectRoom(room, HOTEL_SELECT_ACTION_TYPE.NOTICE)} className="btn-text !font-medium">
            <IconCaution className="[&_path]:fill-custom-gray-500" />
            알림/특전 <IconChevronRight className="[&_path]:fill-custom-gray-500 !w-[16px] !h-[16px]" />
          </Button>
        )}
        <div className="flex-1 flex !justify-end gap-[12px]">
          <div className="flex flex-col !items-end">
            <p className="text-custom-gray-600 font-bold text-[20px] leading-[30px]">
              {exchangeRate > 1
                ? `$${currencyFormat(getPriceByExchangeRate(Number(salePrice), exchangeRate))}`
                : `${currencyFormat(Number(salePrice))}원`}
            </p>
            <p className="flex !justify-end text-[12px] text-custom-gray-500 gap-[6px]">
              {mileage > 0 && isDisplayMileageKorea && (
                <Fragment>
                  <span className="flex gap-[6px]">
                    <KoreanAir />+{mileage}마일 적립
                  </span>
                  ∙
                </Fragment>
              )}
              <span>{nightCount}박</span>
            </p>
          </div>
          <Button
            className={`!w-[72px] !h-[40px] !justify-center font-medium !border-none btn-primary
            ${isRoomViolation ? "!bg-custom-bg-400 !opacity-100 text-custom-gray-900" : "bg-custom-blue-100 text-white"}
          `}
            onClick={() => selectRoom(room, HOTEL_SELECT_ACTION_TYPE.BOOKING)}
            disabled={isRoomViolation}
          >
            예약
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RoomItem;
