import { HOTEL_DETAIL_TABS_VALUE } from "@/constants/app";
import IconCopy from "@/assets/images/svg/ic-copy.svg";
import Button from "@/app/components/user_v2/common/Button";
import { useAppSelector } from "@/store";
import { selectHotelView } from "@/store/hotelSlice";
import { normalizeDangerHtmlHotelContent, time } from "@/utils/common";

const DetailInfo = (props) => {
  const { selectedTab, copyHotelAddress } = props;
  const { hotelContent } = useAppSelector(selectHotelView);

  return (
    <div
      className={`${selectedTab === HOTEL_DETAIL_TABS_VALUE.DETAIL_INFO ? "flex" : "!hidden"} flex gap-[32px] p-[16px] transition-[opacity] duration-500 !items-baseline flex-col`}
    >
      <div className="flex flex-col gap-[8px] !justify-normal !items-baseline w-full">
        <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">위치</span>
        <div className="flex gap-[8px]">
          <span className="text-custom-gray-600 flex items-center leading-[21px]">
            <span className="truncate max-w-[340px] inline-block">{hotelContent?.htlDetailInfo?.addr}</span>
            <Button
              onClick={() => copyHotelAddress(hotelContent?.htlDetailInfo?.addr)}
              className="!border-none !text-custom-blue-100 font-medium !gap-[2px] ml-[8px] !h-[32px] !inline-flex translate-y-[2px] btn-text !px-[6px]"
            >
              <IconCopy /> 복사
            </Button>
          </span>
        </div>
      </div>

      <div className="flex flex-col gap-[8px] !justify-normal !items-baseline w-full">
        <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">연락처</span>
        <div className="flex gap-[8px]">
          <span className="text-custom-gray-600">{hotelContent?.htlDetailInfo?.telno}</span>
        </div>
      </div>

      <div className="flex flex-col gap-[8px] !justify-normal !items-baseline w-full">
        <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">체크인 / 체크아웃</span>
        <div className="flex flex-col !items-baseline gap-[8px]">
          <span className="text-custom-gray-600">
            {time(hotelContent?.htlDetailInfo?.checkinTime)} 이후 /{" "}
            {hotelContent?.htlDetailInfo?.checkoutTime ? `${time(hotelContent?.htlDetailInfo?.checkoutTime)} 이전` : "-"}
          </span>
          <ul className="list-disc text-custom-gray-500 pl-[16px] text-[12px] marker:text-custom-gray-1000 ">
            <li className="marker:text-[10px] before:ml-[-4px]">
              대표 체크인/아웃 시간은 객실별, 요일별로 상이할 수 있습니다. <br />
              자세한 정책은 해당 호텔측에 문의바랍니다.
            </li>
          </ul>
        </div>
      </div>

      <div className="flex flex-col !justify-normal !items-baseline w-full">
        <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">공지사항</span>
        <div
          className="leading-[21px] text-custom-gray-600  [&_b]:!font-bold [&_strong]:!font-bold [&_b]:!mt-[16px] [&_strong]:!mt-[16px] [&_b]:!mb-[4px] [&_strong]:!mb-[4px] [&_b]:inline-block [&_strong]:inline-block [&_b]:text-[14px] [&_strong]:text-[14px]"
          dangerouslySetInnerHTML={{ __html: normalizeDangerHtmlHotelContent(hotelContent?.htlDetailInfo?.htlNotice) }}
        />
      </div>

      <div className="flex flex-col !justify-normal !items-baseline w-full">
        <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">이용안내 및 부대시설</span>
        <div
          className="leading-[21px] text-custom-gray-600  [&_b]:!font-bold [&_strong]:!font-bold [&_b]:!mt-[16px] [&_strong]:!mt-[16px] [&_b]:!mb-[4px] [&_strong]:!mb-[4px] [&_b]:inline-block [&_strong]:inline-block [&_b]:text-[14px] [&_strong]:text-[14px]"
          dangerouslySetInnerHTML={{ __html: normalizeDangerHtmlHotelContent(hotelContent?.htlDetailInfo?.detailDesc) }}
        />
      </div>

      {hotelContent?.htlFacilityList?.length > 0 && (
        <div className="flex flex-col gap-[8px] !justify-normal !items-baseline w-full">
          <span className="text-custom-gray-600 font-bold text-[16px] leading-[21px]">편의시설</span>
          <span className="text-custom-gray-600 leading-[21px]">{hotelContent?.htlFacilityList?.map((el) => el?.facilityName).join(", ")}</span>
        </div>
      )}
    </div>
  );
};

export default DetailInfo;
