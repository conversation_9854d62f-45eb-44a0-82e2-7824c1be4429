import { Skeleton } from "@mui/material";

const HotelDetailLoading = () => {
  return (
    <div className="p-[16px] flex flex-col !items-baseline !justify-normal gap-[16px] h-full [&_span]:!scale-100">
      <Skeleton height={200} width="100%" />
      <Skeleton height={25} width="80%" />
      <Skeleton height={25} width="60%" />
      <Skeleton height={25} width="40%" />
      <Skeleton height={100} width="100%" />
      <Skeleton height={25} width="60%" />
      <Skeleton height={25} width="80%" />
      <Skeleton height={25} width="100%" />
      <Skeleton height={25} width="60%" />
      <Skeleton height={60} width="100%" />
      <Skeleton height={40} width="100%" />
      <Skeleton height={30} width="100%" />
      <Skeleton height={100} width="100%" className="min-h-[100px] flex-1" />
      <Skeleton height={100} width="100%" className="min-h-[100px] flex-1" />
    </div>
  );
};

export default HotelDetailLoading;
