import { useState } from "react";
import { useAppSelector } from "@/store";
import { selectHotelCompareList } from "@/store/hotelSlice";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import HotelCompare from "@/app/components/user_v2/modal/HotelCompare.modal";

const HotelCompareFloatButton = (props) => {
  const { hotelItemRef, setIsOpenSearchList } = props;
  const hotelCompareList = useAppSelector(selectHotelCompareList);
  const [openHotelCompareModal, setOpenHotelCompareModal] = useState(false);

  return (
    <PortalComponent>
      <div
        className={`fixed right-[16px] top-[241px] min-w-[110px] z-[25] flex flex-col cursor-pointer rounded-[16px] border-[2px] border-custom-blue-100 bg-custom-gray-400 p-[16px]
        ${hotelCompareList.length > 0 ? "visible opacity-100" : "invisible opacity-0"} transition-all duration-500
        `}
        onClick={() => setOpenHotelCompareModal(true)}
      >
        <div className="flex min-h-[40px] relative" style={{ width: (hotelCompareList.length - 1) * 15 + 40 }}>
          {hotelCompareList.map((hotel, index) => {
            const { htlMasterId, mainImg } = hotel.htlDetailInfo;
            return (
              <img
                className={`absolute !w-[40px] bg-white !h-[40px] object-cover border-[2px] rounded-[8px] border-solid border-white shadow-custom-300`}
                src={mainImg}
                alt="hotel-compare-img"
                style={{ left: index > 0 ? `${index * 15}px` : 0, zIndex: Math.abs(index - 15) }}
                key={htlMasterId}
              />
            );
          })}
        </div>
        <p className="text-custom-blue-100 text-[18px] font-bold mt-[12px] leading-[27px]">비교함 ({hotelCompareList.length})</p>
      </div>

      <HotelCompare
        hotelItemRef={hotelItemRef}
        setIsOpenSearchList={setIsOpenSearchList}
        open={openHotelCompareModal}
        setOpen={setOpenHotelCompareModal}
      />
    </PortalComponent>
  );
};

export default HotelCompareFloatButton;
