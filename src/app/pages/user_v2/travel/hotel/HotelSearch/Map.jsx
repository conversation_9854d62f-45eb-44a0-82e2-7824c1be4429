import { useEffect } from "react";
import { get } from "lodash";
import { Map as ReactMap, AdvancedMarker, useMap, Marker } from "@vis.gl/react-google-maps";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import IconBed from "@/assets/images/svg/ic-bed.svg";
import { useAppSelector } from "@/store";
import {
  selectFilterHotel,
  selectHotelExchangeRate,
  selectHotelList,
  selectHotelLocation,
  selectHotelSearch,
  selectHotelSearchType,
  selectSelectedHotel,
} from "@/store/hotelSlice";
import { comma, getFirstValidValue } from "@/utils/common";
import { getPriceByExchangeRate } from "@/utils/app";
import { HOTEL_AUTOSEARCH_TYPE, ZOOM_LEVEL } from "@/constants/app";
import IconGGMap from "@/assets/images/svg/ic-ggmap.svg";
import useHotelApi from "@/app/hooks/useHotelApi";

const GOOGLE_API_KEY = import.meta.env.VITE_GOOGLE_API_KEY;

const Map = (props) => {
  const { center, setCenter, setIsOpenSearchList, setIsChangeMapCenter, hotelItemRef } = props;

  const map = useMap();
  const hotelApi = useHotelApi();

  const hotelLocation = useAppSelector(selectHotelLocation);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const hotelSearchType = useAppSelector(selectHotelSearchType);
  const hotelList = useAppSelector(selectHotelList);
  const hotelSearch = useAppSelector(selectHotelSearch);
  const selectedHotel = useAppSelector(selectSelectedHotel);
  const filter = useAppSelector(selectFilterHotel);

  async function selectHotel(hotel) {
    setIsOpenSearchList(true);
    hotelItemRef.current[hotel.htlMasterId].scrollIntoView({ behavior: "smooth" });
    if (selectedHotel && selectedHotel.htlMasterId === hotel.htlMasterId) return;
    const { checkIn, checkOut, roomInfo } = filter;
    const payload = {
      checkIn,
      checkOut,
      roomInfo,
      htlMasterId: hotel.htlMasterId,
    };

    const { latitude, longitude } = hotel;
    if (latitude && longitude) {
      map.panTo({ lat: parseFloat(latitude), lng: parseFloat(longitude) });
      map.setZoom(ZOOM_LEVEL.DETAIL);
    }

    await hotelApi.selectHotel(hotel, hotel.htlMasterId, payload);
  }

  useEffect(() => {
    if (hotelSearch && map) {
      if (hotelSearchType !== HOTEL_AUTOSEARCH_TYPE.MAP) {
        const { htlMasterId, hotelId, regionId, regionUpperId } = filter;

        const hotelFareList = get(hotelSearch, "hotelFareList", []);
        const firstHotel = hotelFareList?.[0];

        let lat = "";
        let lng = "";

        if (hotelLocation) {
          lat = hotelLocation.lat;
          lng = hotelLocation.lng;
        } else if (firstHotel?.latitude && firstHotel?.longitude) {
          lat = firstHotel.latitude;
          lng = firstHotel.longitude;
        }

        if (lat && lng) {
          setCenter({
            lat: parseFloat(lat.toString()),
            lng: parseFloat(lng.toString()),
          });
          setTimeout(() => {
            map.setCenter({ lat: parseFloat(lat.toString()), lng: parseFloat(lng.toString()) });
            if (getFirstValidValue(htlMasterId, hotelId) && !regionId && !regionUpperId) map.setZoom(ZOOM_LEVEL.DETAIL);
            else map.setZoom(ZOOM_LEVEL.DEFAULT);
          }, 0);
        } else {
          map.setZoom(ZOOM_LEVEL.MAP);
        }
      }
    }
  }, [hotelSearch, map, hotelSearchType]);

  return (
    <PortalComponent>
      <div className="z-[15] !absolute top-[226px] right-0 left-0 bottom-0 bg-white">
        <ReactMap
          mapId={GOOGLE_API_KEY}
          style={{ width: "100%", height: "100%" }}
          disableDefaultUI={true}
          defaultCenter={center}
          defaultZoom={12}
          onBoundsChanged={(e) => {
            const changedCenter = e.detail.center;
            if (JSON.stringify(changedCenter) === JSON.stringify(center)) {
              return;
            }
            setIsChangeMapCenter(true);
          }}
        >
          {hotelLocation?.lat && hotelLocation?.lng && hotelLocation?.name && (
            <AdvancedMarker zIndex={1} position={{ lat: parseFloat(hotelLocation.lat.toString()), lng: parseFloat(hotelLocation.lng.toString()) }}>
              <div className="translate-x-[50%] flex w-full">
                <IconGGMap className="scale-[1.2]" />
                <div className="max-w-[200px] ml-2">
                  <h1 className="text-[#EA4335] font-bold text-[14px]">{hotelLocation?.name}</h1>
                </div>
              </div>
            </AdvancedMarker>
          )}
          {hotelList?.length !== 0 ? (
            hotelList?.map((data) => {
              const { id, htlMasterId, salePrice } = data;
              const isSelected = selectedHotel?.htlMasterId === htlMasterId;

              if (data?.latitude && data?.longitude) {
                return (
                  <AdvancedMarker
                    key={id}
                    position={{ lat: parseFloat(data.latitude.toString()), lng: parseFloat(data.longitude.toString()) }}
                    onClick={() => selectHotel(data)}
                    zIndex={isSelected ? 10 : 2}
                  >
                    <div
                      className={`${isSelected ? "!bg-custom-blue-100 before:!border-t-custom-blue-100 !text-white active z-10" : ""}
                        bg-white map-tooltip py-[4px] pr-[8px] pl-[4px] rounded-full border-[2px] border-custom-blue-100 text-custom-gray-300 font-bold relative before:absolute after:absolute shadow-custom-600`}
                    >
                      <div className="flex gap-[8px]">
                        <div
                          className={`flex !items-center !justify-center ${isSelected ? "bg-white [&_svg]:fill-custom-blue-100" : "bg-custom-blue-100 [&_svg]:fill-white"} rounded-full !w-[28px] !h-[28px]`}
                        >
                          <IconBed className="!w-[16px] !h-[16px]" />
                        </div>
                        <p className="text-[15px] flex items-center justify-center leading-[150%]">
                          {exchangeRate > 1 ? `$${comma(getPriceByExchangeRate(salePrice, exchangeRate))}~` : `${comma(salePrice)}원~`}
                        </p>
                      </div>
                    </div>
                  </AdvancedMarker>
                );
              } else {
                return null;
              }
            })
          ) : (
            <></>
          )}
        </ReactMap>
      </div>
    </PortalComponent>
  );
};

export default Map;
