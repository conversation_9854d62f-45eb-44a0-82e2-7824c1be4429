import { forwardRef, Fragment, useEffect, useImperativeHandle, useRef, useState } from "react";
import { groupBy } from "lodash";
import useOpenComponent from "@/app/hooks/useOpenComponent";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import { useAppSelector } from "@/store";
import { selectHotelAutoSearch, selectHotelCities } from "@/store/hotelSlice";
import { CircularProgress } from "@mui/material";

const SearchCity = forwardRef((props, ref) => {
  const { children, onSelect, isAutoSearch, isLoadingAutoSearch, setIsAutoSearch, additionalPosition = { top: 0, left: 0 } } = props;
  const dropdownRef = useRef(null);
  const hotelCities = useAppSelector(selectHotelCities);
  const hotelAutoSearch = useAppSelector(selectHotelAutoSearch);

  const { isOpen, dropdownPosition, clonedChild, setIsOpen, calculatePosition } = useOpenComponent({ children, dropdownRef, isToggle: false });
  const [hotelSections, setHotelSections] = useState({});

  useImperativeHandle(ref, () => ({
    setIsOpen: (value) => {
      setIsOpen(value);
    },
    calculatePosition: () => {
      calculatePosition();
    },
  }));

  useEffect(() => {
    if (hotelCities) {
      const groupedBySection = groupBy(hotelCities, "airportSection");
      setHotelSections(groupedBySection);
    }
  }, [hotelCities]);

  useEffect(() => {
    if (!isOpen) setIsAutoSearch(false);
  }, [isOpen]);

  return (
    <Fragment>
      {clonedChild}
      <PortalComponent>
        <div
          id="layerSaerchCity"
          ref={dropdownRef}
          data-type-city={isAutoSearch ? "search" : "default"}
          className={`layer-saerch-city transition-[opacity] duration-500 type-hotel ${isOpen ? "visible opacity-100" : "invisible opacity-0"}`}
          style={{ top: dropdownPosition.top + 8, left: dropdownPosition.left - additionalPosition.left }}
        >
          <div id="cityForegin" className="city-foregin" style={{ display: "block" }}>
            {!isAutoSearch ? (
              <div className="item-name">
                {hotelSections &&
                  Object.entries(hotelSections).map(([section, hotelMainRegions], position) => {
                    const className = ["domestic", "japan", "china", "asia", "usa", "eu", "soc", "etc"];
                    return (
                      <dl key={section} className={className[position]}>
                        <dt>{section}</dt>
                        {hotelMainRegions.map((region) => {
                          return (
                            <dd
                              key={region.code}
                              onClick={() => {
                                onSelect(region);
                                setIsOpen(false);
                              }}
                            >
                              <a>{region.name}</a>
                            </dd>
                          );
                        })}
                      </dl>
                    );
                  })}
              </div>
            ) : (
              <div
                className="item-searhing w-[580px] max-h-[338px]"
                style={{ display: hotelAutoSearch.length === 0 && !isLoadingAutoSearch ? "none" : "block" }}
              >
                {isLoadingAutoSearch ? (
                  <div className="w-full h-[338px] flex !items-center !justify-center">
                    <CircularProgress />
                  </div>
                ) : (
                  <ul id="hotelAutoSearchArea" className="scrollbar-inner">
                    {hotelAutoSearch.map((hotel, index) => {
                      const { regionId, regionNameLong, type } = hotel;
                      return (
                        <li
                          key={regionId + index}
                          className={type}
                          onClick={() => {
                            onSelect(hotel);
                            setIsOpen(false);
                          }}
                        >
                          <a>
                            <span className="name" dangerouslySetInnerHTML={{ __html: regionNameLong }}></span> <span className="nation"></span>{" "}
                          </a>
                        </li>
                      );
                    })}
                  </ul>
                )}
              </div>
            )}
          </div>
        </div>
      </PortalComponent>
    </Fragment>
  );
});

SearchCity.displayName = "SearchCity";

export default SearchCity;
