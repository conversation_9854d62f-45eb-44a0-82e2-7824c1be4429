import { useState, useRef, useEffect, useMemo } from "react";
import { useClickOutside } from "@/app/hooks/useClickOutside";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionSetHotelFilter, selectFilterHotel } from "@/store/hotelSlice";
import RoomItem from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition/RoomItem";
import { MAX_HOTEL_CLASS_MEMBER } from "@/constants/app";

const ClassMember = () => {
  const dispatch = useAppDispatch();
  const { roomInfo, roomCount } = useAppSelector(selectFilterHotel);

  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const [openLayerClassMember, setOpenLayerClassMember] = useState(false);
  const [roomList, setRoomList] = useState([]);

  const totalPeopleInAllRooms = useMemo(() => {
    return roomList.reduce((acc, room) => acc + Number(room.adultCnt), 0);
  }, [roomList]);

  function toggleOpenLayerClassMember() {
    setOpenLayerClassMember(!openLayerClassMember);
  }

  function addNewRoom() {
    if (roomList.length === MAX_HOTEL_CLASS_MEMBER) return;
    setRoomList([...roomList, { id: roomList.length, adultCnt: 1 }]);
  }

  useEffect(() => {
    const roomInfoValue = roomInfo ? roomInfo : "1";
    const roomsWithPeople = roomInfoValue.split(",").map((el, index) => ({ id: index, adultCnt: Number(el) }));
    setRoomList(roomsWithPeople);
  }, [roomInfo]);

  useEffect(() => {
    if (!openLayerClassMember && roomList.length > 0) {
      const newRoomCount = roomList.length;
      const newRoomInfo = roomList.map((room) => room.adultCnt).join(",");
      dispatch(actionSetHotelFilter({ roomCount: newRoomCount, roomInfo: newRoomInfo }));
    }
  }, [openLayerClassMember]);

  useClickOutside(menuRef, buttonRef, () => setOpenLayerClassMember(false));

  return (
    <div className="!w-[320px] p-[16px] relative">
      <div className="flex flex-col gap-[4px] !items-start font-medium" ref={buttonRef} onClick={toggleOpenLayerClassMember}>
        <p className="text-custom-blue-100">인원, 객실 수</p>
        <a className="text-[16px] leading-[150%]">
          성인 {totalPeopleInAllRooms}명, 객실 {roomCount || roomList.length}개
        </a>
      </div>
      <div className={`layer-class-member type-hotel ${openLayerClassMember ? "!block" : "!hidden"} top-[calc(100%+8px)]`} ref={menuRef}>
        {roomList.map((room) => (
          <RoomItem key={room.id} {...room} setRoomList={setRoomList} />
        ))}
        {roomList.length !== MAX_HOTEL_CLASS_MEMBER && (
          <div className="btn-add-room">
            <button type="button" className="btn-default" onClick={addNewRoom}>
              객실추가
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClassMember;
