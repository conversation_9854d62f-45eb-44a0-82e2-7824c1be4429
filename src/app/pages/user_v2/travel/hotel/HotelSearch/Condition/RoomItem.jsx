import { useState } from "react";

const RoomItem = (props) => {
  const { id, adultCnt, setRoomList } = props;

  const [showDetail, setShowDetail] = useState(true);

  function addAdultCnt() {
    setRoomList((prev) =>
      prev.map((room) => {
        if (room.id === id) room.adultCnt++;
        return room;
      }),
    );
  }

  function minusAdultCnt() {
    setRoomList((prev) =>
      prev.map((room) => {
        if (room.id === id && room.adultCnt > 1) {
          room.adultCnt--;
        }
        return room;
      }),
    );
  }

  function removeRoom(id) {
    setRoomList((prev) => prev.filter((room) => room.id !== id));
  }

  function toggleShowDetail() {
    setShowDetail(!showDetail);
  }

  return (
    <div id="roomInfo_0" name="roomDiv" className={`box-item ${showDetail ? "active" : ""}`}>
      <p className="tit">
        객실
        {id !== 0 && (
          <button type="button" className="btn-remove-item" onClick={() => removeRoom(id)}>
            삭제
          </button>
        )}
      </p>
      <dl>
        <dt>성인</dt>
        <dd className="count flex items-center">
          <button type="button" className="btn-default minus" disabled={adultCnt === 1} onClick={minusAdultCnt}>
            -
          </button>
          <input type="number" name="adultCount" value={adultCnt} readOnly />
          <button type="button" className="btn-default plus" onClick={addAdultCnt}>
            +
          </button>
        </dd>
      </dl>
      {/*									<div class="drp-info" style="color:black; padding:10px 10px 0 10px;font-size: 13px;">※ 1객실 3인 이상인 경우 <a href="/user/v2/board/qna/list" style="color:black;font-weight: bold;text-decoration: underline;">1:1 문의</a>를 이용하여 요청 주시기 바랍니다.</div>*/}
      <button type="button" className="btn-item-arrow" onClick={toggleShowDetail} />
    </div>
  );
};

export default RoomItem;
