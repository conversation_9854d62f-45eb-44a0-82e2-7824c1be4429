import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { debounce, isEmpty } from "lodash";
import { useAppDispatch, useAppSelector } from "@/store";
import {
  actionClearExchangeRate,
  actionClearHotelCompare,
  actionClearSortBy,
  actionHotelAutoSearch,
  actionSelectHotel,
  actionSetHotelFilter,
  actionSetHotelSearchType,
  selectFilterHotel,
} from "@/store/hotelSlice";
import ClassMember from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition/ClassMember";
import KrDateRangePicker from "@/app/components/user_v2/common/KrDateRangePicker";
import { formatKRDateString, momentKR } from "@/utils/date";
import SearchCity from "@/app/pages/user_v2/travel/hotel/HotelSearch/Condition/SearchCity";
import { getPayloadHotelSearch, setQueryParams } from "@/utils/app";
import { HOTEL_AUTOSEARCH_TYPE } from "@/constants/app";
import { getFirstValidValue, stripHTML } from "@/utils/common";
import useHotelApi from "@/app/hooks/useHotelApi";
import { useMap } from "@vis.gl/react-google-maps";
import Button from "@/app/components/user_v2/common/Button";

const Condition = () => {
  const dispatch = useAppDispatch();
  const map = useMap();
  const hotelApi = useHotelApi();

  const filter = useAppSelector(selectFilterHotel);

  const searchCityRef = useRef(null);
  const inputRef = useRef(null);
  const [isAutoSearch, setIsAutoSearch] = useState(false);
  const [isLoadingAutoSearch, setIsLoadingAutoSearch] = useState(false);
  const [isUsingKeyWord, setIsUsingKeyWord] = useState(false);

  function onSelectDate(item) {
    const startDate = momentKR(item.startDate).format("YYYYMMDD");
    const endDate = momentKR(item.endDate).format("YYYYMMDD");
    dispatch(actionSetHotelFilter({ checkIn: startDate, checkOut: endDate }));
  }

  async function search(e) {
    e.preventDefault();
    const { htlMasterId, regionNameLong, regionId, regionUpperId, googleMapPlaceId } = filter;

    const isValid = handleValidate(filter);
    if (isValid === false) return;

    const additionalParams = {};

    const searchParams = {
      ...filter,
      leftBottomLatitude: "",
      leftBottomLongitude: "",
      rightTopLatitude: "",
      rightTopLongitude: "",
    };

    if (isUsingKeyWord || (regionNameLong && !htlMasterId && !regionId && !regionUpperId)) {
      additionalParams.keyword = stripHTML(regionNameLong || "");
      additionalParams.googleMapPlaceId = isUsingKeyWord ? "" : googleMapPlaceId || "";
      searchParams.htlMasterId = "";
      searchParams.regionId = "";
      searchParams.regionUpperId = "";
      if (isUsingKeyWord) searchParams.googleMapPlaceId = "";
    } else if (getFirstValidValue(regionId, regionUpperId)) {
      additionalParams.cityId = regionId;
      additionalParams.upperCityId = regionUpperId;
      additionalParams.keyword = "";
      additionalParams.googleMapPlaceId = "";
      searchParams.htlMasterId = "";
      searchParams.googleMapPlaceId = "";
    } else if (htlMasterId && !regionId && !regionUpperId) {
      additionalParams.hotelId = htlMasterId;
      additionalParams.keyword = "";
      additionalParams.googleMapPlaceId = "";
      searchParams.regionId = "";
      searchParams.regionUpperId = "";
      searchParams.googleMapPlaceId = "";
    }

    setQueryParams(searchParams);

    dispatch(actionSelectHotel(null));
    dispatch(actionClearHotelCompare());
    dispatch(actionClearSortBy());
    dispatch(actionClearExchangeRate());
    dispatch(actionSetHotelFilter(searchParams));
    dispatch(actionSetHotelSearchType(HOTEL_AUTOSEARCH_TYPE.CITY));

    await hotelApi.searchHotel(getPayloadHotelSearch(filter, additionalParams), map);
  }

  function handleValidate(filter) {
    const { checkIn, checkOut } = filter;

    const keyword = inputRef.current.value;
    if (isEmpty(keyword)) {
      alert("출장지를 입력해주세요.");
      return false;
    }

    if (isEmpty(checkIn) || isEmpty(checkOut)) {
      alert("숙박날짜를 선택해주세요.");
      return false;
    }

    if (checkIn === checkOut) {
      alert("숙박날짜를 1박 이상 선택해주세요.");
      return false;
    }

    return true;
  }

  function onSelectSearchCity(region) {
    const { name, countryName, type, regionId } = region;
    let newFilter = {
      regionId: 0,
      regionNameLong: "",
      regionUpperId: filter.regionUpperId,
    };

    if (isAutoSearch) {
      newFilter.regionNameLong = region.regionNameLong;
      if (type === HOTEL_AUTOSEARCH_TYPE.HOTEL) {
        newFilter.htlMasterId = region.regionId;
        newFilter.regionUpperId = "";
      } else if (type === HOTEL_AUTOSEARCH_TYPE.CITY) {
        newFilter.regionId = region.regionId;
        newFilter.regionUpperId = region.regionUpperId == "0" ? region.upperCityId : region.regionUpperId;
      } else if (type === HOTEL_AUTOSEARCH_TYPE.GOOGLEMAP) {
        newFilter.googleMapPlaceId = regionId;
        newFilter.regionId = "";
        newFilter.regionUpperId = "";
        newFilter.htlMasterId = "";
      } else {
        newFilter.regionId = region.regionId;
        newFilter.regionUpperId = region.upperCityId;
      }
    } else {
      newFilter.regionNameLong = name;
      newFilter.regionId = region.regionId;
      newFilter.regionUpperId = region?.regionUpperId || 0;
      if (countryName) newFilter.regionNameLong += ", " + countryName;
    }

    newFilter.regionNameLong = stripHTML(newFilter.regionNameLong);

    setIsUsingKeyWord(false);
    setIsLoadingAutoSearch(true);
    dispatch(actionSetHotelFilter(newFilter));
  }

  function onChangeSearchCity(e) {
    const { value } = e.target;

    if (searchCityRef.current) {
      searchCityRef.current.setIsOpen(true);
      searchCityRef.current.calculatePosition();
    }

    setIsAutoSearch(true);
    setIsLoadingAutoSearch(true);
    setIsUsingKeyWord(true);
    autoSearchByKeyword(value);
  }

  const autoSearchByKeyword = useCallback(
    debounce(async (val) => {
      dispatch(actionSetHotelFilter({ regionNameLong: val }));
      await dispatch(actionHotelAutoSearch({ keyword: val })).unwrap();
      setIsLoadingAutoSearch(false);
    }, 200),
    [],
  );

  const dateCount = useMemo(() => {
    const { checkIn, checkOut } = filter;
    if (checkIn && checkOut) {
      const diffDate = momentKR(checkOut, "YYYYMMDD").diff(momentKR(checkIn, "YYYYMMDD"), "days");
      return diffDate >= 1 ? `${diffDate}박` : "";
    }
    return "";
  }, [filter.checkIn, filter.checkOut]);

  useEffect(() => {
    if (filter && inputRef.current) {
      inputRef.current.value = filter.regionNameLong || "";
    }
  }, [filter, inputRef]);

  return (
    <form id="hotelSearchForm" onSubmit={search}>
      <div className="flex !justify-center !h-[80px]">
        <div className="flex h-full">
          <div className="w-[400px] h-full">
            <div className="w-full font-medium flex flex-col !items-baseline gap-[4px] border-r border-custom-gray-200">
              <SearchCity
                ref={searchCityRef}
                isAutoSearch={isAutoSearch}
                isLoadingAutoSearch={isLoadingAutoSearch}
                setIsAutoSearch={setIsAutoSearch}
                onSelect={onSelectSearchCity}
              >
                <div className="w-full font-medium p-[16px] flex flex-col !items-baseline gap-[4px]">
                  <p className="text-custom-blue-100">출장지, 숙소명, 랜드마크</p>
                  <input
                    ref={inputRef}
                    onChange={onChangeSearchCity}
                    placeholder="도시, 지역, 호텔이름, 랜드마크"
                    className="focus-visible:outline-none !w-full placeholder:!text-[#C9CFD8] text-[16px]"
                  />
                </div>
              </SearchCity>
            </div>
          </div>
          <div className="w-[320px] h-full">
            <div className="w-full h-full font-medium flex flex-col !items-baseline gap-[4px] border-r border-custom-gray-200">
              <div className="flex w-full h-full !justify-normal px-[16px] gap-[8px] items-end">
                <KrDateRangePicker
                  startDate={filter.checkIn && momentKR(filter.checkIn, "YYYYMMDD")}
                  endDate={filter.checkOut && momentKR(filter.checkOut, "YYYYMMDD")}
                  isHotelSearch
                  onSelectDate={(item) => onSelectDate(item)}
                >
                  <div className="flex flex-col !items-start gap-[4px] pb-[16px]">
                    <p className="text-custom-blue-100">숙박날짜</p>
                    <a className="text-[16px] font-medium">
                      <span className={`val ${filter?.checkIn ? "" : "empty"}`}>
                        {formatKRDateString(filter?.checkIn, "YYYYMMDD", "M월 DD일 (dd)")}
                      </span>
                    </a>
                  </div>
                </KrDateRangePicker>
                <span className="text-[16px] font-medium pb-[16px]">-</span>
                <KrDateRangePicker
                  startDate={filter.checkIn && momentKR(filter.checkIn, "YYYYMMDD")}
                  endDate={filter.checkOut && momentKR(filter.checkOut, "YYYYMMDD")}
                  isHotelSearch
                  onSelectDate={(item) => onSelectDate(item)}
                >
                  <div className="flex flex-col gap-[4px] pb-[16px]">
                    <p className="text-custom-blue-100">&nbsp;</p>
                    <a className="text-[16px] font-medium flex gap-[8px]">
                      <span className={`val ${filter?.checkOut ? "" : "empty"}`}>
                        {formatKRDateString(filter?.checkOut, "YYYYMMDD", "M월 DD일 (dd)")}
                      </span>
                      <span className="text-[#9da9be]">{dateCount}</span>
                    </a>
                  </div>
                </KrDateRangePicker>
              </div>
            </div>
          </div>
          <div className="w-[320px] h-full">
            <ClassMember />
          </div>
          <div className="h-full">
            <div className="p-[8px] h-full">
              <Button className="btn-primary h-full !w-[80px] font-bold" type="submit">
                재검색
              </Button>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
};

export default Condition;
