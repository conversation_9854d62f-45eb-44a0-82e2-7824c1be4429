import Button from "@/app/components/user_v2/common/Button";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import IconRefreshV2 from "@/assets/images/svg/ic-refresh-v2.svg";
import { useAppSelector } from "@/store";
import { selectSelectedHotel } from "@/store/hotelSlice";

const RefreshSearchFloatButton = (props) => {
  const { isOpenSearchList, isChangeMapCenter, refreshSearch } = props;
  const selectedHotel = useAppSelector(selectSelectedHotel);

  function getLeftPosition() {
    if (isOpenSearchList && selectedHotel !== null) return "-translate-x-[calc(50%-180px-250px)]";
    if (isOpenSearchList) return "-translate-x-[calc(50%-180px)]";
    return "translate-x-[-50%]";
  }

  return (
    <PortalComponent>
      <Button
        className={`${isChangeMapCenter ? "visible opacity-100" : "invisible opacity-0"} btn-primary fixed z-[25] top-[241px] !min-w-[40px] shadow-custom-100 !rounded-[24px] !h-[40px] !pl-[12px] !pr-[16px] transition-all duration-[0.44s] linear text-white font-bold !justify-center !gap-[4px] !border-none bg-custom-blue-100 left-1/2 transform ${getLeftPosition()}`}
        onClick={refreshSearch}
      >
        <IconRefreshV2 className="w-[20px] h-[20px]" /> 현위치에서 재검색
      </Button>
    </PortalComponent>
  );
};

export default RefreshSearchFloatButton;
