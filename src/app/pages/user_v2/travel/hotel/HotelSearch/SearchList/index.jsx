import { Fragment, useEffect, useRef } from "react";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import BussinessTripRule from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList/BussinessTripRule";
import HotelList from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList/HotelList";
import Button from "@/app/components/user_v2/common/Button";
import IconChevronLeft from "@/assets/images/svg/ic-chevron-left.svg";
import IconChevronRight from "@/assets/images/svg/ic-chevron-right.svg";
import { useAppSelector } from "@/store";
import { selectHotelSearch, selectSelectedHotel } from "@/store/hotelSlice";

const SearchList = (props) => {
  const { isOpenSearchList, setIsOpenSearchList, hotelItemRef } = props;

  const hotelSearch = useAppSelector(selectHotelSearch);
  const selectedHotel = useAppSelector(selectSelectedHotel);
  const containerRef = useRef();

  useEffect(() => {
    if (containerRef.current) containerRef.current.scrollTo({ top: 0, behavior: "smooth" });
  }, [hotelSearch, containerRef]);

  return (
    <PortalComponent>
      <div
        ref={containerRef}
        className={`hidden-scrollbar z-[16] fixed top-[226px] left-0 w-[360px] ${
          !isOpenSearchList ? "-translate-x-[360px]" : "translate-x-0"
        } transition-all duration-500 linear h-[calc(100vh-232px)] overflow-y-auto bg-white border-r-[1px] border-t-[1px] border-custom-gray-200`}
      >
        <BussinessTripRule />
        <HotelList hotelItemRef={hotelItemRef} />
      </div>
      {selectedHotel === null && (
        <Button
          className={`${isOpenSearchList ? "btn-icon" : "btn-white !pr-[10px] !pl-[12px]"} fixed z-[20] top-[241px] !min-w-[40px] shadow-custom-100 !h-[40px] !text-custom-gray-300 font-medium transition-all duration-[0.44s] linear !justify-center !gap-0 !border-none bg-white ${
            isOpenSearchList ? "left-[375px]" : "left-[16px]"
          }`}
          onClick={() => setIsOpenSearchList(!isOpenSearchList)}
        >
          {isOpenSearchList ? (
            <IconChevronLeft />
          ) : (
            <Fragment>
              열기 <IconChevronRight />
            </Fragment>
          )}
        </Button>
      )}
    </PortalComponent>
  );
};

export default SearchList;
