import { Fragment, useState } from "react";
import { isEmpty } from "lodash";
import IconCaution from "@/assets/images/svg/ic-caution.svg";
import IconChevronDown from "@/assets/images/svg/ic-chevron-down.svg";
import { useAppSelector } from "@/store";
import { selectHotelTravelRule } from "@/store/hotelSlice";
import { comma } from "@/utils/common";
import { getMaxAllowedStar } from "@/utils/app";

const BussinessTripRule = () => {
  const travelRuleBaseDTO = useAppSelector(selectHotelTravelRule);
  const [viewAll, setViewAll] = useState(true);

  function toggleView() {
    setViewAll((prev) => !prev);
  }

  function isHasDataView(travelRuleBaseDTO) {
    return travelRuleBaseDTO?.isExceptUseHotelMaxStar || travelRuleBaseDTO?.isExceptUseHotelMax || travelRuleBaseDTO?.isExceptUseHotelEtc;
  }

  return (
    <div
      className={`p-[16px] mx-[16px] mt-[16px] rounded-[8px] bg-custom-bg-100 flex flex-col !items-baseline ${!isEmpty(travelRuleBaseDTO) && isHasDataView(travelRuleBaseDTO) ? "flex" : "hidden"}`}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center justify-normal gap-1">
          <IconCaution />
          <span className="font-bold text-custom-gray-600">출장규정</span>
        </div>
        <IconChevronDown
          className={`transition-transform duration-300 cursor-pointer ${viewAll ? "rotate-180" : ""} [&_path]:fill-custom-gray-600`}
          onClick={toggleView}
        />
      </div>
      <ul
        className={`marker:text-custom-gray-1000 list-disc list-inside marker:!mr-2 !marker:text-green overflow-hidden transition-all duration-300 ${viewAll ? "max-h-80 mt-[16px]" : "max-h-0"} flex-col !items-baseline gap-[4px]`}
      >
        {travelRuleBaseDTO?.isExceptUseHotelMaxStar && (
          <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">{getMaxAllowedStar(travelRuleBaseDTO)}성급</li>
        )}
        {travelRuleBaseDTO?.isExceptUseHotelMax && (
          <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">
            1박당 최대 허용 요금 {comma(travelRuleBaseDTO.maxAllowPerNight)}원
          </li>
        )}
        {travelRuleBaseDTO?.isExceptUseHotelEtc && (
          <Fragment>
            {travelRuleBaseDTO?.hotelEtcRule1 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">① {travelRuleBaseDTO.hotelEtcRule1}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule2 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">② {travelRuleBaseDTO.hotelEtcRule2}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule3 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">③ {travelRuleBaseDTO.hotelEtcRule3}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule4 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">④ {travelRuleBaseDTO.hotelEtcRule4}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule5 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑤ {travelRuleBaseDTO.hotelEtcRule5}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule6 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑥ {travelRuleBaseDTO.hotelEtcRule6}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule7 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑦ {travelRuleBaseDTO.hotelEtcRule7}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule8 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑧ {travelRuleBaseDTO.hotelEtcRule8}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule9 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑨ {travelRuleBaseDTO.hotelEtcRule9}</li>
            )}
            {travelRuleBaseDTO?.hotelEtcRule10 && (
              <li className="text-custom-gray-300 marker:!p-[10px] before:ml-[-4px]">⑩ {travelRuleBaseDTO.hotelEtcRule10}</li>
            )}
          </Fragment>
        )}
      </ul>
    </div>
  );
};

export default BussinessTripRule;
