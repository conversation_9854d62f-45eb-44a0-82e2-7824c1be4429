import { Fragment } from "react";
import SortCondition from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList/SortCondition";
import IconChevronDown from "@/assets/images/svg/ic-chevron-down.svg";
import { HOTEL_SORT_CONDITIONS } from "@/constants/app";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionSetSortBy, selectHotelList, selectSortBy } from "@/store/hotelSlice";
import HotelItem from "@/app/pages/user_v2/travel/hotel/HotelSearch/SearchList/HotelItem";
import Button from "@/app/components/user_v2/common/Button";

const HotelList = (props) => {
  const { hotelItemRef } = props;
  const dispatch = useAppDispatch();
  const hotelList = useAppSelector(selectHotelList);
  const sortBy = useAppSelector(selectSortBy);

  function selectedSortCondtion(value) {
    dispatch(actionSetSortBy(value));
  }

  return (
    <div className="flex flex-col items-baseline gap-[12px] mt-[16px]">
      <div className="flex items-center justify-between w-full px-[16px] h-[40px]">
        <p className="text-custom-gray-600 font-medium">총 {hotelList.length}개</p>
        <SortCondition selected={sortBy} onSelect={selectedSortCondtion}>
          <Button className="btn-text custom-hotel-select cursor-pointer flex items-center gap-[2px] text-custom-gray-500 font-bold [&_path]:!fill-custom-gray-500 !pr-[4px] !pl-[8px] !h-[32px]">
            {HOTEL_SORT_CONDITIONS[sortBy]}
            <IconChevronDown />
          </Button>
        </SortCondition>
      </div>
      <div className="flex flex-col w-full">
        {hotelList.map((hotel) => (
          <Fragment key={hotel.htlMasterId}>
            <HotelItem {...hotel} ref={hotelItemRef} />
          </Fragment>
        ))}
      </div>
    </div>
  );
};

export default HotelList;
