import { Fragment, useEffect, useRef } from "react";
import useOpenComponent from "@/app/hooks/useOpenComponent";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import { DIVIDER_DIRECTION, HOTEL_SORT_CONDITIONS } from "@/constants/app";
import Divider from "@/app/components/user_v2/common/Divider";

const SortCondition = (props) => {
  const { children, selected, onSelect, isRoomSort = false, additionalPosition = { top: 0, left: 0 } } = props;
  const dropdownRef = useRef(null);
  const { isOpen, dropdownPosition, clonedChild, setIsOpen } = useOpenComponent({ children, dropdownRef, closeWhenScroll: true });

  function filterSortConditionByType(isRoomSort) {
    return Object.entries(HOTEL_SORT_CONDITIONS).filter(([key, _]) => (isRoomSort ? !["recommend", "lowGrade", "highGrade"].includes(key) : true));
  }

  useEffect(() => {
    function handleScroll(event) {
      const child = clonedChild.ref.current;
      const target = event.target;
      if (target.contains(child)) {
        setIsOpen(false);
      }
    }
    document.addEventListener("scroll", handleScroll, true);
    return () => {
      document.removeEventListener("scroll", handleScroll, true);
    };
  }, []);

  return (
    <Fragment>
      {clonedChild}
      <PortalComponent>
        <div
          ref={dropdownRef}
          className={`z-[9999] absolute bg-white border border-custom-gray-100 rounded-[8px] p-[4px] min-w-[140px] dropdown-select-animation ${isOpen ? "open" : ""}`}
          style={{
            top: dropdownPosition.top + 8,
            left: dropdownPosition.right - additionalPosition.left - 140,
          }}
        >
          {filterSortConditionByType(isRoomSort).map(([key, value], index) => {
            return (
              <Fragment key={key}>
                <button
                  className={`p-[12px] rounded-[8px] w-full text-start cursor-pointer ${selected === key && "!bg-custom-gray-400"} hover:bg-custom-gray-700 active:bg-custom-gray-1300`}
                  onClick={() => {
                    onSelect(key);
                    setIsOpen(false);
                  }}
                >
                  {value}
                </button>
                {index !== filterSortConditionByType(isRoomSort).length - 1 && <Divider className="my-1" direction={DIVIDER_DIRECTION.HORIZONTAL} />}
              </Fragment>
            );
          })}
        </div>
      </PortalComponent>
    </Fragment>
  );
};

export default SortCondition;
