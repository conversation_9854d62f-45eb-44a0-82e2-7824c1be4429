import { useEffect, useRef, useState } from "react";
import { cloneDeep } from "lodash";
import useOpenComponent from "@/app/hooks/useOpenComponent";
import Select from "@/app/components/user_v2/common/Select";
import Divider from "@/app/components/user_v2/common/Divider";
import { DIVIDER_DIRECTION, HOTEL_CONDITION_MAP, HOTEL_RANGE_FARE_TYPE, INPUT_NUMBER_MAX, INPUT_RANGE_TYPE } from "@/constants/app";
import CheckBox from "@/app/components/user_v2/common/CheckBox";
import Input from "@/app/components/user_v2/common/Input";
import IOSSlider from "@/app/components/user_v2/common/IOSSlider";
import { useAppSelector } from "@/store";
import { selectConditionHotel } from "@/store/hotelSlice";
import { reverseMap } from "@/utils/common";

const SelectHotelFareRange = (props) => {
  const { children, selectList, ...rest } = props;
  const { id, selected, selectedSlider, isCloseWhenChoose, onSelect } = rest;
  const { maxAveragePrice, minAveragePrice, maxSalePrice, minSalePrice } = useAppSelector(selectConditionHotel);

  const dropdownRef = useRef(null);
  const [cloneSelected, setCloneSelected] = useState([]);
  const [cloneSelectedSlider, setCloneSelectedSlider] = useState([]);
  const [sliderSetting, setSliderSetting] = useState({ min: 0, max: 0 });

  const openComponentOptions = useOpenComponent({ children, dropdownRef });
  const { isOpen, setIsOpen } = openComponentOptions;

  function onChangeRangeInput(value, type) {
    let newValue = [];
    if (type === INPUT_RANGE_TYPE.MIN) {
      newValue = [value, cloneSelectedSlider[1]];
    } else if (type === INPUT_RANGE_TYPE.MAX) {
      newValue = [cloneSelectedSlider[0], value];
    }
    setCloneSelectedSlider(newValue);
    onSelect(newValue, HOTEL_CONDITION_MAP.fareRange);
  }

  useEffect(() => {
    if (selected) setCloneSelected(cloneDeep(selected));
    if (selectedSlider) setCloneSelectedSlider(cloneDeep(selectedSlider));
  }, [selected, selectedSlider, isOpen]);

  useEffect(() => {
    if (cloneSelected === reverseMap(HOTEL_RANGE_FARE_TYPE).average) {
      setSliderSetting({ max: maxAveragePrice, min: minAveragePrice });
    } else if (cloneSelected === reverseMap(HOTEL_RANGE_FARE_TYPE).total) {
      setSliderSetting({ max: maxSalePrice, min: minSalePrice });
    }
  }, [cloneSelected, maxAveragePrice, minAveragePrice, maxSalePrice, minSalePrice]);

  return (
    <Select
      {...rest}
      {...openComponentOptions}
      ref={dropdownRef}
      cloneSelected={cloneSelected}
      setCloneSelected={setCloneSelected}
      customOptions={
        <div className="flex flex-col gap-[4px] px-[12px] py-[8px] pt-0 !items-baseline w-[280px] font-medium ">
          {selectList.map((el) => {
            return (
              <CheckBox
                key={el.id}
                className="!border-none !p-0 min-h-[48px] hover:!bg-transparent"
                type="radio"
                labelText={el.name}
                value={el.value}
                checked={selected === el.value}
                onChange={(e) => {
                  const { value } = e.target;
                  onSelect(value, id);
                  isCloseWhenChoose && setIsOpen(false);
                }}
              />
            );
          })}
          <Divider className="w-full" direction={DIVIDER_DIRECTION.HORIZONTAL} />
          <div className="pb-[4px] mt-[16px]">
            <div className="flex gap-[8px] font-medium">
              <Input
                type="number"
                min={sliderSetting.min}
                max={INPUT_NUMBER_MAX}
                isCurrency
                className="w-full"
                placeholder="0원"
                value={cloneSelectedSlider[0]}
                onChange={(value) => onChangeRangeInput(value, INPUT_RANGE_TYPE.MIN)}
              />
              ~
              <Input
                type="number"
                max={INPUT_NUMBER_MAX}
                isCurrency
                className="w-full"
                placeholder="0원"
                value={cloneSelectedSlider[1]}
                onChange={(value) => onChangeRangeInput(value, INPUT_RANGE_TYPE.MAX)}
              />
            </div>

            <div className="bg-custom-gray-700 mt-[8px] rounded-[8px] px-[16px] h-[32px] flex items-center">
              <IOSSlider
                className="custom-hotel-slider"
                valueLabelDisplay="off"
                disableSwap
                value={cloneSelectedSlider}
                max={sliderSetting.max}
                min={sliderSetting.min}
                step={1}
                onChange={(e, value) => {
                  setCloneSelectedSlider(value);
                }}
                onChangeCommitted={(e, value) => {
                  onSelect(value, HOTEL_CONDITION_MAP.fareRange);
                  setCloneSelectedSlider(value);
                }}
              />
            </div>
          </div>
        </div>
      }
    />
  );
};
export default SelectHotelFareRange;
