import { Fragment, useState } from "react";
import Button from "@/app/components/user_v2/common/Button";
import CheckBox from "@/app/components/user_v2/common/CheckBox";
import Divider from "@/app/components/user_v2/common/Divider";
import IconRefresh from "@/assets/images/svg/ic-refresh.svg";
import { useAppDispatch, useAppSelector } from "@/store";
import { convertMapIntoSelectList, comma, reverseMap } from "@/utils/common";
import { HOTEL_CONDITION_MAP, HOTEL_RANGE_FARE_TYPE, SNACK_BAR_POSITION, SNACK_BAR_TYPE } from "@/constants/app";
import SelectHotelRating from "@/app/pages/user_v2/travel/hotel/HotelSearch/FilterArea/SelectHotelRating";
import IconChevronDown from "@/assets/images/svg/ic-chevron-down.svg";
import SelectHotelFacility from "@/app/pages/user_v2/travel/hotel/HotelSearch/FilterArea/SelectHotelFacility";
import SelectHotelFareRange from "@/app/pages/user_v2/travel/hotel/HotelSearch/FilterArea/SelectHotelFareRange";
import SnackBar from "@/app/components/user_v2/common/SnackBar";
import {
  actionClearExchangeRate,
  actionHotelExchangeRate,
  actionSetHotelCondition,
  selectConditionHotel,
  selectHotelExchangeRate,
} from "@/store/hotelSlice";
import { getPriceByExchangeRate } from "@/utils/app";

const FilterArea = (props) => {
  const { isConditionChange, resetCondition } = props;

  const dispatch = useAppDispatch();
  const { gradeCodeMap, facilityCodeMap, maxAveragePrice, minAveragePrice, maxSalePrice, minSalePrice } = useAppSelector(selectConditionHotel);
  const condition = useAppSelector(selectConditionHotel);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const [showSnackBar, setShowSnackBar] = useState(false);

  function onSelect(value, id) {
    dispatch(actionSetHotelCondition({ ...condition, [id]: value }));
  }

  function changeUnitCurrency(e) {
    const { checked } = e.target;
    if (checked) {
      dispatch(actionHotelExchangeRate());
    } else {
      dispatch(actionClearExchangeRate());
    }
    setShowSnackBar(checked);
  }

  function onSelectHotelRange(value, id) {
    let newCondition = { ...condition, [id]: value };
    if (id === HOTEL_CONDITION_MAP.rangeFareType) {
      let fareRange = { min: condition.fareRange[0], max: condition.fareRange[1] };
      if (value === reverseMap(HOTEL_RANGE_FARE_TYPE).average) {
        fareRange = { max: maxAveragePrice, min: minAveragePrice };
      } else if (value === reverseMap(HOTEL_RANGE_FARE_TYPE).total) {
        fareRange = { max: maxSalePrice, min: minSalePrice };
      }
      newCondition = { ...newCondition, fareRange: [fareRange.min, fareRange.max] };
    }
    dispatch(actionSetHotelCondition(newCondition));
  }

  return (
    <div className="custom-hotel-map-search z-[16] h-[72px] top-[162px] left-0 w-full bg-white p-[16px] border-b-[1px] border-t-[1px] border-custom-gray-200 flex items-center">
      <div className="flex gap-[16px] font-medium item-center h-full">
        <Button className="btn-border opacity-disabled" disabled={!isConditionChange} onClick={resetCondition}>
          <IconRefresh />
          초기화
        </Button>
        <Divider />
        <div className="flex item-center gap-[8px]">
          <SelectHotelRating
            id={HOTEL_CONDITION_MAP.gradeCodeMap}
            selectList={convertMapIntoSelectList(gradeCodeMap).reverse()}
            isShowSelectAllBtn
            isCloseWhenChoose={false}
            selected={condition.arrayGradeCode}
            onSelect={onSelect}
          >
            <Button className="custom-hotel-select btn-border font-medium !gap-2 !pr-[10px] h-[40px] !pl-[12px]">
              <span>호텔등급</span>
              <Divider className="!h-4" />
              <span className="text-custom-blue-100">
                {condition.arrayGradeCode.length !== convertMapIntoSelectList(gradeCodeMap).reverse().length
                  ? `${condition.arrayGradeCode.length}개`
                  : "전체"}
              </span>
              <IconChevronDown />
            </Button>
          </SelectHotelRating>
          <SelectHotelFacility
            id={HOTEL_CONDITION_MAP.facilityCodeMap}
            selectList={convertMapIntoSelectList(facilityCodeMap)}
            isShowSelectAllBtn
            isCloseWhenChoose={false}
            selected={condition.arrayFacilityCode}
            onSelect={onSelect}
          >
            <Button className="custom-hotel-select btn-border font-medium !gap-2 !pr-[10px] h-[40px] !pl-[12px]">
              <span>편의시설</span>
              <Divider className="!h-4" />
              <span className="text-custom-blue-100">
                {condition.arrayFacilityCode.length !== convertMapIntoSelectList(facilityCodeMap).length
                  ? `${condition.arrayFacilityCode.length}개`
                  : "전체"}
              </span>
              <IconChevronDown />
            </Button>
          </SelectHotelFacility>
          <SelectHotelFareRange
            id={HOTEL_CONDITION_MAP.rangeFareType}
            isCloseWhenChoose={false}
            selected={condition.rangeFareType}
            selectedSlider={condition.fareRange}
            selectList={convertMapIntoSelectList(HOTEL_RANGE_FARE_TYPE)}
            onSelect={onSelectHotelRange}
          >
            <Button className="custom-hotel-select btn-border font-medium !gap-2 !pr-[10px] h-[40px] !pl-[12px]">
              <span>요금범위</span>
              <Divider className="!h-4" />
              <span className="text-custom-blue-100">
                {HOTEL_RANGE_FARE_TYPE[condition.rangeFareType]}{" "}
                {`(${comma(getPriceByExchangeRate(condition.fareRange[0], exchangeRate))} ~ ${comma(getPriceByExchangeRate(condition.fareRange[1], exchangeRate))})`}
              </span>
              <IconChevronDown />
            </Button>
          </SelectHotelFareRange>
        </div>
        <Divider />

        <SnackBar
          className="w-[352px]"
          type={SNACK_BAR_TYPE.DANGER}
          position={SNACK_BAR_POSITION.BOTTOM_LEFT_OUT}
          plusTop={31}
          open={showSnackBar}
          setOpen={setShowSnackBar}
          title={
            <Fragment>
              USD 요금은 단순 참고용이며 <br /> 실제 결제는 원화(KRW)로 결제됩니다.
            </Fragment>
          }
        >
          <div className="relative">
            <CheckBox
              checked={exchangeRate !== 1}
              className="![&>span]:pl-[26px] !min-h-[40px]"
              labelText={
                <div className="flex gap-[8px]">
                  USD로 보기
                  {exchangeRate > 1 && (
                    <Fragment>
                      <Divider className="w-[1px] !h-[12px]" />
                      송금 기준 환율 {comma(Math.floor(exchangeRate))}원
                    </Fragment>
                  )}
                </div>
              }
              onChange={changeUnitCurrency}
            />
          </div>
        </SnackBar>
      </div>
    </div>
  );
};

export default FilterArea;
