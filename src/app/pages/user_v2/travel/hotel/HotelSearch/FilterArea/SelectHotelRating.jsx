import { useEffect, useRef, useState } from "react";
import { cloneDeep } from "lodash";
import useOpenComponent from "@/app/hooks/useOpenComponent";
import Select from "@/app/components/user_v2/common/Select";

const SelectHotelRating = (props) => {
  const { children, ...rest } = props;
  const { selected } = rest;
  const dropdownRef = useRef(null);
  const [cloneSelected, setCloneSelected] = useState([]);

  const openComponentOptions = useOpenComponent({ children, dropdownRef });
  const { isOpen } = openComponentOptions;

  useEffect(() => {
    if (selected) setCloneSelected(cloneDeep(selected));
  }, [selected, isOpen]);

  return <Select {...rest} {...openComponentOptions} ref={dropdownRef} cloneSelected={cloneSelected} setCloneSelected={setCloneSelected} />;
};

export default SelectHotelRating;
