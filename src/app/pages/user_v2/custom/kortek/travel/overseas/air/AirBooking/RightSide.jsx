import { useMemo, useState, Fragment } from "react";
import { useDispatch } from "react-redux";
import { isEmpty } from "lodash";
import { comma, getSeatTypeName } from "@/utils/common";
import LowestSchedule from "@/app/components/user_v2/modal/LowestSchedule.modal";
import btnTop from "@/assets/images/cmm/btn_top.png";
import { useAppSelector } from "@/store";
import { selectAirOverseasSessionData, selectTravelRuleBase } from "@/store/airSlice";
import { selectUserInfo } from "@/store/userSlice";
import FareRule from "@/app/components/user_v2/modal/FareRule.modal";
import AirBookingCompareSchedule from "@/app/components/user_v2/modal/AirBookingCompareSchedule.modal";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { getFareRule } from "@/service/air";
import useTravelRule from "@/app/hooks/useTravelRule";
import { EXECPT_TIME_TYPE } from "@/constants/app";
import "@/styles/user_v2/member.css";
import DocumentNumber from "@/app/pages/user_v2/travel/overseas/air/Booking/components/DocumentNumber";
import FlightConfirmBookingModal from "@/app/components/user_v2/modal/FlightConfirmBooking.modal";

export default function RightSide(props) {
  const { formState, setFormState, onSubmit } = props;
  const dispatch = useDispatch();
  const { isViolation } = useTravelRule();

  const sessionData = useAppSelector(selectAirOverseasSessionData);
  const { userInfo } = useAppSelector(selectUserInfo);
  const travelRuleBase = useAppSelector(selectTravelRuleBase);

  const { filter, data, lowestFareAndSchedule } = sessionData;
  const [openModal, setOpenModal] = useState(false);
  const [openAirCompareSchedule, setOpenAirCompareSchedule] = useState(false);
  const [fareRules, setFareRules] = useState([]);
  const [openFlightConfirmBookingModal, setOpenFlightConfirmBookingModal] = useState(false);
  const paxTypeFares = useMemo(() => {
    if (lowestFareAndSchedule?.fares?.paxTypeFares) return lowestFareAndSchedule?.fares?.paxTypeFares[0];
    return [];
  }, [lowestFareAndSchedule?.fares?.paxTypeFares]);

  const fare = useMemo(() => {
    if (isEmpty(data)) return null;
    return data.fares.paxTypeFares?.[0];
  }, [data]);

  async function openFareRuleModal(journey) {
    try {
      dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
      const payload = {
        journeys: journey.flights.map((flight) => {
          const { airline, deptAirport, arrAirport, departureDate, journeyKey, fares, pairKey } = flight;
          return {
            journeyKey,
            fareKey: fares.fareKey,
            pairKey,
            airline,
            deptAirport,
            arrAirport,
            departureDate,
            promotionId: "",
            fopPromotionId: "",
          };
        }),
      };
      const response = await getFareRule(payload);
      setFareRules(response.data?.data?.journeys);
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
    }
  }

  return (
    <Fragment>
      <div className="right-side sec-pay-to reserv-detail-cotn !sticky top-0 right-0">
        {userInfo?.workspace?.company?.btmsSetting?.isComparativePrice && userInfo?.workspace?.company?.btmsSetting?.isLowestPrice && (
          <div className="box-line" id="lowestDisplayLayout" style={{ cursor: "pointer" }} onClick={() => setOpenModal(true)}>
            <div className="txt-big clearfix">
              <p className="fl-l">최저가 요금</p>
              <p className="fl-r c-price">
                <strong id="lowestPriceStrong">
                  {comma(paxTypeFares?.airFare + paxTypeFares?.airTax + paxTypeFares?.fuelChg + paxTypeFares?.tasfAmount)}
                </strong>
                원
              </p>
            </div>
          </div>
        )}
        <div className="box-line">
          <div className="txt-big clearfix">
            <p className="fl-l">총 결제요금</p>
            <p className="fl-r c-price">
              <strong id="totalPaymentStrong">
                {comma((fare?.airFare + fare?.airTax + fare?.fuelChg + fare?.tasfAmount) * filter?.adultCount || 1)}
              </strong>
              원
            </p>
          </div>
          <dl className="fare-detail">
            <dt>성인 {filter.adultCount}명</dt>
            <dd>
              <span className="tit">항공료</span>
              <span className="sum">
                {comma(fare?.airFare)}원 x {filter?.adultCount}
              </span>
            </dd>
            <dd>
              <span className="tit">TAX</span>
              <span className="sum">
                {comma(fare?.airTax + fare?.fuelChg)}원 x {filter?.adultCount}
              </span>
            </dd>
            <dd>
              <span className="tit">발권수수료</span>
              <span className="sum" id="tasfAmountSpan">
                {comma(fare?.tasfAmount)}원 x {filter?.adultCount}
              </span>
            </dd>
          </dl>
        </div>
        {isViolation(data) && (
          <Fragment>
            <div className="box-line">
              <p className="txt-big">출장자 규정</p>
              <ul className="brakedown">
                <li>
                  결재 요청 의견 출발일 기준
                  <span>{travelRuleBase?.data?.travelLimitDay === 0 ? "제한없음" : `${travelRuleBase?.data?.travelLimitDay}일 전`}</span>
                </li>
                <li>
                  기본 좌석등급
                  <span>
                    {travelRuleBase?.data?.baseSeatTypeCode} {getSeatTypeName(travelRuleBase?.data?.baseSeatTypeCode)}
                  </span>
                </li>
                {travelRuleBase?.data?.isExceptUse && (
                  <li>
                    {EXECPT_TIME_TYPE[travelRuleBase?.data?.exceptTimeType]}
                    <span>{travelRuleBase?.data?.exceptBordingTime}</span>
                    시간 이상 일 경우
                    <br />
                    <span>{getSeatTypeName(travelRuleBase?.data?.exceptSeatTypeCode)}</span>
                    허용
                  </li>
                )}
              </ul>
            </div>
            <div className="box-line">
              <p className="txt-big">결재 요청 의견</p>
              <div className="opinion-insert">
                <textarea
                  name="violationReason"
                  id="violationReason"
                  maxLength={500}
                  placeholder="규정 외 진행사유를 입력해 주세요. (500자 이내)"
                  value={formState.violationReason}
                  onChange={() => {}}
                />
              </div>
            </div>
          </Fragment>
        )}
        {userInfo?.workspace.company.btmsSetting.isDocumentNumberUse && <DocumentNumber formState={formState} setFormState={setFormState} />}
        <div className="box-applys">
          <button type="button" className="btn-default btn-request" onClick={() => setOpenFlightConfirmBookingModal(true)}>
            예약 요청
          </button>
          <a className="btn-default btn-compare" onClick={() => openFareRuleModal(data)}>
            요금 규정
          </a>
          {userInfo?.workspace?.company?.btmsSetting?.isComparativePrice && (
            <button type="button" className="btn-default btn-compare" onClick={() => setOpenAirCompareSchedule(true)}>
              비교견적서
            </button>
          )}
        </div>
        <div id="btn-top" className="!flex !justify-end">
          <a onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}>
            <img src={btnTop} alt="Top" />
          </a>
        </div>
      </div>
      <LowestSchedule open={openModal} setOpen={setOpenModal} />
      <FareRule fareRules={fareRules} setFareRules={setFareRules} />
      <AirBookingCompareSchedule type="VIEW" defaultSelect={data} open={openAirCompareSchedule} setOpen={setOpenAirCompareSchedule} />
      <FlightConfirmBookingModal
        open={openFlightConfirmBookingModal}
        setOpen={setOpenFlightConfirmBookingModal}
        bookingUserDTOs={formState.bookingUserDTOs}
        onSubmit={onSubmit}
      />
    </Fragment>
  );
}
