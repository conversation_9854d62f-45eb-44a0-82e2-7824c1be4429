import { useState, useEffect, useRef } from "react";
import { isEmpty, set, cloneDeep, get, compact } from "lodash";
import { useNavigate } from "react-router-dom";
import { hanldeInputChangeValidation, emailValidationCheck } from "@/utils/common";
import { useAppSelector, useAppDispatch } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import LeftSide from "@/app/pages/user_v2/custom/kortek/travel/overseas/air/AirBooking/LeftSide";
import RightSide from "@/app/pages/user_v2/custom/kortek/travel/overseas/air/AirBooking/RightSide";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { postPNR, postCompareSchedule } from "@/service/air";
import { momentKR } from "@/utils/date";
import { normalizeCompareSchedule } from "@/utils/app";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import useTravelRule from "@/app/hooks/useTravelRule";
import { BIRTHDAY_LIST, IGNORE_CREAT_PNR_FORM_LIST } from "@/constants/common";
// import css
import "@/styles/user_v2/booking.css";
import "@/styles/user_v2/common.css";
import "@/styles/user_v2/common4Logo.css";
import "@/styles/user_v2/lowestSchedule.css";
import "@/styles/user_v2/member.css";
import "@/styles/user_v2/reservation.css";
import "@/styles/user_v2/reset.css";
import "@/styles/user_v2/search.css";

const INITIAL_STATE = {
  reserverCheck: false,
  ruleConfirmAllCheck: false,
  reservationRuleAgreementDTO: {
    isReservationRule: false,
    isBaggageRule: false,
    isPassportVisaEtcRule: false,
    isTravelRule: false,
    isAgreeServiceRule: false,
    isAgreePrivacyRule: false,
    isAgreePrivacyProvideRule: false,
    isAgreeReserverInfo: false,
    isNameSamePassportRule: false,
  },
  bookingUserDTOs: [],
  travelers: [],
  travelCities: [],
  travelPurposes: [],
  seatZone: "all",
  documentNumberUseCount: 0,
  supplierType: "AMADEUS",
  jsonTravelBookingAirSearchDTO: "",
  compareAirSchedule: "",
  lowestAirSchedule: "",
  airReservationTOStr: "",
  documentNumber: [{ id: 0, value: "" }],
  jsonBusinessTrip: "",
  violationReason: "",
  attachFiles: [],
};

export default function OverseasAirBooking() {
  const { userInfo } = useAppSelector(selectUserInfo);
  const { filter, data, lowestFareAndSchedule, airCompareSchedules } = useAppSelector(selectAirOverseasSessionData);
  const { isViolation } = useTravelRule();

  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [formState, setFormState] = useState(INITIAL_STATE);

  const formRef = useRef(null);

  function onInput(event) {
    const { name } = event.target;
    const inputValid = event.target.getAttribute("data-validateinput");
    if (IGNORE_CREAT_PNR_FORM_LIST.includes(name) || BIRTHDAY_LIST.includes(name)) return;
    const isValid = hanldeInputChangeValidation(event);
    if (isValid) {
      handleChange(event);
      event.target.closest("dd").nextElementSibling.style.display = "none";
    } else {
      event.target.closest("dd").nextElementSibling.style.display = "block";
    }

    if (["email"].includes(inputValid)) {
      handleChange(event);
    }
  }
  function handleChange(event) {
    const { name, value } = event.target;
    setFormState((prev) => {
      return set(cloneDeep(prev), name, value);
    });
  }

  function onChange(event) {
    const WHITE_LIST = ["gender"];
    const { name } = event.target;
    if (WHITE_LIST.some((el) => name.includes(el))) {
      handleChange(event);
    }
  }

  function canChangeUserDTO(name) {
    // TODO: Implement later
    const userDTO = get(formState, `${name.split(".")[0]}`);
    const isBookingUserDTOs = name.includes("bookingUserDTOs");
    if (isBookingUserDTOs) {
      const idOfUserDTO = get(formState, `${name.split(".")[0]}.id`);
      if (idOfUserDTO > 0) return false;
    }
    return true;
  }

  function validation(e, formState) {
    const { departureDays } = filter;
    const regularKoreanOnly = /[a-z0-9]|[\]{}()<>?|`~!@#$%^&*-_+=,.;:"'\\]/g;
    const { travelers } = formState;
    const formElement = getFormElements(formRef);
    const invalidNames = formState.bookingUserDTOs.some((user) => isEmpty(user?.name) || regularKoreanOnly.test(user?.name));
    const invalidLastNames = formState.bookingUserDTOs.some((user) => isEmpty(user?.lastName));
    const invalidLastNamesLength = formState.bookingUserDTOs.some((user) => user?.lastName?.length < 2);
    const invalidFirstNames = formState.bookingUserDTOs.some((user) => isEmpty(user?.firstName));
    const invalidFirstNamesLength = formState.bookingUserDTOs.some((user) => user?.firstName?.length < 2);
    const invalidCellPhoneNumber = formState.bookingUserDTOs.some((traveler) => isEmpty(traveler.cellPhoneNumber));
    const invalidLengthCellPhoneNumber = formState.bookingUserDTOs.some((traveler) => traveler.cellPhoneNumber?.length < 11);
    const emptyEmail = formState.bookingUserDTOs.some((user) => isEmpty(user?.email));
    const emptySettlementManagerEmail = formState.bookingUserDTOs.some((user) => isEmpty(user?.settlementManagerEmail));
    const invalidEmail = formState.bookingUserDTOs.some((user) => !emailValidationCheck(user?.email));
    const invalidPassportCountry = formState.bookingUserDTOs.some((user) => isEmpty(user?.customerPassport.country.id));
    const isCheckRuleAll = formState.ruleConfirmAllCheck;
    const isPassportExpireDateMin = formState.bookingUserDTOs.some((user) => {
      const departureDaysMoment = momentKR(departureDays[0], "YYYYMMDD");
      const passportExpireDate = momentKR(user.customerPassport.expireYmd, "YYYYMMDD");
      return passportExpireDate.diff(departureDaysMoment, "days") < 180;
    });
    const emptyDocumentNumber = formState.documentNumber.some((group) => isEmpty(group.value));
    const isDocumentNumberUse = userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberUse;
    const isDocumentNumberRequired = userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberRequired;
    if (isEmpty(travelers?.[0]?.name)) {
      alert("예약자 한글이름을 입력해주세요.");
      formElement["travelers[0].name"].focus();
      return false;
    } else if (isEmpty(travelers?.[0]?.cellPhoneNumber)) {
      alert("예약자 휴대폰 번호 11자리를 입력해주세요.");
      formElement["travelers[0].cellPhoneNumber"].focus();
      return false;
    } else if (isEmpty(travelers?.[0]?.email)) {
      formElement["travelers[0].email"].focus();
      alert("예약자 이메일을 입력해주세요.");
      return false;
    } else if (!emailValidationCheck(travelers?.[0]?.email)) {
      alert("유효하지 않은 예약자 이메일 주소입니다.\n이메일 주소를 확인해 주세요.");
      formElement["travelers[0].email"].focus();
      return false;
    } else if (!formState.reservationRuleAgreementDTO.isAgreeReserverInfo) {
      alert("예약자 정보 확인 및 동의를 체크해 주세요.");
      return false;
    }

    if (!formState.reservationRuleAgreementDTO.isNameSamePassportRule) {
      alert("탑승객 정보 확인 및 동의를 체크해 주세요.");
      return false;
    }

    if (invalidNames) {
      alert("탑승객 이름을 입력해주세요.");
      return false;
    }

    if (invalidLastNames) {
      alert("탑승객 영문 성을 입력해주세요.");
      return false;
    }
    if (invalidLastNamesLength) {
      alert("탑승객 영문 성이 잘 못 되었습니다.");
      return false;
    }

    if (invalidFirstNames) {
      alert("탑승객 영문 이름을 입력해주세요.");
      return false;
    }
    if (invalidFirstNamesLength) {
      alert("탑승객 영문 이름이 잘 못 되었습니다.");
      return false;
    }

    if (invalidCellPhoneNumber) {
      alert("탑승객 휴대폰 번호를 입력해주세요.");
      return false;
    }
    if (invalidLengthCellPhoneNumber) {
      alert("탑승객 휴대폰 번호 11자리를 입력해주세요.");
      return false;
    }

    if (emptyEmail) {
      alert("탑승객 이메일을 입력해주세요.");
      return false;
    }
    if (invalidEmail) {
      alert("유효하지 않은 탑승객 이메일 주소입니다.\n이메일 주소를 확인해 주세요.");
      return false;
    }

    if (invalidPassportCountry) {
      alert("탑승객 여권 발행국가를 선택해주세요.");
      return false;
    }

    if (emptySettlementManagerEmail) {
      alert("전표담당자의 이메일 주소를 확인해주세요.");
      return false;
    }

    if (!isCheckRuleAll) {
      alert("예약 규정 동의를 체크해주세요.");
      return false;
    }

    if (isDocumentNumberUse && isDocumentNumberRequired) {
      if (emptyDocumentNumber) {
        alert("문서번호를 입력하셔야 합니다.");
        return false;
      }
    }

    if (isViolation(data) && isEmpty(formState.violationReason)) {
      alert("출장규정에 위배됩니다.\n규정 외 진행사유를 입력해주세요.");
      formElement["violationReason"].focus();
      return false;
    }

    if (isPassportExpireDateMin) {
      if (
        confirm(
          "여권 만료일이 출국일로부터 6개월(180일) 미만인 경우 출국이 불가합니다. (입국허가요건이 국가별로 상이함) 항공권 결제 이후 여권정보(여권번호/만료일) 수정은 가능하오니, 여권을 재발급 받으신 후 출국 전까지 여권정보를 수정해주시길 바랍니다. 여권정보 입력을 진행하시려면 ‘확인’버튼을 누르시기 바랍니다.",
        )
      )
        return true;
      return false;
    }

    return true;
  }

  async function onSubmit(e) {
    e.preventDefault();
    const isValid = validation(e, formState);
    if (isValid) {
      const { violationReason, documentNumber, bookingUserDTOs, attachFiles } = formState;
      const payload = {
        violationReason,
        documentNumbers: compact(documentNumber.map((el) => el.value)),
        attachFiles: attachFiles.map((file) => {
          return {
            originFileName: file.originFileName,
            tempFileName: file.tempFileName,
            fileUploadPath: file.fileUploadPath,
            fileSize: file.fileSize,
            attachFileType: file.attachFileType,
          };
        }),
        journeys: data.flights.map((flight) => {
          const { airline, arrAirport, deptAirport, departureDate, journeyKey, arrivalDate, journeyType, pairKey, fares } = flight;
          const { airFare, airTax, fuelChg, tkFee } = fares.paxTypeFares[0];
          return {
            airline,
            arrAirport,
            deptAirport,
            arrivalDate,
            departureDate,
            journeyKey,
            journeyType,
            pairKey,
            fareKey: fares.fareKey,
            airFare,
            airTax,
            fuelChg,
            tkFee,
          };
        }),
        passengers: bookingUserDTOs.map((user) => {
          const {
            birthday,
            gender,
            name,
            firstName,
            lastName,
            email,
            settlementManagerEmail,
            nationalityCode,
            cellPhoneNumber,
            customerPassport,
            travelerMileageInfoDTOs,
          } = user;
          const { passportNumber, expireYmd, country } = customerPassport;
          return {
            userID: user.id,
            dateOfBirth: momentKR(birthday, "YYYYMMDD").format("YYYY-MM-DD"),
            gender,
            nationality: nationalityCode || "KR",
            koreanName: name,
            englishFirstName: firstName.toUpperCase(),
            englishLastName: lastName.toUpperCase(),
            phoneNumber: cellPhoneNumber.replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3"),
            email,
            settlementManagerEmail,
            passportDocumentNo: passportNumber,
            passportExpirationDate: momentKR(expireYmd, "YYYYMMDD").format("YYYY-MM-DD"),
            passportIssuedByCode: country?.id || "KR",
            mileageMemberships: normalizeTravelerMileageInfoDTOs(travelerMileageInfoDTOs),
          };
        }),
      };
      try {
        dispatch(actionIncreaseLoadingCount("loadingBookingCount"));
        const res = await postPNR(payload);
        const id = res.data?.data?.id;
        const payloadCompareSchedule = normalizeCompareSchedule(userInfo, airCompareSchedules, lowestFareAndSchedule, data, isViolation);
        await postCompareSchedule(id, payloadCompareSchedule);
        navigate("/user/v2/overseas/air/bookingComplete/" + id);
      } catch (error) {
        alert("항공 예약이 실패하였습니다.\n다시 시도해주세요.");
      } finally {
        dispatch(actionDecreaseLoadingCount("loadingBookingCount"));
      }
    }
  }

  function normalizeTravelerMileageInfoDTOs(travelerMileageInfoDTOs) {
    return travelerMileageInfoDTOs
      .filter((el) => !isEmpty(el.airline) && !isEmpty(el.mileageMemberNo))
      .map((el) => ({ airline: el.airline, memberNo: el.mileageMemberNo }));
  }

  function getFormElements() {
    if (formRef.current) {
      const formElements = formRef.current.elements;
      const elementsObj = {};
      for (const element of formElements) {
        const name = element.name;
        const type = element.type;
        const id = element.id;
        if (typeof name === "string" && name.trim() !== "") {
          if (type === "radio") elementsObj[id] = element;
          else elementsObj[name] = element;
        }
      }
      return elementsObj;
    }
    return {};
  }

  useEffect(() => {
    if (!isEmpty(userInfo)) {
      const travelers = [
        {
          traverler: { id: userInfo?.id },
          lastName: userInfo?.customerPassport?.lastName,
          firstName: userInfo?.customerPassport?.firstName,
          isReserver: true,
          settlementManagerEmail: true, 
          name: userInfo?.name,
          cellPhoneNumber: userInfo?.cellPhoneNumber,
          email: userInfo?.email,
        },
      ];
      setFormState((prev) => {
        return set(cloneDeep(prev), "travelers", travelers);
      });
    }
  }, [userInfo]);

  return (
    <div id="container" className="pg-reserv">
      <form id="airBookingForm" onInput={onInput} onChange={onChange} ref={formRef}>
        <div className="step-reservation">
          <ol>
            <li className="active">
              <div className="num">1</div>
              <p className="tit">예약 요청</p>
            </li>
            <li>
              <div className="num">2</div>
              <p className="tit">예약 완료</p>
            </li>
          </ol>
        </div>
        <div className="contents default-wd clearfix !block">
          <LeftSide formState={formState} setFormState={setFormState} />
          <RightSide formState={formState} setFormState={setFormState} onSubmit={onSubmit} />
        </div>
      </form>
    </div>
  );
}
