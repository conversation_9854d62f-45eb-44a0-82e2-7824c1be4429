import { useEffect } from "react";
import { get } from "lodash";
import { useParams } from "react-router-dom";
import LeftSide from "@/app/pages/user_v2/custom/kortek/mypage/reservation/TravelAirView/LeftSide";
import RightSide from "@/app/pages/user_v2/mypage/reservation/TravelAirView/RightSide";
import { useAppDispatch } from "@/store";
import { actionAttachFileView, actionCompareAirSchedule, actionReservationTravelView, actionTravelHistories } from "@/store/travelViewSlice";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import { actionTravelRuleBase } from "@/store/airSlice";

export default function TravelAirView() {
  const { travelId } = useParams();
  const dispatch = useAppDispatch();
  const apiWithLoading = useApiWithLoading();

  useEffect(() => {
    apiWithLoading(async () => {
      const response = await Promise.all(
        [actionReservationTravelView, actionCompareAirSchedule, actionTravelHistories]
          .map((action) => dispatch(action({ travelId })))
          .concat(dispatch(actionTravelRuleBase())),
      );
      const bookingAirId = get(response[0], "payload.data.data.travel.bookingAir.id", null);
      if (bookingAirId) {
        await dispatch(actionAttachFileView({ bookingAirId })).unwrap();
      }
    });
  }, [travelId]);

  return (
    <div id="container" className="clearfix default-wd pg-member">
      <div className="path">
        <span>MY페이지</span>
        <span>나의 예약</span>
        <span>예약 내역</span>
        <span className="bold">상세정보</span>
      </div>
      <h2 className="title type-reserv">예약 내역</h2>
      <div className="contents default-wd clearfix reserv-detail-cotn !block">
        <LeftSide />
        <RightSide />
      </div>
    </div>
  );
}
