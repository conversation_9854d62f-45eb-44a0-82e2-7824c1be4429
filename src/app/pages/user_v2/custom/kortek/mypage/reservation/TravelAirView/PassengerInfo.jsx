import { Fragment, useEffect, useMemo, useState } from "react";
import { cloneDeep, get, set } from "lodash";
import { useAppSelector } from "@/store";
import { momentKR } from "@/utils/date";
import { selectCountryList, selectUserInfo } from "@/store/userSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { isEmpty } from "lodash";
import { postUpdatePassport } from "@/service/air";
import { useParams } from "react-router-dom";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import { trimString } from "@/utils/common";
import { DEFAULT_DATE, MAPPED_STATUS_CODE } from "@/constants/app";
import { Autocomplete, TextField } from "@mui/material";
import { CONTACT_ADMIN_ALERT, DEFAULT_COUNTRY } from "@/constants/common";

export default function PassengerInfo(props) {
  const { passenger = {}, index } = props;

  const { travelId } = useParams();
  const {
    data: { travel },
  } = useAppSelector(selectReservationTravelView);
  const { userInfo } = useAppSelector(selectUserInfo);
  const countryList = useAppSelector(selectCountryList);
  const [passportList, setPassportList] = useState([]);
  const apiWithLoading = useApiWithLoading();

  const [isEdit, setIsEdit] = useState(false);
  const [formData, setFormData] = useState({});
  const {
    travelerName,
    travelerLastName,
    travelerFirstName,
    passportLimitDate,
    gender,
    passportNumber,
    email,
    birthday,
    cellPhoneNumber,
    nationalityCode,
    passportNation,
    settlementManagerEmail,
    travelerMileageInfos = [],
  } = useMemo(() => formData, [formData]);

  function combineDateValue(name, value) {
    const baseVal = formData[name.slice(0, -1)];
    const defaultVal = isNaN(baseVal) || !baseVal ? DEFAULT_DATE : baseVal;
    const momentVal = momentKR(defaultVal, "YYYYMMDD");
    if (name.includes("Y")) return momentVal.year(value);
    if (name.includes("M")) return momentVal.month(value - 1);
    if (name.includes("D")) return momentVal.date(value);
  }

  function handleChangeForm(e) {
    const { name, value } = e.target;
    const isBirthDateChange = name.includes("birth");
    const isPassportLimitDate = name.includes("passportLimitDate");
    if (isBirthDateChange || isPassportLimitDate) {
      const combinedVal = combineDateValue(name, Number(value));
      setFormData((prev) => {
        const clonedPrev = cloneDeep(prev);
        return set(clonedPrev, name.slice(0, -1), combinedVal.format("YYYYMMDD"));
      });
    }
    setFormData((prev) => {
      const clonedPrev = cloneDeep(prev);
      return set(clonedPrev, name, value);
    });
  }

  const handleChangeAutocomplete = (name, value, dependName) => {
    if (!value) return;
    setFormData((prev) => {
      const nextState = cloneDeep(prev);
      set(nextState, name, value.code2);
      if (dependName) {
        set(nextState, dependName, value.code2);
      }
      return nextState;
    });
    if (dependName) {
      const selectedItem = countryList.find((item) => item.code2 === value.code2);
      const nextPassportList = [DEFAULT_COUNTRY, selectedItem, ...countryList.filter((item) => item.code2 !== value.code2)];
      setPassportList(nextPassportList);
    }
  };

  function validation() {
    const departureDaysMoment = momentKR(travel?.bookingAir?.bookingAirSchedules?.[0]?.fromDate);
    const passportExpireDate = momentKR(passportLimitDate, "YYYYMMDD");
    const isPassportExpireDateMin = passportExpireDate.diff(departureDaysMoment, "days") < 180;
    if (isEmpty(passportNumber)) {
      alert("여권 번호를 입력해주세요.");
      return false;
    }
    if (isEmpty(passportNation)) {
      alert("여권 발행국을 선택해주세요.");
      return false;
    }
    if (isEmpty(nationalityCode)) {
      alert("국적을 선택해주세요.");
      return false;
    }

    if (isPassportExpireDateMin) {
      if (
        confirm(
          "여권 만료일이 출국일로부터 6개월(180일) 미만인 경우 출국이 불가합니다. (입국허가요건이 국가별로 상이함) 항공권 결제 이후 여권정보(여권번호/만료일) 수정은 가능하오니, 여권을 재발급 받으신 후 출국 전까지 여권정보를 수정해주시길 바랍니다. 여권정보 입력을 진행하시려면 ‘확인’버튼을 누르시기 바랍니다.",
        )
      )
        return true;
      return false;
    }
    return true;
  }

  async function modifyPassportProcess() {
    const valid = validation();
    if (valid) {
      const payload = {
        passengerIdx: index + 1,
        orderKey: travel?.bookingAir?.orderKey,
        updateType: "UpdatePassport",
        bookingAirTravelerId: formData?.id,
        travelId: Number(travelId),
        lastName: travelerLastName,
        firstName: travelerFirstName,
        passportNumber,
        passportLimitDate: momentKR(passportLimitDate, "YYYYMMDD").format("YYYY-MM-DD"),
        birthday: momentKR(birthday, "YYYYMMDD").format("YYYY-MM-DD"),
        passportNation,
        nationalityCode,
        email,
        cellPhoneNumber,
        travelerName,
        travelerMileageInfos,
      };
      await apiWithLoading(
        () => postUpdatePassport(payload),
        (response) => {
          const resultCode = get(response, "data.resultCode");
          if (resultCode?.toLocaleUpperCase() !== "SUCCESS") {
            alert(`처리 중 에러가 발생하였습니다.\n관리자에게 문의 하시기 바랍니다.${CONTACT_ADMIN_ALERT}`);
            return;
          }
          alert("수정되었습니다.");
          location.reload();
        },
        () => {
          alert(`처리 중 에러가 발생하였습니다.\n관리자에게 문의 하시기 바랍니다.${CONTACT_ADMIN_ALERT}`);
        },
      );
    }
  }

  function reset() {
    setFormData(passenger);
  }

  useEffect(() => {
    if (!isEmpty(passenger) && !isEmpty(travel)) {
      reset();
    }
  }, [passenger, travel]);

  useEffect(() => {
    setPassportList([DEFAULT_COUNTRY, ...countryList]);
  }, [countryList]);

  return (
    <Fragment>
      {!isEdit ? (
        <div>
          <div className="edit">
            <p>
              {travelerName} ({travelerLastName} {travelerFirstName})
            </p>
            {![MAPPED_STATUS_CODE.Completed.id, MAPPED_STATUS_CODE.Cancelled.id].includes(travel?.statusCode?.id) && (
              <button type="button" onClick={() => setIsEdit(true)} className="btn-default">
                여권정보 수정
              </button>
            )}
          </div>
          <div className="clearfix [&_dd]:mb-0">
            <dl className="box-col">
              <dt>성별</dt>
              <dd>{gender === "Male" ? "남성" : "여성"}</dd>
            </dl>
            <dl className="box-col">
              <dt>생년월일</dt>
              <dd>
                {trimString(birthday, 0, 4)}년 {trimString(birthday, 4, 6)}월 {trimString(birthday, 6, 8)}일
              </dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>국적</dt>
              <dd>{countryList?.find((el) => el?.code2 === nationalityCode)?.name}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>휴대전화 번호</dt>
              <dd>{cellPhoneNumber}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>이메일 주소</dt>
              <dd>{email}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>전표담당자 이메일 주소</dt>
              <dd>
                {settlementManagerEmail}@{userInfo?.workspace?.company?.btmsSetting?.emailDomain}
              </dd>
            </dl>
            <dl className="box-col" style={{ height: 84, borderLeftStyle: "none" }}>
              <dt>여권 번호</dt>
              <dd>{passportNumber}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>여권 만료 기간</dt>
              <dd>
                {trimString(passportLimitDate, 0, 4)}년 {trimString(passportLimitDate, 4, 6)}월 {trimString(passportLimitDate, 6, 8)}일
              </dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>여권 발행국</dt>
              <dd>{countryList?.find((el) => el?.code2 === passportNation)?.name}</dd>
            </dl>
          </div>
          <div className="clearfix mileage">
            <div className="title">마일리지 회원번호</div>
            <div className="content">
              <div>
                <span>항공사{index + 1}</span>
                <input type="text" name={`airline_${index}`} defaultValue={travelerMileageInfos?.[0]?.airline} readOnly />
                <span>회원번호{index + 1}</span>
                <input type="text" name={`mileageMemberNo_${index}`} defaultValue={travelerMileageInfos?.[0]?.mileageMemberNo} readOnly />
              </div>
              <div>
                <span>항공사{index + 1}</span>
                <input type="text" name={`airline_${index}`} defaultValue={travelerMileageInfos?.[1]?.airline} readOnly />
                <span>회원번호{index + 1}</span>
                <input type="text" name={`mileageMemberNo_${index}`} defaultValue={travelerMileageInfos?.[1]?.mileageMemberNo} readOnly />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <form>
          <div className="edit">
            <button
              type="button"
              onClick={() => {
                reset();
                setIsEdit(false);
              }}
              className="btn-default"
            >
              취소
            </button>
            <button type="button" onClick={modifyPassportProcess} className="btn-default mr-[5px]">
              수정
            </button>
          </div>
          <div className="clearfix names">
            <dl className="box-col">
              <dt>한글이름</dt>
              <dd>{travelerName}</dd>
            </dl>
            <dl className="box-col">
              <dt>영문 성 </dt>
              <dd>{travelerLastName}</dd>
            </dl>
            <dl className="box-col">
              <dt>영문 이름 </dt>
              <dd>{travelerFirstName}</dd>
            </dl>
          </div>
          <div className="clearfix privacy">
            <dl className="box-col" style={{ height: 107 }}>
              <dt>성별</dt>
              <dd>{gender === "Male" ? "남성" : "여성"}</dd>
            </dl>
            <dl className="box-col dates" style={{ height: 107 }}>
              <dt>생년월일</dt>
              <dd className="desc box-ymd box-border">
                <span className="form-select yy">
                  <select name="birthdayY" value={trimString(birthday, 0, 4)} onChange={handleChangeForm}>
                    {Array.from({ length: 100 }, (_, i) => ({
                      value: `${momentKR().year() - i}`,
                      label: `${momentKR().year() - i}년`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
                <span className="form-select mm">
                  <select name="birthdayM" value={trimString(birthday, 4, 6)} onChange={handleChangeForm}>
                    {Array.from({ length: 12 }, (_, i) => ({
                      value: String(i + 1).padStart(2, "0"),
                      label: `${i + 1}월`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
                <span className="form-select dd">
                  <select name="birthdayD" value={trimString(birthday, 6, 8)} onChange={handleChangeForm}>
                    {Array.from({ length: 31 }, (_, i) => ({
                      value: String(i + 1).padStart(2, "0"),
                      label: `${i + 1}일`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
              </dd>
            </dl>
            <dl className="box-col" style={{ height: 107 }}>
              <dt>국적</dt>
              <dd>
                <Autocomplete
                  className="autocomplete-custom outlined"
                  options={countryList}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => <TextField {...params} label="" variant="outlined" size="small" />}
                  value={countryList.find((item) => item.code2 === formData?.nationalityCode)}
                  onChange={(e, next) => handleChangeAutocomplete(`nationalityCode`, next, `passportNation`)}
                  sx={{
                    "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline": {
                      border: "1px solid #e2e4e8 !important",
                    },
                  }}
                />
              </dd>
              <dd style={{ color: "red", fontSize: 11 }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</dd>
            </dl>
          </div>
          <div className="clearfix passport">
            <dl className="box-col" style={{ height: 84 }}>
              <dt>휴대전화 번호</dt>
              <dd>{cellPhoneNumber}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>이메일 주소</dt>
              <dd>{email}</dd>
            </dl>
            <dl className="box-col" style={{ height: 84 }}>
              <dt>
                전표담당자 이메일 주소 <i style={{ color: "#f4516c" }}>*</i>
              </dt>
              <dd className="desc" style={{ display: "flex" }}>
                <input type="text" name="settlementManagerEmail" value={formData?.settlementManagerEmail} style={{ width: "61%" }} />
                <input type="text" placeholder="@kortek.co.kr" style={{ width: "39%" }} readOnly />
              </dd>
            </dl>
          </div>
          <div className="clearfix passport">
            <dl className="box-col" style={{ height: 107 }}>
              <dt>여권 번호</dt>
              <dd className="box-border box-content">
                <input
                  type="text"
                  name="passportNumber"
                  value={formData?.passportNumber}
                  onChange={handleChangeForm}
                  style={{ textTransform: "uppercase" }}
                  maxLength={30}
                />
              </dd>
              <dd style={{ color: "red", fontSize: 11 }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</dd>
            </dl>
            <dl className="box-col dates" style={{ height: 107 }}>
              <dt>여권 만료 기간</dt>
              <dd className="desc box-ymd box-border">
                <span className="form-select yy">
                  <select name="passportLimitDateY" value={trimString(passportLimitDate, 0, 4)} onChange={handleChangeForm}>
                    {Array.from({ length: 20 }, (_, i) => ({
                      value: `${momentKR().year() + i}`,
                      label: `${momentKR().year() + i}년`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
                <span className="form-select mm">
                  <select name="passportLimitDateM" value={trimString(passportLimitDate, 4, 6)} onChange={handleChangeForm}>
                    {Array.from({ length: 12 }, (_, i) => ({
                      value: String(i + 1).padStart(2, "0"),
                      label: `${i + 1}월`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
                <span className="form-select dd">
                  <select name="passportLimitDateD" value={trimString(passportLimitDate, 6, 8)} onChange={handleChangeForm}>
                    {Array.from({ length: 31 }, (_, i) => ({
                      value: String(i + 1).padStart(2, "0"),
                      label: `${i + 1}일`,
                    })).map(({ value, label }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                </span>
              </dd>
              <dd style={{ color: "red", fontSize: 11 }}>* 회원정보에 동일하게 수정 하시길 바랍니다.</dd>
            </dl>
            <dl className="box-col" style={{ height: 107 }}>
              <dt>여권 발행국</dt>
              <dd>
                <Autocomplete
                  className="autocomplete-custom outlined"
                  options={passportList}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => <TextField {...params} label="" variant="outlined" size="small" />}
                  value={passportList.find((item) => item.code2 === formData?.passportNation)}
                  onChange={(e, next) => handleChangeAutocomplete(`passportNation`, next)}
                  sx={{
                    "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline": {
                      border: "1px solid #e2e4e8 !important",
                    },
                  }}
                />
              </dd>
            </dl>
          </div>
          <div className="clearfix mileage">
            <div className="title">마일리지 회원번호</div>
            <div className="content">
              <div>
                <span>항공사1</span>
                <input
                  type="text"
                  name="travelerMileageInfos[0].airline"
                  value={formData?.travelerMileageInfos?.[0]?.airline}
                  onChange={handleChangeForm}
                  style={{ border: "1px solid #e2e4e8" }}
                />
                <span>회원번호1</span>
                <input
                  type="text"
                  name="travelerMileageInfos[0].mileageMemberNo"
                  value={formData?.travelerMileageInfos?.[0]?.mileageMemberNo}
                  onChange={handleChangeForm}
                  style={{ border: "1px solid #e2e4e8" }}
                />
              </div>
              <div>
                <span>항공사1</span>
                <input
                  type="text"
                  name="travelerMileageInfos[1].airline"
                  value={formData?.travelerMileageInfos?.[1]?.airline}
                  onChange={handleChangeForm}
                  style={{ border: "1px solid #e2e4e8" }}
                />
                <span>회원번호1</span>
                <input
                  type="text"
                  name="travelerMileageInfos[1].mileageMemberNo"
                  value={formData?.travelerMileageInfos?.[1]?.mileageMemberNo}
                  onChange={handleChangeForm}
                  style={{ border: "1px solid #e2e4e8" }}
                />
              </div>
            </div>
          </div>
        </form>
      )}
    </Fragment>
  );
}
