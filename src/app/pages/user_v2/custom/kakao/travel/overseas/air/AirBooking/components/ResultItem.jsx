import { useState } from "react";
import { useSelector } from "react-redux";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import ItineraryInformation from "@/app/pages/user_v2/travel/overseas/air/Booking/components/ItineraryInformation";

export default function ResultItem() {
  const { data, filter } = useSelector(selectAirOverseasSessionData);
  const [active, setActive] = useState(true);

  const handleToggleActive = () => {
    setActive(!active);
  };

  return (
    <div className={`box-item result-item ${active ? "active" : ""}`}>
      <h3 className="tit">여정정보</h3>
      <div className="box-cotn info-booking">
        <dl className="plan">
          {data.flights &&
            data.flights.map((flight, index) => {
              return <ItineraryInformation key={index} data={flight} filter={filter} index={index} flightDetailSize={data.flights.length} />;
            })}
        </dl>
      </div>
      <div className="btn-arrow" onClick={handleToggleActive}>
        <button type="button" className="btn-default" />
      </div>
    </div>
  );
}
