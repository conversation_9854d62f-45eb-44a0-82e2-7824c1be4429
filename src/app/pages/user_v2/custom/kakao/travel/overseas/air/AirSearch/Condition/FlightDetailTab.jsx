import { useMemo } from "react";
import moment from "moment";
import { isEmpty } from "lodash";
import { getSeatTypeName, getHourMin, onErrorImgAirline } from "@/utils/common.js";
import { MAPPING_UNIT } from "@/constants/common";
import { SECTION_TYPE } from "@/constants/app";
import AIR_ICONS_PATH from "@/assets/airIcon";
import { nvl } from "@/common";

const FlightDetailTab = (props) => {
  const { flights, filter } = props;
  const isMultiCity = useMemo(() => filter?.sectionType === SECTION_TYPE.MULTICITY, [filter]);

  function getTime(segments, hasStopOverTime = false) {
    let totalTime = 0;
    segments.forEach((segment) => {
      totalTime += Number(segment.flightTime);
      if (hasStopOverTime) totalTime += Number(segment.waitingTime);
    });
    const convertedTime = convertMinutesToHoursAndMinutes(totalTime);
    return `${hasStopOverTime ? "총 " : ""} ${convertedTime.hours}시간 ${convertedTime.minutes ? `${convertedTime.minutes}분` : ""}`;
  }

  function convertMinutesToHoursAndMinutes(totalMinutes) {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return { hours, minutes };
  }

  return flights.map((flight, index) => {
    const { deptAirportName, arrAirportName, deptAirport, arrAirport, segments, fares, freessrs } = flight;
    const freeBaggage = freessrs?.find((freessr) => freessr.ssrType === "Baggage" && freessr.ssrService[0].ssrAmount === 0);
    const quantity = freeBaggage?.ssrService[0]?.ssrValue || 0;
    const unit = MAPPING_UNIT[freeBaggage?.ssrUnit || ""] || "";

    return (
      <dl key={flight.journeyKey + index}>
        <dt>
          <div className="type">{isMultiCity ? `여정 ${index + 1}` : index === 0 ? "가는 편" : "오는 편"}</div>
          <div className="city">
            <span className="dep">
              {deptAirportName}({deptAirport})
            </span>
            <span className="arr">
              {arrAirportName}({arrAirport})
            </span>
          </div>
          <div className="time">{getTime(segments, true)}</div>
        </dt>
        {segments.map((seg, index) => {
          const {
            deptAirport,
            arrAirport,
            deptAirportName,
            departureDate,
            arrivalDate,
            arrAirportName,
            carrierCode,
            carrierCodeName,
            flightNumber,
            cabinClass,
            bookingClass,
            waitingTime,
            legs,
          } = seg;
          const { operatingCarrier, operatingCarrierName } = legs[0];
          return (
            <dd key={seg.flightNumber + index}>
              <div className="plane clearfix">
                <span>
                  <img src={AIR_ICONS_PATH[carrierCode] || ""} onError={onErrorImgAirline} className="w-[30px] h-[20px] object-cover inline-block" />
                  {carrierCodeName}
                </span>
                <span>
                  {carrierCode}
                  {flightNumber}
                </span>
                <span>
                  {getSeatTypeName(cabinClass)}({bookingClass})
                </span>
              </div>
              {operatingCarrier !== carrierCode && (
                <div className="boarding">
                  <span>
                    실제탑승 - {nvl(operatingCarrierName, "")} ({operatingCarrier})
                  </span>
                </div>
              )}
              <div className="schdule">
                <div className="clearfix">
                  <p className="date">{moment(departureDate).format("MM월 DD일 ddd")}</p>
                  <p className="time">{moment(departureDate).format("HH:mm")}</p>
                  <div className="sign point" />
                  <p className="city">
                    <span className="en">{deptAirport}</span>
                    <span className="kr">{deptAirportName}</span>
                  </p>
                </div>
                <p className="time-plan">{getTime([seg])}</p>
                <div className="clearfix">
                  <p className="date">{moment(arrivalDate).format("MM월 DD일 ddd")}</p>
                  <p className="time">{moment(arrivalDate).format("HH:mm")}</p>
                  <div className="sign ing" />
                  <p className="city">
                    <span className="en">{arrAirport}</span>
                    <span className="kr">{arrAirportName}</span>
                  </p>
                </div>
                {waitingTime !== 0 && <p className="overstop">{getHourMin(waitingTime)}</p>}
                <div className="etc">
                  {isEmpty(freessrs) || !freeBaggage ? "무료 수하물 불포함" : `무료 수하물 ${quantity} ${unit}`}
                  <br /> 확정 <strong>{fares?.availableCount}</strong>석 남음
                </div>
              </div>
            </dd>
          );
        })}
      </dl>
    );
  });
};

export default FlightDetailTab;
