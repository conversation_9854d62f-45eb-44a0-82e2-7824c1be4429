import { useState, useRef, useEffect } from "react";
import { isEmpty } from "lodash";
import { useClickOutside } from "@/app/hooks/useClickOutside.js";

const DEPART_SEAT_TYPE_CODES = [
  { id: 1, name: "<PERSON>", label: "일반석" },
  { id: 2, name: "W", label: "프리미엄 이코노미" },
  { id: 3, name: "C", label: "비즈니스" },
  { id: 4, name: "F", label: "일등석" },
];
const ClassMember = (props) => {
  const { filter, setFilter } = props;
  const buttonRef = useRef(null);
  const menuRef = useRef(null);
  const [openDropdown, setOpenDropdown] = useState(false);
  const [codeAndQuantity, setCodeAndQuantity] = useState({
    adultCount: filter.adultCount,
    departSeatTypeCode: filter.departSeatTypeCode,
  });

  function changeSeatTypeCode(e) {
    const { value } = e.target;
    setCodeAndQuantity((prev) => ({ ...prev, departSeatTypeCode: value }));
  }

  function changeAdultCount(type) {
    if (type === "INCREASE") {
      setCodeAndQuantity((prev) => ({ ...prev, adultCount: Number(prev.adultCount) + 1 }));
    } else if (type === "DECREASE") {
      setCodeAndQuantity((prev) => ({ ...prev, adultCount: Number(prev.adultCount) - 1 }));
    }
  }

  function applySearch() {
    setFilter((prev) => ({ ...prev, adultCount: codeAndQuantity.adultCount, departSeatTypeCode: codeAndQuantity.departSeatTypeCode }));
    setOpenDropdown(false);
  }

  useClickOutside(menuRef, buttonRef, () => setOpenDropdown(false));

  useEffect(() => {
    if (!isEmpty(filter)) {
      setCodeAndQuantity({ adultCount: filter.adultCount, departSeatTypeCode: filter.departSeatTypeCode });
    }
  }, [filter]);

  return (
    <div className="passenger">
      <dl className="tit" ref={buttonRef} onClick={() => setOpenDropdown(!openDropdown)}>
        <dt>탑승객</dt>
        <dd className="txt-short">
          <a className="btn-class-member" id="etcBookingInfoTxt">
            성인 {filter.adultCount}, {DEPART_SEAT_TYPE_CODES.find((item) => item.name === filter.departSeatTypeCode)?.label || ""}
          </a>
        </dd>
      </dl>
      {openDropdown && (
        <div className={`layer-class-member ${openDropdown ? "active" : ""}`} ref={menuRef}>
          <p className="tit">인원 &amp; 좌석 등급</p>
          <dl>
            <dt className="font-medium">성인</dt>
            <dd className="count">
              <button
                type="button"
                className="btn-default minus"
                id="adultCountMinus"
                onClick={() => changeAdultCount("DECREASE")}
                disabled={codeAndQuantity.adultCount === 1}
              >
                -
              </button>
              <input type="number" value={codeAndQuantity.adultCount} id="adultCount" readOnly="" />
              <button type="button" className="btn-default plus" id="adultCountPlus" onClick={() => changeAdultCount("INCREASE")}>
                +
              </button>
              <input type="hidden" name="adultCount" defaultValue={1} />
            </dd>
          </dl>
          <ul>
            {DEPART_SEAT_TYPE_CODES.map((item) => (
              <li key={item.id}>
                <label className="form-radio">
                  <input
                    type="radio"
                    defaultValue={item.name}
                    name="departSeatTypeCode"
                    checked={codeAndQuantity.departSeatTypeCode === item.name}
                    onChange={changeSeatTypeCode}
                  />
                  <span className="before:box-content">{item.label}</span>
                </label>
              </li>
            ))}
          </ul>
          <div className="btn-apply">
            <button type="button" className="btn-default" onClick={applySearch}>
              적용
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClassMember;
