import { useMemo, useState, Fragment } from "react";
import { useDispatch } from "react-redux";
import { isEmpty } from "lodash";
import { comma } from "@/utils/common";
import LowestSchedule from "@/app/components/user_v2/modal/LowestSchedule.modal";
import btnTop from "@/assets/images/cmm/btn_top.png";
import { useAppSelector } from "@/store";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { selectUserInfo } from "@/store/userSlice";
import FareRule from "@/app/components/user_v2/modal/FareRule.modal";
import AirBookingCompareSchedule from "@/app/components/user_v2/modal/AirBookingCompareSchedule.modal";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { getFareRule } from "@/service/air";
import DocumentNumber from "@/app/pages/user_v2/travel/overseas/air/Booking/components/DocumentNumber";
import "@/styles/user_v2/member.css";
import FlightConfirmBookingModal from "@/app/components/user_v2/modal/FlightConfirmBooking.modal";

export default function RightSide(props) {
  const { formState, setFormState, onSubmit } = props;
  const dispatch = useDispatch();

  const sessionData = useAppSelector(selectAirOverseasSessionData);
  const { userInfo } = useAppSelector(selectUserInfo);

  const { filter, data, lowestFareAndSchedule } = sessionData;
  const [openModal, setOpenModal] = useState(false);
  const [openAirCompareSchedule, setOpenAirCompareSchedule] = useState(false);
  const [fareRules, setFareRules] = useState([]);
  const [openFlightConfirmBookingModal, setOpenFlightConfirmBookingModal] = useState(false);
  const paxTypeFares = useMemo(() => {
    if (lowestFareAndSchedule?.fares?.paxTypeFares) return lowestFareAndSchedule?.fares?.paxTypeFares[0];
    return [];
  }, [lowestFareAndSchedule?.fares?.paxTypeFares]);

  const fare = useMemo(() => {
    if (isEmpty(data)) return null;
    return data.fares.paxTypeFares?.[0];
  }, [data]);

  async function openFareRuleModal(journey) {
    try {
      dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
      const payload = {
        journeys: journey.flights.map((flight) => {
          const { airline, deptAirport, arrAirport, departureDate, journeyKey, fares, pairKey } = flight;
          return {
            journeyKey,
            fareKey: fares.fareKey,
            pairKey,
            airline,
            deptAirport,
            arrAirport,
            departureDate,
            promotionId: "",
            fopPromotionId: "",
          };
        }),
      };
      const response = await getFareRule(payload);
      setFareRules(response.data?.data?.journeys);
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
    }
  }

  return (
    <Fragment>
      <div className="right-side sec-pay-to reserv-detail-cotn !sticky top-0 right-0">
        {userInfo?.workspace?.company?.btmsSetting?.isComparativePrice && userInfo?.workspace?.company?.btmsSetting?.isLowestPrice && (
          <div className="box-line" id="lowestDisplayLayout" style={{ cursor: "pointer" }} onClick={() => setOpenModal(true)}>
            <div className="txt-big clearfix">
              <p className="fl-l">최저가 요금</p>
              <p className="fl-r c-price">
                <strong id="lowestPriceStrong">
                  {comma(paxTypeFares?.airFare + paxTypeFares?.airTax + paxTypeFares?.fuelChg + paxTypeFares?.tasfAmount)}
                </strong>
                원
              </p>
            </div>
          </div>
        )}
        <div className="box-line">
          <div className="txt-big clearfix">
            <p className="fl-l">총 결제요금</p>
            <p className="fl-r c-price">
              <strong id="totalPaymentStrong">
                {comma((fare?.airFare + fare?.airTax + fare?.fuelChg + fare?.tasfAmount) * filter?.adultCount || 1)}
              </strong>
              원
            </p>
          </div>
          <dl className="fare-detail">
            <dt>성인 {filter.adultCount}명</dt>
            <dd>
              <span className="tit">항공료</span>
              <span className="sum">
                {comma(fare?.airFare)}원 x {filter?.adultCount}
              </span>
            </dd>
            <dd>
              <span className="tit">TAX</span>
              <span className="sum">
                {comma(fare?.airTax + fare?.fuelChg)}원 x {filter?.adultCount}
              </span>
            </dd>
            <dd>
              <span className="tit">발권수수료</span>
              <span className="sum" id="tasfAmountSpan">
                {comma(fare?.tasfAmount)}원 x {filter?.adultCount}
              </span>
            </dd>
          </dl>
        </div>
        <div className="box-line">
          <p className="txt-big">출장자 규정</p>
          <ul className="brakedown [&>li]:!pt-0 [&>li]:before:!top-[10px] [&>li>div>span]:!no-underline">
            <li>
              Business Class upgrade 기준
              <div className="tooltip-custom">
                <span className="tooltip-custom-icon" />
                <span className="tooltip-custom-text">
                  <ul className="font-light no-underline list-disc pl-6">
                    <li>순수 업무수행 목적으로 비행시간 8시간 이상</li>
                    <li>야간비행(22시~06시)후 바로 업무복귀 등 기타 업무 일정 있는 경우 비행시간 5시간 이상</li>
                    <li>교육 및 컨퍼런스, 업체 행사(접대성)의 경우 Economy Class 이용</li>
                    <li>출장자가 임신부인 경우</li>
                    <li>주요 단체 및 업체의 동행, 수행이 필요한 경우</li>
                    <li>좌석 매진 및 기타 특별 사정으로 해당 좌석 등급 이용 불가한 경우</li>
                  </ul>
                </span>
              </div>
            </li>
            <li>항공권: 출장 일정에 맞는 항공권 중, Low fare 이용 원칙</li>
          </ul>
        </div>
        <div className="box-line">
          <p className="txt-big">
            결재 요청 의견 <i style={{ color: "#f4516c" }}>*</i>
          </p>
          <div className="opinion-insert">
            <textarea
              name="violationReason"
              id="violationReason"
              maxLength={500}
              placeholder="- 비즈니스 클래스 발권 요청하는 경우 사유 작성 바랍니다. (ex임신부 등) &#13;&#10;- 규정 외 발권 요청이 없는 경우 &#13;&#10;  “없음”으로 기재 바랍니다."
              value={formState.violationReason}
              onChange={() => {}}
            />
          </div>
        </div>
        {userInfo?.workspace.company.btmsSetting.isDocumentNumberUse && <DocumentNumber formState={formState} setFormState={setFormState} />}
        <div className="box-applys">
          <button type="button" className="btn-default btn-request" onClick={() => setOpenFlightConfirmBookingModal(true)}>
            예약 요청
          </button>
          <a className="btn-default btn-compare" onClick={() => openFareRuleModal(data)}>
            요금 규정
          </a>
          {userInfo?.workspace?.company?.btmsSetting?.isComparativePrice && (
            <button type="button" className="btn-default btn-compare" onClick={() => setOpenAirCompareSchedule(true)}>
              비교견적서
            </button>
          )}
        </div>
        <div id="btn-top" className="!flex !justify-end">
          <a onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}>
            <img src={btnTop} alt="Top" />
          </a>
        </div>
      </div>
      <LowestSchedule open={openModal} setOpen={setOpenModal} />
      <FareRule fareRules={fareRules} setFareRules={setFareRules} />
      <AirBookingCompareSchedule type="VIEW" defaultSelect={data} open={openAirCompareSchedule} setOpen={setOpenAirCompareSchedule} />
      <FlightConfirmBookingModal
        open={openFlightConfirmBookingModal}
        setOpen={setOpenFlightConfirmBookingModal}
        bookingUserDTOs={formState.bookingUserDTOs}
        onSubmit={onSubmit}
      />
    </Fragment>
  );
}
