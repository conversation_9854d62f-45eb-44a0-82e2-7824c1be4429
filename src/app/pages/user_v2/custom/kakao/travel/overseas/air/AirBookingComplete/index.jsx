import { useEffect, useMemo, useState } from "react";
import { isEmpty } from "lodash";
import { useNavigate, useParams } from "react-router-dom";
import { comma, returnPhoneNumber } from "@/utils/common";
import { useAppSelector, useAppDispatch } from "@/store";
import { actionTravelRuleBase } from "@/store/airSlice";
import { actionBtmsManager, actionReservationTravelView, selectBtmsManager4Visa, selectReservationTravelView } from "@/store/travelViewSlice";
import PopMileage from "@/app/components/user_v2/modal/PopMileage.modal";
import PopViewTicketDateWarning from "@/app/components/user_v2/modal/PopViewTicketDateWarning.modal";
import { selectUserInfo } from "@/store/userSlice";
import { BTMS_MANAGER_CODE } from "@/constants/app";
import { getTravelCities } from "@/utils/app";

export default function AirBookingComplete() {
  const { travelId } = useParams();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const {
    data: { travel },
  } = useAppSelector(selectReservationTravelView);
  const { userInfo } = useAppSelector(selectUserInfo);
  const btmsManager4Visa = useAppSelector(selectBtmsManager4Visa);

  const [openPopMileageModal, setOpenPopMileageModal] = useState(true);
  const [openPopViewTicketDateWarning, setOpenPopViewTicketDateWarning] = useState(false);

  function viewReservation() {
    navigate("/user/v2/reservation/travel/view/" + travelId);
  }

  useEffect(() => {
    if (travel?.bookingAir?.isViewTicketDateWarning) {
      setOpenPopViewTicketDateWarning(true);
    }
  }, [travel]);

  useEffect(() => {
    dispatch(actionTravelRuleBase());
    dispatch(actionReservationTravelView({ travelId }));
    dispatch(actionBtmsManager({ managerType: BTMS_MANAGER_CODE.PASSPORT_VISA }));
  }, [travelId]);

  return (
    <div id="container" className="pg-reserv ">
      <div className="step-reservation">
        <ol>
          <li>
            <div className="num">1</div>
            <p className="tit">예약 요청</p>
          </li>
          <li className="active">
            <div className="num">2</div>
            <p className="tit">예약 완료</p>
          </li>
        </ol>
      </div>
      <div className="contents default-wd clearfix !block">
        {/* left-side */}
        <div className="left-side ">
          <div className="left-side sec-resev-request box-content">
            <dl>
              <dt>{getTravelCities(travel).join(" - ")} 예약을 요청했습니다.</dt>
              <dd>
                여행사 예약 담당자와 최종 확인 후 발권이 진행되며, 예약시 기재하신 휴대폰 번호/이메일로 예약안내메일이 나갈 예정입니다.
                <br />
                입국 시 비자 등 추가 필요 서류가 있을 경우 담당자가 함께 안내 드릴 예정입니다.
              </dd>
            </dl>
            <p className="code">
              BTMS 예약번호 <strong>{travelId}</strong>
            </p>
          </div>
          <div className="left-side" style={{ marginTop: 50 }}>
            <dl>
              <dt>
                <a
                  href="https://www.0404.go.kr/dev/notice_view.mofa?id=ATC0000000009020&pagenum=1&mst_id=MST0000000000045&st=title&stext="
                  target="_blank"
                  style={{ fontSize: "26px", color: "#4e81ff" }}
                >
                  <p>
                    <strong> 외교부 해외안전여행 사이트 (각국의 입국허가 요건) 바로가기</strong>
                  </p>
                </a>
              </dt>
              <dd>
                <br />- 지역별 무비자 입국과 비자가 필요한 국가 확인
                <br />- 경로: 외교부{">"}영사서비스{">"}공지사항{">"}각국의 입국허가(여권, 사증 등) 요건 안내
              </dd>
            </dl>
          </div>
          {!isEmpty(btmsManager4Visa) && (
            <div className="left-side" style={{ marginTop: 50, textAlign: "center" }}>
              비자 신청 문의 : {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "FIRST")} -{" "}
              {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "MIDDLE")} -{" "}
              {returnPhoneNumber(btmsManager4Visa?.travelAgencyUser?.phoneNumber, "END")}
            </div>
          )}
        </div>
        <div className="right-side sec-pay-to">
          <div className="box-line">
            <div className="txt-big clearfix">
              <p className="fl-l">총 결제요금</p>
              <p className="fl-r c-price">
                <strong>{comma(travel?.bookingAir?.totalAmount)}</strong>원
              </p>
            </div>
            <dl className="fare-detail">
              <dt>성인 {travel?.bookingAir?.adultCount}명</dt>
              <dd>
                <span className="tit">항공료</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.fareAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
              <dd>
                <span className="tit">TAX</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.taxAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
              <dd>
                <span className="tit">발권수수료</span>
                <span className="sum">
                  {comma(travel?.bookingAir?.commissionAmount / (travel?.bookingAir?.adultCount || 1))} X {travel?.bookingAir?.adultCount}
                </span>
              </dd>
            </dl>
          </div>
          {userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberUse && (
            <div className="box-line">
              <p className="txt-big">
                {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                  : "문서번호"}
              </p>
              {travel?.documentNumbers?.map((documentNumber, index) => (
                <p className="txt" key={index + documentNumber}>
                  {documentNumber?.documentNo}
                </p>
              ))}
            </div>
          )}

          <div className="box-line">
            <p className="txt-big">결재 요청 의견</p>
            <div className="opinion-insert">
              <textarea name="violationReason" id="violationReason" maxLength={500} readOnly>
                {travel?.violationReason}
              </textarea>
            </div>
          </div>
          <div className="box-applys">
            <button type="button" className="btn-default btn-request" onClick={viewReservation}>
              예약 내역 보기
            </button>
          </div>
        </div>
      </div>
      <PopMileage open={openPopMileageModal} isMini openSetOpenModal={() => setOpenPopMileageModal(false)} />
      <PopViewTicketDateWarning open={openPopViewTicketDateWarning} openSetOpenModal={() => setOpenPopViewTicketDateWarning(false)} type="KAKAO" />
    </div>
  );
}
