import SearchCity from "@/app/components/user_v2/common/SearchCity.jsx";
import useOpenComponent from "@/app/hooks/useOpenComponent.js";
import { Fragment, useRef } from "react";

const DepartureCitySelect = (props) => {
  const { children, code, onChooseArrival, additionalPosition = { top: 0, left: 0 } } = props;
  const dropdownRef = useRef(null);
  const { isOpen, dropdownPosition, clonedChild, setIsOpen } = useOpenComponent({ children, dropdownRef });
  const handleChooseCity = (item) => {
    onChooseArrival(item);
    setIsOpen(false);
  };

  return (
    <Fragment>
      {clonedChild}
      <SearchCity
        code={code}
        ref={dropdownRef}
        dropdownPosition={dropdownPosition}
        open={isOpen}
        onChooseCity={handleChooseCity}
        additionalPosition={additionalPosition}
      />
    </Fragment>
  );
};

export default DepartureCitySelect;
