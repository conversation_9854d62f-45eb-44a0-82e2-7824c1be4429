import { useState, useEffect } from "react";
import { isEmpty } from "lodash";
import { useSearchParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import Condition from "@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirSearch/Condition/index.jsx";
import SearchResults from "@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirSearch/SearchResults.jsx";
import { actionAirSearch } from "@/store/airSlice.js";
import { actionSetSearchBgInfo } from "@/store/loadingUserSlice";
import { actionGetBtmsSetting, actionGetMainAirTravelPlaces, actionGetUserAirport, selectUserAirport } from "@/store/userSlice.js";
import { convertFormatDate } from "@/utils/date";
import { getPatternYmd, getDayOfWeekName, getFilterFromSearchParams, normalizeFilter, getSlicedBySectionType } from "@/utils/common";
import { MAPPING_SECTION_TYPE_KR, MAPPING_SEAT_TYPE_CODE_KR } from "@/constants/common";
import { actionTravelRuleBase } from "@/store/airSlice";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { INITIAL_KAKAO_FILTER } from "@/constants/airSearch";
import { SECTION_TYPE } from "@/constants/app";

const AirSearch = () => {
  const dispatch = useDispatch();
  const [filter, setFilter] = useState(INITIAL_KAKAO_FILTER);
  const [searchParams] = useSearchParams();
  const userAirports = useSelector(selectUserAirport);
  const [selectedCompareFares, setSelectedCompareFares] = useState([]);

  function validateCondition(filter) {
    const { sectionType, departureAirportCodes, arrivalAirportCodes, departureDays } = filter;

    const isOneWay = sectionType === SECTION_TYPE.ONEWAY;
    const isMissingArrivalCode = arrivalAirportCodes.some((code) => code === "");
    const isMissingDepartureCode = departureAirportCodes.some((code) => code === "");
    const isMissingDepartureDate = departureDays.some((date) => date === "");

    if ((!isOneWay && isMissingArrivalCode) || (isOneWay && isEmpty(arrivalAirportCodes[0]))) {
      alert("도착지를 입력해주세요.");
      return false;
    }

    if ((!isOneWay && isMissingDepartureCode) || (isOneWay && isEmpty(departureAirportCodes[0]))) {
      alert("출발지를 입력해주세요.");
      return false;
    }

    if ((!isOneWay && isMissingDepartureDate) || (isOneWay && isEmpty(departureDays[0]))) {
      alert("날짜를 선택해주세요.");
      return false;
    }

    return true;
  }

  function airSearch(filter) {
    if (!isEmpty(filter.departureAirportCodes) && !isEmpty(filter.arrivalAirportCodes)) {
      const clonedFilter = normalizeFilter({
        ...filter,
        departureAirportCodes: getSlicedBySectionType(filter.departureAirportCodes, filter.sectionType),
        arrivalAirportCodes: getSlicedBySectionType(filter.arrivalAirportCodes, filter.sectionType),
        departureDays: getSlicedBySectionType(filter.departureDays, filter.sectionType),
      });
      const {
        sectionType,
        departureDays,
        departureAirportCodes,
        departureAirportNames,
        arrivalAirportCodes,
        arrivalAirportNames,
        adultCount,
        departSeatTypeCode,
      } = clonedFilter;

      const payload = {
        journeys: departureDays.map((day, index) => {
          return {
            deptAirport: departureAirportCodes[index],
            arrAirport: arrivalAirportCodes[index],
            departureDate: convertFormatDate(day),
          };
        }),
        adultCount: Number(adultCount),
        cabinClass: departSeatTypeCode,
      };
      const valid = validateCondition(clonedFilter);
      if (valid) {
        let toText = departureAirportNames[departureAirportNames.length - 1];
        let dayText =
          getPatternYmd(departureDays[0], ".") +
          " (" +
          getDayOfWeekName(departureDays[0]) +
          ") – " +
          getPatternYmd(departureDays[departureDays.length - 1], ".") +
          " (" +
          getDayOfWeekName(departureDays[departureDays.length - 1]) +
          ")";

        if (sectionType == "OneWay") {
          toText = arrivalAirportNames[0];
          dayText = getPatternYmd(departureDays[0], ".") + " (" + getDayOfWeekName(departureDays[0]) + ")";
        }
        dispatch(
          actionSetSearchBgInfo({
            desc:
              "<strong>" +
              departureAirportNames[0] +
              "</strong>에서 <strong>" +
              toText +
              "</strong>까지 <br>" +
              MAPPING_SECTION_TYPE_KR[sectionType] +
              " 항공권을 조회하고 있습니다.",
            dayText,
            passengerText: "성인" + adultCount + ", " + MAPPING_SEAT_TYPE_CODE_KR[departSeatTypeCode] || "",
          }),
        );
        const domesticCityList = userAirports.domestic.map((el) => el.code);
        const airlineCode = payload.journeys.map((journey) => {
          return [journey.departureAirportCodes, journey.arrivalAirportCodes];
        });
        const isFlightDomestic = airlineCode.flat().every((el) => {
          return domesticCityList.includes(el);
        });

        if (isFlightDomestic) {
          dispatch(actionIncreaseLoadingCount("loadingSearchBgCount"));
          setTimeout(() => {
            dispatch(actionDecreaseLoadingCount("loadingSearchBgCount"));
          }, [2000]);
        } else {
          dispatch(actionAirSearch(payload));
        }
        setSelectedCompareFares([]);
      }
    }
  }

  useEffect(() => {
    if (!isEmpty(userAirports)) {
      const newFilter = getFilterFromSearchParams(searchParams, INITIAL_KAKAO_FILTER);
      const isOneWay = newFilter.sectionType === SECTION_TYPE.ONEWAY;
      if (isOneWay) newFilter.departureDays[1] = "";
      setFilter(newFilter);
      airSearch(newFilter);
    }
  }, [searchParams, JSON.stringify(userAirports)]);

  useEffect(() => {
    dispatch(actionGetBtmsSetting());
    dispatch(actionTravelRuleBase());
    dispatch(actionGetMainAirTravelPlaces());
    dispatch(actionGetUserAirport());
  }, []);

  return (
    <div id="container" className="pg-search bg-c01">
      <Condition filter={filter} setFilter={setFilter} airSearch={airSearch} />
      <SearchResults filter={filter} selectedCompareFares={selectedCompareFares} setSelectedCompareFares={setSelectedCompareFares} />
    </div>
  );
};

export default AirSearch;
