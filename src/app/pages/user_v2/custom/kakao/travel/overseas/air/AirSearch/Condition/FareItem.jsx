import { useState, Fragment, useMemo } from "react";
import { isEmpty, uniq } from "lodash";
import { useSelector, useDispatch } from "react-redux";
import { selectUserInfo } from "@/store/userSlice";
import { comma, getKrTimeFromSegment, onErrorImgAirline } from "@/utils/common.js";
import FlightDetailTab from "@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirSearch/Condition/FlightDetailTab.jsx";
import FareRegulationsTab from "@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirSearch/Condition/FareRegulationsTab";
import { momentKR } from "@/utils/date";
import { getFareRule } from "@/service/air";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { actionSetAirOverseasSessionData } from "@/store/airSlice";
import useReservationNoticing from "@/app/hooks/useReservationNoticing";
import AIR_ICONS_PATH from "@/assets/airIcon";
import { nvl } from "@/common";

const TAB_NAVIGATION = [
  { id: 1, name: "travelInfomation", label: "여행정보" },
  { id: 2, name: "fareRegulations", label: "운임규정" },
];

const MAX_AIR_COMPARISONS = 3;

const FareItem = (props) => {
  const {
    id,
    filter,
    flights,
    journey,
    airFare,
    tax,
    ticketFare,
    includedCorporateFare,
    selectedFare,
    setSelectedFare,
    selectedCompareFares,
    setSelectedCompareFares,
    type = "DEFAULT",
  } = props;
  const [selectedTab, setSelectedTab] = useState("travelInfomation");
  const [fareRules, setFareRules] = useState([]);
  const { userInfo } = useSelector(selectUserInfo);

  const dispatch = useDispatch();
  const reservationNoticing = useReservationNoticing();
  const isSelectedFare = useMemo(() => selectedCompareFares.some((el) => el.id === id), [selectedCompareFares, id]);
  const indexSelectedFare = useMemo(() => selectedCompareFares.findIndex((el) => el.id === id), [selectedCompareFares, id]);
  const hasCompare = useMemo(() => userInfo?.workspace?.company?.btmsSetting?.isComparativePrice, [userInfo]);
  function selectFare() {
    if (hasCompare) {
      if (isSelectedFare) return;
      const foundAir = selectedCompareFares.find((item) => item.id === id);
      if (selectedCompareFares.length === MAX_AIR_COMPARISONS) {
        alert("선택 가능한 항공편 개수가 초과되었습니다.\n'비교함'에서 삭제 하신 후 추가 하실 수 있습니다.");
        return;
      }
      if (!isEmpty(foundAir)) {
        return;
      }
      setSelectedCompareFares((prev) => [...prev, journey]);
    } else {
      const payload = {
        data: journey,
      };
      dispatch(actionSetAirOverseasSessionData(payload));
      reservationNoticing.overseasShow();
    }
  }

  function openDetailFare(journey) {
    setSelectedTab("travelInfomation");
    setSelectedFare(journey);
  }

  function closeDetailFare() {
    setSelectedFare({});
  }

  async function changeTab(item) {
    const { name } = item;
    if (["fareRegulations"].includes(name)) {
      try {
        dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
        const payload = {
          journeys: journey.flights.map((flight) => {
            const { airline, deptAirport, arrAirport, departureDate, journeyKey, fares, pairKey } = flight;
            return {
              journeyKey,
              fareKey: fares.fareKey,
              pairKey,
              airline,
              deptAirport,
              arrAirport,
              departureDate,
              promotionId: "",
              fopPromotionId: "",
            };
          }),
        };
        const response = await getFareRule(payload);
        setFareRules(response.data?.data?.journeys);
        setSelectedTab(name);
      } catch (error) {
        console.log(error);
      } finally {
        dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
      }
    } else {
      setSelectedTab(name);
    }
  }

  return (
    <div className={`result-item ${selectedFare?.id === id ? "active" : ""}`} style={{ overflow: "unset" }}>
      <div className="clearfix info-default">
        <div className="col-left box-content border-custom">
          {flights.map((flight, index) => {
            const { departureDate, arrivalDate, deptAirport, arrAirport, fares, stops, segments } = flight;
            const departureTerminal = segments[0].legs[0].departureTerminal;
            const airline = segments[0].carrierCode;
            const airlineName = uniq(segments.map((el) => el.carrierCodeName));
            const arrivalTerminal = segments[segments.length - 1].legs[segments[segments.length - 1].legs.length - 1].arrivalTerminal;
            const flightNumbers = segments.map((segment) => `${segment.carrierCode}${segment.flightNumber}`).join(" | ");

            const isMultiAirline = airlineName.length > 1;
            const operatingCarrier = segments[0].legs[0].operatingCarrier;
            const operatingCarrierName = nvl(segments[0].legs[0].operatingCarrierName, "");
            const destinationThrough = segments
              .slice(0, segments.length - 1)
              .map((el) => el.arrAirport)
              .join(", ");
            const formattedDeptTime = momentKR(departureDate).format("YYYY-MM-DD");
            const formattedArrTime = momentKR(arrivalDate).format("YYYY-MM-DD");
            const dayThrough = momentKR(formattedArrTime).diff(momentKR(formattedDeptTime), "days");
            return (
              <div className="clearfix" key={index}>
                <p className="logo">
                  <div>
                    <img
                      src={isMultiAirline ? AIR_ICONS_PATH.TwoFlight : AIR_ICONS_PATH[airline] || ""}
                      onError={onErrorImgAirline}
                      className="w-[30px] h-[20px] object-cover inline-block"
                    />
                    <span className="inline-flex flex-col items-start">
                      <span dangerouslySetInnerHTML={{ __html: airlineName.join("<br/>") }} className="inline-block" />
                      <span className="text-sm text-[#9da9be]">{flightNumbers}</span>
                    </span>
                  </div>
                  {!isMultiAirline && (
                    <span className="relative text-[12px] text-[#4b75bf]">
                      {operatingCarrier !== airline && (
                        <>
                          [실제운항] {operatingCarrierName}
                          {!!operatingCarrier && `(${operatingCarrier})`}
                        </>
                      )}
                    </span>
                  )}
                </p>
                <div className="date">{momentKR(departureDate).format("MM/DD")}</div>
                <div className="info">
                  <p className="time">{momentKR(departureDate).format("HH:mm")}</p>
                  <p className="airport">
                    {deptAirport} {departureTerminal ? `T${departureTerminal}` : ""}
                  </p>
                </div>
                <div className="overstop">
                  <p>&nbsp;{stops ? stops + "회 경유" : ""}</p> <p>{stops ? destinationThrough : ""}</p>
                </div>
                <div className="info">
                  <p className="time">{momentKR(arrivalDate).format("HH:mm")}</p>
                  <p className="airport">
                    {arrAirport} {arrivalTerminal ? `T${arrivalTerminal}` : ""}
                  </p>
                  <div className="over-time">{dayThrough > 0 ? `+${dayThrough}` : ""}</div>
                </div>
                <div className="etc-info">
                  <p className="time">{getKrTimeFromSegment(segments)}</p>
                  <p className="seat">
                    확정 / <strong>{fares?.availableCount}</strong>석 남음
                  </p>
                </div>
              </div>
            );
          })}
        </div>
        <div className="col-right box-content !border-l-0 border-custom">
          {type !== "VIEW" && (
            <div className="btn-add-compare">
              <button
                type="button"
                className={` ${isSelectedFare ? "btn-add-compare" : "btn-default btn-add"}`}
                style={{ marginBottom: 10 }}
                onClick={selectFare}
              >
                {isSelectedFare ? (
                  <Fragment>
                    <span className="label-set">
                      비교함
                      <i className="box-content">{indexSelectedFare + 1}</i>
                    </span>
                  </Fragment>
                ) : hasCompare ? (
                  "비교함 추가"
                ) : (
                  "예약하기"
                )}
              </button>
            </div>
          )}
          <div className="price" style={{ overflow: "unset" }}>
            <div className="fare-detail !pl-[90px]">
              <dd className="mb-0">
                <span className="tit">항공료</span> <span className="sum"> {comma(airFare)}원</span>
              </dd>
              <dd className="mb-0">
                <span className="tit">TAX</span> <span className="sum"> {comma(tax)}원</span>
              </dd>
              <dd className="mb-0">
                <span className="tit">발권수수료</span> <span className="sum">{comma(ticketFare)}원</span>
              </dd>
            </div>
            <p className="type">
              {!!includedCorporateFare && (
                <>
                  기업 운임
                  <div className="tooltip-custom">
                    <span className="tooltip-custom-icon !w-[13px] !h-[13px] !top-[4px]" />
                    <span className="tooltip-custom-text !w-[340px]">
                      기업 고객을 대상으로 항공사가 제공하는 할인된 운임으로 변경 <br /> 및 취소가 유연하며 수수료 감면 또는 면제 될 수 있는
                      요금입니다.
                    </span>
                  </div>
                </>
              )}
            </p>{" "}
            <p className="val"> {comma(airFare + tax + ticketFare)}원</p>
          </div>
          {type !== "VIEW" && (
            <div className="btn-more">
              <button type="button" className="btn-default">
                {selectedFare?.id === id ? (
                  <span className="close text-[13px] !text-[#3f4e73] font-normal !opacity-100" onClick={closeDetailFare}>
                    접기
                  </span>
                ) : (
                  <span className="view" onClick={() => openDetailFare(journey)}>
                    상세 보기
                  </span>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
      {/* 세부사항 */}
      {selectedFare?.id === id && (
        <div className="info-booking">
          <ul className="btn-tabs">
            {TAB_NAVIGATION.map((item) => (
              <li key={item.id} className={`${selectedTab === item.name ? "active" : ""}`}>
                <a className="btn-default" onClick={() => changeTab(item)}>
                  {item.label}
                </a>
              </li>
            ))}
          </ul>
          <div className="fare plan active">
            {selectedTab === TAB_NAVIGATION[0].name && <FlightDetailTab flights={journey.flights} filter={filter} />}
            {selectedTab === TAB_NAVIGATION[1].name && <FareRegulationsTab fareRules={fareRules} />}
          </div>
        </div>
      )}
      {/* // 세부사항 */}
    </div>
  );
};

export default FareItem;
