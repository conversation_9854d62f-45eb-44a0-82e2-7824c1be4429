import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { selectUserInfo } from "@/store/userSlice";

export default function LayerCompareBox(props) {
  const { selectedCompareFares, setOpenAirCompareSchedule } = props;
  const ref = useRef(null);

  const [rectTop, setRectTop] = useState(0);
  const [isFixed, setIsFixed] = useState(false);
  const { userInfo } = useSelector(selectUserInfo);

  useEffect(() => {
    let befScroll = true;
    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      if (scrollTop === 0) {
        befScroll = true;
      } else if (scrollTop !== 0 && befScroll) {
        setRectTop(ref?.current?.getBoundingClientRect?.()?.top || 0 + scrollTop);
        befScroll = false;
      }

      if (scrollTop > rectTop) {
        if (!isFixed) {
          setIsFixed(true);
        }
      } else {
        if (isFixed) {
          setIsFixed(false);
        }
      }
    };
    if (ref.current) {
      document.addEventListener("scroll", handleScroll);
    }
    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, [ref, rectTop, isFixed]);

  return (
    userInfo?.workspace?.company?.btmsSetting?.isComparativePrice && (
      <div className={`layer-compare-box !top-[100px] ${isFixed ? "fixed" : ""}`} ref={ref}>
        <div className="inner">
          <p className="tit">비교함</p>
          <ol>
            <li className={`${selectedCompareFares.length > 0 ? "active" : ""}`}>1</li>
            <li className={`${selectedCompareFares.length > 1 ? "active" : ""}`}>2</li>
            <li className={`${selectedCompareFares.length > 2 ? "active" : ""}`}>3</li>
          </ol>
        </div>
        <div className="btn-compare">
          <a className="btn-default" onClick={() => setOpenAirCompareSchedule(true)}>
            비교하기
          </a>
        </div>
      </div>
    )
  );
}
