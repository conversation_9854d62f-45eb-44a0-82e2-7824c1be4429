import { useState, useMemo } from "react";
import { useSelector } from "react-redux";
import IOSSlider from "@/app/components/user_v2/common/IOSSlider.jsx";
import { selectAirSearch } from "@/store/airSlice.js";
import { momentKR } from "@/utils/date";
import { ALLIANCE_KR } from "@/constants/app";

const VIA = [
  { id: 1, value: "0", checked: true, label: "직항" },
  { id: 2, value: "1", checked: true, label: "1회" },
  { id: 3, value: "2", checked: true, label: "2회 이상" },
];

const FARE_TYPES = ["기업운임", "일반운임"];

const LeftSide = (props) => {
  const { filter, condition, conditionTime, setCondition, setConditionTime, uniqueAirlines, uniqueAlliances, resetFilter } = props;
  const airSearch = useSelector(selectAirSearch);

  const [sliceAirlineMenu, setSliceAirlineMenu] = useState(3);
  const [rangeTime, setRangeTime] = useState("startTime");

  const keyRangeTime = useMemo(() => (rangeTime === "startTime" ? "departureTime" : "arrivalTime"), [rangeTime]);

  function handleChangeCondition(e) {
    const { name, value, checked } = e.target;
    setCondition((prev) => ({
      ...prev,
      [name]: checked ? [...prev[name], value] : prev[name].filter((el) => el !== value),
    }));
  }

  function handleChangeSlider(item, value, type) {
    const newTime = conditionTime[keyRangeTime].map((el) => {
      if (el.name === item.id) {
        return { ...el, value };
      }
      return el;
    });
    if (type === "CHANGE") {
      setConditionTime((prev) => ({ ...prev, [keyRangeTime]: newTime }));
    } else {
      setCondition((prev) => ({ ...prev, [keyRangeTime]: newTime }));
    }
  }

  function handleChangeSliderDuration(name, value, type) {
    const newTime = conditionTime[name];
    newTime.value = value;
    if (type === "CHANGE") {
      setConditionTime((prev) => ({ ...prev, [name]: newTime }));
    } else {
      setCondition((prev) => ({ ...prev, [name]: newTime }));
    }
  }

  function formatTime(decimal) {
    const hours = Math.floor(decimal);
    const minutes = Math.round((decimal - hours) * 60);
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    return `${formattedHours}:${formattedMinutes}`;
  }

  function convertDecimalHoursToKoreanTime(decimalHours) {
    const hours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - hours) * 60);
    return `${hours}시간 ${minutes}분`;
  }

  function deselectAllAirline() {
    setCondition((prev) => ({ ...prev, airlines: [] }));
  }

  function selectAllAirline() {
    const flattened = airSearch.data.journeyInfos.flatMap((obj) => Object.values(obj).flat());
    const airlines = flattened.map((obj) => ({ airline: obj.airline, name: obj.airlineName }));
    const uniqueAirlines = [...new Map(airlines.map((item) => [item["airline"], item])).values()];
    setCondition((prev) => ({ ...prev, airlines: uniqueAirlines.map((el) => el.airline) }));
  }

  return (
    <div className="left-side">
      <div className="sec-regulation">
        <h2>나의 출장 규정</h2>
        <dl className="airport">
          <dt>항공권</dt>
          <dd>출장 일정에 맞는 항공권 중, Low fare 이용 원칙</dd>
        </dl>
        <dl className="seat-df">
          <dt className="mb-4">
            Business Class upgrade 기준
            <span className="tooltip-custom">
              <span className="tooltip-custom-icon" />
              <span className="tooltip-custom-text">
                <ul className="list-disc pl-6">
                  <li>순수 업무수행 목적으로 비행시간 8시간 이상</li>
                  <li>야간비행(22시~06시)후 바로 업무복귀 등 기타 업무 일정 있는 경우 비행시간 5시간 이상</li>
                  <li>교육 및 컨퍼런스, 업체 행사(접대성)의 경우 Economy Class 이용</li>
                  <li>출장자가 임신부인 경우</li>
                  <li>주요 단체 및 업체의 동행, 수행이 필요한 경우</li>
                  <li>좌석 매진 및 기타 특별 사정으로 해당 좌석 등급 이용 불가한 경우</li>
                </ul>
              </span>
            </span>
          </dt>
        </dl>
      </div>
      {/* // 나의 출장 규정 */}
      {/* 검색결과 내 검색 */}
      <div className="sec-in-search">
        <h2>검색결과 내 검색</h2>
        <form>
          <dl>
            <dt>운임 조건</dt>
            {FARE_TYPES.map((item, index) => (
              <dd className="mb-0" key={item + index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="arrayCorporateFare"
                    defaultValue={item}
                    checked={condition.arrayCorporateFare.includes(item)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">
                    {item}
                    {index === 0 && (
                      <div className="tooltip-custom">
                        <div className="tooltip-custom-icon" />
                        <div className="tooltip-custom-text !w-[390px]">
                          기업 고객을 대상으로 항공사가 제공하는 할인된 운임으로 변경 <br /> 및 취소가 유연하며 수수료 감면 또는 면제 될 수 있는
                          요금입니다.
                        </div>
                      </div>
                    )}
                  </span>
                </label>
              </dd>
            ))}
          </dl>
          <dl>
            <dt>경유</dt>
            {VIA.map((item, index) => (
              <dd className="mb-0" key={item.id + index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="arrayStopOverCount"
                    defaultValue={item.value}
                    className="item"
                    checked={condition.arrayStopOverCount.includes(item.value)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">{item.label}</span>
                </label>
              </dd>
            ))}
          </dl>
          <dl>
            <dt>얼라이언스</dt>
            {uniqueAlliances.map((item, index) => (
              <dd className="mb-0" key={index}>
                <label className="form-chkbox">
                  <input
                    type="checkbox"
                    name="alliances"
                    defaultValue={item}
                    checked={condition.alliances.includes(item)}
                    onChange={handleChangeCondition}
                  />
                  <span className="after:box-content">{ALLIANCE_KR[item] || ""}</span>
                </label>
              </dd>
            ))}
          </dl>
          <div className="airline">
            <dl id="airlineSearchFilter">
              <dt>항공사</dt>
              {uniqueAirlines.slice(0, sliceAirlineMenu).map((item, index) => (
                <dd key={item.airline + index}>
                  <label className="form-chkbox">
                    <input
                      type="checkbox"
                      name="airlines"
                      defaultValue={item.airline}
                      checked={condition.airlines.includes(item.airline)}
                      onChange={handleChangeCondition}
                    />
                    <span className="after:box-content">{item.name}</span>
                  </label>
                </dd>
              ))}
            </dl>
            <div className="btn-regi-all flex gap-1">
              <button type="button" className="btn-default sel" onClick={() => selectAllAirline()}>
                모두 선택
              </button>
              <span className="after:box-content">|</span>
              <button type="button" className="btn-default clear active" onClick={() => deselectAllAirline()}>
                모두 해제
              </button>
            </div>
            <div id="airlineButton" className="btn-view-all" style={{ display: "block" }}>
              <button type="button" className="btn-default" onClick={() => setSliceAirlineMenu(uniqueAirlines.length)}>
                모든 항공사 보기
              </button>
            </div>
          </div>
          <dl className="range-time" id="departReturnTimeArea">
            <dt>시간대</dt>
            <dd className="btn-time flex gap-2">
              <label>
                <input
                  type="radio"
                  name="timeOption"
                  defaultValue="startTime"
                  checked={rangeTime === "startTime"}
                  onChange={() => setRangeTime("startTime")}
                />
                <span className="after:box-content">출발시간</span>
              </label>
              <label>
                <input
                  type="radio"
                  name="timeOption"
                  defaultValue="arrivalTime"
                  checked={rangeTime === "arrivalTime"}
                  onChange={() => setRangeTime("arrivalTime")}
                />
                <span className="after:box-content">도착시간</span>
              </label>
            </dd>
            {conditionTime[keyRangeTime]?.map((item, index) => (
              <dd key={item?.name + index} className="in-slide" id="departReturnTime_0">
                <p className="tit">
                  <strong>
                    {item?.name} {keyRangeTime === "departureTime" ? "출발" : "도착"}
                  </strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {momentKR(filter.departureDays[0], "YYYYMMDD").format("ddd")} {formatTime(item?.value[0])} - {formatTime(item?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  value={item?.value}
                  max={24}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSlider(item, value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSlider(item, value, "COMMIT");
                  }}
                />
                <input type="hidden" defaultValue="0000" /> <input type="hidden" defaultValue={2400} />
              </dd>
            ))}
          </dl>
          <dl className="range-time">
            <dt>소요시간</dt>
            {conditionTime.leadTime?.value && (
              <dd className="in-slide" id="leadTime">
                <p className="tit">
                  <strong>
                    {filter.departureAirportNames[0]}({filter.departureAirportCodes[0]}) 출발
                  </strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {convertDecimalHoursToKoreanTime(conditionTime.leadTime?.value[0])} -{" "}
                    {convertDecimalHoursToKoreanTime(conditionTime.leadTime?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  min={0.5}
                  value={conditionTime.leadTime?.value}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSliderDuration("leadTime", value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSliderDuration("leadTime", value, "COMMIT");
                  }}
                />
                <input type="hidden" id="leadStartTime" />
                <input type="hidden" id="leadEndTime" />
              </dd>
            )}
            {conditionTime.stopOverTime?.value && (
              <dd className="in-slide" id="stopWaitTime">
                <p className="tit">
                  <strong>경유지 대기시간</strong>
                </p>
                <p className="time">
                  <span className="after:box-content">
                    {convertDecimalHoursToKoreanTime(conditionTime.stopOverTime?.value[0])} -{" "}
                    {convertDecimalHoursToKoreanTime(conditionTime.stopOverTime?.value[1])}
                  </span>
                </p>
                <IOSSlider
                  valueLabelDisplay="off"
                  disableSwap
                  value={conditionTime.stopOverTime?.value}
                  max={56}
                  step={0.5}
                  onChange={(e, value) => {
                    handleChangeSliderDuration("stopOverTime", value, "CHANGE");
                  }}
                  onChangeCommitted={(e, value) => {
                    handleChangeSliderDuration("stopOverTime", value, "COMMIT");
                  }}
                />
                <input type="hidden" id="stopWaitStartTime" />
                <input type="hidden" id="stopWaitEndTime" />
              </dd>
            )}
          </dl>
          <button type="reset" className="btn-default btn-init" onClick={resetFilter}>
            초기화
          </button>
        </form>
      </div>
      {/* //검색결과 내 검색 */}
    </div>
  );
};

export default LeftSide;
