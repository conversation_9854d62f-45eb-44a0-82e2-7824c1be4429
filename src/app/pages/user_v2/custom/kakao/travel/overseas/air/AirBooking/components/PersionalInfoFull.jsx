import { useState, useEffect, Fragment } from "react";
import { isEmpty, set, get, cloneDeep } from "lodash";
import PassengerSearch from "@/app/components/user_v2/modal/PassengerSearch.modal";
import { useAppSelector } from "@/store";
import { selectUserInfo, selectCountryList } from "@/store/userSlice";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { nvl } from "@/utils/common";
import { momentKR } from "@/utils/date";
import "@/styles/user_v2/persionalInfo.css";
import { Autocomplete, TextField } from "@mui/material";
import { DEFAULT_COUNTRY } from "@/constants/common";
import { DEFAULT_DATE } from "@/constants/app";

export default function PersionalInfoFull(props) {
  const { formState, setFormState } = props;

  const { userInfo } = useAppSelector(selectUserInfo);
  const { filter } = useAppSelector(selectAirOverseasSessionData);
  const countryList = useAppSelector(selectCountryList);
  const [passportList, setPassportList] = useState([]);

  const [active, setActive] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [position, setPosition] = useState("0");

  const handleOpenModal = (index) => {
    setPosition(index);
    setOpenModal(true);
  };

  function applyUserInfo(userInfo, defaultVal = {}) {
    return {
      id: nvl(userInfo?.id, 0),
      name: nvl(userInfo?.name, ""),
      firstName: nvl(userInfo?.customerPassport?.firstName, ""),
      lastName: nvl(userInfo?.customerPassport?.lastName, ""),
      gender: nvl(userInfo?.gender, "Male"),
      birthday: nvl(userInfo?.birthday, DEFAULT_DATE),
      nationalityCode: nvl(userInfo?.country?.code2, ""),
      cellPhoneNumber: nvl(userInfo?.cellPhoneNumber, ""),
      email: nvl(userInfo?.email, ""),
      customerPassport: {
        passportNumber: nvl(userInfo?.customerPassport?.passportNumber, ""),
        expireYmd: userInfo?.customerPassport?.expireYmd || DEFAULT_DATE,
        country: {
          id: nvl(userInfo?.customerPassport?.country?.code2, ""),
        },
      },
      travelerMileageInfoDTOs: [
        { airline: nvl(userInfo?.mileageInfos?.[0]?.airline, ""), mileageMemberNo: nvl(userInfo?.mileageInfos?.[0]?.mileageMemberNo, "") },
        { airline: nvl(userInfo?.mileageInfos?.[1]?.airline, ""), mileageMemberNo: nvl(userInfo?.mileageInfos?.[0]?.mileageMemberNo, "") },
      ],
    };
  }

  async function reserverCheck(e) {
    const { checked } = e.target;
    const clonedFormState = cloneDeep(formState);
    const { bookingUserDTOs } = clonedFormState;

    const newFormState = {
      ...clonedFormState,
      reserverCheck: !checked,
      bookingUserDTOs: bookingUserDTOs.map((user, index) => {
        if (index !== 0) return user;
        else if (checked) return getInititalUser(userInfo); // RESET DEAFAULT
        return applyUserInfo(userInfo, user);
      }),
    };
    setFormState(newFormState);
  }

  function toggleCheckbox(e) {
    const { name } = e.target;
    const valBool = get(formState, name);
    setFormState((prev) => {
      return set(cloneDeep(prev), name, !valBool);
    });
  }

  function handleChangeDate(e, position, key) {
    const { name, value } = e.target;
    const mappedKey = `bookingUserDTOs[${position}].${key}`;
    const date = momentKR(get(formState, mappedKey), "YYYYMMDD");
    switch (name) {
      case "year":
        date.year(Number(value));
        break;
      case "month":
        date.month(Number(value) - 1);
        break;
      case "day":
        date.date(Number(value));
        break;
      default:
        break;
    }

    setFormState((prev) => {
      return set(cloneDeep(prev), mappedKey, date.format("YYYYMMDD"));
    });
  }

  const handleChangeAutocomplete = (name, value, dependName) => {
    if (!value) return;
    setFormState((prev) => {
      const nextState = cloneDeep(prev);
      set(nextState, name, value.code2);
      if (dependName) {
        set(nextState, dependName, value.code2);
      }
      return nextState;
    });
    if (dependName) {
      const selectedItem = countryList.find((item) => item.code2 === value.code2);
      const nextPassportList = [DEFAULT_COUNTRY, selectedItem, ...countryList.filter((item) => item.code2 !== value.code2)];
      setPassportList(nextPassportList);
    }
  };

  function getInititalUser(userInfo) {
    return {
      id: 0,
      name: "",
      firstName: "",
      lastName: "",
      gender: "Male",
      birthday: DEFAULT_DATE,
      nationalityCode: "KR",
      cellPhoneNumber: "",
      email: "",
      settlementManagerEmail: "",
      customerPassport: {
        passportNumber: "",
        expireYmd: userInfo?.customerPassport?.expireYmd || DEFAULT_DATE,
        country: {
          id: "KR",
        },
      },
      travelerMileageInfoDTOs: [
        { airline: "", mileageMemberNo: "" },
        { airline: "", mileageMemberNo: "" },
      ],
    };
  }

  function selectSearchCustomer(customer) {
    setFormState((prev) => {
      const clonedPrev = cloneDeep(prev);
      if (position === 0) {
        clonedPrev.reserverCheck = false;
      }
      return set(clonedPrev, `bookingUserDTOs[${position}]`, applyUserInfo(customer));
    });
    setOpenModal(false);
  }

  useEffect(() => {
    if (!isEmpty(filter) && !isEmpty(userInfo)) {
      const adultCount = Number(filter.adultCount);
      const bookingUserDTOs = Array.from({ length: adultCount }).map((_) => getInititalUser(userInfo));
      setFormState({ ...formState, bookingUserDTOs });
    }
  }, [filter, userInfo]);

  useEffect(() => {
    setPassportList([DEFAULT_COUNTRY, ...countryList]);
  }, [countryList]);

  return (
    <Fragment>
      <div className={`box-item personal-info ${active ? "active" : ""}`}>
        <h3 className="tit">탑승객 정보</h3>
        <div className="box-cotn passgner">
          <p className="agree">
            <label className="form-chkbox">
              <input
                type="checkbox"
                id="isNameSamePassportRule"
                name="reservationRuleAgreementDTO.isNameSamePassportRule"
                checked={formState.reservationRuleAgreementDTO.isNameSamePassportRule}
                onChange={toggleCheckbox}
              />
              <span>탑승객의 영문성명은 여권과 정확히 일치함을 확인하고 동의합니다.</span>
            </label>
          </p>
          <p className="caution-america">
            미주 노선은 여권정보가 반드시 필요합니다. 정확한 여권정보를 입력하세요. <br />※ 여권 발급 예정자이거나 확인이 어려운 경우 임의의 값을
            입력한 후, 반드시 출발 72시간 전까지 유효한 여권정보를 입력하세요.
            <br /> ※ 여권 유효기간은 출국일 기준 6개월 이상 남아 있어야 출국이 가능합니다.
          </p>
          {formState.bookingUserDTOs.map((user, index) => (
            <Fragment key={index}>
              <div className="search">
                <p>성인{index + 1}</p>
                {index === 0 && (
                  <p className="ml-[10px]">
                    <label className="form-chkbox special">
                      <input
                        type="checkbox"
                        id="reserverCheck"
                        name="reserverCheck"
                        checked={formState.reserverCheck}
                        onChange={(e) => {
                          reserverCheck(e);
                        }}
                      />
                      <span
                        style={{ fontSize: 15 }}
                        onClick={() =>
                          setFormState((prev) => {
                            return { ...prev, reserverCheck: !prev.reserverCheck };
                          })
                        }
                      >
                        예약자 정보 동일
                      </span>
                    </label>
                  </p>
                )}
                {userInfo?.searchAuth !== "AUTHA" && (
                  <a className="btn-default" onClick={() => handleOpenModal(index)}>
                    탑승객 검색
                  </a>
                )}
              </div>
              {userInfo?.searchAuth !== "AUTHA" && (
                <i className="qustion" style={{ float: "right" }}>
                  <span className="cn">
                    ■ 직원별 권한 종류에 따른 [검색+매칭] 기능 제한
                    <br /> • 일반A : 항공/호텔 예약시 직원 [검색+매칭] 사용 불가, 정보 수기 입력
                    <br /> • 일반B : 항공/호텔 예약시 같은 사업장의 부서 직원만 [검색+매칭] 사용 가능
                    <br /> • 일반C : 항공/호텔 예약시 같은 사업장의 직원만 [검색+매칭] 사용 가능
                    <br /> • 관리자 : 항공/호텔 예약시 전체 사업장의 직원 [검색+매칭] 사용 가능
                  </span>
                </i>
              )}
              <div className="clearfix names">
                <dl className="box-col ">
                  <dt>
                    한글이름 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <input
                      type="text"
                      name={`bookingUserDTOs[${index}].name`}
                      className="upper"
                      placeholder="예) 홍길동"
                      value={user.name}
                      maxLength={20}
                      data-validateinput="koreanOnly"
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    한국 이름이 없는 경우 영문으로 입력하거나 영문 발음대로 한글로 기입해주시길 바랍니다.
                  </dd>
                </dl>
                <dl className="box-col ">
                  <dt>
                    영문 성 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <input
                      type="text"
                      name={`bookingUserDTOs[${index}].lastName`}
                      placeholder="성(HONG)"
                      className="upper"
                      data-validateinput="koreanNenglish"
                      value={user.lastName}
                      maxLength={20}
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    여권의 영문성을 정확하게 입력하세요. <br />
                    한글, 숫자, 특수문자, 띄어쓰기 입력이 불가합니다.
                  </dd>
                </dl>
                <dl className="box-col ">
                  <dt>
                    영문 이름 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <input
                      type="text"
                      name={`bookingUserDTOs[${index}].firstName`}
                      placeholder="이름(GILDONG)"
                      className="upper"
                      data-validateinput="english"
                      value={user.firstName}
                      maxLength={30}
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    여권의 영문이름을 정확하게 입력하세요. <br />
                    한글, 숫자, 특수문자, 띄어쓰기 입력이 불가합니다.
                  </dd>
                </dl>
              </div>
              <div className="clearfix privacy">
                <dl className="box-col">
                  <dt>
                    성별 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <label className="form-radio">
                      <input
                        type="radio"
                        name={`bookingUserDTOs[${index}].gender`}
                        defaultValue="Male"
                        checked={user.gender === "Male"}
                        onChange={() => {}}
                      />
                      <span>남성</span>
                    </label>
                    <label className="form-radio">
                      <input
                        type="radio"
                        name={`bookingUserDTOs[${index}].gender`}
                        defaultValue="Female"
                        checked={user.gender === "Female"}
                        onChange={() => {}}
                      />
                      <span>여성</span>
                    </label>
                  </dd>
                </dl>
                <dl className="box-col">
                  <dt>
                    생년월일 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc box-ymd">
                    <span className="form-select yy">
                      <select
                        className="!box-content"
                        name="year"
                        value={momentKR(user.birthday, "YYYYMMDD").format("YYYY")}
                        onChange={(e) => handleChangeDate(e, index, "birthday")}
                      >
                        {Array.from({ length: 100 }, (_, i) => ({
                          value: `${momentKR().year() - i}`,
                          label: `${momentKR().year() - i}년`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                    <span className="form-select mm">
                      <select
                        className="!box-content"
                        name="month"
                        value={momentKR(user.birthday, "YYYYMMDD").format("MM")}
                        onChange={(e) => handleChangeDate(e, index, "birthday")}
                      >
                        {Array.from({ length: 12 }, (_, i) => ({
                          value: String(i + 1).padStart(2, "0"),
                          label: `${i + 1}월`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                    <span className="form-select dd">
                      <select
                        className="!box-content"
                        name="day"
                        value={momentKR(user.birthday, "YYYYMMDD").format("DD")}
                        onChange={(e) => handleChangeDate(e, index, "birthday")}
                      >
                        {Array.from({ length: 31 }, (_, i) => ({
                          value: String(i + 1).padStart(2, "0"),
                          label: `${i + 1}일`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                  </dd>
                </dl>
                <dl className="box-col ">
                  <dt>
                    국적 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd>
                    <Autocomplete
                      className="autocomplete-custom"
                      name={`bookingUserDTOs[${index}].nationalityCode`}
                      options={countryList}
                      getOptionLabel={(option) => option.name}
                      renderInput={(params) => <TextField {...params} label="" variant="standard" />}
                      value={countryList.find((item) => item.code2 === user.nationalityCode)}
                      onChange={(e, next) =>
                        handleChangeAutocomplete(
                          `bookingUserDTOs[${index}].nationalityCode`,
                          next,
                          `bookingUserDTOs[${index}].customerPassport.country.id`,
                        )
                      }
                    />
                  </dd>
                </dl>
              </div>
              <div className="clearfix contact">
                <dl className="box-col ">
                  <dt>
                    휴대전화 번호 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <input
                      type="text"
                      name={`bookingUserDTOs[${index}].cellPhoneNumber`}
                      maxLength={11}
                      placeholder="- 없이 입력 해 주세요"
                      value={user.cellPhoneNumber}
                      data-validateinput="numberOnly"
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    휴대전화 번호는 숫자만 입력 가능합니다.
                  </dd>
                </dl>
                <dl className="box-col">
                  <dt>
                    이메일 주소 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc">
                    <input
                      type="text"
                      name={`bookingUserDTOs[${index}].email`}
                      placeholder="<EMAIL>"
                      value={user.email}
                      maxLength={100}
                      data-validateinput="email"
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    이메일 주소 형식을 확인하세요(a@b.c)
                  </dd>
                </dl>
                <dl className="box-col">
                  {/* <dt>
                    전표담당자 이메일 주소 <i style={{ color: "#f4516c" }}>*</i>
                  </dt>
                  <dd className="desc" style={{ display: "flex" }}>
                    <input
                      type="text"
                      id="settlementManagerEmail_0"
                      name={`bookingUserDTOs[${index}].settlementManagerEmail`}
                      value={user.settlementManagerEmail}
                      maxLength={100}
                      style={{ width: "61%" }}
                      onChange={() => {}}
                    />
                    <input type="text" placeholder="@kortek.co.kr" style={{ width: "39%" }} readOnly />
                  </dd> */}
                </dl>
              </div>
              <div className="clearfix passport">
                <dl className="box-col ">
                  <dt>여권 번호</dt>
                  <dd className="desc">
                    <input
                      type="text"
                      id="passportNumber_0"
                      name={`bookingUserDTOs[${index}].customerPassport.passportNumber`}
                      value={user.customerPassport.passportNumber}
                      placeholder="예) *********"
                      maxLength={30}
                      data-validateinput="upperCaseEnglishAndNumber"
                      onChange={() => {}}
                    />
                  </dd>
                  <dd className="validate" style={{ display: "none" }}>
                    번호는 영문,숫자만 입력 가능합니다
                  </dd>
                </dl>
                <dl className="box-col dates">
                  <dt>여권 만료 기간</dt>
                  <dd className="desc box-ymd">
                    <span className="form-select yy">
                      <select
                        className="!box-content"
                        name="year"
                        value={momentKR(user.customerPassport.expireYmd, "YYYYMMDD").format("YYYY")}
                        onChange={(e) => handleChangeDate(e, index, "customerPassport.expireYmd")}
                      >
                        {Array.from({ length: 20 }, (_, i) => ({
                          value: `${momentKR().year() + i}`,
                          label: `${momentKR().year() + i}년`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                    <span className="form-select mm">
                      <select
                        className="!box-content"
                        name="month"
                        value={momentKR(user.customerPassport.expireYmd, "YYYYMMDD").format("MM")}
                        onChange={(e) => handleChangeDate(e, index, "customerPassport.expireYmd")}
                      >
                        {Array.from({ length: 12 }, (_, i) => ({
                          value: String(i + 1).padStart(2, "0"),
                          label: `${i + 1}월`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                    <span className="form-select dd">
                      <select
                        className="!box-content"
                        name="day"
                        value={momentKR(user.customerPassport.expireYmd, "YYYYMMDD").format("DD")}
                        onChange={(e) => handleChangeDate(e, index, "customerPassport.expireYmd")}
                      >
                        {Array.from({ length: 31 }, (_, i) => ({
                          value: String(i + 1).padStart(2, "0"),
                          label: `${i + 1}일`,
                        })).map(({ value, label }) => (
                          <option key={value} value={value}>
                            {label}
                          </option>
                        ))}
                      </select>
                    </span>
                  </dd>
                </dl>
                <dl className="box-col ">
                  <dt>여권 발행국</dt>
                  <dd>
                    <Autocomplete
                      id="passportCountry_0"
                      className="autocomplete-custom"
                      name={`bookingUserDTOs[${index}].customerPassport.country.id`}
                      options={passportList}
                      getOptionLabel={(option) => option.name}
                      renderInput={(params) => <TextField {...params} label="" variant="standard" />}
                      value={passportList.find((item) => item.code2 === user.customerPassport?.country?.id)}
                      onChange={(e, next) => handleChangeAutocomplete(`bookingUserDTOs[${index}].customerPassport.country.id`, next)}
                    />
                  </dd>
                </dl>
              </div>
              <div className="clearfix mileage">
                <div className="title">마일리지 회원번호</div>
                <div className="content">
                  <div>
                    <span>항공사1</span>
                    <input
                      type="text"
                      id="airline_0_0"
                      name={`bookingUserDTOs[${index}].travelerMileageInfoDTOs[0].airline`}
                      value={user.travelerMileageInfoDTOs[0].airline}
                      placeholder="한글/영문 입력"
                      onChange={() => {}}
                    />
                    <span>회원번호1</span>
                    <input
                      type="text"
                      id="mileageMemberNo_0_0"
                      name={`bookingUserDTOs[${index}].travelerMileageInfoDTOs[0].mileageMemberNo`}
                      value={user.travelerMileageInfoDTOs[0].mileageMemberNo}
                      placeholder="숫자/알파벳 입력"
                      onChange={() => {}}
                    />
                  </div>
                  <div>
                    <span>항공사2</span>
                    <input
                      type="text"
                      id="airline_1_0"
                      name={`bookingUserDTOs[${index}].travelerMileageInfoDTOs[1].airline`}
                      value={user.travelerMileageInfoDTOs[1].airline}
                      placeholder="한글/영문 입력"
                      onChange={() => {}}
                    />
                    <span>회원번호2</span>
                    <input
                      type="text"
                      id="mileageMemberNo_1_0"
                      name={`bookingUserDTOs[${index}].travelerMileageInfoDTOs[1].mileageMemberNo`}
                      value={user.travelerMileageInfoDTOs[1].mileageMemberNo}
                      placeholder="숫자/알파벳 입력"
                      onChange={() => {}}
                    />
                  </div>
                </div>
              </div>
            </Fragment>
          ))}
          {/* //탑승객 정보 */}
        </div>
        <div className="btn-arrow" onClick={() => setActive(!active)}>
          <button type="button" className="btn-default" />
        </div>
      </div>
      <PassengerSearch
        position={position}
        open={openModal}
        setOpenModal={setOpenModal}
        companyId={""}
        workspaceId={userInfo?.workspace?.id}
        selectSearchCustomer={selectSearchCustomer}
      />
    </Fragment>
  );
}
