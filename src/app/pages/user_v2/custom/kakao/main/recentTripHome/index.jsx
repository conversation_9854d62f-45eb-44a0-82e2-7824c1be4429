import { nvl } from "@/common";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";
import AirSessionUtil from "@/utils/airSessionUtil.js";
import AirUtil from "@/utils/airUtil.js";
import DateUtil from "@/utils/dateUtil";
import QueryString from "qs";
import { Link, useNavigate } from "react-router-dom";

export default function RecentTripHome() {
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);

  let data;
  if (airLocalStorageUtil.get(userInfo?.id) != null) data = airLocalStorageUtil.get(userInfo?.id).reverse();
  return (
    <div className="default-wd recent-trip-home">
      <h2 className="tit">최근 검색한 스케줄</h2>
      {data != null && nvl(data, "") != "" ? (
        <div className="box-col" id="recentAir">
          {data.map((item, index) => {
            let arrivalAirportText = "";
            if (item.sectionType == "MultiCity") {
              arrivalAirportText =
                item.departureAirportNames[item.departureAirportNames.length - 1] +
                "(" +
                item.departureAirportCodes[item.departureAirportNames.length - 1] +
                ")";
            } else {
              if (item.sectionType == "RoundTrip") {
                arrivalAirportText = item.departureAirportNames[1] + "(" + item.departureAirportCodes[1] + ")";
              } else {
                arrivalAirportText = item.arrivalAirportNames[0] + "(" + item.arrivalAirportCodes[0] + ")";
              }
            }
            return (
              index < 2 && (
                <div
                  key={index}
                  className={`${index == 0 ? "left-col" : "right-col"} ${item.sectionType == "MultiCity" ? "multi" : item.sectionType == "RoundTrip" ? "round" : "oneway"}`}
                >
                  <Link
                    to={""}
                    onClick={(e) => {
                      e.preventDefault();
                      const { labelPassenger, ...dataRequest } = item;
                      dataRequest.itineraryCount =
                        item.sectionType === "MultiCity" ? dataRequest.departureAirportCodes.length : item.sectionType === "RoundTrip" ? 2 : 1;
                      dataRequest.itineraryType = item.sectionType;
                      const dataQuery = QueryString.stringify(dataRequest, { arrayFormat: "repeat" });
                      AirSessionUtil.setSession("recentAirSearch", item);
                      if (item.isOverseas == 0) navigate(`/user/v2/domestic/air/search?${dataQuery}`);
                      else navigate(`/user/v2/overseas/air/search?${dataQuery}`);
                    }}
                  >
                    <div className="inner">
                      <p className="type">
                        <span>{item.sectionType == "MultiCity" ? "다구간" : item.sectionType == "RoundTrip" ? "왕복" : "편도"}</span>
                      </p>
                      <div className="city">
                        <p>{`${item.departureAirportNames[0]}(${item.departureAirportCodes[0]})`}</p>
                        <p>{arrivalAirportText}</p>
                      </div>
                      <p className="period">
                        {DateUtil.getPatternYmd(item.departureDays[0], ". ")} {DateUtil.getDayOfWeekName(item.departureDays[0])} -{" "}
                        {DateUtil.getPatternYmd(item.departureDays[item.departureDays.length - 1], ". ")}{" "}
                        {DateUtil.getDayOfWeekName(item.departureDays[item.departureDays.length - 1])}
                      </p>
                      <p className="info">
                        {item.sectionType === "MultiCity" ? "다구간" : item.sectionType === "RoundTrip" ? "왕복" : "편도"}, 승객 {item.adultCount}명,{" "}
                        {AirUtil.getSeatTypeName(item.departSeatTypeCode)}
                      </p>
                    </div>
                  </Link>
                  <Link to={""} className="btn-delete" onClick={() => airLocalStorageUtil.remove(item.id, userInfo.id)}></Link>
                </div>
              )
            );
          })}
        </div>
      ) : (
        <div className="none" id="recentAirNoData">
          <div className="inner">최근 검색한 스케줄이 없습니다.</div>
        </div>
      )}
    </div>
  );
}
