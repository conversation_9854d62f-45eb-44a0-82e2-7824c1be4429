import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import PopMileage from "@/app/components/user_v2/modal/PopMileage.modal.jsx";
import AirSearchForm from "@/app/pages/user_v2/custom/kakao/main/airSearchForm";
import BoardHome from "@/app/pages/user_v2/custom/kakao/main/boardHome";
import ComingTripHome from "@/app/pages/user_v2/custom/kakao/main/comingTripHome";
import MajorTripHome from "@/app/pages/user_v2/custom/kakao/main/majorTripHome";
import RecentTripHome from "@/app/pages/user_v2/custom/kakao/main/recentTripHome";
import ServiceCsHome from "@/app/pages/user_v2/main/serviceCsHome";
import { useAppDispatch } from "@/store/index.js";
import { actionGetMainAirTravelPlaces, actionGetUserAirport } from "@/store/userSlice.js";

const TicketMain = () => {
  const [openModal, setOpenModal] = useState(Cookies.get("noShowPopup") || "true");
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(actionGetMainAirTravelPlaces());
    dispatch(actionGetUserAirport());
  }, [dispatch]);

  return (
    <div className="pg-main" id="container">
      <div className="contents">
        <AirSearchForm />
        <ComingTripHome />
        <MajorTripHome />
        <RecentTripHome />
        <BoardHome />
        <ServiceCsHome />
      </div>
      <PopMileage open={openModal === "true"} openSetOpenModal={setOpenModal} />
    </div>
  );
};

export default TicketMain;
