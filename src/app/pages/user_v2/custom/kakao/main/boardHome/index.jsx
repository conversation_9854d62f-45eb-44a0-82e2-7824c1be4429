import { useAppSelector } from "@/store";
import { selectMainAirTravelPlaces } from "@/store/userSlice";
import { goBoard } from "@/utils/app";

import moment from "moment";

import { Link } from "react-router-dom";

export default function BoardHome() {
  const data = useAppSelector(selectMainAirTravelPlaces);
  const { faqs, notices } = data;
  return (
    <div className="board-home">
      <div className="default-wd inner">
        <div className="box-col">
          <p className="tit">자주 묻는 질문</p>
          <ul>
            {faqs &&
              faqs.map((item) => {
                return (
                  <li key={item.boardId}>
                    <Link
                      onClick={() => goBoard("faq", item.categoryCodeName, item.boardId, item.subCategoryCodeId)}
                    >{`[${item.categoryCodeName}] ${item.title}`}</Link>
                    <span className="date">{moment(item.createDate).format("yyyy-MM-DD")}</span>
                  </li>
                );
              })}
          </ul>
          <a href="/user/v2/board/faq/list" className="btn-more">
            모두 보기
          </a>
        </div>
        <div className="box-col">
          <p className="tit">공지사항</p>
          <ul>
            {notices &&
              notices.map((item) => {
                return (
                  <li key={item.boardId}>
                    <Link
                      onClick={() => goBoard("notice", item.categoryCodeName, item.boardId, item.subCategoryCodeId)}
                    >{`[${item.categoryCodeName}] ${item.title}`}</Link>
                    <span className="date">{moment(item.createDate).format("yyyy-MM-DD")}</span>
                  </li>
                );
              })}
          </ul>
          <a href="/user/v2/board/notice/list" className="btn-more">
            모두 보기
          </a>
        </div>
      </div>
    </div>
  );
}
