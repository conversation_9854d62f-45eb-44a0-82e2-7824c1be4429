import IMG_URL from "@/assets/images/travelplace";
import { Navigation } from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";

import { useAppDispatch, useAppSelector } from "@/store";
import { choosePlace } from "@/store/placeSlice";
import { selectMainAirTravelPlaces } from "@/store/userSlice";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "@/styles/user_v2/swipper.css";

export default function MajorTripHome() {
  const dispatch = useAppDispatch();
  const mainAirTravelPlaces = useAppSelector(selectMainAirTravelPlaces);

  return (
    <div className="mayjor-trip-home">
      <div className="default-wd">
        <h2 className="tit">주요 출장지</h2>
        <div className="h-[270px] w-[1080px] relative">
          <Swiper
            slidesPerView={5}
            spaceBetween={15}
            loop={true}
            navigation={true}
            modules={[Navigation]}
            style={{
              textAlign: "center",
              height: "100%",
              width: "100%",
              position: "unset",
            }}
            noSwiping={true}
          >
            {mainAirTravelPlaces.mainAirTravelPlaces &&
              mainAirTravelPlaces.mainAirTravelPlaces.map((item) => {
                return (
                  <SwiperSlide
                    key={item.mainAirTravelPlaceId}
                    onClick={() => {
                      dispatch(choosePlace(item));
                    }}
                  >
                    <a href="#none">
                      <img src={IMG_URL[item.filePath]} alt="" width={270} height={203} style={{ borderRadius: "10px" }} />
                      <span className="name">
                        <span className="kr">{item.replaceAirportName}</span>
                        <span className="en text-left">{item.airportCode}</span>
                      </span>
                    </a>
                  </SwiperSlide>
                );
              })}
          </Swiper>
        </div>
      </div>
    </div>
  );
}
