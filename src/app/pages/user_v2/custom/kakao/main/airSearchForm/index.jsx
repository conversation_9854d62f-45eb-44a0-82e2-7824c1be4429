import { useEffect, useState } from "react";
import { cloneDeep, isEmpty, isNull } from "lodash";
import { Link, useNavigate } from "react-router-dom";
import moment from "moment";
import QueryString from "qs";

import { nvl } from "@/common";
import KrDateRangePicker from "@/app/components/user_v2/common/KrDateRangePicker";
import KrSingleDateRangePicker from "@/app/components/user_v2/common/KrSingleDateRangePicker";
import LayerClassMember from "@/app/components/user_v2/common/LayerClassMember";
import DepartureCitySelect from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/DepartureCitySelect";

import "moment/dist/locale/ko";
import { useAppDispatch, useAppSelector } from "@/store";
import { removePlace, selectPlace } from "@/store/placeSlice";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";
import { formatKRDateString } from "@/utils/date";
import { SECTION_TYPE } from "@/constants/app";
import { selectUserInfo } from "@/store/userSlice";
import { swapArrayLoc, swapArrayPositionLoc } from "@/utils/common";
import AirSessionUtil from "@/utils/airSessionUtil";

moment.locale("ko");

const MAP_SECTION_TYPE = {
  RoundTrip: "type-round",
  OneWay: "type-oneway",
  MultiCity: "type-multi",
};

const MAP_PASSENGER = {
  M: "일반석",
  W: "프리미엄 이코노미",
  C: "비즈니스",
  F: "일등석",
};

const initState = {
  departureAirportCodes: ["SEL", ""],
  departureAirportNames: ["서울", ""],
  arrivalAirportCodes: ["", ""],
  arrivalAirportNames: ["", ""],
  adultCount: 1,
  departSeatTypeCode: "M",
  departureDays: ["", ""],
  departureDayTexts: ["", ""],
  itineraryType: "RoundTrip",
  itineraryCount: 2,
  labelPassenger: "일반석",
  sectionType: "RoundTrip",
  isOverseas: 1,
  stopOverType: null,
  headDepartAirport: 1,
};

export default function AirSearchForm() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const majorSelectTrip = useAppSelector(selectPlace);
  const { userInfo } = useAppSelector(selectUserInfo);

  const [overseas, setOverseas] = useState(1);
  const [chooseIndex, setChooseIndex] = useState(0);
  const [chooseDateIndex, setChooseDateIndex] = useState(0);
  const [sectionType, setSectionType] = useState("RoundTrip");
  const [typeAirport, setTypeAirport] = useState("departure");

  const [formState, setFormState] = useState(cloneDeep(initState));

  const handleSelectOverSeas = (overseas) => {
    dispatch(removePlace());
    setOverseas(overseas);
    setSectionType("RoundTrip");
    setFormState((prev) => {
      if (overseas == 0) {
        return {
          ...prev,
          ...cloneDeep(initState),
          sectionType: sectionType,
          isOverseas: overseas,
          departureAirportCodes: [""],
          departureAirportNames: [""],
        };
      } else {
        return {
          ...prev,
          ...cloneDeep(initState),
          sectionType: sectionType,
          isOverseas: overseas,
        };
      }
    });
  };

  const handleSelectSectionType = (sectionType) => {
    dispatch(removePlace());
    setSectionType(sectionType);
    setFormState((prev) => {
      const clonedPrev = cloneDeep(prev);
      if (overseas == 0) {
        return {
          ...prev,
          ...cloneDeep(initState),
          sectionType: sectionType,
          isOverseas: overseas,
          departureAirportCodes: clonedPrev.departureAirportCodes,
          departureAirportNames: clonedPrev.departureAirportNames,
          arrivalAirportCodes: clonedPrev.arrivalAirportCodes,
          arrivalAirportNames: clonedPrev.arrivalAirportNames,
        };
      } else {
        if (sectionType !== SECTION_TYPE.ROUNDTRIP) {
          clonedPrev.departureAirportCodes[1] = "";
          clonedPrev.departureAirportNames[1] = "";
          clonedPrev.arrivalAirportCodes[1] = "";
          clonedPrev.arrivalAirportNames[1] = "";
        } else {
          clonedPrev.departureAirportCodes[1] = clonedPrev.arrivalAirportCodes[0];
          clonedPrev.departureAirportNames[1] = clonedPrev.arrivalAirportNames[0];
          clonedPrev.arrivalAirportCodes[1] = clonedPrev.departureAirportCodes[0];
          clonedPrev.arrivalAirportNames[1] = clonedPrev.departureAirportNames[0];
        }
        return {
          ...clonedPrev,
          sectionType: sectionType,
          isOverseas: overseas,
        };
      }
    });
  };

  const handleChooseArrival = (item) => {
    if (sectionType === "RoundTrip") {
      setFormState((prev) => {
        if (typeAirport == "departure") {
          return {
            ...prev,
            departureAirportCodes: [item.code, prev.arrivalAirportCodes[0]],
            departureAirportNames: [item.name, prev.arrivalAirportNames[0]],
            arrivalAirportCodes: [prev.arrivalAirportCodes[0], item.code],
            arrivalAirportNames: [prev.arrivalAirportNames[0], item.name],
          };
        } else {
          return {
            ...prev,
            arrivalAirportCodes: [item.code, prev.departureAirportCodes[0]],
            arrivalAirportNames: [item.name, prev.departureAirportNames[0]],
            departureAirportCodes: [prev.departureAirportCodes[0], item.code],
            departureAirportNames: [prev.departureAirportNames[0], item.name],
          };
        }
      });
    } else if (sectionType === "OneWay") {
      if (typeAirport == "departure") {
        setFormState((prev) => {
          return {
            ...prev,
            departureAirportCodes: [item.code, ""],
            departureAirportNames: [item.name, ""],
          };
        });
      } else {
        setFormState((prev) => {
          return {
            ...prev,
            arrivalAirportCodes: [item.code, ""],
            arrivalAirportNames: [item.name, ""],
          };
        });
      }
    } else if (sectionType === "MultiCity") {
      if (chooseIndex === 0) {
        if (typeAirport == "departure") {
          setFormState((prev) => {
            prev.departureAirportCodes.splice(0, 1, item.code);
            prev.departureAirportNames.splice(0, 1, item.name);
            return {
              ...prev,
            };
          });
        } else {
          setFormState((prev) => {
            prev.arrivalAirportCodes.splice(0, 1, item.code);
            prev.arrivalAirportNames.splice(0, 1, item.name);
            prev.departureAirportCodes.splice(1, 1, item.code);
            prev.departureAirportNames.splice(1, 1, item.name);
            return {
              ...prev,
            };
          });
        }
      } else if (chooseIndex === formState.departureAirportCodes.length - 1) {
        if (typeAirport == "departure") {
          setFormState((prev) => {
            prev.departureAirportCodes.splice(chooseIndex, 1, item.code);
            prev.departureAirportNames.splice(chooseIndex, 1, item.name);
            // prev.arrivalAirportCodes.splice(chooseIndex - 1, 1, item.code);
            // prev.arrivalAirportNames.splice(chooseIndex - 1, 1, item.name);
            return {
              ...prev,
            };
          });
        } else {
          setFormState((prev) => {
            prev.arrivalAirportCodes.splice(chooseIndex, 1, item.code);
            prev.arrivalAirportNames.splice(chooseIndex, 1, item.name);
            return {
              ...prev,
            };
          });
        }
      } else {
        if (typeAirport === "departure") {
          setFormState((prev) => {
            prev.departureAirportCodes.splice(chooseIndex, 1, item.code);
            prev.departureAirportNames.splice(chooseIndex, 1, item.name);
            return {
              ...prev,
            };
          });
        } else {
          setFormState((prev) => {
            prev.arrivalAirportCodes.splice(chooseIndex, 1, item.code);
            prev.arrivalAirportNames.splice(chooseIndex, 1, item.name);
            prev.departureAirportCodes.splice(chooseIndex + 1, 1, item.code);
            prev.departureAirportNames.splice(chooseIndex + 1, 1, item.name);
            return {
              ...prev,
            };
          });
        }
      }
    }
  };

  const handleSelectDate = (date) => {
    if (sectionType === "MultiCity") {
      setFormState((prev) => {
        prev.departureDays.splice(chooseDateIndex, 1, moment(date.startDate).format("YYYYMMDD"));
        prev.departureDayTexts.splice(chooseDateIndex, 1, formatKRDateString(moment(date.startDate).format("YYYY-MM-DD")));

        return {
          ...prev,
        };
      });
    } else {
      setFormState((prev) => {
        return {
          ...prev,
          departureDays: [moment(date.startDate).format("YYYYMMDD"), moment(date.endDate).format("YYYYMMDD")],
          departureDayTexts: [
            formatKRDateString(moment(date.startDate).format("YYYY-MM-DD")),
            formatKRDateString(moment(date.endDate).format("YYYY-MM-DD")),
          ],
        };
      });
    }
  };

  const handleSelectPassenger = (data) => {
    setFormState((prev) => {
      return {
        ...prev,
        adultCount: data.adultCount,
        departSeatTypeCode: data.departSeatTypeCode,
        labelPassenger: MAP_PASSENGER[data.departSeatTypeCode],
      };
    });
  };

  const handleValidateForm = () => {
    if (sectionType === "MultiCity") {
      let arrivalAirportCodeEmptyCount = 0,
        departureAirportCodeEmptyCount = 0;
      formState.arrivalAirportCodes.forEach((i) => {
        if (nvl(i, "") == "") arrivalAirportCodeEmptyCount++;
      });

      if (arrivalAirportCodeEmptyCount > 0) {
        alert("도착지를 입력해주세요.");
        return false;
      }

      formState.departureAirportCodes.forEach((i) => {
        if (nvl(i, "") == "") departureAirportCodeEmptyCount++;
      });

      if (departureAirportCodeEmptyCount > 0) {
        alert("출발지를 입력해주세요.");
        return false;
      }
    } else {
      if (formState.departureAirportCodes[0] == "") {
        alert("출발지를 입력해주세요.");
        return false;
      } else if (formState.arrivalAirportCodes[0] == "") {
        alert("도착지를 입력해주세요.");
        return false;
      }
    }
    if (formState.departureDays.length === 0) {
      alert("날짜를 선택해주세요.");
      return false;
    }
    let emptyDayCount = 0;
    formState.departureDays.forEach((item) => {
      if (nvl(item, "").trim() === "") emptyDayCount++;
    });
    if (emptyDayCount > 0) {
      alert("날짜를 선택해주세요.");
      return false;
    }
    return true;
  };

  const handleAirMainSearch = () => {
    const isValid = handleValidateForm();
    if (!isValid) return;
    else {
      const { labelPassenger, ...dataRequest } = formState;
      dataRequest.itineraryCount = sectionType === "MultiCity" ? dataRequest.departureAirportCodes.length : sectionType === "RoundTrip" ? 2 : 1;
      dataRequest.itineraryType = sectionType;
      if (dataRequest.stopOverType === null) delete dataRequest.stopOverType;
      const data = QueryString.stringify(dataRequest, { arrayFormat: "repeat" });
      airLocalStorageUtil.set(formState, userInfo.id);
      AirSessionUtil.setSession("recentAirSearch", dataRequest);
      if (overseas == 0) navigate(`/user/v2/domestic/air/search?${data}`);
      else navigate(`/user/v2/overseas/air/search?${data}`);
    }
  };

  function handleSwapDepartureArrival(position) {
    const clonedFormState = cloneDeep(formState);
    if (sectionType === SECTION_TYPE.MULTICITY) {
      let positionSwap = position ? position : 0;
      if (clonedFormState.arrivalAirportCodes[positionSwap] === "") {
        alert("도착지를 선택해주세요.");
        return;
      }
      swapArrayPositionLoc(clonedFormState, "departureAirportCodes", "arrivalAirportCodes", positionSwap);
      swapArrayPositionLoc(clonedFormState, "departureAirportNames", "arrivalAirportNames", positionSwap);
      setFormState(clonedFormState);
    } else {
      if (clonedFormState.arrivalAirportCodes[0] === "") {
        alert("도착지를 선택해주세요.");
        return;
      }
      swapArrayLoc(clonedFormState, "departureAirportCodes", "arrivalAirportCodes");
      swapArrayLoc(clonedFormState, "departureAirportNames", "arrivalAirportNames");
      setFormState(clonedFormState);
    }
  }

  useEffect(() => {
    if (!isEmpty(majorSelectTrip)) {
      if (overseas == 0) {
        setOverseas(1);
        setSectionType("RoundTrip");
      }
      setFormState((prev) => {
        if (sectionType == "MultiCity") {
          prev.arrivalAirportCodes.splice(0, 1, majorSelectTrip.airportCode);
          prev.arrivalAirportNames.splice(0, 1, majorSelectTrip.replaceAirportName);
          const newArrivalAirportCodes = cloneDeep(prev.arrivalAirportCodes);
          const newArrivalAirportNames = cloneDeep(prev.arrivalAirportNames);
          return {
            ...prev,
            ...cloneDeep(initState),
            arrivalAirportCodes: newArrivalAirportCodes,
            arrivalAirportNames: newArrivalAirportNames,
          };
        } else {
          const newObject = {
            ...prev,
            ...cloneDeep(initState),
            arrivalAirportCodes: [majorSelectTrip.airportCode, initState.departureAirportCodes[0]],
            arrivalAirportNames: [majorSelectTrip.replaceAirportName, initState.departureAirportNames[0]],
            departureAirportCodes: [initState.departureAirportCodes[0], majorSelectTrip.airportCode],
            departureAirportNames: [initState.departureAirportNames[0], majorSelectTrip.replaceAirportName],
          };
          return newObject;
        }
      });
    }
  }, [majorSelectTrip]);

  return (
    <form id="airSearchForm">
      <input type="hidden" id="sectionType" name="sectionType" defaultValue={sectionType} />
      <input type="hidden" id="isOverseas" name="isOverseas" defaultValue={formState.isOverseas} />
      <input type="hidden" id="userId" defaultValue={userInfo?.id} />

      <div
        id="searchTicketBox"
        className={`default-wd search-trip-home ${overseas == 0 ? "type-domestic" : "type-foregin"} `}
        style={{ marginBottom: 0 }}
      >
        <p className="copy">어디로 출장가세요? </p>
        <div className="tab-area">
          <Link to={""} className={`btn-default ${overseas == 1 && "active"}`} data-nation="foregin" onClick={() => handleSelectOverSeas(1)}>
            <span>해외</span>
          </Link>
        </div>
        <div id="ticketSearch" className={MAP_SECTION_TYPE[sectionType]}>
          <div className="top-area">
            <button
              type="button"
              className={`btn-default ${sectionType == "RoundTrip" && "active"}`}
              data-ticket-type="round"
              onClick={() => handleSelectSectionType("RoundTrip")}
            >
              왕복
            </button>
            <button
              type="button"
              className={`btn-default ${sectionType == "OneWay" && "active"}`}
              data-ticket-type="oneway"
              onClick={() => handleSelectSectionType("OneWay")}
            >
              편도
            </button>
            <button
              type="button"
              className={`btn-default ${sectionType == "MultiCity" && "active"}`}
              data-ticket-type="multi"
              onClick={() => handleSelectSectionType("MultiCity")}
            >
              다구간
            </button>
            <div className="etc">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  name="stopOverType"
                  defaultValue={0}
                  onChange={() =>
                    setFormState((prev) => {
                      return {
                        ...prev,
                        stopOverType: prev.stopOverType === 0 ? null : 0,
                      };
                    })
                  }
                />
                <span>직항만</span>
              </label>
              <p className="more">정확한 출장규정 반영을 위하여 최종 목적지를 포함한 여정을 선택해 주세요.</p>
            </div>
          </div>
          <div className="mid-area">
            <div className="box-line city">
              <label
                className="form-radio"
                onClick={() => {
                  setFormState((prev) => {
                    return {
                      ...prev,
                      headDepartAirport: 1,
                    };
                  });
                }}
              >
                <input type="radio" name="headDepartAirport" defaultValue={1} defaultChecked />
                <span className="before:box-content">여정 1</span>
              </label>
              <DepartureCitySelect
                code={overseas === 0 ? "Domestic" : "Overseas"}
                onChooseArrival={handleChooseArrival}
                additionalPosition={{ top: 5, left: 20 }}
              >
                <Link
                  className={`depature btn-city-layer ${formState.departureAirportNames[0] ? "in" : ""}`}
                  onClick={() => {
                    setTypeAirport("departure");
                    setChooseIndex(0);
                  }}
                >
                  <span className="default">출발지 선택</span>
                  <span className="val">
                    <strong>{formState.departureAirportNames[0]}</strong> <span>{formState.departureAirportCodes[0]}</span>
                  </span>
                </Link>
              </DepartureCitySelect>
              <div className="arrival-before" onClick={() => handleSwapDepartureArrival(0)}></div>
              <DepartureCitySelect code={overseas === 0 ? "Domestic" : "Overseas"} onChooseArrival={handleChooseArrival}>
                <Link
                  className={`arrival btn-city-layer box-content ${formState.arrivalAirportCodes[0] ? "in" : ""}`}
                  onClick={() => {
                    setTypeAirport("arrival");
                    setChooseIndex(0);
                  }}
                >
                  <span className="default">도착지 선택</span>
                  <span className="val">
                    <strong>{formState.arrivalAirportNames[0]}</strong> <span>{formState.arrivalAirportCodes[0]}</span>
                  </span>
                </Link>
              </DepartureCitySelect>
              <input type="hidden" id="departureAirportCode_1" name="departureAirportCodes" defaultValue="SEL" />
              <input type="hidden" id="departureAirportName_1" name="departureAirportNames" defaultValue="서울" />
              <input type="hidden" id="arrivalAirportCode_1" name="arrivalAirportCodes" defaultValue="" />
              <input type="hidden" id="arrivalAirportName_1" name="arrivalAirportNames" defaultValue="" />
            </div>
            <div
              className="box-line period"
              style={{
                boxSizing: "content-box",
              }}
            >
              {sectionType === "RoundTrip" ? (
                <>
                  <KrDateRangePicker onSelectDate={handleSelectDate} sectionType={sectionType} key="123">
                    <a
                      className={`start btn-datepicker ${formState.departureDays[0] ? "in" : ""}`}
                      style={{
                        boxSizing: "content-box",
                      }}
                    >
                      <span className="default">가는 날 선택</span>
                      <span className="val" id="dateDepature_1">
                        {moment(formState.departureDays[0]).format("YYYY년MM월DD일")}
                      </span>
                    </a>
                  </KrDateRangePicker>
                  <KrDateRangePicker onSelectDate={handleSelectDate} sectionType={sectionType} key="12">
                    <a
                      className={`end btn-datepicker ${formState.departureDays[1] ? "in" : ""}`}
                      style={{
                        boxSizing: "content-box",
                      }}
                    >
                      <span className="default">오는 날 선택</span>
                      <span className="val" id="dateArrival_1">
                        {moment(formState.departureDays[1]).format("YYYY년MM월DD일")}
                      </span>
                    </a>
                  </KrDateRangePicker>
                </>
              ) : (
                <>
                  <KrSingleDateRangePicker key={0} onSelectDate={handleSelectDate} sectionType={sectionType} minDate={new Date()}>
                    <a
                      onClick={() => setChooseDateIndex(0)}
                      className={`start btn-datepicker ${formState.departureDays[0] ? "in" : ""}`}
                      style={{
                        boxSizing: "content-box",
                      }}
                    >
                      <span className="default">가는 날 선택</span>
                      <span className="val" id="dateDepature_1">
                        {moment(formState.departureDays[0]).format("YYYY년MM월DD일")}
                      </span>
                    </a>
                  </KrSingleDateRangePicker>
                  <KrSingleDateRangePicker onSelectDate={handleSelectDate} sectionType={sectionType}>
                    <a
                      to={""}
                      className={`end btn-datepicker ${formState.departureDays[1] ? "in" : ""}`}
                      style={{
                        boxSizing: "content-box",
                      }}
                    >
                      <span className="default">오는 날 선택</span>
                      <span className="val" id="dateArrival_1">
                        {moment(formState.departureDays[1]).format("YYYY년MM월DD일")}
                      </span>
                    </a>
                  </KrSingleDateRangePicker>
                </>
              )}
            </div>
            {sectionType !== "MultiCity" && <LayerClassMember key={sectionType} onSaveData={handleSelectPassenger} />}
            <div className={`search ${sectionType === "MultiCity" && "hidden"}`}>
              <button type="button" onClick={handleAirMainSearch} className="btn-default">
                검색
              </button>
            </div>
          </div>
          <div
            className="bottom-area"
            style={{
              display: overseas === 0 && "none",
            }}
          >
            <div className="bottom-area">
              {formState.departureAirportCodes.map((item, index) => {
                if (index === 0) return null;
                else
                  return (
                    <div className="sec-item" key={index}>
                      <div className="box-line city">
                        <label
                          className="form-radio"
                          onClick={() => {
                            setFormState((prev) => {
                              return {
                                ...prev,
                                headDepartAirport: index + 1,
                              };
                            });
                          }}
                        >
                          <input type="radio" name="headDepartAirport" defaultValue={index + 1} checked={formState.headDepartAirport === index + 1} />
                          <span className="before:box-content">여정 {index + 1}</span>
                        </label>
                        <DepartureCitySelect
                          code={overseas === 0 ? "Domestic" : "Overseas"}
                          onChooseArrival={handleChooseArrival}
                          additionalPosition={{ top: 5, left: 20 }}
                        >
                          <Link
                            className={`depature btn-city-layer ${formState.departureAirportCodes[index] ? "in" : ""}`}
                            onClick={() => {
                              setTypeAirport("departure");
                              setChooseIndex(index);
                            }}
                          >
                            <span className="default">출발지 선택</span>
                            <span className="val">
                              <strong>{formState.departureAirportNames[index]}</strong> <span>{formState.departureAirportCodes[index]}</span>
                            </span>
                          </Link>
                        </DepartureCitySelect>
                        <div className="arrival-before" onClick={() => handleSwapDepartureArrival(index)}></div>
                        <DepartureCitySelect code={overseas === 0 ? "Domestic" : "Overseas"} onChooseArrival={handleChooseArrival}>
                          <Link
                            className={`arrival btn-city-layer box-content ${formState.arrivalAirportCodes[index] ? "in" : ""}`}
                            onClick={() => {
                              setTypeAirport("arrival");
                              setChooseIndex(index);
                            }}
                          >
                            <span className="default">도착지 선택</span>
                            <span className="val">
                              <strong>{formState.arrivalAirportNames[index]}</strong> <span>{formState.arrivalAirportCodes[index]}</span>
                            </span>
                          </Link>
                        </DepartureCitySelect>
                        <input type="hidden" id="departureAirportCode_1" name="departureAirportCodes" defaultValue="SEL" />
                        <input type="hidden" id="departureAirportName_1" name="departureAirportNames" defaultValue="서울" />
                        <input type="hidden" id="arrivalAirportCode_1" name="arrivalAirportCodes" defaultValue="" />
                        <input type="hidden" id="arrivalAirportName_1" name="arrivalAirportNames" defaultValue="" />
                      </div>
                      <div
                        className="box-line period"
                        style={{
                          boxSizing: "content-box",
                        }}
                      >
                        <KrSingleDateRangePicker
                          key={new Date(moment(formState.departureDays[index - 1] || new Date()).format("YYYY-MM-DD"))}
                          onSelectDate={handleSelectDate}
                          sectionType={sectionType}
                          minDate={new Date(moment(formState.departureDays[index - 1] || new Date()).format("YYYY-MM-DD"))}
                        >
                          <Link
                            onClick={() => setChooseDateIndex(index)}
                            className={`start btn-datepicker ${formState.departureDays[index] ? "in" : ""}`}
                            style={{
                              boxSizing: "content-box",
                            }}
                          >
                            <span className="default">가는 날 선택</span>
                            <span className="val" id="dateDepature_1">
                              {moment(formState.departureDays[index]).format("YYYY년MM월DD일")}
                            </span>
                          </Link>
                        </KrSingleDateRangePicker>
                        <KrSingleDateRangePicker onSelectDate={handleSelectDate} sectionType={sectionType}>
                          <Link
                            to={""}
                            className={`end btn-datepicker ${formState.departureDays[index] ? "in" : ""}`}
                            style={{
                              boxSizing: "content-box",
                            }}
                          >
                            <span className="default">오는 날 선택</span>
                            <span className="val" id="dateArrival_1">
                              {moment(formState.departureDays[index]).format("YYYY년MM월DD일")}
                            </span>
                          </Link>
                        </KrSingleDateRangePicker>
                      </div>
                      <div
                        className="btn-multi-ctrl remove"
                        style={{
                          display: formState.departureAirportCodes.length > 2 ? "block" : "none",
                        }}
                        onClick={() => {
                          setFormState((prev) => {
                            prev.departureAirportCodes.splice(index, 1);
                            prev.departureAirportNames.splice(index, 1);
                            prev.arrivalAirportCodes.splice(index, 1);
                            prev.arrivalAirportNames.splice(index, 1);
                            if (index + 1 === formState.headDepartAirport) prev.headDepartAirport = 1;
                            else prev.headDepartAirport = index + 1;
                            return {
                              ...prev,
                            };
                          });
                        }}
                      >
                        <button type="button" className="btn-default" />
                      </div>
                    </div>
                  );
              })}
            </div>
            <div className="multi-btn-wrap">
              <div
                className="btn-multi-ctrl add"
                onClick={() => {
                  setFormState((prev) => {
                    if (prev.departureAirportCodes.length > 5) return prev;
                    return {
                      ...prev,
                      departureAirportCodes: [...prev.departureAirportCodes, ""],
                      departureAirportNames: [...prev.departureAirportNames, ""],
                      arrivalAirportCodes: [...prev.arrivalAirportCodes, ""],
                      arrivalAirportNames: [...prev.arrivalAirportNames, ""],
                    };
                  });
                }}
              >
                <button type="button" className="btn-default">
                  <span className="btn-plus">여정추가</span>
                </button>
              </div>

              {sectionType === "MultiCity" && <LayerClassMember onSaveData={handleSelectPassenger} />}
              <div className="search" onClick={handleAirMainSearch}>
                <button type="button" className="btn-default">
                  검색
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  );
}
