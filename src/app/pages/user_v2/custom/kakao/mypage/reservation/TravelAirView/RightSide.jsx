import { useState } from "react";
import { isEmpty } from "lodash";
import { useParams } from "react-router-dom";
import AirCompareSchedule from "@/app/components/user_v2/modal/AirCompareSchedule.modal";
import ReservationFareRule from "@/app/components/user_v2/modal/ReservationFareRule.modal";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionSavedFareRule } from "@/store/airSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import { comma } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { selectUserInfo } from "@/store/userSlice";
import { MAPPED_STATUS_CODE } from "@/constants/app";

export default function RightSide() {
  const {
    data: { travel },
    compareAirSchedules,
  } = useAppSelector(selectReservationTravelView);
  const { userInfo } = useAppSelector(selectUserInfo);

  const { travelId } = useParams();
  const dispatch = useAppDispatch();
  const apiWithLoading = useApiWithLoading();

  const [openAirBookingCompareScheduleModal, setOpenAirBookingCompareScheduleModal] = useState(false);
  const [openFareRuleModal, setOpenFareRuleModal] = useState(false);

  async function handleOpenAirBookingCompareScheduleModal() {
    setOpenAirBookingCompareScheduleModal(true);
  }

  async function handleOpenFareRuleModal() {
    await apiWithLoading(
      () => dispatch(actionSavedFareRule({ travelId })).unwrap(),
      () => {
        setOpenFareRuleModal(true);
      },
    );
  }

  return (
    <div className="right-side sec-pay-to !sticky top-0 right-0">
      {/* S: 총 결제요금 */}
      <div className="box-line">
        <div className="txt-big clearfix">
          <p className="fl-l">총 결제요금</p>
          <p className="fl-r c-price">
            <strong>{comma(travel?.bookingAir?.totalAmount)}</strong>원
          </p>
        </div>
        <dl className="fare-detail">
          <dt>성인 {travel?.bookingAir?.adultCount}명</dt>
          <dd>
            <span className="tit">항공료</span>
            <span className="sum">
              {comma(travel?.bookingAir?.fareAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
          <dd>
            <span className="tit">TAX</span>
            <span className="sum">
              {comma(travel?.bookingAir?.taxAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
          <dd>
            <span className="tit">발권수수료</span>
            <span className="sum">
              {comma(travel?.bookingAir?.commissionAmount / (travel?.bookingAir?.adultCount || 1))}원 X {travel?.bookingAir?.adultCount}
            </span>
          </dd>
        </dl>
      </div>
      <div className="box-line">
        <p className="txt-big">출장자 규정</p>
        <ul className="brakedown [&>li]:!pt-0 [&>li]:before:!top-[10px] [&>li>div>span]:!no-underline">
          <li>
            Business Class upgrade 기준
            <div className="tooltip-custom">
              <span className="tooltip-custom-icon" />
              <span className="tooltip-custom-text">
                <ul className="font-light no-underline list-disc pl-6">
                  <li>순수 업무수행 목적으로 비행시간 8시간 이상</li>
                  <li>야간비행(22시~06시)후 바로 업무복귀 등 기타 업무 일정 있는 경우 비행시간 5시간 이상</li>
                  <li>교육 및 컨퍼런스, 업체 행사(접대성)의 경우 Economy Class 이용</li>
                  <li>출장자가 임신부인 경우</li>
                  <li>주요 단체 및 업체의 동행, 수행이 필요한 경우</li>
                  <li>좌석 매진 및 기타 특별 사정으로 해당 좌석 등급 이용 불가한 경우</li>
                </ul>
              </span>
            </div>
          </li>
          <li>항공권: 출장 일정에 맞는 항공권 중, Low fare 이용 원칙</li>
        </ul>
      </div>
      <div className="box-line">
        <p className="txt-big">결재</p>
        <dl
          className={`pay-status ${travel?.statusCode?.id === MAPPED_STATUS_CODE.Completed.id ? "complete" : [MAPPED_STATUS_CODE.Rejected.id, MAPPED_STATUS_CODE.Cancelled.id].includes(travel?.statusCode?.id) ? "reject" : "ready"}`}
        >
          <dt>결재 상태</dt>
          <dd>{travel?.statusCode?.name}</dd>
        </dl>

        {[MAPPED_STATUS_CODE.Approved.id, MAPPED_STATUS_CODE.Completed.id, MAPPED_STATUS_CODE.Rejected.id, MAPPED_STATUS_CODE.Cancelled.id].includes(
          travel?.statusCode?.id,
        ) && <div className="paid-date">결재일 {momentKR(travel?.modifyDate, 0).format("yyyy년 MM월 DD일(ddd) HH:mm")}</div>}
        <div>
          <p className="txt-medium clearfix">결재 의견</p>
          <dd>{travel?.approvalMemo}</dd>
        </div>
      </div>

      <div className="box-line">
        <p className="txt-big">결재 요청 의견</p>
        <div className="opinion-insert">
          <textarea name="violationReason" id="violationReason" maxLength="500" readOnly defaultValue={travel?.violationReason} />
        </div>
      </div>
      {!isEmpty(travel?.documentNumbers) ? (
        <div className="box-line">
          <p className="txt-big">
            {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
              ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
              : "문서번호"}
          </p>
          <ul>
            {travel?.documentNumbers?.map((item, index) => (
              <li key={index + item}>{item?.documentNo}</li>
            ))}
          </ul>
        </div>
      ) : (
        userInfo?.workspace?.company?.btmsSetting?.isDocumentNumberUse && (
          <div className="box-line">
            <p className="txt-big">
              {userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                ? userInfo?.workspace?.company?.btmsSetting?.documentNumberUseTitle
                : "문서번호"}
            </p>
            <ul></ul>
          </div>
        )
      )}

      {/* TODO: order new key overseasDocumentNo*/}
      {travel?.overseasDocumentNo && (
        <div>
          <p className="txt-medium clearfix">카카오 해외출장신청서 문서번호</p>
          <p className="txt" id="overseasDocumentNo">
            {travel?.overseasDocumentNo}
          </p>
        </div>
      )}

      <div className="box-applys">
        {!isEmpty(compareAirSchedules) && (
          <a onClick={handleOpenAirBookingCompareScheduleModal} className="btn-default btn-compare">
            비교견적서
          </a>
        )}
      </div>
      <div className="box-applys">
        <a className="btn-default btn-compare" onClick={handleOpenFareRuleModal}>
          요금 규정
        </a>
      </div>

      <AirCompareSchedule travel={travel} open={openAirBookingCompareScheduleModal} setOpen={setOpenAirBookingCompareScheduleModal} />
      <ReservationFareRule open={openFareRuleModal} setOpen={setOpenFareRuleModal} />
    </div>
  );
}
