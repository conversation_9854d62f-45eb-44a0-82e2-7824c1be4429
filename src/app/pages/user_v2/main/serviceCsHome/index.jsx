import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { returnPhoneNumber } from "@/utils/common";

import { get } from "lodash";

export default function ServiceCsHome() {
  const { gnbUserInfo } = useAppSelector(selectUserInfo);

  function getPhoneNumberOrEmail(gnbUserInfo, type) {
    let result = "";
    const btmsManagers = get(gnbUserInfo, "workspace.company.btmsManagers", []);

    if (!btmsManagers || !btmsManagers.length) {
      return result;
    }

    for (const manager of btmsManagers) {
      const { travelAgencyUser, managerType } = manager;
      if (managerType !== type) {
        continue;
      }

      result =
        returnPhoneNumber(travelAgencyUser?.phoneNumber, "FIRST") +
        "-" +
        returnPhoneNumber(travelAgencyUser?.phoneNumber, "MIDDLE") +
        "-" +
        returnPhoneNumber(travelAgencyUser?.phoneNumber, "END");
      if (result.length < 12) {
        result = travelAgencyUser.email || "";
      }

      if (result) {
        return result;
      }
    }

    return result;
  }

  const airPhoneNumberOrEmail = getPhoneNumberOrEmail(gnbUserInfo, "Air");
  const hotelRentCarPhoneNumberOrEmail = getPhoneNumberOrEmail(gnbUserInfo, "Hotel_RentCar");
  const passportVisaPhoneNumberOrEmail = getPhoneNumberOrEmail(gnbUserInfo, "Passport_Visa");
  const bizTrainingPhoneNumberOrEmail = getPhoneNumberOrEmail(gnbUserInfo, "BizTraining");

  if (!airPhoneNumberOrEmail && !hotelRentCarPhoneNumberOrEmail && !passportVisaPhoneNumberOrEmail && !bizTrainingPhoneNumberOrEmail) {
    return null;
  }

  return (
    <div className="default-wd service-cs-home">
      <h2 className="tit">서비스별 문의</h2>
      <div className="clearfix">
        {airPhoneNumberOrEmail && <div className="box-col air">
          <div className="ico">
            <i />
          </div>
          <p className="name">항공 서비스 문의</p>
          <p className="num">{airPhoneNumberOrEmail}</p>
        </div>}
        {hotelRentCarPhoneNumberOrEmail && <div className="box-col hotel">
          <div className="ico">
            <i />
          </div>
          <p className="name">호텔/렌터카 서비스 문의</p>
          <p className="num">{hotelRentCarPhoneNumberOrEmail}</p>
        </div>}
        {passportVisaPhoneNumberOrEmail && <div className="box-col visa">
          <div className="ico">
            <i />
          </div>
          <p className="name">비자 신청 문의</p>
          <p className="num">{passportVisaPhoneNumberOrEmail}</p>
        </div>}
        {bizTrainingPhoneNumberOrEmail && <div className="box-col mice">
          <div className="ico">
            <i />
          </div>
          <p className="name">단체행사 견적 문의</p>
          <p className="num">{bizTrainingPhoneNumberOrEmail}</p>
        </div>}
      </div>
    </div>
  );
}
