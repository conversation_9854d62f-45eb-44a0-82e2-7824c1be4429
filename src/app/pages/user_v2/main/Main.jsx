import PopMileage from "@/app/components/user_v2/modal/PopMileage.modal.jsx";
import AirSearchForm from "@/app/pages/user_v2/main/airSearchForm";
import BoardHome from "@/app/pages/user_v2/main/boardHome";
import ComingTripHome from "@/app/pages/user_v2/main/comingTripHome";
import MajorTripHome from "@/app/pages/user_v2/main/majorTripHome";
import RecentTripHome from "@/app/pages/user_v2/main/recentTripHome";
import ServiceCsHome from "@/app/pages/user_v2/main/serviceCsHome";
import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { useAppDispatch } from "@/store/index.js";
import { actionGetMainAirTravelPlaces, actionGetUserAirport } from "@/store/userSlice.js";
import { useMainUrl } from "@/app/hooks/useMainUrl";
import { MENU_USE } from "@/constants/app";
import { useNavigate } from "react-router-dom";
import { URL } from "@/constants/url";

const TicketMain = () => {
  const [openModal, setOpenModal] = useState(Cookies.get("noShowPopup") || "true");
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { menuUse } = useMainUrl();

  useEffect(() => {
    dispatch(actionGetMainAirTravelPlaces());
    dispatch(actionGetUserAirport());
  }, [dispatch]);

  useEffect(() => {
    if (menuUse === MENU_USE.ONLY_HOTEL) {
      navigate(URL.HotelMain);
    }
  }, [menuUse, navigate]);

  return (
    <div className="pg-main" id="container">
      <div className="contents">
        <AirSearchForm />
        <ComingTripHome />
        <MajorTripHome />
        <RecentTripHome />
        <BoardHome />
        <ServiceCsHome />
      </div>
      <PopMileage open={openModal === "true"} openSetOpenModal={setOpenModal} />
    </div>
  );
};

export default TicketMain;
