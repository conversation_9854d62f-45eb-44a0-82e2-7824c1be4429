import { useState, useRef, useEffect, cloneElement } from "react";

const MODAL_CLASS = ".modal";

const useOpenComponent = (props) => {
  const { children = null, isToggle = true, dropdownRef } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, right: 0, childrenWidth: 0 });
  const buttonRef = useRef(null);

  const calculatePosition = (type = "DEFAULT") => {
    if (buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const buttonWidth = buttonRect.width;
      const viewportHeight = window.innerHeight;

      const positions = {
        top: buttonRect.bottom + window.scrollY,
        left: buttonRect.left + window.scrollX,
        right: buttonRect.left + window.scrollX + buttonWidth,
        bottom: viewportHeight - buttonRect.bottom,
      };

      setDropdownPosition({
        top: positions.top,
        left: positions.left,
        right: positions.left + buttonWidth,
        childrenWidth: buttonWidth,
      });
      setIsOpen(type === "DEFAULT" ? (!isToggle ? true : !isOpen) : true);
    }
  };

  const handleClickOutside = (event) => {
    const target = event.target;
    const isModal = target.closest(MODAL_CLASS);
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target) &&
      buttonRef.current &&
      !buttonRef.current.contains(event.target) &&
      isModal === null
    ) {
      setIsOpen(false);
    }
  };

  const handleResize = () => {
    if (isOpen) {
      calculatePosition("RESIZE");
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      window.addEventListener("resize", handleResize);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", handleResize);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("resize", handleResize);
    };
  }, [isOpen]);

  return {
    isOpen,
    setIsOpen,
    calculatePosition,
    clonedChild:
      children !== null
        ? cloneElement(children, {
            ref: buttonRef,
            "is-open": isOpen.toString(),
            onClick: () => {
              children.props.onClick && children.props.onClick();
              calculatePosition();
            },
          })
        : null,
    dropdownPosition,
  };
};

export default useOpenComponent;
