import { useMemo } from "react";
import DateUtil from "@/utils/dateUtil";

function useBookingAirScheduleMap(props) {
  const { bookingAirSchedule = [] } = props;

  return useMemo(() => {
    const bookingAirScheduleMap = new Map();
    let bookingAirSchedules = null;
    let seq = 1;
    bookingAirSchedule.forEach((schedule) => {
      if (!schedule.isVia) {
        bookingAirSchedules = [];
        bookingAirScheduleMap.set(seq++, bookingAirSchedules);
      }
      bookingAirSchedules.push(schedule);
    });
    bookingAirScheduleMap.forEach((list) => {
      let totalTime = "";
      list.forEach((schedule) => {
        if (schedule.isVia && schedule.groundTime?.trim()) {
          totalTime = DateUtil.plusHHMM(totalTime, schedule.groundTime);
        }
        if (schedule.leadTime?.trim()) {
          totalTime = DateUtil.plusHHMM(totalTime, schedule.leadTime);
        }
      });
      list[0] = { ...list[0], totalTime };
    });

    return {
      bookingAirScheduleMap: Object.fromEntries(bookingAirScheduleMap),
      bookingAirScheduleMapSize: bookingAirScheduleMap.size,
    };
  }, [bookingAirSchedule]);
}

export default useBookingAirScheduleMap;
