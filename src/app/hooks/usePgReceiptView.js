import { useEffect } from "react";

export function usePgReceiptView() {
  useEffect(() => {
    const loadJQuery = () => {
      return new Promise((resolve, reject) => {
        const script = document.createElement("script");
        script.src = "https://code.jquery.com/jquery-3.6.0.min.js";
        script.type = "text/javascript";
        script.onload = () => {
          window.jQuery = window.$;
          resolve();
        };
        script.onerror = reject;
        document.body.appendChild(script);
      });
    };

    const loadAuthScript = () => {
      const script = document.createElement("script");
      script.src = "https://iniweb.inicis.com/js/auth.js";
      script.type = "text/javascript";
      script.defer = true;
      document.body.appendChild(script);
    };

    loadJQuery()
      .then(loadAuthScript)
      .catch((error) => {
        console.error("Failed to load jQuery:", error);
      });

    return () => {
      document
        .querySelectorAll('script[src="https://code.jquery.com/jquery-3.6.0.min.js"], script[src="https://iniweb.inicis.com/js/auth.js"]')
        .forEach((script) => {
          document.body.removeChild(script);
        });
    };
  }, []);

  function pgReceiptView(tid, approvalNumber) {
    const receiptUrl = "https://iniweb.inicis.com/app/publication/apReceipt.jsp?noTid=" + tid;
    // receiptUrl += '&authFlg=1|0|2';

    //인증코드 3 : 승인번호 ==> https://iniweb.inicis.com/js/auth.js
    window.showReceipt(receiptUrl, "3", approvalNumber);
  }

  return { pgReceiptView };
}
