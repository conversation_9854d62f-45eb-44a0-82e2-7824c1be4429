import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";

const useReservationNoticing = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  function overseasShow() {
    dispatch(actionIncreaseLoadingCount("loadingBookingNoticeCount"));
  }

  function overseasConfirmBtn() {
    dispatch(actionDecreaseLoadingCount("loadingBookingNoticeCount"));
    // dispatch(actionIncreaseLoadingCount("loadingBookingCount"));
    navigate("/user/v2/overseas/air/booking");
  }

  return {
    overseasShow,
    overseasConfirmBtn,
  };
};

export default useReservationNoticing;
