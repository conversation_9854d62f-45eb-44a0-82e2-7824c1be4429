import { useAppDispatch } from "@/store";
import { actionDecreaseLoadingCount, actionIncreaseLoadingCount } from "@/store/loadingUserSlice";

const useApiWithLoading = () => {
  const dispatch = useAppDispatch();
  return async (apiCall, callback, afterError, loadingType = "loadingDefaultCount") => {
    try {
      dispatch(actionIncreaseLoadingCount(loadingType));
      const response = await apiCall();
      callback?.(response);
    } catch (error) {
      afterError?.(error);
      console.error(error);
    } finally {
      dispatch(actionDecreaseLoadingCount(loadingType));
    }
  };
};

export default useApiWithLoading;
