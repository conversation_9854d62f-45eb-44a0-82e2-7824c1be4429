import { useMemo } from "react";
import { useLocation } from "react-router-dom";

function useQueryParams(multipleKeys) {
  const location = useLocation();

  return useMemo(() => {
    const params = new URLSearchParams(location.search);
    const queryParams = {};
    multipleKeys?.forEach((key) => {
      queryParams[key] = [];
    });
    for (const [key, value] of params.entries()) {
      if (queryParams[key]) {
        queryParams[key].push(value);
        continue;
      }
      queryParams[key] = value;
    }

    return queryParams;
  }, [multipleKeys, location.search]);
}

export default useQueryParams;
