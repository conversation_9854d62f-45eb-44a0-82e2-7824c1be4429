import { useEffect, useState } from "react";
import { useAppSelector } from "@/store";
import { selectRewardMileSettings } from "@/store/userSlice";

const useRewardMileSettings = () => {
  const rewardMileSettings = useAppSelector(selectRewardMileSettings);

  const [isDisplayMileageKorea, setIsDisplayMileageKorea] = useState(false);

  useEffect(() => {
    if (rewardMileSettings?.isUse && rewardMileSettings?.rewardMileType === "KE") {
      setIsDisplayMileageKorea(true);
    } else {
      setIsDisplayMileageKorea(false);
    }
  }, [rewardMileSettings]);

  return { isDisplayMileageKorea };
};

export default useRewardMileSettings;
