import { useMemo } from "react";

function useCsCallNumber(props) {
  const { btmsSetting = {}, defaultNumber = "1544-2351" } = props;

  return useMemo(() => {
    const { csCallNumber1, csCallNumber2, csCallNumber3 } = btmsSetting;
    let result = defaultNumber;
    if (csCallNumber1) {
      result = csCallNumber1;
      if (csCallNumber2) {
        result += ` - ${csCallNumber2}`;
        if (csCallNumber3) {
          result += ` - ${csCallNumber3}`;
        }
      }
    }
    return result;
  }, [btmsSetting, defaultNumber]);
}

export default useCsCallNumber;
