import { useState, useRef, useEffect, useCallback } from "react";
import { isEqual } from "lodash";

const useFormElements = (options) => {
  const formRef = useRef();
  const [formElements, setFormElements] = useState({});
  const [resetKey, setResetKey] = useState(0);

  const triggerReset = useCallback(() => {
    setResetKey((prevKey) => prevKey + 1);
  }, []);
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (formRef.current) {
        clearInterval(intervalId);
        const elements = formRef.current.elements;
        const elementsObj = {};
        for (const element of elements) {
          const { name, id } = element;
          if (!options || options?.mappingType === "ID") {
            if (typeof id === "string" && id.trim() !== "") {
              elementsObj[id] = element;
            }
          }
          if (options?.mappingType === "NAME") {
            if (typeof name === "string" && name.trim() !== "") {
              elementsObj[name] = element;
            }
          }
        }
        if (!isEqual(formElements, elementsObj)) setFormElements(elementsObj);
      }
    }, 0);
    return () => {
      clearInterval(intervalId);
    };
  }, [options, resetKey]);

  return [formRef, formElements, triggerReset];
};

export default useFormElements;
