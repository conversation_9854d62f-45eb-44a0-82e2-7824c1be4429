import { MENU_PRIMARY, MENU_USE } from "@/constants/app";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

export const useMainUrl = () => {
  const { userInfo } = useAppSelector(selectUserInfo);
  const company = userInfo?.workspace?.company;
  // eslint-disable-next-line no-unsafe-optional-chaining
  const menuUse = company?.btmsSetting?.menuUse;
  const menuPrimary = company?.btmsSetting?.menuPrimary;

  let mainUrl = "/user/v2/main";
  let reservationListUrl = "/user/v2/reservation/travel/list";

  switch (menuUse) {
    case MENU_USE.ALL:
      if (menuPrimary === MENU_PRIMARY.AIR) {
        mainUrl = "/user/v2/main";
        reservationListUrl = "/user/v2/reservation/travel/list";
      } else if (menuPrimary === MENU_PRIMARY.HOTEL) {
        mainUrl = "/user/v2/hotel/main";
        reservationListUrl = "/user/v2/reservation/hotel/list";
      }
      break;
    case MENU_USE.ONLY_AIR:
      mainUrl = "/user/v2/main";
      reservationListUrl = "/user/v2/reservation/travel/list";
      break;
    case MENU_USE.ONLY_HOTEL:
      mainUrl = "/user/v2/hotel/main";
      reservationListUrl = "/user/v2/reservation/hotel/list";
      break;
    default:
      break;
  }

  return {
    mainUrl,
    menuPrimary,
    menuUse,
    reservationListUrl,
  };
};
