import { lazy, Suspense, useEffect, useMemo } from "react";
import { Route, Routes } from "react-router-dom";
import { COMPANY_SITECODE, DEFAULT_LAYOUT, MOBILE_LAYOUT, NONE_LAYOUT } from "@/constants/app";
import lazyWithPreload from "@/utils/lazyWithPreload";
import MainRedirect from "@/app/components/user_v2/common/MainRedirect";
import { URL, MOBILE_URL } from "@/constants/url";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

const MobileLayout = lazy(() => import("@/app/layouts/MobileLayout"));
const DefaultLayout = lazy(() => import("@/app/layouts/DefaultLayout"));
const PrivateLayout = lazy(() => import("@/app/layouts/PrivateLayout"));

const Login = lazy(() => import("@/app/pages/Login"));
const MJoinAgreement = lazy(() => import("@/app/pages/mobile/MJoinAgreement"));

const TicketMain = lazy(() => import("@/app/pages/user_v2/main/Main"));
const KakaoTicketMain = lazy(() => import("@/app/pages/user_v2/custom/kakao/main/Main"));

const JoinForm = lazy(() => import("@/app/pages/user_v2/joinForm"));
const MJoinForm = lazy(() => import("@/app/pages/mobile/joinForm"));
const AirOverseasTicketSearch = lazy(() => import("@/app/pages/user_v2/travel/overseas/air/AirSearch"));
const KakaoAirOverseasTicketSearch = lazy(() => import("@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirSearch"));

// lazyWithPreload: chọn vé có hiện tượng trắng nên cần load trước css
// const OverseasAirBooking = lazyWithPreload(() => import("@/app/pages/user_v2/travel/overseas/air/Booking"));
// const KakaoOverseasAirBooking = lazyWithPreload(() => import("@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirBooking"));
// const KortekOverseasAirBooking = lazyWithPreload(() => import("@/app/pages/user_v2/custom/kortek/travel/overseas/air/AirBooking"));
const OverseasAirBooking = lazy(() => import("@/app/pages/user_v2/travel/overseas/air/Booking"));
const KakaoOverseasAirBooking = lazy(() => import("@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirBooking"));
const KortekOverseasAirBooking = lazy(() => import("@/app/pages/user_v2/custom/kortek/travel/overseas/air/AirBooking"));

const AirBookingComplete = lazy(() => import("@/app/pages/user_v2/travel/overseas/air/AirBookingComplete"));
const KakaoAirBookingComplete = lazy(() => import("@/app/pages/user_v2/custom/kakao/travel/overseas/air/AirBookingComplete"));

const TravelAirView = lazy(() => import("@/app/pages/user_v2/mypage/reservation/TravelAirView"));
const KakaoTravelAirView = lazy(() => import("@/app/pages/user_v2/custom/kakao/mypage/reservation/TravelAirView"));
const KortekTravelAirView = lazy(() => import("@/app/pages/user_v2/custom/kortek/mypage/reservation/TravelAirView"));

// mobile
const MMain = lazy(() => import("@/app/pages/mobile/main"));
const MKakaoMain = lazy(() => import("@/app/pages/mobile/custom/kakao/main"));

const MOverseasAirMain = lazy(() => import("@/app/pages/mobile/travel/overseas/air/AirMain"));
const MKakaoOverseasAirMain = lazy(() => import("@/app/pages/mobile/custom/kakao/travel/overseas/air/AirMain"));

const MOverseasAirSearch = lazy(() => import("@/app/pages/mobile/travel/overseas/air/AirSearch"));
const MKakaoOverseasAirSearch = lazy(() => import("@/app/pages/mobile/custom/kakao/travel/overseas/air/AirSearch"));

const MOverseasAirBooking = lazy(() => import("@/app/pages/mobile/travel/overseas/air/AirBooking"));
const MKakaoOverseasAirBooking = lazy(() => import("@/app/pages/mobile/custom/kakao/travel/overseas/air/AirBooking"));

const MOverseasAirBookingComplete = lazy(() => import("@/app/pages/mobile/travel/overseas/air/AirBookingComplete"));
const MKakaoOverseasAirBookingComplete = lazy(() => import("@/app/pages/mobile/custom/kakao/travel/overseas/air/AirBookingComplete"));

const MTravelAirView = lazy(() => import("@/app/pages/mobile/mypage/reservation/TravelAirView"));

const NotFound = lazy(() => import("@/app/pages/results/NotFound"));

// hotel
const MainHotel = lazy(() => import("@/app/pages/user_v2/mainHotel"));
const HotelSearch = lazy(() => import("@/app/pages/user_v2/travel/hotel/HotelSearch"));

const MAP_ROUTES_BY_COMPANY = {
  [COMPANY_SITECODE.KAKAO_COMPANY_SITECODE]: {
    [URL.AirMain]: <KakaoTicketMain />,
    [URL.AirSearch]: <KakaoAirOverseasTicketSearch />,
    [URL.OverseasAirBooking]: <KakaoOverseasAirBooking />,
    [URL.TravelAirView]: <KakaoTravelAirView />,
    [URL.AirCompleteBooking]: <KakaoAirBookingComplete />,
    // mobile
    [MOBILE_URL.Main]: <MKakaoMain />,
    [MOBILE_URL.OverseasAirMain]: <MKakaoOverseasAirMain />,
    [MOBILE_URL.OverseasAirSearch]: <MKakaoOverseasAirSearch />,
    [MOBILE_URL.OverseasAirBooking]: <MKakaoOverseasAirBooking />,
    [MOBILE_URL.TravelAirView]: <MTravelAirView />,
    [MOBILE_URL.AirCompleteBooking]: <MKakaoOverseasAirBookingComplete />,
  },
  [COMPANY_SITECODE.KORTEK_COMPANY_SITECODE]: {
    [URL.OverseasAirBooking]: <KortekOverseasAirBooking />,
    [URL.TravelAirView]: <KortekTravelAirView />,
  },
};

const userItems = [
  {
    key: URL.Home,
    layout: DEFAULT_LAYOUT,
    component: <MainRedirect />,
    private: true,
  },
  {
    key: URL.AirMain,
    layout: DEFAULT_LAYOUT,
    component: <TicketMain />,
    private: true,
  },
  {
    key: URL.HotelMain,
    layout: DEFAULT_LAYOUT,
    component: <MainHotel />,
    private: true,
  },
  {
    key: URL.AirSearch,
    layout: DEFAULT_LAYOUT,
    component: <AirOverseasTicketSearch />,
    private: true,
  },
  {
    key: URL.OverseasAirBooking,
    layout: DEFAULT_LAYOUT,
    component: <OverseasAirBooking />,
    private: true,
  },
  {
    key: URL.AirCompleteBooking,
    layout: DEFAULT_LAYOUT,
    component: <AirBookingComplete />,
    private: true,
  },
  {
    key: URL.TravelAirView,
    layout: DEFAULT_LAYOUT,
    component: <TravelAirView />,
    private: true,
  },
  {
    key: URL.HotelSearch,
    layout: DEFAULT_LAYOUT,
    component: <HotelSearch />,
    private: true,
  },
  {
    key: URL.Login,
    layout: NONE_LAYOUT,
    component: <Login />,
    private: false,
  },
  {
    key: URL.MJoinAgreement,
    layout: NONE_LAYOUT,
    component: <MJoinAgreement />,
    private: false,
  },
  {
    key: URL.JoinForm,
    layout: DEFAULT_LAYOUT,
    component: <JoinForm />,
    private: false,
  },
  {
    key: "*",
    component: <NotFound />,
    layout: NONE_LAYOUT,
    private: false,
  },
];

const userItemsMobile = [
  {
    key: MOBILE_URL.Main,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MMain />,
  },
  {
    key: MOBILE_URL.OverseasAirMain,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MOverseasAirMain />,
  },
  {
    key: MOBILE_URL.OverseasAirSearch,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MOverseasAirSearch />,
  },
  {
    key: MOBILE_URL.OverseasAirBooking,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MOverseasAirBooking />,
  },
  {
    key: MOBILE_URL.OverseasAirBookingComplete,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MOverseasAirBookingComplete />,
  },
  {
    key: MOBILE_URL.TravelAirView,
    layout: MOBILE_LAYOUT,
    private: true,
    component: <MTravelAirView />,
  },
  {
    key: MOBILE_URL.JoinForm,
    layout: MOBILE_LAYOUT,
    private: false,
    component: <MJoinForm />,
  },
];

const sharedItems = [];

function getItems() {
  return [...userItems, ...userItemsMobile, ...sharedItems];
}

export default function Routers() {
  const items = getItems();
  const { userInfo } = useAppSelector(selectUserInfo);
  const companySitecode = useMemo(() => userInfo?.workspace?.company?.siteCode, [userInfo]);

  function getCustomViewName(item) {
    const isCustom = userInfo?.workspace?.company?.isCustom;
    if (isCustom) {
      const customView = MAP_ROUTES_BY_COMPANY?.[companySitecode]?.[item.key];
      if (customView) return customView;
    }

    return item.component;
  }

  // useEffect(() => {
  //   if (Object.values(URL).includes(location.pathname)) {
  //     if (companySitecode === COMPANY_SITECODE.KAKAO_COMPANY_SITECODE) KakaoOverseasAirBooking.preload();
  //     OverseasAirBooking.preload();
  //   }
  // }, []);

  return (
    <Routes>
      {items.map((item) => {
        let element = getCustomViewName(item);

        switch (item.layout) {
          case DEFAULT_LAYOUT:
            element = <DefaultLayout>{element}</DefaultLayout>;
            if (item.private) element = <PrivateLayout>{element}</PrivateLayout>;
            break;
          case MOBILE_LAYOUT:
            element = <MobileLayout>{element}</MobileLayout>;
            break;
          default:
            break;
        }

        element = <Suspense fallback={null}>{element}</Suspense>;
        return <Route key={item.key} path={item.key} element={element} />;
      })}
    </Routes>
  );
}
