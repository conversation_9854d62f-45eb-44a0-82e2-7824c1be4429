import { useState, Fragment, useEffect } from "react";
import { isEmpty } from "lodash";
import { useSelector } from "react-redux";
import { selectUserInfo } from "@/store/userSlice";
import { substring4IndexOf, trimString } from "@/utils/common";
import UserInfo from "@/app/components/user_v2/Header/UserInfo";
import Logo from "@/assets/images/cmm/logo.png";
import { useMainUrl } from "@/app/hooks/useMainUrl";
import { MENU_USE } from "@/constants/app";

const HEADERS = [
  { id: 1, name: "air", link: "/user/v2/main", label: "항공권" },
  { id: 2, name: "hotel", link: "/user/v2/hotel/main", label: "호텔" },
];

const Header = () => {
  const { userInfo, homepageSettingInfo } = useSelector(selectUserInfo);
  const [headers, setHeaders] = useState(HEADERS);

  const { mainUrl, menuUse, reservationListUrl } = useMainUrl();

  function menuSetting() {
    let activeString = "";
    const url = location.href;
    if (
      url.indexOf("/hotel/main") > -1 ||
      url.indexOf("/hotel/search") > -1 ||
      url.indexOf("/hotel/view") > -1 ||
      url.indexOf("/hotel/booking") > -1
    ) {
      activeString = "hotel";
    } else if (
      url.indexOf("/main") > -1 ||
      url.indexOf("/air/search") > -1 ||
      url.indexOf("/air/booking") > -1 ||
      url.indexOf("/air/offlineBooking") > -1
    ) {
      activeString = "air";
    }

    setHeaders((prev) => {
      if (menuUse === MENU_USE.ALL) {
        return prev.map((header) =>
          header.name === activeString ? { ...header, active: true, isShow: true } : { ...header, active: false, isShow: true },
        );
      } else if (menuUse === MENU_USE.ONLY_AIR) {
        return prev.map((header) =>
          header.name === activeString ? { ...header, active: true, isShow: true } : { ...header, active: false, isShow: false },
        );
      } else if (menuUse === MENU_USE.ONLY_HOTEL) {
        return prev.map((header) =>
          header.name === activeString ? { ...header, active: true, isShow: true } : { ...header, active: false, isShow: false },
        );
      }
      return prev.map((header) => (header.name === activeString ? { ...header, active: true } : { ...header, active: false }));
    });
  }

  useEffect(() => {
    menuSetting();
  }, [location.href, menuUse]);

  return (
    <div id="header">
      <div className="clearfix">
        <div className="left-col [&_img]:!w-auto">
          <h1>
            <a href={mainUrl}>
              {!isEmpty(homepageSettingInfo) ? (
                homepageSettingInfo?.isUseDefaultLogo ? (
                  <img src={Logo} alt="LOGO" />
                ) : (
                  <img
                    src={
                      import.meta.env.VITE_STORAGE_URL +
                      "/btms" +
                      trimString(homepageSettingInfo?.loginLogoAttachFile?.fileUploadPath, 16, -1) +
                      homepageSettingInfo?.loginLogoAttachFile?.tempFileName
                    }
                    alt="BTMS"
                  />
                )
              ) : (
                <img src={Logo} alt="LOGO" />
              )}
            </a>
          </h1>
          <div id="gnb">
            <ul className="depth01">
              {headers.map(
                (header) =>
                  header.isShow && (
                    <li className={`${header.name} ${header.active ? "active" : ""} after:!w-full after:!left-0`} key={header.id}>
                      <a href={header.link}>{header.label}</a>
                    </li>
                  ),
              )}
              {!substring4IndexOf(userInfo?.workspace?.company?.btmsSetting?.url, ".").includes("kakao") && (
                <li className="more">
                  <a>
                    부가서비스 <i />
                  </a>
                  <ul className="depth02">
                    <li>
                      <a href="https://trvins.meritzfire.com/copr/tb?bizNo=MTI0ODY4NjM3MA==&travelPlace=WFg=&usrInpYn=WQ==" target="_blank">
                        해외여행보험
                      </a>
                    </li>
                  </ul>
                </li>
              )}
              <li className="more">
                <a>
                  더 보기
                  <i />
                  <ul className="depth02">
                    <li>
                      <a href="/user/v2/board/notice/list">공지사항</a>
                    </li>
                    <li>
                      <a href="/user/v2/board/faq/list">자주 묻는 질문</a>
                    </li>
                    {!substring4IndexOf(userInfo?.workspace?.company?.btmsSetting?.url, ".").includes("mastercard") && (
                      <Fragment>
                        <li>
                          <a href="/user/v2/board/qna/list">1:1문의 내역</a>
                        </li>
                        <li>
                          <a href="/user/v2/mypage/managerInfo">담당자 안내</a>
                        </li>
                      </Fragment>
                    )}
                  </ul>
                </a>
              </li>
            </ul>
          </div>
        </div>
        <input type="hidden" id="url" defaultValue="btms" />
        <div className="right-col">
          <p className="name">
            <a className="link" href={reservationListUrl}>
              MY페이지
            </a>
          </p>
          <div className="box-mypage z-[9999]" id="gnbDiv_UserInfo">
            <UserInfo>
              <a className="link">{userInfo?.name}</a>
            </UserInfo>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
