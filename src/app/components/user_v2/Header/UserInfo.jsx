import { Fragment, useRef } from "react";
import useOpenComponent from "@/app/hooks/useOpenComponent";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";

const UserInfo = (props) => {
  const { children } = props;
  const { userInfo } = useAppSelector(selectUserInfo);

  const dropdownRef = useRef(null);
  const { isOpen, dropdownPosition, clonedChild } = useOpenComponent({ children, dropdownRef });

  return (
    <Fragment>
      {clonedChild}
      <PortalComponent>
        {isOpen && (
          <div
            ref={dropdownRef}
            className="layer fixed z-[999] w-[262px] rounded-[5px] bg-white border border-[#EBEEF3] pt-[19px] pb-[12px] shadow-custom-400"
            style={{ top: dropdownPosition.top + 29, left: dropdownPosition.right - 155 }}
          >
            <p className="name px-[20px] text-[17px] font-medium mb-[11px]">{userInfo?.name}</p>
            <p className="team px-[20px] pb-[20px] mb-[6px] border-b border-[#EBEEF3]">
              {userInfo?.workspace?.name} / {userInfo?.department?.name} / {userInfo?.position?.name}
            </p>
            <p className="link leading-[20px] px-[20px] py-[8px]" id="account">
              <a className=" leading-[20px] w-full" href="/customer/v2/account/info">
                회원정보 수정
              </a>
            </p>
            <p className="link leading-[20px] px-[20px] py-[8px]" id="logoutBtn">
              <a className=" leading-[20px] w-full" href="/logout">
                로그아웃
              </a>
            </p>
          </div>
        )}
      </PortalComponent>
    </Fragment>
  );
};

export default UserInfo;
