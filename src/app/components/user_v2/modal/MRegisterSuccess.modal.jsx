import { Modal } from "@mui/material";

export default function MRegisterSuccessModal({ open, onClose }) {
  const handleClose = () => {
    window.location.href = "/login";
    onClose();
  };
  return (
    <Modal open={open} className="flex justify-center items-center ring-0 select-none">
      <div id="container" className="w-full bg-white flex items-center justify-center flex-col h-full">
        <div className="contents complete-join-cotn">
          <dl>
            <dt>BTMS 회원가입이 완료되었습니다.</dt>
          </dl>
          <div className="box-btn w-full">
            <span onClick={handleClose} className="btns-cmm round-basic color-b w-75">
              로그인
            </span>
          </div>
        </div>
      </div>
    </Modal>
  );
}
