import { useRef, useState } from "react";
import { Modal } from "@mui/material";
import { isEmpty } from "lodash";
import { useParams } from "react-router-dom";
import { postModifyRequestProcess } from "@/service/air";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

export default function ModifyRequest(props) {
  const { open, travel, setOpen } = props;

  const { travelId } = useParams();
  const textareaRef = useRef(null);
  const [requestInfo, setRequestInfo] = useState("");
  const [disabledBtn, setDisabledBtn] = useState(false);

  function handleClose() {
    setOpen(false);
    setRequestInfo("");
  }

  async function modifyRequest() {
    if (isEmpty(requestInfo)) {
      alert("변경/취소 사유를 입력해주세요.");
      textareaRef.current.focus();
      return;
    }

    if (requestInfo.length > 500) {
      alert("500자 이하로 입력해주세요.");
      textareaRef.current.focus();
      return;
    }

    const payload = { travelId, requestInfo, parentTravelModifyRequestId: 0 };
    setDisabledBtn(true);
    try {
      const response = await postModifyRequestProcess(payload);
      const resultCode = response.data?.resultCode;
      if (resultCode == "SUCCESS") {
        //$("#changeRequireResult").modal();
        alert("변경 요청이 접수 되었습니다.\n담당자 확인 후 답변 드리겠습니다.");
        location.reload();
      } else {
        alert(`변경요청 내용 등록 중 에러가 발생하였습니다.\n다시 시도해 주세요.${CONTACT_ADMIN_ALERT}`);
      }
    } catch (error) {
      alert(`등록중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
    } finally {
      setDisabledBtn(false);
      handleClose();
    }
  }

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popResevChange"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[576px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>예약 변경/취소 요청</h2>
        </div>
        <div className="modal-cotn">
          <div className="desc">
            • 항공권 변경 시 발권대행료는 환불되지 않습니다. <br />• 예약에 따라 변경 수수료가 발생 할 수 있으니, 항공권의 요금 규정에 따라 변경 가능
            여부를 <br /> 확인 한 후 진행 합니다. 자세한 사항은 담당자에게 문의 바랍니다.
          </div>
          <div className="table-cmm type-fare">
            <table>
              <colgroup>
                <col style={{ width: "100%" }} />
              </colgroup>
              <thead>
                <tr>
                  <th>영문 이름</th>
                </tr>
              </thead>
              <tbody>
                {travel?.bookingAir?.bookingAirTravelers?.map((item) => {
                  const { id, travelerLastName, travelerFirstName } = item;
                  return (
                    <tr key={id}>
                      <td>
                        {travelerLastName} {travelerFirstName}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
          <div className="box-text">
            <textarea
              ref={textareaRef}
              id="requestInfo"
              value={requestInfo}
              onChange={(e) => setRequestInfo(e.target.value)}
              placeholder="변경 하실 내용을 입력하세요. (10자-500자)"
              maxLength={500}
            />
          </div>
          <div className="bottom-btn">
            <a onClick={modifyRequest} id="changeProcessBtn" disabled={disabledBtn} className="btn-default btn-print">
              변경 요청
            </a>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
