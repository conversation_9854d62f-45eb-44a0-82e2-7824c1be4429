import { Modal } from "@mui/material";
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";

import logo from "@/assets/images/cmm/logo.png";
import Footer from "../Footer";

export default function JoinAgreementModal({ open, onCLose, checkData, setCheckData, isGeneralVersion, company }) {
  const [isAgreeAll, setIsAgreeAll] = useState(false);
  const handleCheckAll = () => {
    setIsAgreeAll(!isAgreeAll);
    setCheckData({
      ...checkData,
      isAgreeService: !isAgreeAll,
      isAgreePrivacy: !isAgreeAll,
      isAgreePrivacyId: !isAgreeAll,
      isAgreePrivacyProvide: !isAgreeAll,
      isAgreePrivacyMarketing: !isAgreeAll,
    });
  };

  const handleClose = () => {
    if (!checkData.isAgreeService) {
      alert("BTMS계정 이용 약관에 동의하지 않으셨습니다.\n다시 확인해 주세요!");
      return;
    }

    if (!checkData.isAgreePrivacy) {
      alert("개인정보 수집 및 이용에 대한 안내에\n동의하지 않으셨습니다. 다시 확인해 주세요!");
      return;
    }

    if (!checkData.isAgreePrivacyId) {
      alert("고유식별정보 처리에 대한 안내에\n동의하지 않으셨습니다. 다시 확인해 주세요!");
      return;
    }
    if (!checkData.isAgreePrivacyProvide) {
      alert("개인정보 제3자 제공 안내에 동의하지 않으셨습니다.\n다시 확인해 주세요!");
      return;
    }

    onCLose();
  };

  useEffect(() => {
    setIsAgreeAll(Object.values(checkData).every((value) => value));
  }, [checkData]);
  return (
    <Modal open={open} className="flex justify-center items-center">
      <div id="container" className="clearfix default-wd pg-login w-full h-full" style={{ backgroundColor: "white" }}>
        <div id="header">
          <div className="clearfix">
            <div className="left-col">
              <h1>
                <Link to={"/#"} onClick={() => (window.location.href = "/login")}>
                  <img
                    src={
                      isGeneralVersion || !company || !company.homepageSetting || company.homepageSetting.isUseDefaultLogo
                        ? logo
                        : import.meta.env.VITE_STORAGE_URL +
                          "/" +
                          company.homepageSetting?.loginLogoAttachFile?.fileUploadPath +
                          company.homepageSetting?.loginLogoAttachFile?.tempFileName
                    }
                    alt="LOGO"
                    style={{ width: "auto" }}
                  />
                </Link>
              </h1>
            </div>
          </div>
        </div>
        <div className="contents policy-agree-cotn !flex items-center justify-center flex-col pb-0">
          <h2 className="title w-[400px]">
            BTMS계정 <br />
            서비스약관에 동의해주세요.
          </h2>
          <div className="box-form">
            <div className="all">
              <label className="form-chkbox">
                <input type="checkbox" id="agreeCheckAll" checked={isAgreeAll} onChange={handleCheckAll} />
                <span>모두 동의합니다.</span>
              </label>
              <p className="desc">
                전체동의는 필수 및 선택정보에 대한 동의도 포함되어 있으며, 개별적으로도 동의를 선택하실 수 있습니다. 선택항목에 대한 동의를 거부하시는
                경우에도 서비스는 이용이 가능합니다.
              </p>
            </div>
            <p className="cell">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  id="isAgreeService"
                  name="isAgreeService"
                  defaultValue="true"
                  checked={checkData.isAgreeService}
                  onChange={() => {
                    setCheckData({
                      ...checkData,
                      isAgreeService: !checkData.isAgreeService,
                    });
                  }}
                />
                <span>[필수] BTMS계정 이용 약관</span>
              </label>
            </p>
            <p className="cell">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  id="isAgreePrivacy"
                  name="isAgreePrivacy"
                  defaultValue="true"
                  checked={checkData.isAgreePrivacy}
                  onChange={() => {
                    setCheckData({
                      ...checkData,
                      isAgreePrivacy: !checkData.isAgreePrivacy,
                    });
                  }}
                />
                <span>[필수] 개인정보 수집 및 이용 동의</span>
              </label>
            </p>
            <p className="cell">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  id="isAgreePrivacyId"
                  name="isAgreePrivacyId"
                  defaultValue="true"
                  checked={checkData.isAgreePrivacyId}
                  onChange={() => {
                    setCheckData({
                      ...checkData,
                      isAgreePrivacyId: !checkData.isAgreePrivacyId,
                    });
                  }}
                />
                <span>[필수] 고유식별정보 처리에 대한 안내</span>
              </label>
            </p>
            <p className="cell">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  id="isAgreePrivacyProvide"
                  name="isAgreePrivacyProvide"
                  defaultValue="true"
                  checked={checkData.isAgreePrivacyProvide}
                  onChange={() => {
                    setCheckData({
                      ...checkData,
                      isAgreePrivacyProvide: !checkData.isAgreePrivacyProvide,
                    });
                  }}
                />
                <span>[필수] 개인정보 제3자 제공 안내</span>
              </label>
            </p>
            <p className="cell">
              <label className="form-chkbox">
                <input
                  type="checkbox"
                  id="isAgreePrivacyMarketing"
                  name="isAgreePrivacyMarketing"
                  checked={checkData.isAgreePrivacyMarketing}
                  onChange={() => {
                    setCheckData({
                      ...checkData,
                      isAgreePrivacyMarketing: !checkData.isAgreePrivacyMarketing,
                    });
                  }}
                />
                <span>[선택] 마케팅 및 광고에 활용</span>
              </label>
            </p>
          </div>
          <div className="btns" style={{ textAlign: "center", width: "400px" }}>
            <button type="button" id="agreeBtn" className="btn-default btn-confirm" onClick={handleClose}>
              확인
            </button>
          </div>
        </div>
        <Footer />
      </div>
    </Modal>
  );
}
