import { useMemo } from "react";
import { isEmpty } from "lodash";
import { Modal } from "@mui/material";
import { useSearchParams } from "react-router-dom";
import FlightDetailTab from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/FlightDetailTab";
import { getFilterFromSearchParams } from "@/utils/common";

const FlightDetail = (props) => {
  const { flightDetail, setFlightDetail } = props;
  const [searchParams] = useSearchParams();
  const filter = useMemo(() => {
    return getFilterFromSearchParams(searchParams, {});
  }, [searchParams]);

  function handleColse() {
    setFlightDetail({});
  }

  return (
    <Modal open={!isEmpty(flightDetail)} onClose={handleColse}>
      <div
        id="popFlightDetail"
        className="modal-wrap modal !block !pl-0 max-w-none box-content outline-none bg-white relative w-[846px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>여행 정보</h2>
        </div>
        <div className="tab-wrap modal-cotn" id="flightDetailLayerData">
          {!isEmpty(flightDetail) && <FlightDetailTab flights={flightDetail} filter={filter} />}
        </div>
        <a href="#close-modal" rel="modal:close" className="close-modal" onClick={handleColse}>
          Close
        </a>
      </div>
    </Modal>
  );
};

export default FlightDetail;
