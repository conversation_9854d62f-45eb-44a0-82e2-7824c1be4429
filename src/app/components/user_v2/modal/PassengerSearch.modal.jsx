import { useState, useEffect } from "react";
import QueryString from "qs";
import { useDispatch } from "react-redux";
import { Modal } from "@mui/material";
import { postCustomerList } from "@/service/common";
import { nvl } from "@/common";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";

const HEADERS_TABLE = [
  { id: 1, title: "성명", key: "" },
  { id: 2, title: "이름", key: "" },
  { id: 3, title: "회사·소속", key: "" },
  { id: 4, title: "부서명", key: "" },
  { id: 5, title: "이메일", key: "" },
  { id: 6, title: "선택", key: "" },
];

export default function PassengerSearch(props) {
  const { open, setOpenModal, companyId, workspaceId, selectSearchCustomer } = props;

  const dispatch = useDispatch();
  const [searchPassengerName, setSearchPassengerName] = useState("");
  const [customerList, setCustomerList] = useState([]);

  const handleCloseModal = () => {
    setOpenModal(false);
    setCustomerList([]);
    setSearchPassengerName("");
  };

  const handleSearch = async () => {
    const payload = QueryString.stringify({
      workspaceId,
      companyId,
      name: searchPassengerName,
    });
    try {
      dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
      const res = await postCustomerList(payload);
      setCustomerList(res.data);
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
    }
  };

  useEffect(() => {
    if (!open) {
      handleCloseModal();
    }
  }, [open]);

  return (
    <Modal open={open} onClose={handleCloseModal}>
      <div
        id="popPassengerSearch"
        className="modal-wrap modal block max-w-none p-0 outline-none bg-white relative w-[790px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        style={{
          boxShadow: "0 2px 10px 4px rgba(41, 48, 54, 0.2)",
        }}
      >
        <div className="modal-head">
          <h2>탑승객 검색</h2>
        </div>
        <div className="modal-cotn">
          <div className="box-saerch-cmm">
            <input
              type="text"
              id="searchPassengerName"
              placeholder="임직원 이름을 입력해 주세요."
              value={searchPassengerName}
              onChange={(e) => {
                e.preventDefault();
                if (e.keyCode == 13) handleSearch();
                setSearchPassengerName(e.target.value);
              }}
            />
            <button type="button" id="passengerSearchBtn" className="btn-search" onClick={handleSearch} />
          </div>
          <div className="list-result">
            <div className="total">
              총 <span id="searchPassengerCount">{customerList.length}</span>건
            </div>
            <div className="slimScrollDiv">
              <div className="scrollbar-inner overflow-y-scroll !max-h-[360px] !pr-[20px]">
                <div className="table-cmm type-fare ">
                  <table>
                    <colgroup>
                      <col style={{ width: 80 }} />
                      <col style={{ width: 95 }} />
                      <col style={{ width: 110 }} />
                      <col style={{ width: 135 }} />
                      <col style={{ width: 200 }} />
                      <col style={{ width: "*" }} />
                    </colgroup>
                    <thead>
                      <tr>
                        {HEADERS_TABLE.map((header) => (
                          <th key={header.id}>{header.title}</th>
                        ))}
                      </tr>
                    </thead>
                    <tbody id="searchNoneTbody">
                      {customerList.length === 0 ? (
                        <tr>
                          <td colSpan={HEADERS_TABLE.length}>검색하신 회원 정보가 없습니다.</td>
                        </tr>
                      ) : (
                        customerList.map((customer) => {
                          return (
                            <tr key={customer.key}>
                              <td className="code !py-0 !h-[50px] !text-black">
                                <div>{nvl(customer?.employeeNo, "-")}</div>
                              </td>
                              <td className="name !py-0 !h-[50px] !text-black">
                                <div>{customer?.name}</div>
                              </td>
                              <td className="company !py-0 !h-[50px] !text-black">
                                <div>{customer?.workspace?.name}</div>
                              </td>
                              <td className="team !py-0 !h-[50px] !text-black">
                                <div>{typeof customer?.department === "undefined" ? nvl(customer?.department, "") : customer?.department?.name}</div>
                              </td>
                              <td className="email !py-0 !h-[50px] !text-black">
                                <div>{customer?.email}</div>
                              </td>
                              <td className="!py-0 !h-[50px]">
                                <button
                                  type="button"
                                  className="btn-default btn-select"
                                  name="selectPassengerBtn"
                                  onClick={() => selectSearchCustomer(customer)}
                                >
                                  선택
                                </button>
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                    <tbody id="passenger-data" />
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <a className="close-modal" onClick={handleCloseModal}>
          Close
        </a>
      </div>
    </Modal>
  );
}
