import { Modal } from "@mui/material";
import { Link } from "react-router-dom";

import logo from "@/assets/images/cmm/logo.png";
import Footer from "../Footer";

export default function RegisterSuccessModal({ open, onClose, isGeneralVersion, company }) {
  const handleClose = () => {
    window.location.href = "/login";
    onClose();
  };
  return (
    <Modal open={open} className="flex justify-center items-center ring-0 select-none">
      <div id="container" className="clearfix default-wd bg-white w-full h-full">
        <div id="header">
          <div className="clearfix">
            <div className="left-col">
              <h1>
                <Link to={"/#"} onClick={() => (window.location.href = "/login")}>
                  <img
                    src={
                      isGeneralVersion || !company || !company.homepageSetting || company.homepageSetting.isUseDefaultLogo
                        ? logo
                        : import.meta.env.VITE_STORAGE_URL +
                          "/" +
                          company.homepageSetting?.loginLogoAttachFile?.fileUploadPath +
                          company.homepageSetting?.loginLogoAttachFile?.tempFileName
                    }
                    alt="LOGO"
                    style={{ width: "auto" }}
                  />
                </Link>
              </h1>
            </div>
          </div>
        </div>
        <div className=" pw-reset-cotn mt-[120px] h-[500px]">
          <h2 className="title text-[26px] mb-[50px] font-medium w-[400px]">BTMS 회원가입이 완료되었습니다.</h2>
          <div className="btms w-[400px]" style={{ textAlign: "center" }}>
            <button type="button" onClick={handleClose} className="btn-default btn-link-send">
              로그인
            </button>
          </div>
        </div>
        <Footer />
      </div>
    </Modal>
  );
}
