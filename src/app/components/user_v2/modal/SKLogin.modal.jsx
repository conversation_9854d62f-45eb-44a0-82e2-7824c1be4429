import { useState } from "react";
import Cookies from "js-cookie";

import loginImg from "@/assets/images/sk/login.jpg";
import CloseIcon from "@mui/icons-material/Close";

export default function SKLogin() {
  const isSk = window.location.hostname.includes("sk.tourvis.com");
  const [isShow, setIsShow] = useState(isSk ? Cookies.get("isShowSKLoginPopup") || "true" : "false");

  const [isChecked, setIsChecked] = useState(false);

  const handleClose = () => {
    setIsShow("false");
    Cookies.set("isShowSKLoginPopup", !isChecked ? "true" : "false", { expires: 1 });
  };

  const handleCheckedInput = () => {
    setIsChecked(!isChecked);
  };

  return (
    <div
      style={{
        display: isShow === "true" ? "flex" : "none",
        flexDirection: "column",
        position: "absolute",
        top: 0,
        right: 36,
        zIndex: 99999,
        borderRadius: 12,
        boxShadow: "rgba(100, 100, 111, 0.2) 0px 7px 29px 0px",
        overflow: "hidden",
      }}
    >
      <img src={loginImg} width={300} />
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          paddingLeft: 12,
          paddingRight: 12,
          height: 50,
        }}
      >
        <div>
          <label
            className="form-chkbox"
            style={{
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
            }}
          >
            <input type="checkbox" name="today" id="today" />
            <span style={{ fontWeight: 500 }} onClick={handleCheckedInput}>
              오늘 하루 그만보기
            </span>
          </label>
        </div>
        <button type="button" style={{ display: "flex", cursor: "pointer" }} onClick={handleClose}>
          <a className="close-btn">닫기</a>
          <CloseIcon />
        </button>
      </div>
    </div>
  );
}
