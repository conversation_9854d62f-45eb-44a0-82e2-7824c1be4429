import { Fragment } from "react";
import { Modal } from "@mui/material";

export default function PopViewTicketDateWarning(props) {
  const { open, openSetOpenModal, type = "DEFAULT" } = props;

  function handleClose() {
    openSetOpenModal(false);
  }

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popViewTicketDateWarning"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[350px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          {type === "KAKAO" ? (
            <Fragment>
              <h2>
                예약하신 항공권은 출발일 임박 또는
                <br />
                항공사 발권시한이 임박한 예약으로
                <br />
                여행사 담당자와 예약 확인 후 진행하셔야 하며,
                <br />
                결제 진행 시 1:1문의로 확인 요청 또는
                <br />
                담당 여행사 직원에게 문의 부탁 드립니다.
              </h2>
              <h4>
                <br />
                업무시간 (평일9시~18시) 내 연락처 : 02-6911-8185
                <br />
                업무시간 외 긴급서비스 번호 : 1544-2351
              </h4>
            </Fragment>
          ) : (
            <h2>
              예약하신 항공권은 출발일 임박 또는
              <br />
              항공사 발권시한이 임박한 예약으로
              <br />
              담당자와 예약 확인 후 진행하셔야 하며,
              <br />
              결제 진행 시 1:1문의로 확인 요청 또는
              <br />
              담당직원에게 문의 부탁 드립니다.
            </h2>
          )}
        </div>
        <div className="tab-wrap modal-cotn">
          <div className="box-btns">
            <a onClick={handleClose} className="btn-default b-reserch close">
              닫기
            </a>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
