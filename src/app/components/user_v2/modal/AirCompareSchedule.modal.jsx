import { Fragment, useState } from "react";
import { uniq } from "lodash";
import { SECTION_TYPE } from "@/constants/app";
import { comma, onErrorImgAirline } from "@/utils/common";
import { momentKR } from "@/utils/date";
import { Modal } from "@mui/material";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import AIR_ICONS_PATH from "@/assets/airIcon";

export default function AirCompareSchedule(props) {
  const { open, travel, setOpen } = props;
  const { compareAirSchedules } = useAppSelector(selectReservationTravelView);

  const [showDetail, setShowDetail] = useState(false);

  function handleClose() {
    setOpen(false);
  }

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popPlaneCompare"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[1196px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>항공 스케줄 비교</h2>
        </div>
        <div className="modal-cotn bg-white rounded-[8px]">
          <div className="city-date">
            <p className="dep">
              {compareAirSchedules?.[0]?.compareScheduleDetails?.[0]?.flightDetails?.[0]?.fromAirportName}(
              {compareAirSchedules?.[0]?.compareScheduleDetails?.[0]?.flightDetails?.[0]?.fromAirportCode})
            </p>
            <p className="arr">
              {compareAirSchedules?.slice(-1)?.[0]?.compareScheduleDetails.slice(-1)?.[0]?.flightDetails?.[0]?.fromAirportName}(
              {compareAirSchedules?.slice(-1)?.[0]?.compareScheduleDetails.slice(-1)?.[0]?.flightDetails?.[0]?.fromAirportCode})
            </p>
            <div className="date">
              {momentKR(compareAirSchedules?.[0]?.compareScheduleDetails?.[0]?.flightDetails?.[0]?.fromDate).format("YYYY. MM. DD (ddd)")} -{" "}
              {momentKR(compareAirSchedules?.slice(-1)?.[0]?.compareScheduleDetails.slice(-1)?.[0]?.flightDetails?.[0]?.fromDate).format(
                "YYYY. MM. DD (ddd)",
              )}
            </div>
            {travel?.bookingAir?.bookingAirLowest && (
              <div className="lowest">최저가 요금 : {comma(travel?.bookingAir?.bookingAirLowest?.totalAmount)}원</div>
            )}
          </div>
          <p className="desc-info">ⓘ 비교견적표의 요금은 실제 예약된 금액이 아니며 담당자의 최종 확인이 필요합니다.</p>
          {/* DD :: view-all 클레스로 자세히 보기 활성화 */}
          <div className="inner-scoll ![&:dd]:mb-0">
            <div className="box-scroll">
              <div className={`list-schdule ${showDetail ? "view-all" : ""}`}>
                <div className="cell-col box-head">
                  <p className="title box-content">&nbsp;</p>
                  {travel?.bookingAir?.sectionType === SECTION_TYPE.MULTICITY ? (
                    compareAirSchedules?.[0]?.compareScheduleDetails?.map((item, index) => {
                      return (
                        <div key={index} className={`cell-row ${index === 0 ? "dep" : "arrs"}`}>
                          <p className="tit-01">여정 {index + 1}</p>
                          <div className="inner">
                            <dl>
                              <dt className="leading-[18px]">기본정보</dt>
                              <dd className="mb-0">
                                <p className="air box-content !pb-[26px]">항공편</p>
                                <p className="seat">좌석</p>
                                <p className="overstop">경유</p>
                              </dd>
                            </dl>
                            <dl className="plan">
                              <dt className="leading-[18px]">여정정보</dt>
                              <dd className="mb-0">
                                <p>출발</p>
                                <p>도착</p>
                                <p>탑승시간</p>
                                <p>경유</p>
                                <p>총 소요시간</p>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <Fragment>
                      <div className="cell-row dep">
                        <p className="tit-01">가는편</p>
                        <div className="inner">
                          <dl>
                            <dt className="leading-[18px]">기본정보</dt>
                            <dd className="mb-0">
                              <p className="air box-content !pb-[26px]">항공편</p>
                              <p className="seat">좌석</p>
                              <p className="overstop">경유</p>
                            </dd>
                          </dl>
                          <dl className="plan">
                            <dt className="leading-[18px]">여정정보</dt>
                            <dd className="mb-0">
                              <p>출발</p>
                              <p>도착</p>
                              <p>탑승시간</p>
                              <p>경유</p>
                              <p>총 소요시간</p>
                            </dd>
                          </dl>
                        </div>
                      </div>
                      {travel?.bookingAir?.sectionType === SECTION_TYPE.ROUNDTRIP && (
                        <div className="cell-row arrs">
                          <p className="tit-01">오는편</p>
                          <div className="inner">
                            <dl>
                              <dt className="leading-[18px]">기본정보</dt>
                              <dd className="mb-0">
                                <p className="air box-content !pb-[26px]">항공편</p>
                                <p className="seat">좌석</p>
                                <p className="overstop">경유</p>
                              </dd>
                            </dl>
                            <dl className="plan">
                              <dt className="leading-[18px]">여정정보</dt>
                              <dd className="mb-0">
                                <p>출발</p>
                                <p>도착</p>
                                <p>탑승시간</p>
                                <p>경유</p>
                                <p>총 소요시간</p>
                              </dd>
                            </dl>
                          </div>
                        </div>
                      )}
                    </Fragment>
                  )}

                  {/* 요금정보 */}
                  <div className="cell-row fee">
                    <p className="tit-01">요금정보</p>
                    <div className="inner">
                      <dl>
                        <dd className="mb-0 leading-[18px]">총 예상요금 (성인1)</dd>
                      </dl>
                    </div>
                  </div>
                  {/* 운임규정 */}
                  <div className="cell-row fare">
                    <p className="tit-01">운임규정</p>
                    <div className="inner">
                      <dl>
                        <dd>
                          <p>무료 수화물</p>
                          <p>&nbsp;</p>
                        </dd>
                      </dl>
                    </div>
                  </div>
                </div>
                {/* DD : active 클레스로 선택 활성화 */}
                {compareAirSchedules?.map((item, index) => {
                  const { id, totalAmount, isCorporateFare, isMasterSchedule, baggageAllowance, compareScheduleDetails } = item;
                  return (
                    <div key={id} className={`cell-col schdule-0${index + 1} ${isMasterSchedule ? "active" : ""}`}>
                      <div className="box-radio">
                        <label className="form-chkbox">
                          <input type="checkbox" defaultChecked={isMasterSchedule} disabled />
                          <span>스케줄 {index + 1}</span>
                        </label>
                      </div>
                      {compareScheduleDetails.map((item, index) => {
                        const {
                          flightDetails,
                          departureTerminal,
                          arrivalTerminal,
                          seatWaiting,
                          flightHour,
                          flightMin,
                          stopoverNo,
                          totalHour,
                          totalMin,
                        } = item;
                        const { fromAirportCode, seatClassName, bookingClassCode, fromDate } = flightDetails?.[0] || {};
                        const airlineName = uniq(flightDetails.map((air) => air?.airlineName));
                        const isMultiAirline = airlineName.length > 1;

                        const lastFlightDetails = flightDetails?.[flightDetails?.length - 1];
                        return (
                          <Fragment key={index}>
                            <div className="info-default">
                              <p className="box-content h-[30px]">
                                <img
                                  src={isMultiAirline ? AIR_ICONS_PATH.TwoFlight : AIR_ICONS_PATH[flightDetails?.[0]?.airlineCode] || ""}
                                  onError={onErrorImgAirline}
                                  className="w-[30px] h-[20px] object-contain inline-block top-0 float-left"
                                  alt="항공사로고"
                                  style={{
                                    transform: isMultiAirline && "scale(1.15)",
                                  }}
                                />
                                <span dangerouslySetInnerHTML={{ __html: airlineName.join("<br/>") }} className="name" />{" "}
                                <span className="code">
                                  {flightDetails?.map((flight, index) => (
                                    <Fragment key={index}>
                                      {flight?.airlineCode}
                                      {flight?.airlineFlightNo}편{index < flightDetails?.length - 1 && ", "}
                                    </Fragment>
                                  ))}
                                </span>
                              </p>
                              <p>
                                {seatClassName} ({bookingClassCode}) / {seatWaiting ? "대기" : "확정"}
                              </p>
                              {stopoverNo === 0 ? <p>직항</p> : <p>{stopoverNo}회 경유</p>}
                            </div>
                            <div className="info-plan box-content">
                              <a onClick={() => setShowDetail(true)} className="btn-default">
                                자세히보기
                              </a>
                              <ul className="[&>li]:leading-[18px]">
                                <li>
                                  {fromAirportCode} {departureTerminal ? `T${departureTerminal}` : ""} /{" "}
                                  {momentKR.utc(fromDate).format("YYYY-MM-DD HH:mm")}
                                </li>
                                <li>
                                  {lastFlightDetails && lastFlightDetails?.toAirportCode}
                                  {arrivalTerminal && ` T${arrivalTerminal}`} /{" "}
                                  {lastFlightDetails && momentKR.utc(lastFlightDetails?.toDate).format("YYYY-MM-DD HH:mm")}
                                </li>
                                <li>
                                  {flightHour}시간 {flightMin}분
                                </li>
                                <li>
                                  {flightDetails
                                    ?.filter((_, index) => index !== 0)
                                    .map((flight) => flight?.fromAirportCode)
                                    .join(", ")}
                                  &nbsp;
                                </li>
                                <li>
                                  {totalHour}시간 {totalMin}분
                                </li>
                              </ul>
                            </div>
                          </Fragment>
                        );
                      })}
                      <p className="info-fee">
                        <strong>
                          <span>{comma(totalAmount)}</span>원
                        </strong>
                        {isCorporateFare ? " / 기업요금" : ""}
                      </p>
                      <div className="info-rule" style={{ height: 75 }}>
                        <p className="baggage">{baggageAllowance ? baggageAllowance : "없음"}</p>
                        <div className="refund">&nbsp;</div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
