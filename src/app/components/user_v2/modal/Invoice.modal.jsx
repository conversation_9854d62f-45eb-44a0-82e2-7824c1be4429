import { useEffect, useState } from "react";
import { Modal } from "@mui/material";
import { isEmpty } from "lodash";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import "@/styles/user_v2/invoice.modal.css";

const INVOICE_TYPE = { INVOICE: "INVOICE", INVOICE_MASTER: "INVOICE_MASTER" };

export default function Invoice(props) {
  const { open, setOpen } = props;
  const {
    data: { travel },
    invoice: { invoiceMasterList },
  } = useAppSelector(selectReservationTravelView);
  const [transformedInvoiceList, setTransformedInvoiceList] = useState([]);

  function handleClose() {
    setOpen(false);
  }

  useEffect(() => {
    setTransformedInvoiceList(invoiceMasterList.map((invoice) => ({ ...invoice, type: "INVOICE_MASTER", hasTravel: !isEmpty(travel) })));
  }, [travel, invoiceMasterList]);

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popInvoice"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[576px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2 className="pt-[24px] pr-[30px] pb-[30px] pl-[30px]">인보이스</h2>
        </div>
        <div className="modal-cotn pt-0 pr-[30px] pb-[30px] pl-[30px] bg-white rounded-[8px]">
          <div className="table-cmm type-fare">
            <table>
              <colgroup>
                <col style={{ width: "*" }} />
                <col style={{ width: 80 }} />
              </colgroup>
              <thead>
                <tr>
                  <th className="left">인보이스</th>
                  <th>프린트</th>
                </tr>
              </thead>
              <tbody>
                {transformedInvoiceList.map((invoice) => {
                  const { invoiceId } = invoice;
                  const subPath = invoice.hasTravel
                    ? invoice.type === INVOICE_TYPE.INVOICE
                      ? "invoice"
                      : invoice.type === INVOICE_TYPE.INVOICE_MASTER
                        ? "hotel_invoice"
                        : "invc"
                    : "invc";

                  return (
                    <tr key={invoiceId}>
                      <td className="left">인보이스</td>
                      <td>
                        <a
                          href={`${import.meta.env.VITE_STORAGE_URL}/btms/${subPath}/${invoice.fileName}`}
                          className="btn-default btn-confirm"
                          name="payInfoPrintBtn"
                          target="_blank"
                        >
                          프린트
                        </a>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
