import { isEmpty } from "lodash";
import { useSelector } from "react-redux";
import { Modal } from "@mui/material";
import { selectAirOverseasSessionData } from "@/store/airSlice";
import { COP_CODE } from "@/constants/app";
import FareItem from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/FareItem";

const LowestSchedule = (props) => {
  const { open, setOpen, type = "DEFAULT", addNewAirCompare } = props;
  const { lowestFareAndSchedule } = useSelector(selectAirOverseasSessionData);

  function handleColse() {
    setOpen(false);
  }

  return (
    <Modal open={open} onClose={handleColse} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popLowestSchedule"
        className="modal-wrap modal nw-popup block max-w-none p-0 box-content max-h-[90vh] outline-none bg-white relative w-[906px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="h-auto bg-white rounded-[8px]">
          <div className="modal-head">
            <h2>최저가 요금 스케줄</h2>
            <div className="flex">
              <p className="desc scheduleDescription">인천(ICN) - 하노이(하노이)의 최저가 요금 스케줄입니다.</p>
              <div className="ico-wrap">
                <span className="ico-box in-ico">In Policy</span>
                <span className="ico-box out-ico">Out of Policy</span>
              </div>
            </div>
          </div>
          <div className="modal-cotn">
            {!isEmpty(lowestFareAndSchedule) &&
              (() => {
                const { flights, id } = lowestFareAndSchedule;
                const paxTypeFaresDisplay = lowestFareAndSchedule.fares.paxTypeFares[0];
                const airFare = Number(paxTypeFaresDisplay.airFare);
                const tax = Number(paxTypeFaresDisplay.airTax) + Number(paxTypeFaresDisplay.fuelChg);
                const ticketFare = Number(paxTypeFaresDisplay?.tasfAmount ?? 0); // Update later!
                const fareTypes = lowestFareAndSchedule.fares.fareTypes;
                const includedCorporateFare = fareTypes.some((type) => type.includes(COP_CODE));
                return (
                  <FareItem
                    id={id}
                    key={id}
                    flights={flights}
                    journey={lowestFareAndSchedule}
                    airFare={airFare}
                    tax={tax}
                    ticketFare={ticketFare}
                    includedCorporateFare={includedCorporateFare}
                    selectedFare={lowestFareAndSchedule}
                    setSelectedFare={() => {}}
                    selectedCompareFares={[]}
                    setSelectedCompareFares={() => {}}
                    type="VIEW"
                  />
                );
              })()}
          </div>
          <a rel="modal:close" className="close-modal" onClick={handleColse}>
            Close
          </a>
          {!["BOOKING"].includes(type) && (
            <div className="btn-request-reserv">
              {/* 비교함 담기 버튼 */}
              <button className="btn-default on addCompareSchedule" onClick={() => addNewAirCompare(lowestFareAndSchedule)}>
                비교함 담기
              </button>
            </div>
          )}
          <a rel="modal:close" className="close-modal" onClick={handleColse}>
            Close
          </a>
        </div>
      </div>
    </Modal>
  );
};

export default LowestSchedule;
