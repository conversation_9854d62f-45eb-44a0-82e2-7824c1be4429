import { Fragment, useMemo } from "react";
import { Modal } from "@mui/material";
import { useAppSelector } from "@/store";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import useApiWithLoading from "@/app/hooks/useApiWithLoading";
import "@/styles/user_v2/receipt.modal.css";
import { getCrsCardReceiptLink } from "@/service/air";
import { useParams } from "react-router-dom";
import { usePgReceiptView } from "@/app/hooks/usePgReceiptView";
import { CHARGE_TYPE } from "@/constants/app";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

export default function Receipt(props) {
  const { open, setOpen } = props;

  const { travelId } = useParams();
  const {
    data: { travel },
    payInfo: { paymentList },
    bookingAirTickets,
  } = useAppSelector(selectReservationTravelView);

  const { pgReceiptView } = usePgReceiptView();
  const apiWithLoading = useApiWithLoading();

  const totalReceiptCount = useMemo(() => {
    let count = 0;
    travel?.bookingAir?.bookingAirTickets?.forEach((ticket) => {
      ticket?.bookingAirTicketPayments.forEach((payment) => {
        if (payment.paymentType === "CARD" && travel?.bookingAir?.gdsType === "AMADEUS") {
          count++;
        }
      });
    });
    paymentList?.forEach((payment) => {
      if (
        payment?.paymentStatus !== "ERROR" &&
        payment?.gubun !== "GROUP_DEPOSIT" &&
        payment?.gubun !== "VISA_DEPOSIT" &&
        payment?.gubun !== "GROUP_REFUND" &&
        payment?.gubun !== "VISA_REFUND" &&
        payment?.gubun !== "MULTI_DEPOSIT" &&
        payment?.gubun !== "MULTI_REFUND" &&
        payment?.gubun !== "REFUND" &&
        payment?.gubun !== "DOMESTIC_REFUND"
      ) {
        count += payment?.cardList?.length;
      }
    });
    return count;
  }, [paymentList, travel]);

  function handleClose() {
    setOpen(false);
  }

  async function viewCrsCardReceipt(userType, bookingAirTicketId) {
    await apiWithLoading(
      () => getCrsCardReceiptLink({ userType, travelId, bookingAirTicketId }),
      (response) => {
        const url = response.data;
        if (url) {
          if (url.indexOf("Error") <= 0 && url.indexOf("Exception") <= 0) {
            window.open(url, "crsCardReceipt_" + bookingAirTicketId);
          } else {
            alert(`CRS카드전표 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          }
        } else {
          alert(`CRS카드전표 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
        }
      },
      () => {
        alert(`CRS카드전표 발행 오류.${CONTACT_ADMIN_ALERT}`);
      },
    );
  }

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popReceipt"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none shadow-none bg-white relative w-[576px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>영수증 보기</h2>
        </div>
        <div className="modal-cotn bg-white rounded-[8px]">
          <p className="total">
            총 <strong>{totalReceiptCount}</strong>건
          </p>
          {totalReceiptCount === 0 ? (
            <div className="table-cmm type-fare">
              <table>
                <colgroup>
                  <col width="165px" />
                  <col width="125px" />
                  <col width="*" />
                </colgroup>
                <thead>
                  <tr>
                    <th>티켓번호</th>
                    <th>항목</th>
                    <th>영수증</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td />
                    <td />
                    <td />
                  </tr>
                  <tr>
                    <td />
                    <td />
                    <td />
                  </tr>
                  <tr>
                    <td />
                    <td />
                    <td />
                  </tr>
                  <tr>
                    <td />
                    <td />
                    <td />
                  </tr>
                </tbody>
              </table>
            </div>
          ) : (
            <div className="table-cmm type-fare">
              <table>
                <colgroup>
                  <col style={{ width: "165px" }} />
                  <col style={{ width: "125px" }} />
                  <col style={{ width: "*" }} />
                </colgroup>
                <thead>
                  <tr>
                    <th>티켓번호</th>
                    <th>항목</th>
                    <th>영수증</th>
                  </tr>
                </thead>
                <tbody>
                  {bookingAirTickets?.map((ticket, ticketIndex) =>
                    ticket?.bookingAirTicketPayments?.map((payment, paymentIndex) => {
                      const isMatching = payment.paymentType === "CARD" && travel?.bookingAir?.gdsType === "AMADEUS";
                      return (
                        isMatching && (
                          <tr key={`${ticketIndex}-${paymentIndex}`}>
                            <td>{ticket.ticketNo}</td>
                            <td>{payment?.airFareType?.value}</td>
                            {payment?.voidType !== "Emd" && payment?.voidType !== "EmdVoid" && payment?.voidType !== "EmdRefund" && (
                              <td>
                                <button type="button" className="btn-default btn-view-receipt" onClick={() => viewCrsCardReceipt("user", ticket?.id)}>
                                  보기
                                </button>
                              </td>
                            )}
                          </tr>
                        )
                      );
                    }),
                  )}

                  {paymentList.map(
                    (payment, paymentIndex) =>
                      payment?.paymentStatus !== "ERROR" &&
                      payment?.gubun !== "GROUP_DEPOSIT" &&
                      payment?.gubun !== "VISA_DEPOSIT" &&
                      payment?.gubun !== "GROUP_REFUND" &&
                      payment?.gubun !== "VISA_REFUND" &&
                      payment?.gubun !== "MULTI_DEPOSIT" &&
                      payment?.gubun !== "MULTI_REFUND" &&
                      payment.gubun !== "REFUND" &&
                      payment?.gubun !== "DOMESTIC_REFUND" &&
                      payment?.cardList?.map((card, cardIndex) => {
                        return (
                          <tr key={`${paymentIndex}-${cardIndex}`}>
                            <td>{card?.goodName}</td>
                            <td>
                              {payment?.paymentChargeList?.map((charge, chargeIndex) => (
                                <Fragment key={chargeIndex}>
                                  {CHARGE_TYPE[charge?.chargeType]}
                                  {chargeIndex < payment?.paymentChargeList?.length - 1 && <br />}
                                </Fragment>
                              ))}
                            </td>
                            <td>
                              <button
                                type="button"
                                className="btn-default btn-view-receipt"
                                onClick={() => pgReceiptView(card?.tId, card?.approvalNumber)}
                              >
                                보기
                              </button>
                            </td>
                          </tr>
                        );
                      }),
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
