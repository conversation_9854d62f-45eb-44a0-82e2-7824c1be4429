import { Fragment, useEffect, useState } from "react";
import { Dialog } from "@mui/material";
import IconInfoFill from "@/assets/images/svg/ic-info-fill.svg";
import Button from "@/app/components/user_v2/common/Button";
import { useAppSelector } from "@/store";
import { selectFilterHotel, selectHotelCancelRes, selectHotelExchangeRate, selectHotelView } from "@/store/hotelSlice";
import { momentKR } from "@/utils/date";
import { CANCEL_RES_RULE_CLASSES, Y_N } from "@/constants/app";
import { comma } from "@/utils/common";
import IconClose from "@/assets/images/svg/ic-close.svg";
import { hotelBooking, getPriceByExchangeRate } from "@/utils/app";

const PopCancelResRule = (props) => {
  const { open, setOpen, isBooking = false, selectedRoom } = props;

  const { hotelContent } = useAppSelector(selectHotelView);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const filter = useAppSelector(selectFilterHotel);
  const hotelCancelRes = useAppSelector(selectHotelCancelRes);

  const [option, setOption] = useState({
    isCancelDeadline: false,
    date: "",
    roomCancelChargeList: [],
    roomSalePrice: 0,
  });

  function handleClose() {
    setOpen(false);
  }

  function action() {
    if (!isBooking) handleClose();
    else hotelBooking(hotelContent, selectedRoom, filter);
  }

  useEffect(() => {
    if (hotelCancelRes?.roomJson && open) {
      const xRoomJson = JSON.parse(hotelCancelRes.roomJson);
      const cancelDeadLine = xRoomJson.cancelDeadLine;
      const displayCancelDate = xRoomJson.displayCancelDate;
      const roomCancelChargeList = xRoomJson.roomCancelChargeList;
      const roomSalePrice = xRoomJson.roomSalePrice;
      let cancelDeadLineYes = false;
      let year, month, day;
      const today = momentKR().format("YYYYMMDD");

      if (displayCancelDate.length > 0 && displayCancelDate >= today) {
        cancelDeadLineYes = true;
        year = displayCancelDate.slice(0, 4);
        month = displayCancelDate.slice(4, 6);
        day = displayCancelDate.slice(6, 8);
      } else if (cancelDeadLine.length > 0 && cancelDeadLine >= today) {
        cancelDeadLineYes = true;
        year = cancelDeadLine.slice(0, 4);
        month = cancelDeadLine.slice(4, 6);
        day = cancelDeadLine.slice(6, 8);
      }

      if (cancelDeadLineYes) {
        if (roomCancelChargeList !== null) {
          const cancelFirstItem = roomCancelChargeList[0];
          if (cancelFirstItem.cancelAvailableYn == "Y") {
            const displayCancelDate = cancelFirstItem.toDateText;
            year = displayCancelDate.slice(0, 4);
            month = displayCancelDate.slice(4, 6);
            day = displayCancelDate.slice(6, 8);
          }
        }
      }

      setOption({
        isCancelDeadline: cancelDeadLineYes,
        date: `${year}.${month}.${day}`,
        roomCancelChargeList,
        roomSalePrice,
      });
    } else {
      setOption({
        isCancelDeadline: false,
        date: "",
        roomCancelChargeList: [],
        roomSalePrice: 0,
      });
    }
  }, [hotelCancelRes, open]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      className="custom-dialog"
      sx={{ "& .MuiDialog-container": { "& .MuiPaper-root": { maxWidth: "1200px" } } }}
    >
      <div id="popCancelRule" className="rounded-[16px] w-[440px] modal-wrap max-w-none modal block outline-none bg-white shadow-none p-0 h-full">
        <div className="modal-head !h-[64px] !p-[16px] flex">
          <h2 className="!p-0 !text-[16px] !font-bold !leading-normal">취소 규정 안내</h2>
        </div>
        <div className="modal-cotn !px-[0px] !pb-[16px] overflow-y-auto max-h-[calc(100vh-70px-160px)]">
          {option.isCancelDeadline && (
            <div className="mb-[16px] !px-[16px]">
              <div className="flex !justify-normal !items-start gap-[6px]">
                <IconInfoFill className="[&_path]:fill-custom-gray-500 shrink-0 w-[16px]" />
                <div>
                  <p className="font-medium text-custom-gray-600 leading-[21px]">해당 객실의 무료취소기한은</p>
                  <p className="font-medium text-custom-gray-600 leading-[21px]">
                    <span className="text-custom-blue-100">
                      {option.roomCancelChargeList?.[0]?.cancelAvailableYn === Y_N.Y
                        ? momentKR(option.roomCancelChargeList?.[0]?.toDateText, "YYYYMMDD").format("YYYY년 MM월 DD일 17:00시")
                        : momentKR(option.date, "YYYY.MM.DD").format("YYYY년 MM월 DD일 17:00시")}
                    </span>{" "}
                    까지 입니다.
                  </p>
                </div>
              </div>

              <div className="mt-[8px]">
                <div className="flex gap-[2px] !items-start">
                  <span className="text-custom-gray-1000">•</span>
                  <span className="text-[12px] text-custom-gray-300">
                    무료취소기한 이전에 취소하시면 수수료 없이 전액 환불되며, <br />
                    무료취소기한 이후 취소 또는 변경 시 취소수수료가 적용되어 환불되지 않습니다.
                  </span>
                </div>
              </div>
            </div>
          )}

          <div className="!px-[16px]">
            <div className="grid grid-cols-2 bg-custom-bg-100 rounded-[8px] py-[12px] text-custom-gray-600 font-medium">
              <p className="text-center">취소시점</p>
              <p className="text-center">수수료 금액</p>
            </div>
            {!option.isCancelDeadline ? (
              <div className="grid grid-cols-2 rounded-[8px] py-[12px] text-custom-gray-600 font-medium">
                <p className="text-center">취소불가</p>
                <p className="text-center">환불불가</p>
              </div>
            ) : option.roomCancelChargeList?.[0]?.cancelAvailableYn === Y_N.Y ? (
              option.roomCancelChargeList.map((roomCancelChargeListElement, index) => {
                const fromDate = roomCancelChargeListElement.fromDateText;
                const fromYear = fromDate.slice(0, 4);
                const fromMonth = fromDate.slice(4, 6);
                const fromDay = fromDate.slice(6, 8);

                const toDate = roomCancelChargeListElement.toDateText;
                const toYear = toDate.slice(0, 4);
                const toMonth = toDate.slice(4, 6);
                const toDay = toDate.slice(6, 8);

                let roomSalePrice = option.roomSalePrice;
                const percent = roomCancelChargeListElement.percent;
                const price = roomCancelChargeListElement.price;

                if (price != null) {
                  roomSalePrice = price;
                } else {
                  roomSalePrice = (roomSalePrice * percent) / 100;
                }
                return (
                  <div key={index} className="grid grid-cols-2 rounded-[8px] py-[12px] text-custom-gray-600 font-medium">
                    <p className="text-center">
                      {fromYear}.{fromMonth}.{fromDay} ~ {toYear}.{toMonth}.{toDay}
                    </p>
                    <p className="text-center">
                      {roomSalePrice > 0
                        ? `${exchangeRate > 1 ? `$${comma(getPriceByExchangeRate(roomSalePrice, exchangeRate))}` : `${comma(roomSalePrice)}원`}`
                        : "없음"}
                    </p>
                  </div>
                );
              })
            ) : (
              <Fragment>
                <div className="grid grid-cols-2 rounded-[8px] py-[12px] text-custom-gray-600 font-medium">
                  <p className="text-center">{option.date} 17:00 까지</p>
                  <p className="text-center">없음</p>
                </div>
                <div className="grid grid-cols-2 rounded-[8px] py-[12px] text-custom-gray-600 font-medium">
                  <p className="text-center">{option.date} 17:00 부터</p>
                  <p className="text-center">환불불가</p>
                </div>
              </Fragment>
            )}
          </div>
          <div className="!px-[16px] pt-[16px] shadow-custom-500">
            <Button onClick={action} className="btn-primary w-full bg-custom-blue-100 border-none text-white font-bold !item-center !justify-center">
              {isBooking ? "예약 진행하기" : "확인"}
            </Button>
          </div>
        </div>
        <Button className="btn-icon absolute !min-w-[32px] !h-[32px] !right-[16px] !top-[16px]" onClick={handleClose}>
          <IconClose />
        </Button>
      </div>
    </Dialog>
  );
};

export default PopCancelResRule;
