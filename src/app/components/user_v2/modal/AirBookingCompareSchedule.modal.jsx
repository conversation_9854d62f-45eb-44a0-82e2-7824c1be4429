import { useState, useMemo, useEffect, Fragment } from "react";
import { isEmpty, cloneDeep, set, uniq } from "lodash";
import { useSelector, useDispatch } from "react-redux";
import { Modal } from "@mui/material";
import { SECTION_TYPE } from "@/constants/app";
import DateUtil from "@/utils/dateUtil";
import { selectAirOverseasSessionData, actionSetAirOverseasSessionData } from "@/store/airSlice";
import AirUtil from "@/utils/airUtil.js";
import { momentKR } from "@/utils/date";
import { comma, onErrorImgAirline } from "@/utils/common";
import { COP_CODE } from "@/constants/app";
import LowestSchedule from "@/app/components/user_v2/modal/LowestSchedule.modal";
import FlightDetail from "@/app/components/user_v2/modal/FlightDetail.modal";
import FareRule from "@/app/components/user_v2/modal/FareRule.modal";
import useReservationNoticing from "@/app/hooks/useReservationNoticing";
import { getFareRule } from "@/service/air";
import { actionIncreaseLoadingCount, actionDecreaseLoadingCount } from "@/store/loadingUserSlice";
import { selectUserInfo } from "@/store/userSlice";
import { postCalcCommission } from "@/service/common";
import AIR_ICONS_PATH from "@/assets/airIcon";
import { nvl } from "@/common";

const MAX_AIR_COMPARISONS = 3;

const AirBookingCompareSchedule = (props) => {
  const { type = "DEFAULT", defaultSelect, selectedCompareFares, setSelectedCompareFares, open, setOpen } = props;

  const dispatch = useDispatch();
  const { userInfo } = useSelector(selectUserInfo);
  const reservationNoticing = useReservationNoticing();
  const { filter, airCompareSchedules, lowestFareAndSchedule } = useSelector(selectAirOverseasSessionData);

  const [isLoaded, setIsLoaded] = useState(false);
  const [viewAllDetail, setViewAllDetail] = useState(false);
  const [openLowestFare, setOpenLowestFare] = useState(false);
  const [flightDetail, setFlightDetail] = useState({});
  const [fareRules, setFareRules] = useState([]);
  const [airCompares, setAirCompares] = useState([]);
  const [selectedCompareFare, setSelectedCompareFare] = useState({});

  const canBooking = useMemo(() => isEmpty(selectedCompareFare) || airCompares.length !== MAX_AIR_COMPARISONS, [selectedCompareFare, airCompares]);
  const isDefault = type === "DEFAULT";
  const totalLowestFare = useMemo(() => {
    if (isEmpty(lowestFareAndSchedule)) return 0;
    const paxTypeFaresDisplay = lowestFareAndSchedule.fares.paxTypeFares[0];
    const airFare = Number(paxTypeFaresDisplay.airFare);
    const tax = Number(paxTypeFaresDisplay.airTax) + Number(paxTypeFaresDisplay.fuelChg);
    const ticketFare = Number(paxTypeFaresDisplay?.tasfAmount ?? 0); // Update later!
    return airFare + tax + ticketFare;
  }, [lowestFareAndSchedule]);

  const airPortInfo = useMemo(() => {
    if (isEmpty(filter))
      return { departureAirportCodes: "", departureAirportNames: "", arrivalAirportCodes: "", arrivalAirportNames: "", departYmd: "", returnYmd: "" };
    const { sectionType, departureAirportNames, departureAirportCodes, arrivalAirportNames, departureDays, arrivalAirportCodes } = filter;
    const isMultiCity = sectionType === SECTION_TYPE.MULTICITY;
    return {
      departureAirportCodes: departureAirportCodes[0],
      departureAirportNames: departureAirportNames[0],
      arrivalAirportCodes: isMultiCity ? arrivalAirportCodes[arrivalAirportCodes.length - 1] : arrivalAirportCodes[0],
      arrivalAirportNames: isMultiCity ? arrivalAirportNames[arrivalAirportNames.length - 1] : arrivalAirportNames[0],
      departYmd: departureDays[0],
      returnYmd: departureDays[departureDays.length - 1],
    };
  }, [filter]);

  function getSeatTypeCode(types) {
    const seatTypeCodeArray = ["M", "W", "C", "F"];
    let position = 0;
    types.forEach((type) => {
      const index = seatTypeCodeArray.indexOf(type);
      if (index > position) position = index;
    });

    return seatTypeCodeArray[position];
  }

  function booking() {
    if (isEmpty(selectedCompareFare) || airCompares.length !== MAX_AIR_COMPARISONS) return;
    const payload = {
      airCompareSchedules: airCompares,
      data: selectedCompareFare,
    };
    dispatch(actionSetAirOverseasSessionData(payload));
    reservationNoticing.overseasShow();
  }

  function addNewAirCompare(air) {
    const foundAir = airCompares.find((item) => item.id === air.id);
    if (airCompares.length === MAX_AIR_COMPARISONS) {
      alert("선택 가능한 항공편 개수가 초과되었습니다.\n'비교함'에서 삭제 하신 후 추가 하실 수 있습니다.");
      return;
    }

    if (!isEmpty(foundAir)) {
      alert("이미 추가된 항공편입니다.");
      return;
    }
    setAirCompares([...airCompares, air]);
    setOpenLowestFare(false);
  }

  function handleClose() {
    setOpen(false);
    setIsLoaded(false);
    setViewAllDetail(false);
    setSelectedCompareFare({});
  }

  function getTime(segments) {
    let totalTime = 0;
    segments.forEach((segment) => {
      totalTime += Number(segment.waitingTime) + Number(segment.flightTime);
    });
    return convertMinutesToHoursAndMinutes(totalTime);
  }

  function convertMinutesToHoursAndMinutes(totalMinutes) {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours}시간 ${minutes ? `${minutes}분` : ""}`;
  }

  function removeAirCompares(flight) {
    if (!isEmpty(selectedCompareFares)) {
      setSelectedCompareFares((prev) => {
        return prev.filter((item) => item.id !== flight.id);
      });
    } else {
      setAirCompares(airCompares.filter((item) => item.id !== flight.id));
    }
  }

  function getLeftTitleHtml(className, title) {
    return (
      <div className={`cell-row ${className}`}>
        <p className="tit-01"> {title}</p>
        <div className="inner">
          <dl>
            <dt className="leading-[18px]">기본정보</dt>
            <dd className="mb-0">
              <p className="air box-content !pb-10" style={{ height: "40px" }}>
                항공편
              </p>
              <p className="seat">좌석</p> <p className="overstop">경유</p>
            </dd>
          </dl>
          <dl className="plan">
            <dt className="leading-[18px]">여정정보</dt>
            <dd className="mb-0">
              <p>출발</p> <p>도착</p> <p>탑승시간</p> <p>경유</p> <p>총 소요시간</p>
            </dd>
          </dl>
        </div>
      </div>
    );
  }

  async function openFareRuleModal(journey) {
    try {
      dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
      const payload = {
        journeys: journey.flights.map((flight) => {
          const { airline, deptAirport, arrAirport, departureDate, journeyKey, fares, pairKey } = flight;
          return {
            journeyKey,
            fareKey: fares.fareKey,
            pairKey,
            airline,
            deptAirport,
            arrAirport,
            departureDate,
            promotionId: "",
            fopPromotionId: "",
          };
        }),
      };
      const response = await getFareRule(payload);
      setFareRules(response.data?.data?.journeys);
    } catch (error) {
      console.log(error);
    } finally {
      dispatch(actionDecreaseLoadingCount("loadingDefaultCount"));
    }
  }

  useEffect(() => {
    async function fetchCommission() {
      const selectedItems = !isEmpty(airCompareSchedules) ? airCompareSchedules : selectedCompareFares;

      try {
        let flights = selectedItems;
        if (!isEmpty(lowestFareAndSchedule)) {
          flights = [...flights, lowestFareAndSchedule];
        }
        const payload = flights.map((air) => {
          const fare = air.fares.paxTypeFares[0];
          const cabinClasses = uniq(air.flights.map((el) => el.segments.map((seg) => seg.cabinClass)).flat());
          return {
            fareAmount: fare.airFare,
            discountAmount: 0,
            taxAmount: fare.airTax,
            seatTypeCode: getSeatTypeCode(cabinClasses),
          };
        });
        const response = await postCalcCommission(payload);
        const data = response.data.values;
        setAirCompares(
          selectedItems.map((fare, index) => {
            return set(fare, "fares.paxTypeFares[0].tasfAmount", data[index]);
          }),
        );
        if (!isEmpty(lowestFareAndSchedule)) {
          dispatch(
            actionSetAirOverseasSessionData({
              lowestFareAndSchedule: set(cloneDeep(lowestFareAndSchedule), "fares.paxTypeFares[0].tasfAmount", data[data.length - 1]),
            }),
          );
        }
        setIsLoaded(true);
      } catch (error) {
        console.log(error);
        setOpen(false);
      }
    }
    if (open) {
      fetchCommission();
    }
  }, [open, airCompareSchedules, selectedCompareFares]);

  useEffect(() => {
    if (!isEmpty(defaultSelect)) {
      setSelectedCompareFare(defaultSelect);
    }
  }, [defaultSelect]);

  return (
    <Fragment>
      <Modal open={isLoaded} onClose={handleClose}>
        <div
          id="popPlaneCompare"
          className="modal-wrap modal block max-w-none p-0 outline-none bg-white relative w-[1196px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
        >
          <div className="modal-head">
            <h2>항공 스케줄 비교</h2>
          </div>
          <div className="modal-cotn" id="airCompareScheduleLayerData">
            <div className="city-date">
              <p className="dep">
                {airPortInfo.departureAirportNames} ({airPortInfo.departureAirportCodes})
              </p>
              <p className="arr">
                {airPortInfo.arrivalAirportNames} ({airPortInfo.arrivalAirportCodes})
              </p>
              <div className="date" style={{ paddingRight: 13 }}>
                {DateUtil.getPatternYmd(airPortInfo.departYmd, ".")} ({DateUtil.getDayOfWeekName(DateUtil.getPatternYmd(airPortInfo.departYmd, "-"))})
                - {DateUtil.getPatternYmd(airPortInfo.returnYmd, ".")} (
                {DateUtil.getDayOfWeekName(DateUtil.getPatternYmd(airPortInfo.returnYmd, "-"))})
              </div>
              {userInfo?.workspace?.company?.btmsSetting?.isLowestPrice && isDefault && (
                <div className="date text-[#ff4e50] cursor-pointer underline underline-offset-2" onClick={() => setOpenLowestFare(true)}>
                  최저가 요금 : {comma(totalLowestFare)}원
                </div>
              )}
            </div>
            <p className="desc-info">ⓘ 비교견적표의 요금은 실제 예약된 금액이 아니며 담당자의 최종 확인이 필요합니다.</p>
            <div className="inner-scoll">
              <div className="box-scroll">
                <div className={`list-schdule ${viewAllDetail ? "view-all" : ""}`}>
                  <div className="cell-col box-head">
                    <p className="title box-content">선호하는 스케줄을 선택해 주세요.</p>
                    {filter?.sectionType === SECTION_TYPE.MULTICITY ? (
                      filter?.departureAirportCodes?.map((departCode, index) => {
                        return getLeftTitleHtml(index === 0 ? "dep" : "arrs", "여정 " + (index + 1));
                      })
                    ) : (
                      <Fragment>
                        {getLeftTitleHtml("dep", "가는편")}
                        {filter?.sectionType === SECTION_TYPE.ROUNDTRIP && getLeftTitleHtml("arrs", "오는편")}
                      </Fragment>
                    )}
                    <div className="cell-row fee">
                      <p className="tit-01">요금정보</p>
                      <div className="inner">
                        <dl>
                          <dd className="mb-0">총 예상요금 (성인1)</dd>
                        </dl>
                      </div>
                    </div>
                    <div className="cell-row fare">
                      <p className="tit-01">운임규정</p>
                      <div className="inner">
                        <dl>
                          <dd className="mb-0">
                            <p>무료 수화물</p> <p>취소/환불 규정</p>
                          </dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                  {airCompares.map((air, index) => {
                    const { id, flights, fares } = air;
                    const paxTypeFaresDisplay = fares.paxTypeFares[0];
                    const airFare = Number(paxTypeFaresDisplay.airFare);
                    const tax = Number(paxTypeFaresDisplay.airTax) + Number(paxTypeFaresDisplay.fuelChg);
                    const ticketFare = Number(paxTypeFaresDisplay?.tasfAmount ?? 0); // Update later!
                    const fareTypes = fares.fareTypes;
                    const includedCorporateFare = fareTypes.some((type) => type.includes(COP_CODE));
                    const combinedQuantity = flights
                      .map((el) => {
                        const foundedBaggage = el.freessrs.find((freessr) => freessr.ssrType === "Baggage" && freessr.ssrService[0].ssrAmount === 0);
                        const quantity = foundedBaggage?.ssrService[0]?.ssrValue || 0;
                        const unit = foundedBaggage?.ssrUnit || "";
                        return `${quantity}${unit}`;
                      })
                      .join(", ");
                    return (
                      <div key={id} className={`cell-col schdule-0${index + 1} ${selectedCompareFare?.id === id ? "active" : ""}`}>
                        <div className="box-radio">
                          <label className="form-chkbox">
                            <input
                              checked={selectedCompareFare?.id === id}
                              type="checkbox"
                              onClick={() => isDefault && setSelectedCompareFare(air)}
                              onChange={() => {}}
                            />
                            <span>스케줄 {index + 1}</span>
                          </label>
                        </div>
                        {flights.map((flight, index) => {
                          const { arrAirport, deptAirport, segments, fares, stops, departureDate, arrivalDate } = flight;
                          const flightNumbers = segments.map((segment) => segment.flightNumber + "편").join(", ");
                          const fullRbd = segments.map((el) => el.bookingClass).join("");
                          const availableCount = fares.availableCount;
                          const airline = segments[0].carrierCode;
                          const airlineName = uniq(segments.map((el) => el.carrierCodeName));

                          const operatingCarrier = segments[0].legs[0].operatingCarrier;
                          const operatingCarrierName = nvl(segments[0].legs[0].operatingCarrierName, "");
                          const isDifferentCarrier = operatingCarrier !== airline;

                          const isMultiAirline = airlineName.length > 1;
                          const departureTerminal = segments[0].legs[0].departureTerminal;
                          const arrivalTerminal = segments[segments.length - 1].legs[segments[segments.length - 1].legs.length - 1].arrivalTerminal;
                          const flightTime = segments.reduce((total, segment) => total + Number(segment.flightTime), 0);
                          const destinationThrough = segments
                            .slice(0, segments.length - 1)
                            .map((el) => `${el.arrAirportName} (${el.arrAirport})`)
                            .join(", ");

                          return (
                            <Fragment key={flight.id}>
                              <div className="info-default">
                                <p className={`compareIterneray_${index} box-content`} style={{ height: isDifferentCarrier ? 40 : 59 }}>
                                  <img
                                    src={isMultiAirline ? AIR_ICONS_PATH.TwoFlight : AIR_ICONS_PATH[airline] || ""}
                                    onError={onErrorImgAirline}
                                    className="airlineImg w-[30px] h-[20px] object-contain inline-block top-0 float-left"
                                    style={{
                                      transform: isMultiAirline && "scale(1.15)",
                                    }}
                                  />
                                  <span dangerouslySetInnerHTML={{ __html: airlineName.join("<br/>") }} className="name" />{" "}
                                  <span className="code ">{flightNumbers}</span>
                                </p>
                                {/* {!isMultiAirline && ( */}
                                <span className="relative text-[12px] text-[#4b75bf] leading-[17px]">
                                  {isDifferentCarrier && (
                                    <>
                                      [실제운항] {operatingCarrierName}
                                      {!!operatingCarrier && `(${operatingCarrier})`}
                                    </>
                                  )}
                                </span>
                                {/* )} */}
                                <p>
                                  {AirUtil.getSeatTypeName(segments[0].cabinClass)}({fullRbd}) / {AirUtil.getSeatWaitingName(!availableCount)}
                                </p>
                                {!stops ? (
                                  <p>{AirUtil.getSeatWaitingName(availableCount)}</p>
                                ) : (
                                  <p>
                                    {stops}회 경유
                                    <a className="relative text-[12px] text-[#4b75bf] leading-[17px]" onClick={() => setFlightDetail(flights)}>
                                      [상세보기]
                                    </a>
                                  </p>
                                )}
                              </div>
                              <div className="info-plan box-content active">
                                {!viewAllDetail && (
                                  <a className="btn-default" onClick={() => setViewAllDetail(true)}>
                                    자세히보기
                                  </a>
                                )}
                                <ul>
                                  <li className="leading-[18px]">
                                    {deptAirport} {departureTerminal ? `T${departureTerminal}` : ""} /{" "}
                                    {momentKR(departureDate).format("YYYY-MM-DD HH:mm")}
                                  </li>
                                  <li className="leading-[18px]">
                                    {arrAirport} {arrivalTerminal ? `T${arrivalTerminal}` : ""} / {momentKR(arrivalDate).format("YYYY-MM-DD HH:mm")}
                                  </li>
                                  <li className="leading-[18px]">{convertMinutesToHoursAndMinutes(flightTime)}</li>
                                  <li className={`leading-[18px] line-clamp-1 min-h-[18px] ${stops ? "" : "invisible"}`}>
                                    {stops ? destinationThrough : "stops"}
                                  </li>
                                  <li className="leading-[18px]">{getTime(segments)}</li>
                                </ul>
                              </div>
                            </Fragment>
                          );
                        })}
                        <p className="info-fee">
                          <strong>
                            <span>{comma(airFare + tax + ticketFare)}</span> 원
                          </strong>
                          {includedCorporateFare ? "/ 기업요금" : ""}
                        </p>
                        <div className="info-rule" style={{ height: 75 }}>
                          <p className="baggage">{combinedQuantity}</p>
                          <div className="refund">
                            {isDefault && (
                              <a className="btn-default" onClick={() => openFareRuleModal(air)}>
                                운임규정
                              </a>
                            )}
                          </div>
                        </div>
                        {isDefault && (
                          <button type="button" className="btn-default btn-delete" onClick={() => removeAirCompares(air)}>
                            삭제
                          </button>
                        )}
                      </div>
                    );
                  })}
                  {Array.from({ length: MAX_AIR_COMPARISONS - airCompares.length }).map((_, index) => {
                    return (
                      <div key={index} className={`cell-col schdule-0${airCompares.length + index + 1}`}>
                        <div className="box-radio">
                          <label className="form-chkbox">
                            <input type="checkbox" disabled />
                            <span>스케줄{airCompares.length + index + 1}</span>
                          </label>
                        </div>
                        <div className="btn-add-col">
                          <button type="button" className="btn-default" onClick={handleClose}>
                            + 추가
                          </button>
                        </div>
                        <button type="button" className="btn-default btn-delete" disabled>
                          삭제
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            {isDefault && (
              <div className="btn-request-reserv" id="airCompareScheduleBtnArea">
                <a
                  className={`btn-default ${canBooking ? "off" : "on"}`}
                  style={{ cursor: canBooking ? "default" : "pointer" }}
                  onClick={booking}
                  disabled={canBooking}
                >
                  예약요청
                </a>
              </div>
            )}
          </div>
          <a className="close-modal" onClick={handleClose}>
            Close
          </a>
        </div>
      </Modal>

      <LowestSchedule open={openLowestFare} setOpen={setOpenLowestFare} addNewAirCompare={addNewAirCompare} />
      <FlightDetail flightDetail={flightDetail} setFlightDetail={setFlightDetail} />
      <FareRule fareRules={fareRules} setFareRules={setFareRules} />
    </Fragment>
  );
};

export default AirBookingCompareSchedule;
