import { useAppDispatch, useAppSelector } from "@/store";
import { selectFlightSetting } from "@/store/flightSettingSlice";
import { actionGetFlightSetting } from "@/store/flightSettingSlice";
import { selectUserInfo } from "@/store/userSlice";
import { returnPhoneNumber } from "@/utils/common";
import CloseIcon from "@mui/icons-material/Close";
import { Modal } from "@mui/material";
import moment from "moment";
import { useEffect, useState } from "react";
import Button from "../common/Button";

const PASSENGER_INFO_TABLE_HEAD = [
  {
    label: "구분",
    key: "type",
  },
  {
    label: "영문성",
    key: "lastName",
  },
  {
    label: "영문이름",
    key: "firstName",
  },
  {
    label: "성별",
    key: "gender",
  },
  {
    label: "생년월일",
    key: "birthday",
  },
  {
    label: "여권 만료 기간",
    key: "passportExpiredDate",
  },
];

const RESERVER_INFO_TABLE_HEAD = [
  {
    id: "name",
    label: "예약자명",
  },
  {
    id: "cellPhoneNumber",
    label: "휴대폰 번호",
  },
  {
    id: "email",
    label: "이메일",
  },
];

export default function FlightConfirmBookingModal(props) {
  const { open, setOpen, bookingUserDTOs, onSubmit } = props;
  const dispatch = useAppDispatch();
  const { userInfo } = useAppSelector(selectUserInfo);
  const contents = useAppSelector(selectFlightSetting);

  const [listContents, setListContents] = useState([]);
  const handleClose = () => setOpen(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
    setOpen(false);
  };

  useEffect(() => {
    dispatch(actionGetFlightSetting(userInfo?.workspace?.company?.id));
  }, [dispatch, userInfo?.workspace?.company?.id]);

  useEffect(() => {
    if (contents) {
      setListContents(JSON.parse(contents));
    }
  }, [contents]);

  return (
    <Modal open={open} onClose={handleClose} className="flex justify-center items-center">
      <div className="bg-white rounded-[20px]">
        <div className="flex justify-between p-[16px] h-[64px]">
          <span className="font-bold text-[16px] leading-[150%]">예약내역 최종 확인</span>
          <button onClick={handleClose}>
            <CloseIcon style={{ color: "#6E7587" }} />
          </button>
        </div>
        <div className="flex flex-col gap-[16px] px-[16px] max-w-[700px] w-[670px]">
          <div className="flex flex-col gap-[8px] items-start w-full">
            <span className="text-left font-bold text-[14px] w-full">탑승객 정보</span>
            <div className="flex flex-col gap-[4px] !items-start w-full">
              <div className="flex gap-[4px]">
                <span className="block w-[4px] h-[4px] bg-[#AFB3BD] rounded-full shrink-0" />
                <span className="break-words leading-[150%]">
                  탑승객 영문명, 생년월일, 여권번호, 여권 유효기간(6개월 이상) 등 다시 한 번 확인해주시기 바랍니다.
                </span>
              </div>
              {listContents &&
                listContents.length > 0 &&
                listContents.map((content, index) => (
                  <div key={index} className="flex gap-[4px] w-full justify-start">
                    <span className="block w-[4px] h-[4px] bg-[#AFB3BD] rounded-full shrink-0" />
                    <span style={{ wordBreak: "break-all leading-[150%]" }}>{content}</span>
                  </div>
                ))}
            </div>
            <div className="w-full">
              <table className="w-full my-[16px]">
                <thead>
                  <tr className="h-[40px]">
                    {PASSENGER_INFO_TABLE_HEAD.map((head, index) => (
                      <th
                        key={head.id}
                        className={`font-medium text-[14px] text-[#292C32] leading-[150%] bg-[#242A3708] 
                          ${index === 0 || index === PASSENGER_INFO_TABLE_HEAD.length - 1 ? "flex h-[40px] items-center !justify-center" : ""}
                          ${index === 0 ? "rounded-tl-[8px] rounded-bl-[8px]" : ""}
                          ${index === PASSENGER_INFO_TABLE_HEAD.length - 1 ? "rounded-tr-[8px] rounded-br-[8px]" : ""}
                          `}
                      >
                        {head.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {bookingUserDTOs.map((data, index) => {
                    return (
                      <tr className="h-[40px] text-center border-b border-[#E9EAED] leading-[150%]" key={data.key}>
                        <td className="w-[15%]">{`성인 ${index + 1}`}</td>
                        <td className="w-[15%]">{data.lastName ? data.lastName.toUpperCase() : ""}</td>
                        <td className="w-[15%]">{data.firstName ? data.firstName.toUpperCase() : ""}</td>
                        <td className="w-[15%]">{data.gender === "Male" ? "남" : "여"}</td>
                        <td className="w-[20%]">{moment(data.birthday).format("YYYY-MM-DD")}</td>
                        <td className="w-[20%]">{moment(data.customerPassport.expireYmd).format("YYYY-MM-DD")}</td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
            <div className="flex flex-col gap-[16px] items-start w-full">
              <span className="font-bold text-[14px] leading-[150%] w-full">예약자 정보</span>
              <div className="w-full">
                <table className="w-full">
                  <thead>
                    <tr className="h-[40px]">
                      {RESERVER_INFO_TABLE_HEAD.map((head, index) => (
                        <th
                          key={head.id}
                          className={`font-medium text-[14px] text-[#292C32] leading-[150%] bg-[#242A3708] 
                            ${index === 0 ? "rounded-tl-[8px] rounded-bl-[8px]" : ""}
                            ${index === RESERVER_INFO_TABLE_HEAD.length - 1 ? "rounded-tr-[8px] rounded-br-[8px]" : ""}
                          `}
                        >
                          {head.label}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="h-[40px] text-center">
                      <td>{userInfo?.name ? userInfo?.name.toUpperCase() : ""}</td>
                      <td className="w-1/3">{returnPhoneNumber(userInfo?.cellPhoneNumber, "ALL")}</td>
                      <td className="w-1/3">{userInfo?.email}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-center border-t border-[#E9EAED] gap-[8px] p-[16px]">
          <Button
            className="flex-1 btn-third !justify-center h-[48px] font-bold text-custom-gray-300 bg-custom-gray-700 !border-none "
            onClick={handleClose}
          >
            다시입력
          </Button>
          <Button className="flex-1 btn-primary !justify-center h-[48px] font-bold text-white bg-custom-blue-100 !border-none" onClick={handleSubmit}>
            확인완료
          </Button>
        </div>
      </div>
    </Modal>
  );
}
