import { Fragment } from "react";
import { isEmpty } from "lodash";
import { useAppDispatch, useAppSelector } from "@/store";
import { actionDecreaseLoadingCount, actionIncreaseLoadingCount } from "@/store/loadingUserSlice";
import { selectReservationTravelView } from "@/store/travelViewSlice";
import request from "@/utils/request";
import { Modal } from "@mui/material";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

export default function ETicket(props) {
  const { open, setOpen } = props;
  const {
    data: { travel },
    eticketListMap,
  } = useAppSelector(selectReservationTravelView);
  const dispatch = useAppDispatch();

  function handleClose() {
    setOpen(false);
  }

  function viewTicket(event, ticket) {
    if (ticket.gdsType === "DIRECT" && !ticket?.eticket4Direct) {
      event.preventDefault();
      return;
    }
    const eticket4DirectFilePath = ticket?.eticket4Direct;
    if (eticket4DirectFilePath) {
      const eTicketUrl = `${import.meta.env.VITE_STORAGE_URL}/btms/${eticket4DirectFilePath}`;
      window.open(eTicketUrl, "_blank");
      return;
    }
    const eTicketUrls =
      travel?.bookingAir?.gdsType === "SABRE" ? Object.entries(eticketListMap).flatMap(([key, value]) => value.map((item) => item.eticket)) : [];
    const sabreEticketUrl = ticket?.eticket;
    if (eTicketUrls.length > 0 && typeof sabreEticketUrl != "undefined" && sabreEticketUrl != "") {
      window.open(sabreEticketUrl, "eticket_" + ticket.id);
      return;
    }
    if (eTicketUrls.length > 0) {
      alert(`E-Ticket SABRE 발행 오류.${CONTACT_ADMIN_ALERT}`);
      return;
    }
    dispatch(actionIncreaseLoadingCount("loadingDefaultCount"));
    request({ url: `/user/travels/${travel.id}/ticket/link/${ticket?.id}`, method: "GET" })
      .then(function (response) {
        const url = response.data;
        if (!url) {
          alert(`E-Ticket 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        if (url.indexOf("Error") > 0 || url.indexOf("Exception") > 0) {
          alert(`E-Ticket 응답 Link URL 오류.${CONTACT_ADMIN_ALERT}`);
          return;
        }
        window.open(url, "eticket_" + ticket?.id);
      })
      .catch(() => alert(`E-Ticket 발행 오류.${CONTACT_ADMIN_ALERT}`))
      .finally(() => dispatch(actionDecreaseLoadingCount("loadingDefaultCount")));
  }

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popEticket"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[576px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>E-Ticket</h2>
        </div>
        <div className="modal-cotn">
          <div className="desc">
            • 항공권은 이메일을 통해 전자항공권(E-Ticket)으로 발송됩니다. <br />• 탑승시간 최소 2시간 전에 체크인을 해 주시기 바랍니다. <br />•
            E-Ticket 확인서는 구간별 1 장이 필요합니다.
          </div>
          <div className="table-cmm type-fare">
            <table>
              <colgroup>
                <col style={{ width: 30 }} />
                <col style={{ width: "*" }} />
                <col style={{ width: 100 }} />
              </colgroup>
              <thead>
                <tr>
                  <th>{/*<label class="form-chkbox"><input type="checkbox"><span></span></label>*/}</th>
                  <th className="left">영문 이름</th>
                  <th>E-ticket</th>
                </tr>
              </thead>
              <tbody>
                {travel?.bookingAir?.bookingAirTravelers?.map((traveler, index) => {
                  const { lastName, firstName } = traveler;
                  return Object.entries(eticketListMap).map(([key, value]) => {
                    if (key === index && !travel?.isCancel) {
                      return value.map((item, index) => {
                        const { voidType, gdsType, eticket4Direct } = item;
                        // TODO: NEED DATA TO TEST
                        if (!["Emd", "EmdVoid", "EmdRefund", "AgentVoid", "Refund"].includes(voidType)) {
                          return (
                            <tr key={item.voidType + index}>
                              <td></td>
                              <td className="left">
                                {lastName} {firstName}
                              </td>
                              <td>
                                {gdsType === "DIRECT" ? (
                                  <Fragment>
                                    {isEmpty(eticket4Direct) ? (
                                      <a className="btn-default btn-confirm">확인</a>
                                    ) : (
                                      <a className="btn-default btn-confirm" onClick={(event) => viewTicket(event, item)}>
                                        확인
                                      </a>
                                    )}
                                  </Fragment>
                                ) : (
                                  <a className="btn-default btn-confirm" onClick={(event) => viewTicket(event, item)}>
                                    확인
                                  </a>
                                )}
                              </td>
                            </tr>
                          );
                        }
                      });
                    }
                  });
                })}
              </tbody>
            </table>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
