import { Fragment, useEffect, useRef, useState } from "react";
import { Dialog } from "@mui/material";
import { useMap } from "@vis.gl/react-google-maps";
import useRewardMileSettings from "@/app/hooks/useRewardMileSettings";
import { useAppDispatch, useAppSelector } from "@/store";
import IconStar from "@/assets/images/svg/ic-star.svg";
import KoreanAir from "@/assets/images/svg/koreanair_symbol.svg";
import {
  actionRemoveHotelCompareById,
  selectConditionHotel,
  selectFilterHotel,
  selectHotelCompareList,
  selectHotelExchangeRate,
  selectSelectedHotel,
} from "@/store/hotelSlice";
import Divider from "@/app/components/user_v2/common/Divider";
import Image from "@/app/components/user_v2/common/Image";
import { DIVIDER_DIRECTION, HOTEL_COMPARE_SECTION, MAX_HOTEL_COMPARISION, ZOOM_LEVEL } from "@/constants/app";
import IconLocationFill from "@/assets/images/svg/ic-location-fill.svg";
import IconClose from "@/assets/images/svg/ic-close.svg";
import Button from "@/app/components/user_v2/common/Button";
import { comma, normalizeDangerHtmlHotelContent } from "@/utils/common";
import { getPriceByExchangeRate, getRoomMinSalePrice } from "@/utils/app";
import useHotelApi from "@/app/hooks/useHotelApi";

const HotelCompare = (props) => {
  const { open, setOpen, hotelItemRef, setIsOpenSearchList } = props;
  const containerRef = useRef();

  const map = useMap();
  const hotelApi = useHotelApi();
  const dispatch = useAppDispatch();

  const { nightCount } = useAppSelector(selectConditionHotel);
  const { isDisplayMileageKorea } = useRewardMileSettings();
  const exchangeRate = useAppSelector(selectHotelExchangeRate);
  const hotelCompareList = useAppSelector(selectHotelCompareList);
  const filter = useAppSelector(selectFilterHotel);
  const selectedHotel = useAppSelector(selectSelectedHotel);
  const [clientHeight, setClientHeight] = useState(2);

  function handleClose() {
    setOpen(false);
  }

  function removeFromCompare(htlMasterId) {
    dispatch(actionRemoveHotelCompareById(htlMasterId));
    if (hotelCompareList.length === 1) handleClose();
  }

  async function selectHotel(hotel) {
    const htlMasterId = hotel.htlDetailInfo.htlMasterId;
    if (selectedHotel && selectedHotel.htlMasterId === hotel.htlMasterId) return;
    const { checkIn, checkOut, roomInfo } = filter;
    const payload = {
      checkIn,
      checkOut,
      roomInfo,
      htlMasterId,
    };
    handleClose();
    setIsOpenSearchList(true);

    const { latitude, longitude } = hotel.htlDetailInfo;
    if (latitude && longitude) {
      map.panTo({ lat: parseFloat(latitude), lng: parseFloat(longitude) });
      map.setZoom(ZOOM_LEVEL.DETAIL);
    }

    hotelItemRef.current[htlMasterId].scrollIntoView({ behavior: "smooth" });
    await hotelApi.selectHotel(hotel.htlDetailInfo, htlMasterId, payload);
  }

  useEffect(() => {
    requestAnimationFrame(() => {
      if (containerRef.current) {
        setClientHeight(containerRef.current.clientHeight);
      }
    });
  }, [hotelCompareList, open]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      className="custom-dialog"
      sx={{ "& .MuiDialog-container": { "& .MuiPaper-root": { maxWidth: "1082px" } } }}
    >
      <div
        id="popHotelCompare"
        className="custom-dialog rounded-[16px] modal-wrap max-w-none modal block outline-none bg-white shadow-none w-full p-0 h-full"
      >
        <div className="modal-head !h-[64px] !p-[16px] flex">
          <h2 id="selectedRoomGradeName" className="!p-0 !text-[16px] !font-bold !leading-normal">
            상품비교함
          </h2>
        </div>
        <div className="modal-cotn" ref={containerRef}>
          <div className="overflow-y-auto max-h-[calc(100vh-70px-176px-96px)] hidden-scrollbar hotel-compare-dialog">
            {Object.entries(HOTEL_COMPARE_SECTION).map(([section, _], index) => {
              return (
                <div className="grid grid-cols-3 relative" key={section}>
                  {Array.from({ length: MAX_HOTEL_COMPARISION }).map((_, position) => {
                    const hotel = hotelCompareList[position];
                    if (!hotel) {
                      return index === 0 ? (
                        <div className="border-r border-custom-gray-100" key={position}>
                          <div
                            className="absolute transform -translate-x-1/2 -translate-y-1/2"
                            style={{
                              top: `calc(${clientHeight}px / 2)`,
                              left: `calc((100% / ${MAX_HOTEL_COMPARISION} * ${position}) + (100% / ${MAX_HOTEL_COMPARISION} / 2))`,
                            }}
                          >
                            <p className="text-custom-gray-500 text-[18px] font-bold whitespace-nowrap relative">비교할 호텔을 추가해 주세요.</p>
                          </div>
                        </div>
                      ) : (
                        <div className="border-r border-custom-gray-100" />
                      );
                    }
                    const { htlMasterId, htlGrade, htlNameKr, mainImg, addr, htlLocationDesc, detailDesc } = hotel.htlDetailInfo;
                    const { minSalePrice } = hotel.roomView;
                    const mileage = getRoomMinSalePrice(hotel.roomView);

                    switch (section) {
                      case HOTEL_COMPARE_SECTION.GENERAL:
                        return (
                          <div
                            key={htlMasterId}
                            className={`border-r pb-[16px] border-custom-gray-100 border-solid px-[16px] flex flex-col gap-[16px] !items-baseline`}
                          >
                            <Image src={mainImg} alt="hotel image" className="!h-[140px] object-cover w-full rounded-[8px]" />
                            <div className="flex gap-[8px] flex-col !items-baseline w-full">
                              <div className="flex gap-[2px] flex-col !items-baseline">
                                <div className="flex gap-[4px] !justify-normal">
                                  <span className="text-custom-gray-300">{htlGrade >= 1 ? `${htlGrade}성급` : "성급 없음"}</span>
                                  <span className="flex">
                                    {Array.from({ length: Math.floor(Number(htlGrade || 1)) }).map((_, index) => (
                                      <IconStar key={index} />
                                    ))}
                                  </span>
                                </div>

                                <h2 className="text-custom-gray-600 text-[18px] font-bold leading-[27px]">{htlNameKr}</h2>
                              </div>

                              <div className="w-full text-custom-gray-500">
                                <p className="text-[22px] font-bold text-custom-gray-600 text-end">
                                  {exchangeRate > 1 ? `$${comma(getPriceByExchangeRate(minSalePrice, exchangeRate))}` : `${comma(minSalePrice)}원`}
                                  <span>~</span>
                                </p>
                                <p className="text-end flex !justify-end gap-[4px] text-[12px] mt-[8px] leading-[20px] text-custom-gray-500">
                                  {mileage > 0 && isDisplayMileageKorea && (
                                    <Fragment>
                                      <span className="flex gap-[4px]">
                                        <KoreanAir />
                                        최대 {mileage}마일 적립
                                      </span>
                                      <span>∙</span>
                                    </Fragment>
                                  )}
                                  {nightCount}박 평균가
                                </p>
                              </div>
                            </div>

                            <Divider className="w-full flex-1" direction={DIVIDER_DIRECTION.HORIZONTAL} />
                          </div>
                        );
                      case HOTEL_COMPARE_SECTION.LOCATION:
                        return (
                          <div
                            key={htlMasterId}
                            className={`border-r border-custom-gray-100 border-solid px-[16px] pb-[16px] flex flex-col gap-[8px] !items-baseline !justify-normal`}
                          >
                            <h2 className="text-[16px] font-bold text-custom-gray-600">위치</h2>
                            <p>{addr}</p>
                            {htlLocationDesc && (
                              <p className="flex gap-[4px] text-[12px] text-custom-blue-100">
                                <IconLocationFill className="shrink-0" />
                                {htlLocationDesc}
                              </p>
                            )}

                            <Divider className="w-full mt-[8px] flex-1" direction={DIVIDER_DIRECTION.HORIZONTAL} />
                          </div>
                        );
                      case HOTEL_COMPARE_SECTION.AMENITIES:
                        return (
                          <div
                            key={htlMasterId}
                            className={`border-r border-custom-gray-100 border-solid px-[16px] pb-[16px] flex flex-col gap-[8px] !items-baseline`}
                          >
                            <h2 className="text-[16px] font-bold text-custom-gray-600">편의시설</h2>
                            <p className="leading-[21px]">{hotel?.htlFacilityList.map((data) => data.facilityName).join(", ")}</p>
                            <Divider className="w-full mt-[8px] flex-1" direction={DIVIDER_DIRECTION.HORIZONTAL} />
                          </div>
                        );
                      case HOTEL_COMPARE_SECTION.INFORMATION:
                        return (
                          <div
                            key={htlMasterId}
                            className={`border-r border-custom-gray-100 border-solid px-[16px] pb-[16px] flex flex-col !items-baseline !justify-normal`}
                          >
                            <h2 className="text-[16px] font-bold text-custom-gray-600">호텔정보</h2>
                            <div
                              className="leading-[21px] text-custom-gray-600  [&_b]:!font-bold [&_strong]:!font-bold [&_b]:!mt-[16px] [&_strong]:!mt-[16px] [&_b]:!mb-[4px] [&_strong]:!mb-[4px] [&_b]:inline-block [&_strong]:inline-block [&_b]:text-[14px] [&_strong]:text-[14px]"
                              dangerouslySetInnerHTML={{ __html: normalizeDangerHtmlHotelContent(detailDesc) }}
                            ></div>
                          </div>
                        );
                    }
                  })}
                </div>
              );
            })}
          </div>
          {Object.entries(HOTEL_COMPARE_SECTION).map(([section, _], index) => {
            return (
              <div className="grid grid-cols-3 relative" key={section}>
                {Array.from({ length: MAX_HOTEL_COMPARISION }).map((_, position) => {
                  const hotel = hotelCompareList[position];
                  if (!hotel) return <div key={position} className="border-r border-custom-gray-100" />;
                  const { htlMasterId } = hotel.htlDetailInfo;
                  switch (section) {
                    case HOTEL_COMPARE_SECTION.ACTION:
                      return (
                        <div key={htlMasterId} className={`border-r border-custom-gray-100 border-solid shadow-custom-500 p-[16px] flex gap-[4px]`}>
                          <Button
                            className="flex-1 btn-third !justify-center h-[48px] font-bold text-custom-gray-300 bg-custom-gray-700 !border-none"
                            onClick={() => removeFromCompare(htlMasterId)}
                          >
                            비교함에서 삭제
                          </Button>
                          <Button
                            onClick={() => selectHotel(hotel)}
                            className="flex-1 btn-primary !justify-center h-[48px] font-bold text-white bg-custom-blue-100 !border-none"
                          >
                            객실선택
                          </Button>
                        </div>
                      );
                  }
                })}
              </div>
            );
          })}
        </div>
        <Button className="btn-icon absolute !min-w-[32px] !h-[32px] !right-[16px] !top-[16px]" onClick={handleClose}>
          <IconClose />
        </Button>
      </div>
    </Dialog>
  );
};

export default HotelCompare;
