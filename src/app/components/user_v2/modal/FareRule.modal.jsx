import { isEmpty } from "lodash";
import { Modal } from "@mui/material";
import FareRegulationsTab from "@/app/pages/user_v2/travel/overseas/air/AirSearch/Condition/FareRegulationsTab";

const FareRule = (props) => {
  const { fareRules, setFareRules } = props;

  function handleColse() {
    setFareRules([]);
  }
  return (
    <Modal open={!isEmpty(fareRules)} onClose={handleColse}>
      <div
        id="popFareRule"
        className="modal-wrap modal block max-w-none p-0 box-content outline-none bg-white relative w-[846px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>운임 규정</h2>
        </div>
        <FareRegulationsTab fareRules={fareRules} type="VIEW" className="tab-wrap modal-cotn mr-8" onConfirm={handleColse} />

        <a className="close-modal" onClick={handleColse}>
          Close
        </a>
      </div>
    </Modal>
  );
};

export default FareRule;
