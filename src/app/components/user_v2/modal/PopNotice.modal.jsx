import { useEffect, useState } from "react";
import { Dialog } from "@mui/material";
import { useAppSelector } from "@/store";
import { selectHotelNotice } from "@/store/hotelSlice";
import Button from "@/app/components/user_v2/common/Button";
import IconClose from "@/assets/images/svg/ic-close.svg";
import { normalizeDangerHtmlHotelContent } from "@/utils/common";

const PopNotice = (props) => {
  const { open, selectedRoom, setOpen } = props;
  const hotelNotice = useAppSelector(selectHotelNotice);
  const [roomNotiFullText, setRoomNotiFullText] = useState("");

  function handleClose() {
    setOpen(false);
  }

  useEffect(() => {
    if (hotelNotice?.roomJson) {
      const xRoomJson = JSON.parse(hotelNotice.roomJson);
      setRoomNotiFullText(xRoomJson.roomNotiFullText);
    } else {
      setRoomNotiFullText("");
    }
  }, [hotelNotice]);

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      className="custom-dialog"
      sx={{ "& .MuiDialog-container": { "& .MuiPaper-root": { maxWidth: "1200px" } } }}
    >
      <div id="popCancelRule" className="rounded-[16px] w-[560px] modal-wrap max-w-none modal block outline-none bg-white shadow-none p-0 h-full">
        <div className="modal-head !h-[64px] !p-[16px] flex">
          <h2 className="!p-0 !text-[16px] !font-bold !leading-normal">알림/특전</h2>
        </div>
        <div className="modal-cotn !px-[0px] !pb-[16px]">
          <div className="notice-desc !px-[16px] !pb-[16px] text-custom-gray-600 leading-[21px] overflow-y-auto max-h-[calc(100vh-142px-160px)] hidden-scrollbar [&_b]:!font-bold [&_b]:!mb-[8px] [&_strong]:!mb-[8px] [&_b]:!mt-[16px] [&_strong]:!mt-[16px] [&_b]:inline-block [&_strong]:inline-block [&_strong]:!font-bold flex flex-col gap-[16px] !items-baseline !justify-normal">
            {selectedRoom?.roomPromotionList?.length > 0 && (
              <div className="border-b border-custom-gray-100 w-full pb-[16px]">
                <p className="text-custom-blue-100 font-bold mb-[8px]">프로모션 & 특전 정보</p>
                <div
                  dangerouslySetInnerHTML={{
                    __html: normalizeDangerHtmlHotelContent(
                      selectedRoom?.roomPromotionList?.[0]?.promotionDesc || selectedRoom?.roomPromotionList?.[0]?.promotionInfo,
                    ),
                  }}
                  className="text-custom-gray-600"
                />
              </div>
            )}
            <div dangerouslySetInnerHTML={{ __html: normalizeDangerHtmlHotelContent(roomNotiFullText) }} />
          </div>
          <div className="!px-[16px] pt-[16px] shadow-custom-500">
            <Button
              onClick={handleClose}
              className="w-full bg-custom-blue-100 btn-primary !h-[48px] border-none text-white font-bold !item-center justify-center"
            >
              확인
            </Button>
          </div>
        </div>
        <Button className="btn-icon absolute !min-w-[32px] !h-[32px] !right-[16px] !top-[16px]" onClick={handleClose}>
          <IconClose />
        </Button>
      </div>
    </Dialog>
  );
};

export default PopNotice;
