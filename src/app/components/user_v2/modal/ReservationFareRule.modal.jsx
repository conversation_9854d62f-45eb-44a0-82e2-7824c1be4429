import { useAppSelector } from "@/store";
import { selectSavedFareRule } from "@/store/airSlice";
import { replaceCarriageReturns } from "@/utils/common";
import { Modal } from "@mui/material";
import { isEmpty } from "lodash";
import { useEffect, useState } from "react";

export default function ReservationFareRule(props) {
  const { open, setOpen } = props;

  const fareRules = useAppSelector(selectSavedFareRule);
  const [selectedTab, setSelectedTab] = useState(-1);

  function handleClose() {
    setOpen(false);
  }

  useEffect(() => {
    if (!isEmpty(fareRules)) {
      setSelectedTab(fareRules[0].fareComponentNo);
    }
  }, [fareRules]);

  return (
    <Modal open={open} onClose={handleClose} sx={{ "&": { overflow: "auto" } }}>
      <div
        id="popFareRule"
        className="modal-wrap modal block max-w-none box-content max-h-[90vh] p-0 outline-none bg-white shadow-none relative w-[846px] top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
      >
        <div className="modal-head">
          <h2>운임 규정</h2>
        </div>
        <div className="tab-wrap modal-cotn bg-white rounded-[8px]" id="fareRuleLayerData">
          <ul className="tab-btm">
            {fareRules.map((rule) => {
              const { fareComponentNo } = rule;
              return (
                <li key={fareComponentNo} className={`${selectedTab === fareComponentNo ? "active" : ""}`} id="layerFareRuleTab_0">
                  <a className="btn-default" onClick={() => setSelectedTab(fareComponentNo)}>
                    운임규정{fareComponentNo}
                  </a>
                </li>
              );
            })}
          </ul>
          <p className="desc-tbl">※ 전체 여정의 규정 중 가장 제한적인 규정이 적용됩니다.</p>
          {fareRules.map((rule) => {
            const { fareComponentNo, fareRuleItems } = rule;

            return (
              <div key={fareComponentNo} className={`tbl-cmm ${selectedTab === fareComponentNo ? "active" : ""}`}>
                <div className="slimScrollDiv" style={{ position: "relative", overflow: "hidden", width: "auto" }}>
                  <div className="scrollbar-inner custom-set" name="fareRuleLayerScroll" style={{ overflow: "scroll", width: "auto" }}>
                    <table>
                      <colgroup>
                        <col style={{ width: 131 }} /> <col />
                      </colgroup>
                      <tbody>
                        {fareRuleItems.map((item, index) => {
                          return (
                            <tr key={item.title + index}>
                              <th dangerouslySetInnerHTML={{ __html: item.title }} />
                              <td dangerouslySetInnerHTML={{ __html: replaceCarriageReturns(item.contents) }} />
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            );
          })}
          <div className="btn-confirm">
            <a onClick={handleClose} className="btn-default">
              확인
            </a>
          </div>
        </div>
        <a onClick={handleClose} className="close-modal ">
          Close
        </a>
      </div>
    </Modal>
  );
}
