import { useState } from "react";
import { Dialog } from "@mui/material";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, FreeMode, Thumbs, Pagination } from "swiper/modules";
import { DEAULT_POP_SLIDE_IMAGE_TITLE } from "@/constants/app";
import Button from "@/app/components/user_v2/common/Button";
import IconClose from "@/assets/images/svg/ic-close.svg";
import Image from "@/app/components/user_v2/common/Image";

import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/thumbs";

const PopSlideRoomImages = (props) => {
  const { open, setOpen, title = DEAULT_POP_SLIDE_IMAGE_TITLE, imageList = [] } = props;
  const [thumbsSwiper, setThumbsSwiper] = useState(null);

  function handleClose() {
    setOpen(false);
    setThumbsSwiper(null);
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      className="custom-dialog"
      sx={{ "& .MuiDialog-container": { "& .MuiPaper-root": { maxWidth: "1200px" } } }}
    >
      <div
        id="popRoomPictures"
        className="custom-hotel-slide-images rounded-[16px] modal-wrap max-w-none modal block outline-none bg-white shadow-none w-full p-0 h-full"
      >
        <div className="modal-head !h-[64px] !p-[16px] flex">
          <h2 id="selectedRoomGradeName" className="!p-0 !text-[16px] !font-bold !leading-normal">
            {title}
          </h2>
        </div>
        <div className="modal-cotn !px-[16px] !pb-[16px] overflow-y-auto max-h-[calc(100vh-70px-160px)]">
          <div className="slide-area images-list [&_.swiper-slide]:!w-[1024px] !px-[72px]">
            <Swiper
              spaceBetween={10}
              slidesPerView="auto"
              watchSlidesProgress
              navigation={{ nextEl: ".swiper-button-custom-next", prevEl: ".swiper-button-custom-prev" }}
              thumbs={{ swiper: thumbsSwiper }}
              modules={[FreeMode, Navigation, Pagination, Thumbs]}
              observer
              observeParents
              pagination={{
                el: ".swiper-pagination",
                type: "fraction",
              }}
            >
              {imageList.map((el, index) => {
                const { imgUrl } = el;
                return (
                  <SwiperSlide key={index + imgUrl}>
                    <Image className="h-full" src={imgUrl} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
            <div className="swiper-pagination swiper-pagination-fraction"></div>
            <div className="swiper-button-custom">
              <div className="swiper-button swiper-button-custom-next"></div>
              <div className="swiper-button swiper-button-custom-prev"></div>
            </div>
          </div>
          <div className="slide-area thumbs-list">
            <Swiper
              slidesPerView="auto"
              onSwiper={setThumbsSwiper}
              spaceBetween={10}
              modules={[Navigation, Thumbs]}
              centeredSlides
              centeredSlidesBounds
              observer
              observeParents
              onTouchEnd={(swiper, event) => {
                if (imageList.length > 12) {
                  const clickedIndex = event.target.swiperSlideIndex;
                  swiper.slideToLoop(clickedIndex);
                }
              }}
            >
              {imageList.map((el, index) => {
                const { imgUrl } = el;
                return (
                  <SwiperSlide key={index + imgUrl}>
                    <Image className="h-full" src={imgUrl} />
                  </SwiperSlide>
                );
              })}
            </Swiper>
          </div>
        </div>
        <Button className="btn-icon absolute !min-w-[32px] !h-[32px] !right-[16px] !top-[16px]" onClick={handleClose}>
          <IconClose />
        </Button>
      </div>
    </Dialog>
  );
};

export default PopSlideRoomImages;
