import { useClickOutside } from "@/app/hooks/useClickOutside";
import { CircularProgress, Input } from "@mui/material";
import { useRef, useState } from "react";

function StayInfoAutoComplete(props) {
  const { value, onChange, onClear, options, debounceSearch, isLoading, labelText, valueText } = props;
  const [isOpenDropdown, setIsOpenDropdown] = useState(false);
  const dropdownRef = useRef(null);
  const inputRef = useRef(null);

  useClickOutside(dropdownRef, null, () => {
    setIsOpenDropdown(false);
  });

  return (
    <div ref={dropdownRef} className="relative">
      <Input
        inputRef={inputRef}
        className="w-full"
        value={value}
        onChange={debounceSearch}
        onFocus={() => setIsOpenDropdown(true)}
        sx={{
          "&:before": { borderBottom: "1px solid #e2e4e8" },
          "&:after": { borderBottom: "1px solid #e2e4e8" },
          "&:hover:not(.Mui-disabled):before": { borderBottom: "1px solid #e2e4e8" },
        }}
      />
      {!!value && (
        <button
          className="clear-icon text-gray-400 hover:text-gray-600"
          onClick={() => {
            onClear();
            inputRef.current?.focus();
          }}
        >
          <span className="text-xl">&times;</span>
        </button>
      )}
      {isOpenDropdown && (
        <div className="stay-autocomplete-paper absolute w-full bg-white z-[10] top-full">
          <ul className="stay-autocomplete-listbox min-h-[35px]">
            {isLoading ? (
              <li className="flex items-center justify-center">
                <CircularProgress size={20} />
              </li>
            ) : (
              <>
                {options.length ? (
                  options.map((item) => (
                    <li
                      key={item[valueText]}
                      className="stay-autocomplete-option"
                      onClick={() => {
                        onChange(item);
                        setIsOpenDropdown(false);
                      }}
                    >
                      {item[labelText]} ({item[valueText]})
                    </li>
                  ))
                ) : (
                  <li className="text-center text-gray-500">검색 결과가 없습니다.</li>
                )}
              </>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}

export default StayInfoAutoComplete;
