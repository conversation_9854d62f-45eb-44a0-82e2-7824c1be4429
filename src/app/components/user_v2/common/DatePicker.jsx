import React, { useEffect, useRef } from "react";
import $ from "@/utils/jquery.daterangepicker.min.js";

const DateRangePicker = ({
  initialSettings,
  onApply,
  onCancel,
  onHide,
  onHideCalendar,
  onShow,
  onShowCalendar,
  onClick,
  onEvent,
  onCallback,
  children,
}) => {
  const pickerRef = useRef(null);

  useEffect(() => {
    const $picker = $(pickerRef.current);

    $picker.daterangepicker(initialSettings, (...args) => {
      if (typeof onCallback === "function") {
        onCallback(...args);
      }
    });

    const eventHandlers = {
      show: onShow,
      hide: onHide,
      showCalendar: onShowCalendar,
      hideCalendar: onHideCalendar,
      apply: onApply,
      cancel: onCancel,
      dateclick: onClick,
    };

    Object.entries(eventHandlers).forEach(([event, handler]) => {
      if (typeof handler === "function") {
        $picker.on(`${event}.daterangepicker`, (e, picker) => {
          if (typeof onEvent === "function") onEvent(e, picker);
          handler(e, picker);
        });
      }
    });

    return () => {
      $picker.data("daterangepicker")?.remove();
    };
  }, [initialSettings, onApply, onCancel, onHide, onHideCalendar, onShow, onShowCalendar, onEvent, onCallback, onClick]);

  return React.cloneElement(React.Children.only(children), {
    ref: pickerRef,
  });
};

export default DateRangePicker;
