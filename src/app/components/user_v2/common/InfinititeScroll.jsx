import { useEffect, useRef } from "react";

const InfiniteScroll = ({ children, rootMargin = "400px", loader, fetchMore, hasMore, className }) => {
  const pageEndRef = useRef(null);

  useEffect(() => {
    if (hasMore) {
      const observer = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            fetchMore();
          }
        },
        {
          rootMargin, // Trigger fetchMore before reaching the end
          threshold: 0.5, // Trigger when the entire element is in view
        },
      );

      if (pageEndRef.current) {
        observer.observe(pageEndRef.current);
      }

      return () => {
        if (pageEndRef.current) {
          observer.unobserve(pageEndRef.current);
        }
      };
    }
  }, [hasMore, fetchMore]);

  return (
    <div className={className}>
      {children}

      {hasMore ? <div ref={pageEndRef}>{loader}</div> : ""}
    </div>
  );
};

export default InfiniteScroll;
