import moment from "moment";
import DateRangePicker from "@/app/components/user_v2/common/DatePicker";
import { HOTEL_CALENDAR_TEMPLATE, NOMAL_CALENDAR_TEMPLAYE } from "@/constants/app";
import "@/styles/user_v2/index.css";

const KrDateRangePicker = (props) => {
  const handleCallback = (event, picker) => {
    onSelectDate(picker);
  };

  const { startDate, endDate, children, onSelectDate, isHotelSearch = false } = props;

  return (
    <DateRangePicker
      initialSettings={{
        minDate: moment().format("MM/DD/YYYY"),
        startDate,
        endDate,
        autoApply: true,
        autoUpdateInput: true,
        template: isHotelSearch ? HOTEL_CALENDAR_TEMPLATE : NOMAL_CALENDAR_TEMPLAYE,
        locale: {
          format: "MM/DD/YYYY",
          separator: " - ",
          applyLabel: "Apply",
          cancelLabel: "Cancel",
          fromLabel: "From",
          toLabel: "To",
          customRangeLabel: "Custom",
          daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
          monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
          firstDay: 0,
        },
      }}
      onClick={(e, picker) => {
        if (isHotelSearch) {
          picker.maxDate = moment(picker.startDate.format("YYYY-MM-DD")).add("27", "d");
          picker.updateView();
        }
      }}
      onApply={handleCallback}
    >
      {children}
    </DateRangePicker>
  );
};

export default KrDateRangePicker;
