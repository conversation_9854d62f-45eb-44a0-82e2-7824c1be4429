import { useClickOutside } from "@/app/hooks/useClickOutside.js";
import { useRef, useState } from "react";
import { Link } from "react-router-dom";

const departSeatTypeCodeList = [
  {
    label: "일반석",
    value: "M",
  },
  {
    label: "프리미엄 이코노미",
    value: "W",
  },
  {
    label: "비즈니스",
    value: "C",
  },
  {
    label: "일등석",
    value: "F",
  },
];

const LayerClassMember = (props) => {
  const { onSaveData } = props;
  const buttonRef = useRef(null);
  const menuRef = useRef(null);

  const [openDropdown, setOpenDropdown] = useState(false);
  const [adultCount, setAdultCount] = useState(1);
  const [departSeatTypeCode, setDepartSeatTypeCode] = useState("M");

  useClickOutside(menuRef, buttonRef, () => setOpenDropdown(false));

  const handleAdultCountMinus = () => {
    setAdultCount((prev) => {
      if (prev === 1) {
        return 1;
      }
      return prev - 1;
    });
  };

  const handleAdultCountPlus = () => {
    setAdultCount((prev) => {
      if (prev === 9) {
        return 9;
      }
      return prev + 1;
    });
  };

  const handleSaveData = () => {
    onSaveData({
      adultCount,
      departSeatTypeCode,
    });
    setOpenDropdown(false);
  };

  return (
    <div
      className="box-line passenger"
      style={{
        boxSizing: "content-box",
      }}
    >
      <p className="tit txt-short" ref={buttonRef} onClick={() => setOpenDropdown(!openDropdown)}>
        <Link to={""} className="btn-class-member" id="etcBookingInfoTxt">
          {`성인 ${adultCount}, ${departSeatTypeCodeList.find((i) => i.value == departSeatTypeCode).label}`}
        </Link>
      </p>
      {openDropdown && (
        <div className="layer-class-member active" ref={menuRef}>
          <p className="tit">인원 &amp; 좌석 등급</p>
          <dl>
            <dt className="font-medium">성인</dt>
            <dd className="count">
              <button type="button" className="btn-default minus" id="adultCountMinus" onClick={handleAdultCountMinus} disabled={adultCount === 1}>
                -
              </button>
              <input type="number" value={adultCount} id="adultCount" readOnly />
              <button type="button" className="btn-default plus" id="adultCountPlus" onClick={handleAdultCountPlus} disabled={adultCount === 9}>
                +
              </button>
              <input type="hidden" name="adultCount" value={adultCount} />
            </dd>
          </dl>
          <ul>
            {departSeatTypeCodeList.map((item) => {
              return (
                <li key={item.value}>
                  <label className="form-radio">
                    <input
                      type="radio"
                      value={item.value}
                      id={`departSeatTypeCode_${item.value}`}
                      name="departSeatTypeCode"
                      onChange={() => setDepartSeatTypeCode(item.value)}
                    />
                    <span className="before:box-content">{item.label}</span>
                  </label>
                </li>
              );
            })}
          </ul>
          <div className="btn-apply">
            <button type="button" className="btn-default" onClick={handleSaveData}>
              적용
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default LayerClassMember;
