import { forwardRef, Fragment } from "react";
import PortalComponent from "@/app/components/user_v2/common/PortalComponent";
import Button from "@/app/components/user_v2/common/Button";
import CheckBox from "@/app/components/user_v2/common/CheckBox";
import Divider from "@/app/components/user_v2/common/Divider";
import { DIVIDER_DIRECTION, HOTEL_CONDITION_MAP, NO_STAR } from "@/constants/app";

const Select = forwardRef((props, ref) => {
  const {
    id,
    isOpen,
    clonedChild,
    isShowSelectAllBtn,
    isCloseWhenChoose,
    cloneSelected,
    selectList = [],
    dropdownPosition,
    additionalPosition,
    customOptions,
    setCloneSelected,
    setIsOpen,
    onSelect,
  } = props;

  function toggleSelectAll(isSelectAll) {
    if (isSelectAll) {
      const allItems = selectList.map((el) => el.value);
      setCloneSelected(allItems);
      onSelect(allItems, id);
    } else {
      setCloneSelected([]);
      onSelect([], id);
    }
  }

  function onSelectItem(e) {
    const { value, checked } = e.target;
    if (checked) {
      const newSelected = [...cloneSelected, value];
      setCloneSelected(newSelected);
      onSelect(newSelected, id);
    } else {
      const newSelected = cloneSelected.filter((el) => el !== value);
      setCloneSelected(newSelected);
      onSelect(newSelected, id);
    }
  }

  return (
    <Fragment>
      {clonedChild}
      <PortalComponent>
        <div
          ref={ref}
          style={{ top: dropdownPosition.top + 8, left: dropdownPosition.left - (additionalPosition?.left || 0) }}
          className={`z-[9999] custom-hotel-map-search absolute bg-white border border-custom-gray-100 rounded-[8px] p-[4px] pb-[6px] min-w-[140px] dropdown-select-animation ${isOpen ? "open" : ""}`}
        >
          {customOptions ? (
            customOptions
          ) : (
            <Fragment>
              {isShowSelectAllBtn && (
                <div className={`gap-[4px] font-medium ${id === HOTEL_CONDITION_MAP.facilityCodeMap ? "grid grid-cols-2" : "flex items-center "}`}>
                  <Button
                    className="btn-border !min-w-[120px] !justify-center"
                    disabled={cloneSelected?.length === selectList?.length}
                    onClick={() => toggleSelectAll(true)}
                  >
                    전체 선택
                  </Button>
                  <Button
                    className="btn-border !min-w-[120px] !justify-center"
                    disabled={cloneSelected?.length === 0}
                    onClick={() => toggleSelectAll(false)}
                  >
                    전체 해제
                  </Button>
                </div>
              )}
              <div
                className={`${id === HOTEL_CONDITION_MAP.facilityCodeMap ? "grid grid-cols-2 gap-y-[4px]" : "flex flex-col !items-baseline gap-[4px]"} w-full mt-[4px] font-medium`}
              >
                {selectList?.length > 0 &&
                  selectList.map((el, index) => {
                    let labelText = el.name;
                    if (id === HOTEL_CONDITION_MAP.gradeCodeMap && el.value == 0) labelText = NO_STAR;
                    return (
                      <div
                        key={el.id}
                        className="w-full"
                        onClick={() => {
                          isCloseWhenChoose && setIsOpen(false);
                        }}
                      >
                        <Fragment>
                          <CheckBox
                            className="!border-none"
                            labelText={labelText}
                            checked={cloneSelected?.includes(el.value)}
                            value={el.value}
                            onChange={onSelectItem}
                          />
                          {index < selectList.length - 1 && !(id === HOTEL_CONDITION_MAP.facilityCodeMap && index === selectList.length - 2) && (
                            <Divider
                              className={`${id === HOTEL_CONDITION_MAP.facilityCodeMap ? (index % 2 === 0 ? "ml-2" : "mr-2") : "mx-2"}`}
                              direction={DIVIDER_DIRECTION.HORIZONTAL}
                            />
                          )}
                        </Fragment>
                      </div>
                    );
                  })}
              </div>
            </Fragment>
          )}
        </div>
      </PortalComponent>
    </Fragment>
  );
});

Select.displayName = "Select";

export default Select;
