import { useMainUrl } from "@/app/hooks/useMainUrl";
import { MENU_PRIMARY, MENU_USE } from "@/constants/app";
import { Navigate } from "react-router-dom";
import { URL } from "@/constants/url";
export default function MainRedirect() {
  const { menuPrimary, menuUse } = useMainUrl();

  if (menuUse === MENU_USE.ALL) {
    return <Navigate to={menuPrimary === MENU_PRIMARY.AIR ? URL.AirMain : URL.HotelMain} replace />;
  }

  if (menuUse === MENU_USE.ONLY_AIR) {
    return <Navigate to={URL.AirMain} replace />;
  }

  if (menuUse === MENU_USE.ONLY_HOTEL) {
    return <Navigate to={URL.HotelMain} replace />;
  }

  return null;
}
