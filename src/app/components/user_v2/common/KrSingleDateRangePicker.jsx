import "@/styles/user_v2/index.css";
import moment from "moment";
import DateRangePicker from "react-bootstrap-daterangepicker";

const KrSingleDateRangePicker = (props) => {
  const { minDate, key } = props;
  const handleCallback = (event, picker) => {
    onSelectDate(picker);
  };

  const { children, onSelectDate } = props;
  return (
    <DateRangePicker
      key={key}
      initialSettings={{
        minDate: moment(minDate).format("MM/DD/YYYY"),
        autoApply: true,
        autoUpdateInput: true,
        locale: {
          format: "MM/DD/YYYY",
          separator: " - ",
          applyLabel: "Apply",
          cancelLabel: "Cancel",
          fromLabel: "From",
          toLabel: "To",
          customRangeLabel: "Custom",
          daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
          monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
          firstDay: 0,
        },
        singleDatePicker: true,
      }}
      onApply={handleCallback}
    >
      {children}
    </DateRangePicker>
  );
};

export default KrSingleDateRangePicker;
