import useDebounce from "@/app/hooks/useDebounce.js";
import { nvl } from "@/common/index.js";
import { getAirportAutoSearch } from "@/service/mainAirTravelPlaceService.js";
import { useAppSelector } from "@/store";
import { selectMainAirTravelPlaces, selectUserAirport } from "@/store/userSlice";
import { stripHTMLTagInAirportList } from "@/utils/app";
import { forwardRef, useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { Link } from "react-router-dom";

const renderCityList = (cityList, overseasAirportList, cb) => {
  const className = ["domestic", "japan", "china", "asia", "usa", "eu", "soc", "etc"];
  if (!cityList) return [];
  return cityList.slice(0, 8).map((city, index) => {
    return (
      city.sectionId == index + 1 && (
        <dl key={city.sectionId} className={className[city.sectionId]}>
          <dt>{city.airportSection}</dt>
          {overseasAirportList.map((airport) => {
            return (
              airport.sectionId == index + 1 && (
                <dd key={airport.code} onClick={() => cb(airport)}>
                  <a>{airport.name}</a>
                </dd>
              )
            );
          })}
        </dl>
      )
    );
  });
};

const SearchCity = forwardRef((props, ref) => {
  const { dropdownPosition, code, open, onChooseCity, additionalPosition = { top: 0, left: 0 } } = props;

  const userAirports = useAppSelector(selectUserAirport);
  const mainAirTravelPlace = useAppSelector(selectMainAirTravelPlaces);

  const [searchInput, setSearchInput] = useState("");
  const [isSeaching, setIsSeaching] = useState(false);
  const [searchResult, setSearchResult] = useState([]);
  const debouncedSearchInput = useDebounce(searchInput, 500);

  const handleChooseCity = (item) => {
    onChooseCity(item);
    setSearchInput("");
  };

  useEffect(() => {
    const fetchSearch = async () => {
      try {
        if (!debouncedSearchInput.trim() || isSeaching) return;
        setIsSeaching(true);
        const res = await getAirportAutoSearch({
          keyword: debouncedSearchInput,
          isOverseas: null,
        });
        setSearchResult(stripHTMLTagInAirportList(res));
      } catch (error) {
        console.log("Error in fetchSearch", error);
      } finally {
        setIsSeaching(false);
      }
    };
    fetchSearch();
  }, [debouncedSearchInput]);

  useEffect(() => {
    if (!open) {
      setSearchInput("");
    }
  }, [open]);

  // NEED API SERVER TO RENDER DATA
  if (!open) return <></>;
  return ReactDOM.createPortal(
    <div
      id="layerSaerchCity"
      className="layer-saerch-city"
      data-type-city="default"
      style={{ top: dropdownPosition.top + 8, left: dropdownPosition.left - additionalPosition.left }}
      ref={ref}
    >
      {code == "Overseas" ? (
        <div id="cityForegin" className="city-foregin" style={{ display: "block" }}>
          <div className="insert-word">
            <input
              type="text"
              id="searchAirport"
              placeholder="도시명 또는 공항명을 입력하세요"
              className="citySearchInput box-content"
              autoComplete="off"
              onChange={(e) => setSearchInput(e.target.value)}
              value={searchInput}
            />
          </div>
          <div
            className="item-name"
            style={{
              display: searchInput.trim() && "none",
            }}
          >
            {renderCityList(mainAirTravelPlace.airportMainSections, userAirports?.overseas, handleChooseCity)}
          </div>
          <div
            className="item-searhing"
            style={{
              display: !searchInput.trim() ? "none" : "block",
              overflowY: "auto",
              maxHeight: "400px",
            }}
          >
            <ul
              id="airportAutoSearchArea"
              style={{
                display: !searchInput.trim() ? "none" : "block",
              }}
            >
              {searchResult.map((item) => {
                return (
                  <li key={item.id} onClick={() => handleChooseCity(item)}>
                    <a name="airportSelect" id={item.id}>
                      <span
                        className="name"
                        dangerouslySetInnerHTML={{
                          __html: `${item.code === item.cityDTO?.cityCode ? item.cityDTO.name : item.name} (${item.code})`,
                        }}
                      ></span>
                      <span
                        className="nation"
                        dangerouslySetInnerHTML={{
                          __html: nvl(item.countryName, ""),
                        }}
                      ></span>
                    </a>
                  </li>
                );
              })}
            </ul>
          </div>
        </div>
      ) : (
        <div id="cityDomestic" className="city-domestic" style={{ display: "block" }}>
          <ul className="item-name">
            {userAirports?.domestic &&
              userAirports.domestic.map((item, index) => {
                return (
                  <li key={item.code + index}>
                    <Link to={""} onClick={() => handleChooseCity(item)}>{`${item.name} (${item.code})`}</Link>
                  </li>
                );
              })}
          </ul>
        </div>
      )}
    </div>,
    document.body,
  );
});

SearchCity.displayName = "SearchCity";

export default SearchCity;
