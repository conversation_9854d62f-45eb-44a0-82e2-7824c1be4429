import { forwardRef } from "react";

const Button = forwardRef((props, ref) => {
  const { children, className, ...rest } = props;

  return (
    <button
      ref={ref}
      className={`custom-button pl-[10px] h-[40px] pr-[12px] min-w-[48px] rounded-[8px] flex items-center gap-[4px] justify-center ${className}`}
      {...rest}
    >
      {children}
    </button>
  );
});

Button.displayName = "Button";

export default Button;
