import { Fragment, useState } from "react";
import NoImage from "@/app/components/user_v2/common/NoImage";

const Image = (props) => {
  const { src, className, ...rest } = props;
  const [loading, setLoading] = useState(true);
  const [isErrorImage, setIsErrorImage] = useState(false);

  return (
    <Fragment>
      {(isErrorImage || !src || loading) && <NoImage className={props.className} />}
      <img
        {...rest}
        src={src}
        className={`${isErrorImage || !src || loading ? "hidden" : ""} ${className}`}
        onLoad={(e) => {
          setLoading(false);
        }}
        onError={(e) => {
          setIsErrorImage(true);
        }}
      />
    </Fragment>
  );
};

export default Image;
