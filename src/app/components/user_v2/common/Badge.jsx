import { BADGE_TYPE } from "@/constants/app";

const Badge = (props) => {
  const { className, type = BADGE_TYPE.CORLOR.PRIMARY, size = BADGE_TYPE.SIZE.MD, text = "" } = props;

  function getStylesByType(type, size) {
    let styles = "";

    switch (type) {
      case BADGE_TYPE.CORLOR.SUCCESS:
        styles += "text-custom-green-100 bg-custom-bg-200";
        break;
      case BADGE_TYPE.CORLOR.DANGER:
        styles += "text-custom-red-100 bg-custom-red-200";
        break;
      case BADGE_TYPE.CORLOR.PRIMARY:
        styles += "text-custom-blue-100 bg-custom-gray-400";
        break;
      case BADGE_TYPE.CORLOR.SECONDARY:
        styles += "text-custom-gray-500 bg-custom-gray-1300";
        break;
      default:
        break;
    }

    switch (size) {
      case BADGE_TYPE.SIZE.SMALL:
        styles += " rounded-[8px]";
        break;
      case BADGE_TYPE.SIZE.MD:
        styles += " rounded-[4px]";
        break;
      default:
        break;
    }
    return styles;
  }

  return <div className={`px-[6px] py-[2px] font-medium text-[12px] ${getStylesByType(type, size)} ${className}`}>{text}</div>;
};

export default Badge;
