import { Fragment } from "react";
import { useSelector } from "react-redux";
import { selectLoadingUser } from "@/store/loadingUserSlice";
import { momentKR } from "@/utils/date";
import RoadingSearch from "@/assets/images/cmm/roading_search.gif";
import RoadingBooking from "@/assets/images/cmm/roading_booking.gif";
import useReservationNoticing from "@/app/hooks/useReservationNoticing";
import HotelPcGif from "@/assets/images/cmm/hotel_pc.gif";
import "@/styles/user_v2/common.css";

const Loading = () => {
  const {
    loadingSearchCount,
    loadingSearchBgCount,
    loadingPaymentCount,
    loadingPaymentBgCount,
    loadingBookingNoticeCount,
    loadingBookingCount,
    loadingDefaultCount,
    loadingHotelCount,
    searchBgInfo,
  } = useSelector(selectLoadingUser);
  const reservationNoticing = useReservationNoticing();

  return (
    <Fragment>
      {loadingSearchCount > 0 && (
        <div id="loading-search">
          <div className="inner">
            <div className="img">
              <img src={RoadingSearch} alt="" />
            </div>
            <p className="desc" />
            <div className="date">
              <p />
              <p />
            </div>
          </div>
          if
        </div>
      )}

      {loadingSearchBgCount > 0 && (
        <div id="loading-search-bg">
          <div className="inner">
            <div className="img flex items-center !justify-center">
              <img src={RoadingSearch} alt="" />
            </div>
            <p className="desc" dangerouslySetInnerHTML={{ __html: searchBgInfo.desc }}></p>
            <div className="date">
              <p>{searchBgInfo.dayText}</p>
              <p>{searchBgInfo.passengerText}</p>
            </div>
          </div>
        </div>
      )}

      {loadingPaymentCount > 0 && (
        <div id="loading-payment">
          <div className="inner">
            <div className="img">
              <img src="/static/user/images/cmm/roading_payment.gif" alt="이미지" />
            </div>
            <dl>
              <dt>결제 진행 중입니다.</dt>
              <dd>결제 완료까지 다소 시간이 걸릴 수 있습니다.</dd>
            </dl>
            <p className="desc">
              결제 진행 중 브라우저를 닫거나, 새로고침 하시면 결제 오류가 발생할 수 있습니다. <br />
              결제가 완료 될 때 까지 잠시만 기다려주세요.
            </p>
          </div>
        </div>
      )}

      {loadingPaymentBgCount > 0 && (
        <div id="loading-payment-bg">
          <div className="inner">
            <div className="img">
              <img src="/static/user/images/cmm/roading_payment.gif" alt="이미지" />
            </div>
            <dl>
              <dt>결제 진행 중입니다.</dt>
              <dd>결제 완료까지 다소 시간이 걸릴 수 있습니다.</dd>
            </dl>
            <p className="desc">
              결제 진행 중 브라우저를 닫거나, 새로고침 하시면 결제 오류가 발생할 수 있습니다. <br />
              결제가 완료 될 때 까지 잠시만 기다려주세요.
            </p>
          </div>
        </div>
      )}

      {loadingBookingNoticeCount > 0 && (
        <div id="loading-booking-notice">
          <div className="inner">
            <p className="leading-[18px]">
              항공권 운임은 잔여 좌석에 따라 실시간으로 달라질 수 있습니다.
              <br />
              이후 예약상황 및 가격정책의 변경 등으로 인해
              <br />
              스케쥴 및 운임의 변동이 있을 수 있습니다.
              <br />
              <br />
              {momentKR().format("YYYY-MM-DD")} 기준, 유류할증료와 세금 및 제반요금 포함된 성인 1명 기준 운임입니다.
            </p>
            <button id="lbn-confirm-btn" onClick={() => reservationNoticing.overseasConfirmBtn()}>
              확인
            </button>
          </div>
        </div>
      )}

      {loadingBookingCount > 0 && (
        <div id="loading-booking">
          <div className="inner">
            <div className="img flex items-center !justify-center">
              <img src={RoadingBooking} alt="이미지" />
            </div>
            <dl>
              <dt>예약 진행 중입니다.</dt>
              <dd>
                새로고침 하시면 오류가 발생할 수 있으니 <br />
                잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
      )}

      {loadingDefaultCount > 0 && (
        <div id="loading-default" style={{ backdropFilter: "blur(16px)" }}>
          <div className="inner !w-[320px] !h-[320px] flex flex-col !rounded-[20px] gap-[16px] !pt-[90px] !pb-[25px]">
            <div className="dot-flashing"></div>
            <dl>
              <dt className="!text-[22px]">
                <span className="leading-[150%]">처리 중입니다.</span>
              </dt>
              <dd className="!font-medium !text-custom-gray-500 !text-[16px]">
                <span className="leading-[150%]">
                  새로고침 하시면 오류가 발생할 수 있으니 <br />
                  잠시만 기다려주세요.
                </span>
              </dd>
            </dl>
          </div>
        </div>
      )}

      {loadingHotelCount > 0 && (
        <div id="loading-hotel" style={{ backdropFilter: "blur(16px)" }}>
          <div className="inner !rounded-[20px] flex flex-col !justify-normal !w-[320px] !h-[320px]">
            <div className="img">
              <img src={HotelPcGif} alt="이미지" />
            </div>
            <dl>
              <p className="text-custom-gray-300 text-[16px] font-bold leading-[24px]">
                잠시만 기다려 주세요.
                <br />
                실시간 객실과 요금을 조회 중입니다
              </p>
            </dl>
          </div>
        </div>
      )}
    </Fragment>
  );
};

export default Loading;
