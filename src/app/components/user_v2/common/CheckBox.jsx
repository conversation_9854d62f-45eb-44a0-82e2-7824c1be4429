const CheckBox = (props) => {
  const { labelText, type = "checkbox", name = "", className, value, checked, onChange } = props;
  return (
    <label
      className={`${type === "checkbox" ? "form-chkbox" : "form-radio"} custom-checkbox hover:bg-[#F8F9F9] active:bg-[#F4F4F5] transition-all duration-[0.44s] ease-in-out border pl-[10px] h-[40px] pr-[12px] min-h-[48px] rounded-[8px] flex items-center cursor-pointer border-custom-gray-100 border-solid ${className}`}
    >
      <input type={type} checked={checked} value={value} onChange={onChange} name={name} />
      <span className="after:box-content">{labelText}</span>
    </label>
  );
};

export default CheckBox;
