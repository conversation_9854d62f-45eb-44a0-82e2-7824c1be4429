import { useEffect, useState } from "react";
import useDebounce from "@/app/hooks/useDebounce";
import { useAppSelector } from "@/store";
import { selectHotelExchangeRate } from "@/store/hotelSlice";
import { getPriceByExchangeRate } from "@/utils/app";

const Input = (props) => {
  const { className, type, isCurrency = false, value, min, max, ...rest } = props;
  const { onChange } = rest;

  const [inputValue, setInputValue] = useState(value);
  const debouncedSearchInput = useDebounce(inputValue, 500);
  const exchangeRate = useAppSelector(selectHotelExchangeRate);

  function onChangeInput(e) {
    const { value } = e.target;
    let removeCommaValue = value;
    if (type === "number") {
      removeCommaValue = Number(value.replace(/,/g, ""));
      if (isCurrency) removeCommaValue = removeCommaValue * exchangeRate;
      if (!/^\d*\.?\d*$/.test(removeCommaValue) || removeCommaValue > max) return;
    }
    setInputValue(removeCommaValue);
  }

  function formatValue(value, type) {
    if (type === "number") {
      if (isCurrency) return getPriceByExchangeRate(Number(value), exchangeRate).toLocaleString("ko-KR");
      return Number(value).toLocaleString("ko-KR");
    }
    return value;
  }

  useEffect(() => {
    if (debouncedSearchInput == 0 || !debouncedSearchInput) return;
    onChange(debouncedSearchInput);
  }, [debouncedSearchInput]);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <div className={`rounded-[8px] border border-custom-gray-100 border-solid min-h-[48px] ${className} flex justify-normal`}>
      <input
        type="text"
        className="w-full flex-1 focus:outline-none px-[16px]"
        value={formatValue(inputValue, type)}
        onBlur={() => {
          if (min && inputValue < min) setInputValue(min);
        }}
        {...rest}
        onChange={onChangeInput}
      />
    </div>
  );
};

export default Input;
