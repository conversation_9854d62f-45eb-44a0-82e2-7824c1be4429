import { useNavigate } from "react-router-dom";
import Footer<PERSON>ogo from "@/assets/images/cmm/footer_logo.png";
import "@/styles/user_v2/footer.css";

const FOOTER_LINKS = [
  { id: 1, link: "/user/v2/board/notice/list", name: "공지사항" },
  { id: 2, link: "/customer/v2/etc/condition", name: "서비스 이용약관" },
  { id: 3, link: "/customer/v2/etc/privacyPolicy", name: "개인정보처리방침" },
];

const FAMILY_SITES_OPTIONS = [
  { id: 1, name: "투어비스 비즈", link: "#" },
  { id: 2, name: "투어비스", link: "https://air.tourvis.com" },
  { id: 3, name: "PRIVIA Travel", link: "https://priviatravel.com/" },
  { id: 4, name: "타이드스퀘어", link: "https://trvins.meritzfire.com" },
];

const Footer = () => {
  const navigate = useNavigate();

  return (
    <div id="footer">
      <div className="default-wd">
        <div className="clearfix">
          <div className="logo">
            <img src={FooterLogo} alt="타이드스퀘어/투어비스" />
          </div>
          <div className="lnb">
            <ul>
              {FOOTER_LINKS.map((link) => (
                <li key={link.id}>
                  <a href="@/app/components/user_v2/Footer/index.jsx#" onClick={() => navigate(link.link)}>
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
          <div className="family-site ">
            <p className="tit">패밀리사이트</p>
            <div className="box-select">
              <ul>
                {FAMILY_SITES_OPTIONS.map((option) => (
                  <li key={option.id}>
                    <a href={option.link} target="_blank">
                      {option.name}
                    </a>
                  </li>
                ))}
              </ul>
              <i className="ico" />
            </div>
          </div>
        </div>
        <p className="company-name" style={{ marginBottom: 0, paddingBottom: 6 }}>
          ㈜타이드스퀘어 투어비스
          <span className="company-name-right">
            &nbsp; &nbsp; 대표이사 : 윤민 &nbsp; &nbsp; 서울특별시 중구 남대문로 78, 8층 에이호(명동1가, 타임워크명동빌딩) (우)04534
          </span>
        </p>
        <p className="address">
          사업자등록 : 497-85-00706 &nbsp; &nbsp; 관광사업등록증번호 : 제2015-000033호 &nbsp; &nbsp; 통신판매업신고번호 : 제2017-서울중구-1310호
        </p>
        <p className="copy">Copyright © TIDESQUARE TOURVIS Co.,Ltd. All Rights Reserved.</p>
      </div>
    </div>
  );
};

export default Footer;
