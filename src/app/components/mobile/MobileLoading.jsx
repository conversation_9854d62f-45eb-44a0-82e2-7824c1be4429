import roading_booking from "@/assets/images/cmm/roading_booking.gif";
import hotel_pc from "@/assets/images/cmm/hotel_pc.gif";
import { useAppSelector } from "@/store";
import { selectAppLoading } from "@/store/loadingSlice.mobile";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import roading_search2 from "@/assets/mobile/images/loading/roading_search2.gif";
import "@/styles/mobile/css/footer.css";

function MobileLoading() {
  const { type, message, open, data } = useAppSelector(selectAppLoading);
  if (!open) return null;
  const { departureDayTexts, adultCount, departSeatTypeText } = data;

  return (
    <>
      {type === TYPE_MOBILE_LOADING.DEFAULT && (
        <div id="loading-default">
          <div className="inner">
            <div className="img flex justify-center">
              <img src={roading_booking} alt="이미지" />
            </div>
            <dl>
              <dt>처리 중입니다.</dt>
              <dd>
                새로고침 하시면 오류가 발생할 수 있으니 <br />
                잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
      )}

      {type === TYPE_MOBILE_LOADING.HOTEL && (
        <div id="loading-hotel" style={{ display: "block" }}>
          <div className="inner">
            <div className="img">
              <img src={hotel_pc} alt="이미지" />
            </div>
            <dl>
              <dt id="loadingDt">예약을 요청 중입니다.</dt>
              <dd id="loadingDd">
                새로고침 또는 뒤로가기를 할 경우 예약이 <br />
                정상 진행되지 않습니다. 잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
      )}

      {type === TYPE_MOBILE_LOADING.SEARCH && (
        <div className="jquery-modal blocker current">
          <div id="popReservIng" className="modal-wrap modal" style={{ paddingTop: 70, display: "inline-block" }}>
            <div className="modal-cotn">
              <p className="load-img flex items-center justify-center">
                <img src={roading_search2} alt="로딩 이미지" />
              </p>
              {message || (
                <dl>
                  <dt>
                    <strong>서울</strong>에서&nbsp;<strong>방콕</strong>까지 <br />
                    왕복 항공권을 조회하고 있습니다.
                  </dt>
                  <dd>
                    {departureDayTexts?.[0] ?? "2024.11.05 (화)"} – {departureDayTexts?.[1] ?? "2024.11.07 (목)"}
                    <br />
                    성인{adultCount ?? 1}, {departSeatTypeText ?? "일반석"}
                  </dd>
                </dl>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default MobileLoading;
