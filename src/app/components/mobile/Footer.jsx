import { useState } from "react";
import ConditionList from "@/app/pages/mobile/mypage/board/ConditionList";
import NoticeList from "@/app/pages/mobile/mypage/board/NoticeList";
import PrivacyPolicyList from "@/app/pages/mobile/mypage/board/PrivacyPolicyList";
import roading_booking from "@/assets/mobile/images/cmm/roading_booking.gif";
import roading_search2 from "@/assets/mobile/images/loading/roading_search2.gif";
// import "@/styles/mobile/css/footer.css";

function MobileFooter() {
  const [isOpenModal, setIsOpenModal] = useState({
    noticeList: false,
    conditionList: false,
    privacyPolicyList: false,
  });

  return (
    <>
      <div id="popReservIng" className="modal-wrap fade modal" style={{ paddingTop: 70 }} tabIndex="-1" role="dialog" aria-modal="true">
        <div className="modal-cotn ">
          <p className="load-img">
            <img src={roading_search2} alt="로딩 이미지" />
          </p>
          <dl>
            <dt>예약 진행 중입니다.</dt>
            <dd>
              새로고침 하시면 오류가 발생할 수 있으니 <br />
              잠시만 기다려주세요.
            </dd>
          </dl>
        </div>
      </div>

      <div id="popReservNoti" className="modal-wrap">
        <div className="modal-cotn">
          <dt>
            항공권 운임은 잔여 좌석에 따라 실시간으로 달라질 수 있습니다. 이후 예약상황 및 가격정책의 변경 등으로 인해 스케쥴 및 운임의 변동이 있을 수
            있습니다.
          </dt>
          <dl>{"yyyy-MM-dd"} 기준, 유류할증료와 세금 및 제반요금 포함된 성인 1명 기준 운임입니다.</dl>
          <button id="noti-confirm-btn">확인</button>
        </div>
      </div>
      <footer id="footer">
        <div className="box-lnb">
          <div className="col">
            <a className="btn-member-info" data-page-layer="notice-list" onClick={() => setIsOpenModal({ ...isOpenModal, noticeList: true })}>
              공지사항
            </a>
            <span className="bar">|</span>
            <a className="btn-member-info" data-page-layer="condition-list" onClick={() => setIsOpenModal({ ...isOpenModal, conditionList: true })}>
              서비스 이용약관
            </a>
            <span className="bar">|</span>
            <a
              className="btn-member-info"
              data-page-layer="privacy-policy-list"
              onClick={() => setIsOpenModal({ ...isOpenModal, privacyPolicyList: true })}
            >
              개인정보처리방침
            </a>
          </div>
        </div>
        <div className="info">
          대표이사 : 윤민 I 사업자등록번호 : 497 - 85 - 00706 <br />
          주소 : 서울특별시 중구 남대문로 78, 8층 에이호(명동1가, 타임워크명동빌딩) (우)04534 <br />
          관광사업등록증번호 : 제2015-000033호 <br /> 통신판매업신고번호 : 제2017-서울중구-1310호 <br />
          고객센터 :
        </div>
        <p className="copyright">
          Copyright© <strong>TIDESQUARE TOURVIS</strong> Co.,Ltd. All Rights Reserved.
        </p>
      </footer>
      <style
        dangerouslySetInnerHTML={{
          __html:
            "\n    #loading-default{ position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: #fff; z-index: 10000; background-color: rgba(0,0,0,0.3);}\n\t#loading-default .inner{ position: absolute; top: 50%; left: 50%; width: 320px; height: 243px; margin: -120px  0 0 -160px; border-radius: 10px; text-align: center; color: #3b7ff3; background-color: #fff;}\n\t#loading-default .img{ padding-top: 60px; margin-bottom: 30px; }\n\t#loading-default dt{ margin-bottom: 16px; font-size: 18px; line-height: 27px; color: #000; font-weight: 700; }\n\t#loading-default dd{ margin-bottom: 41px; font-weight: 300; line-height: 20px; color:#000;  }\n\n    #loading-search-bg{ position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: #fff; z-index: 10000; background-color: rgba(0,0,0,0.3);}\n\t#loading-search-bg .inner{ position: absolute; top: 50%; left: 50%; width: 95%; padding: 15px 0 20px; margin: -220px 0 0 -179px; border-radius: 10px; text-align: center; color: #3b7ff3; background-color: #fff;}\n\t#loading-search-bg .img{ margin-bottom: 30px; }\n\t#loading-search-bg .desc{ margin-bottom: 20px; font-size: 26px; line-height: 38px;  }\n\t#loading-search-bg .desc strong{ font-weight: 700; }\n\t#loading-search-bg .date{ margin-bottom: 4px; font-size: 17px; line-height: 25px; }\n",
        }}
      />

      <>
        <div id="loading-default" style={{ display: "none" }}>
          <div className="inner">
            <div className="img">
              <img src={roading_booking} alt="이미지" />
            </div>
            <dl>
              <dt>처리 중입니다.</dt>
              <dd>
                새로고침 하시면 오류가 발생할 수 있으니 <br />
                잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
        <div id="loading-hotel" style={{ display: "none" }}>
          <div className="inner">
            <div className="img">
              <img src="/static/user/images/cmm/hotel_pc.gif" alt="이미지" />
            </div>
            <dl>
              <dt id="loadingDt">예약을 요청 중입니다.</dt>
              <dd id="loadingDd">
                새로고침 또는 뒤로가기를 할 경우 예약이 <br />
                정상 진행되지 않습니다. 잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
        <div id="loading-search-bg" style={{ display: "none" }}>
          <div className="inner">
            <div className="img">
              <img src="/static/user/images/cmm/roading_search.gif" />
            </div>
            <p className="desc" />
            <div className="date">
              <p />
              <p />
            </div>
          </div>
        </div>
      </>
      {isOpenModal.noticeList && <NoticeList onClose={() => setIsOpenModal((prev) => ({ ...prev, noticeList: false }))} />}
      {isOpenModal.conditionList && <ConditionList onClose={() => setIsOpenModal((prev) => ({ ...prev, conditionList: false }))} />}
      {isOpenModal.privacyPolicyList && <PrivacyPolicyList onClose={() => setIsOpenModal((prev) => ({ ...prev, privacyPolicyList: false }))} />}
    </>
  );
}

export default MobileFooter;
