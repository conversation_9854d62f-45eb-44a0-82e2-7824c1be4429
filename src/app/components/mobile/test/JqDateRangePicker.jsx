import { useEffect } from "react";
import moment from "moment";
import $ from "@/utils/jquery.custom";
import { nvl } from "@/common";
import DateUtil from "@/utils/dateUtil";

function getInputDayLabel(_val) {
  let week = ["일", "월", "화", "수", "목", "금", "토"];

  let today = new Date(_val).getDay();
  let todayLabel = week[today];
  return todayLabel;
}

function dateCount() {
  let d1 = $(".search-ticket-cn .sec-day").eq(0).find(".dep .val").data("ymd").toString();
  let d2 = $(".search-ticket-cn .sec-day").eq(0).find(".arr .val").data("ymd").toString();
  if (d1) {
    d1 = new Date(d1.substring(0, 4), d1.substring(4, 6), d1.substring(6, 8));
  }
  if (d2) {
    d2 = new Date(d2.substring(0, 4), d2.substring(4, 6), d2.substring(6, 8));
  }
  let cDay = 24 * 60 * 60 * 1000,
    dif = d2 - d1;
  return parseInt(dif / cDay);
}

function addZero(i) {
  let rtn = i + 100;
  return rtn.toString().substring(1, 3);
}

function converDateString(dt) {
  return dt.getFullYear() + "" + addZero(eval(dt.getMonth() + 1)) + "" + addZero(dt.getDate());
}

function getDt10(s, i) {
  let newDt = new Date(s);
  newDt.setDate(newDt.getDate() + i);
  return converDateString(newDt);
}

let convDate = moment();

const JqDateRangePicker = (props) => {
  const { open: isOpen, onClose, ticketType: originTicketType, dateElem: originDateElement, formData, onChangeFormData, onSearch, isDisable } = props;
  let selectSectionType = sessionStorage.getItem("sectionType");
  selectSectionType = selectSectionType == "RoundTrip" ? "type-round" : selectSectionType == "OneWay" ? "type-oneway" : "type-multi";
  const ticketType = originTicketType ?? selectSectionType;
  const dateElem = $(originDateElement);

  const handleConfirmCalendar = () => {
    if (isDisable) {
      handleDestroyCalendar();
      return;
    }
    const nextFormData = { ...formData };
    const dateRange200 = { val: $("#date-range200").val(), dataYmd: $("#date-range200").attr("data-ymd") };
    const dateRange201 = { val: $("#date-range201").val(), dataYmd: $("#date-range201").attr("data-ymd") };
    if (ticketType === "type-round") {
      if (
        ($("#two-inputs-container .first-date-selected").length > 0 && $("#two-inputs-container .last-date-selected").length > 0) ||
        $("#two-inputs-container .same-date-selected").length > 0
      ) {
        $(".search-ticket-cn .sec-day .dep").eq(0).find(".val").text(dateRange200.val).attr("data-ymd", dateRange200.dataYmd);
        $(".search-ticket-cn .sec-day .dep").eq(0).find(".val").data("ymd", dateRange200.dataYmd);
        $(".search-ticket-cn .sec-day .arr .val").text(dateRange201.val).attr("data-ymd", dateRange201.dataYmd);
        $(".search-ticket-cn .sec-day .arr .val").data("ymd", dateRange201.dataYmd);
        $(".search-ticket-cn .sec-day .arr").addClass("in-value");
        $(".search-ticket-cn .sec-day .dep").eq(0).addClass("in-value");
        // dataRoundPriod = dateCount();
        nextFormData.departureDay_1 = dateRange200.dataYmd;
        nextFormData.departureDayText_1 = dateRange200.val;
        nextFormData.departureDay_2 = dateRange201.dataYmd;
        nextFormData.departureDayText_2 = dateRange201.val;
      }
    } else {
      if (nvl(dateRange200.dataYmd, "") != "") {
        dateElem.addClass("in-value");
        dateElem.find(".val").text(dateRange200.val).attr("data-ymd", dateRange200.dataYmd);
        dateElem.find(".val").data("ymd", dateRange200.dataYmd);
        if (ticketType == "type-oneway") {
          nextFormData.departureDay_1 = dateRange200.dataYmd;
          nextFormData.departureDayText_1 = dateRange200.val;
        } else {
          let itineraryCount = $(".plan-item").length + 1;
          let id = dateElem.find("p:eq(1)").attr("id");
          let index = parseInt(id.split("_")[1]) - 1;
          for (let i = 0; i < itineraryCount; i++) {
            if (i == index) {
              nextFormData[`departureDay_${i + 1}`] = dateRange200.dataYmd;
              nextFormData[`departureDayText_${i + 1}`] = dateRange200.val;
            }
          }
        }
      }
    }
    $("#dateForm").data("dateRangePicker").destroy();
    $("#date-range200").val("").data("ymd", 0).attr("data-ymd", "");
    $("#date-range201").val("").data("ymd", 0).attr("data-ymd", "");
    !!onChangeFormData && onChangeFormData(nextFormData);
    !!onSearch && onSearch(nextFormData);
    onClose();
  };

  const handleDestroyCalendar = () => {
    $("#dateForm").data("dateRangePicker").destroy();
    onClose();
  };

  useEffect(() => {
    if (!isOpen) return;
    let setFunc = ["setDateRange", "setSingleDate"];
    let objSetValue = {
      typeRound: async function (s, s1, s2) {
        let dispDate1 = moment(new Date(s1)).format("M" + "월 " + "D" + "일");
        let dispDate2 = moment(new Date(s2)).format("M" + "월 " + "D" + "일");
        dispDate1 = dispDate1 + " (" + getInputDayLabel(moment(new Date(s1)).format("Y" + "-" + "MM" + "-" + "DD")) + ")";
        dispDate2 = dispDate2 + " (" + getInputDayLabel(moment(new Date(s2)).format("Y" + "-" + "MM" + "-" + "DD")) + ")";
        $("#date-range200").val(dispDate1);
        $("#date-range200").attr("data-ymd", s1.replace(/\//g, ""));
        $("#date-range201").val(dispDate2);
        $("#date-range201").attr("data-ymd", s2.replace(/\//g, ""));
      },
      typeOneway: function (s) {
        let dispDate1 = moment(new Date(s)).format("M" + "월 " + "D" + "일");
        dispDate1 = dispDate1 + " (" + getInputDayLabel(moment(new Date(s)).format("Y" + "-" + "MM" + "-" + "DD")) + ")";
        $("#date-range200").val(dispDate1);
        $("#date-range200").attr("data-ymd", s.replace(/\//g, ""));
      },
    };
    let startBookingDate = convDate;
    let endBookingDate = convDate.clone().add(1, "year");
    let objCalendarOpt = {
      inline: true,
      format: "YYYY/MM/DD",
      startDate: startBookingDate,
      endDate: endBookingDate,
      singleMonth: false,
      separator: "~",
      container: "#two-inputs-container",
      alwaysOpen: true,
      hoveringTooltip: false,
      minDays: 1,
      maxDays: 365,
    };
    if (ticketType === "type-round") {
      $(".select-day .arr .city span").text($("#arrivalAirportCode_1").val());
      objCalendarOpt.singleDate = false;
      objCalendarOpt.setValue = objSetValue.typeRound;
      setFunc = setFunc[0];
      $(".layer-pages.select-day .sec-top .arr").show();
      if (
        $(".search-ticket-cn .sec-day").eq(0).find(".dep .val").data("ymd") != undefined &&
        $(".search-ticket-cn .sec-day").eq(0).find(".dep .val") != undefined
      ) {
        if (dateCount() < 0) {
          let dat1 = $(".search-ticket-cn .sec-day").eq(0).find(".dep .val").data("ymd");
          dat1 = getDt10(dat1.substring(0, 4) + "-" + dat1.substring(4, 6) + "-" + dat1.substring(6, 8), 0);
          $(".sec-day").find(".arr .val").data("ymd", dat1);
          $(".sec-day").find(".arr .val").attr("data-ymd", dat1);
        }
      }
    } else {
      if (ticketType == "type-multi" && !isDisable) {
        let id = dateElem.find("p:eq(1)").attr("id");
        let index = parseInt(id.split("_")[1]) - 1;
        let itineraryCount = $(".plan-item").length + 1;
        let beforeDate = "";
        let afterDate = "";
        for (let i = 0; i < index; i++) {
          if (formData[`departureDay_${i + 1}`]) {
            beforeDate = formData[`departureDay_${i + 1}`];
          }
        }
        for (let j = itineraryCount - 1; j > index; j--) {
          if (formData["departureDay_" + j + 1]) {
            afterDate = formData["departureDay_" + j + 1];
          }
        }
        if (nvl(beforeDate, "") != "") {
          objCalendarOpt.startDate = DateUtil.getPatternYmd(beforeDate, "/");
        } else {
          objCalendarOpt.startDate = startBookingDate;
        }
        if (nvl(afterDate, "") != "") {
          objCalendarOpt.endDate = DateUtil.getPatternYmd(afterDate, "/");
        } else {
          objCalendarOpt.endDate = endBookingDate;
        }
        if (nvl(beforeDate, "") == "" && nvl(afterDate, "") == "") {
          objCalendarOpt.startDate = startBookingDate;
        }
      }
      objCalendarOpt.singleDate = true;
      objCalendarOpt.setValue = objSetValue.typeOneway;
      setFunc = setFunc[1];
      $(".layer-pages.select-day .sec-top .arr").hide();
    }
    $("#JqDateRangePicker #dateForm").jsDateRangePicker(objCalendarOpt);
    if (dateElem.find(".val").data("ymd") != undefined) {
      $("#dateForm")
        .data("dateRangePicker")
        // eslint-disable-next-line no-unexpected-multiline
        [setFunc](dateElem.closest(".sec-day").find(".dep .val").data("ymd") + "", dateElem.closest(".sec-day").find(".arr .val").data("ymd") + "");
    }
    if ($("#two-inputs-container div.first-date-selected").length) {
      $(".layer-pages.select-day .layer-cotn").scrollTop($("#two-inputs-container .toMonth.first-date-selected").position().top - 50);
    }
  }, [isOpen, ticketType, dateElem]);

  return (
    <div className="layer-pages select-day" style={{ display: isOpen ? "block" : "none" }} id="JqDateRangePicker">
      <div className="layer-head">
        <p className="tit">날짜선택</p>
      </div>
      <div className="layer-cotn">
        <div className="sec-top">
          <div className="clearfix">
            <div className="dep">
              <p className="city">
                가는날 <span />
              </p>
              <p className="val">
                <input
                  id="date-range200"
                  className="input-box date-range200"
                  size={20}
                  defaultValue=""
                  placeholder="날짜 선택 "
                  data-ymd=""
                  readOnly="readonly"
                />
              </p>
            </div>
            <div className="arr">
              <p className="city">
                오는날 <span />
              </p>
              <p className="val">
                <input
                  id="date-range201"
                  className="input-box date-range201"
                  size={20}
                  defaultValue=""
                  placeholder="날짜 선택"
                  data-ymd=""
                  readOnly="readonly"
                />
              </p>
            </div>
          </div>
          <div className="week">
            <p className="sun">일</p>
            <p>월</p>
            <p>화</p>
            <p>수</p>
            <p>목</p>
            <p>금</p>
            <p>토</p>
          </div>
        </div>
        <div className="sec-calendar">
          <div className="fix">
            <input id="dateForm" className="date-input" type="text" name="datefilter" defaultValue="" />
            <div id="two-inputs-container" style={{ visibility: isDisable ? "hidden" : "visible" }}></div>
          </div>
          <div className="box-btn">
            <button type="button" onClick={handleConfirmCalendar} className="btn-confirm btns-cmm round-basic color-b w-75 layer-pages-close">
              확인
            </button>
          </div>
        </div>
      </div>
      <div className="box-btn-close layer-pages-close">
        <button type="button" className="btns-cmm" onClick={handleDestroyCalendar}>
          닫기
        </button>
      </div>
    </div>
  );
};

export default JqDateRangePicker;
