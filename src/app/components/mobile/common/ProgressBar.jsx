import { useMemo } from "react";
import { useAppSelector } from "@/store";
import { selectProgressBar } from "@/store/loadingSlice.mobile";
import "@/styles/mobile/css/progressbar.css";

function ProgressBar() {
  const { isOpen, type } = useAppSelector(selectProgressBar);
  const message = useMemo(() => {
    if (type === "reservation") {
      return (
        <>
          잠시만 기다려 주세요.
          <br />
          실시간으로 요금을 조회하고 있습니다.
        </>
      );
    }
    if (type === "fareRule") {
      return (
        <>
          잠시만 기다려 주세요.
          <br />
          운임규정을 조회하고 있습니다.
        </>
      );
    }
    return (
      <>
        잠시만 기다려 주세요.
        <br />
        처리 중입니다.
      </>
    );
  }, [type]);

  return (
    isOpen && (
      <div id="progressBar" className="!block">
        <div className="poplayer01">
          <div className="cont">
            <p>
              <div id="progressbox"></div>
            </p>
          </div>
          <div className="cont_text">
            <p className="progress_txt">{message}</p>
          </div>
          <div className="contbg"></div>
        </div>
        <iframe className="dimfrm" marginWidth="0" marginHeight="0" frameBorder="0"></iframe>
      </div>
    )
  );
}

export default ProgressBar;
