import { styled } from "@mui/material/styles";
import { Slider } from "@mui/material";

const IOSSlider = styled(Slider)(({ theme }) => ({
  color: "#4E81FF",
  height: 3,
  padding: "15px 0",
  "& .<PERSON><PERSON><PERSON><PERSON><PERSON>-thumb": {
    height: 18,
    width: 18,
    // backgroundColor: "#4E81FF",
    backgroundColor: "#FFFFFF",
    boxShadow: "0 0 2px 0px rgba(0, 0, 0, 0.1)",
    border: "1px solid #4e81ff",
    "&:focus, &:hover, &.Mui-active": {
      boxShadow: "none",
      // Reset on touch devices, it doesn't add specificity
      // "@media (hover: none)": {
      //   boxShadow: iOSBoxShadow,
      // },
    },
    "&:before": {
      boxShadow: "none",
      // "0px 0px 1px 0px rgba(0,0,0,0.2), 0px 0px 0px 0px rgba(0,0,0,0.14), 0px 0px 1px 0px rgba(0,0,0,0.12)",
    },
  },
  "& .MuiSlider-valueLabel": {
    fontSize: 12,
    fontWeight: "normal",
    top: -6,
    backgroundColor: "unset",
    color: "#4E81FF",
    "&::before": {
      display: "none",
    },
    "& *": {
      background: "transparent",
      color: theme.palette.mode === "dark" ? "#fff" : "#C9CFD8",
    },
  },
  "& .MuiSlider-track": {
    border: "none",
    height: 3,
  },
  "& .MuiSlider-rail": {
    opacity: 0.5,
    boxShadow: "inset 0px 0px 4px -2px #000",
    backgroundColor: "#D0D0D0",
  },
}));
IOSSlider.displayName = "MIOSSlider";
export default IOSSlider;
