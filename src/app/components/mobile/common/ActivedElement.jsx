import { cloneDeep } from "lodash";

function ActivedElement(props) {
  const { id, elements, onChange, children } = props;

  const handleToggle = () => {
    const nextActivedElement = cloneDeep(elements);
    const index = nextActivedElement.indexOf(id);
    if (index !== -1) {
      nextActivedElement.splice(index, 1);
    } else {
      nextActivedElement.push(id);
    }
    onChange(nextActivedElement);
  };

  return (
    <div className={`box-item ${elements.includes(id) ? "active" : ""}`}>
      {children}
      <button type="button" className="btn-arrow" onClick={handleToggle}></button>
    </div>
  );
}

export default ActivedElement;
