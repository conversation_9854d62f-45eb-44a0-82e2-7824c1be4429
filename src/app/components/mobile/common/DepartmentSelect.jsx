import { useState, useEffect } from "react";
import { cloneDeep } from "lodash";
import { requestWithMobileLoading } from "@/utils/app";
import qs from "qs";

function DepartmentMultiSelectBoxForm(props) {
  const { id, workspaceId, departmentId, onChange } = props;
  const [selects, setSelects] = useState([]);

  const make = (parentId) => {
    const params = {
      workspaceId,
      parentId: parentId || null,
    };
    requestWithMobileLoading({
      url: "/common/v2/company/department/childList",
      method: "POST",
      data: qs.stringify(params),
      success: (response) => {
        const list = response.data.resultList;
        const newSelect = generateSelectOptions(list, selects.length, null);
        if (newSelect) {
          onChange("");
          setSelects((prevSelects) => [...cloneDeep(prevSelects), newSelect]);
        }
      },
    });
  };

  const setting = (workspaceId, departmentId) => {
    const params = {
      workspaceId,
      departmentId,
    };
    requestWithMobileLoading({
      url: "/common/v2/company/department/recursiveDepartmentList",
      method: "POST",
      data: qs.stringify(params),
      success: (response) => setOption(response.data.resultList ?? []),
    });
  };

  const setOption = (setList) => {
    const promises = setList.map(async (setData, index) => {
      const params = {
        workspaceId,
        parentId: setData.parentId || null,
      };
      return requestWithMobileLoading({
        url: "/common/v2/company/department/childList",
        method: "POST",
        data: qs.stringify(params),
        success: (response) => generateSelectOptions(response.data.resultList, index, setData),
        error: () => null,
      });
    });

    Promise.all(promises).then((results) => {
      const validResults = results.filter((result) => result !== null);
      setSelects(validResults);
    });
  };

  const generateSelectOptions = (list, index, setData) => {
    if (!list?.length) {
      return null;
    }
    return {
      id: `departmentId_${index}`,
      options: list.map((item) => ({
        value: item.id,
        text: item.name,
        selected: setData ? setData.id === item.id : false,
      })),
    };
  };

  const subMake = (event, index) => {
    const value = event.target.value;
    onChange(value);
    setSelects((prevSelects) => prevSelects.slice(0, index + 1));
    if (value) {
      make(value);
    }
  };

  useEffect(() => {
    if (!departmentId) {
      setSelects([]);
      make(null);
      return;
    }
    setting(workspaceId, departmentId);
  }, [workspaceId, departmentId]);

  return (
    <div id={id}>
      {selects.map((select, index) => (
        <span className="select-cmm" key={select.id}>
          <select id={select.id} name={select.name} onChange={(event) => subMake(event, index)}>
            <option value="">선택하세요.</option>
            {select.options.map((option) => (
              <option key={option.value} value={option.value} selected={option.selected}>
                {option.text}
              </option>
            ))}
          </select>
        </span>
      ))}
    </div>
  );
}

export default DepartmentMultiSelectBoxForm;
