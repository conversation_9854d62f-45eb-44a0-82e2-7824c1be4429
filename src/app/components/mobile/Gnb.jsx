import { useCallback, useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import { appMobileLoading, requestWithMobileLoading } from "@/utils/app";
import { TYPE_MOBILE_LOADING } from "@/constants/app";
import { actionCodeList } from "@/store/travelViewSlice";
import cmmImages from "@/assets/mobile/images/cmm";
import useCsCallNumber from "@/app/hooks/useCsCallNumber";
import QnaWrite from "@/app/pages/mobile/mypage/board/QnaWrite";
import QnaRegistPop from "@/app/pages/mobile/mypage/board/include/QnaRegistPop";
import QnaList from "@/app/pages/mobile/mypage/board/QnaList";
import AccountInfo from "@/app/pages/mobile/mypage/customer/account/AccountInfo";
import NoticeList from "@/app/pages/mobile/mypage/board/NoticeList";
import ManagerInfo from "@/app/pages/mobile/mypage/board/ManagerInfo";
import FaqList from "@/app/pages/mobile/mypage/board/FaqList";
import ConditionList from "@/app/pages/mobile/mypage/board/ConditionList";
import PrivacyPolicyList from "@/app/pages/mobile/mypage/board/PrivacyPolicyList";
import qs from "qs";

function Gnb({ active, onUnActive }) {
  const { userInfo } = useAppSelector(selectUserInfo);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const csCallNumber = useCsCallNumber({ btmsSetting: userInfo.workspace?.company?.btmsSetting });
  const [isOpenModal, setIsOpenModal] = useState({
    qnaWrite: false,
    qnaRegistPop: false,
    qnaList: false,
    accountInfo: false,
    noticeList: false,
    managerInfo: false,
    faqList: false,
    conditionList: false,
    privacyPolicyList: false,
  });
  const [qnaListData, setQnaListData] = useState([]);

  const handleOpenQnaWrite = () => {
    appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
    dispatch(actionCodeList({ groupCode: "QNA_CATEGORY_CODE" })).finally(() => {
      appMobileLoading.off();
      setIsOpenModal((prev) => ({ ...prev, qnaWrite: true }));
    });
  };

  const handleRetrieveQnaList = useCallback(() => {
    requestWithMobileLoading({
      url: "/m/board/qna/listAjax",
      data: qs.stringify({ pages: 1, rowCountPerPage: 10, parameterFlag: "MOBILE", categoryCodeId: "" }),
      method: "POST",
      success: (res) => setQnaListData(res.data?.list ?? []),
    });
  }, []);

  return (
    <div className={`side-menu-wrap ${active && "active"}`}>
      <div className="sec-top" style={{ padding: "59px 16px 0px", backgroundImage: `url(${cmmImages["bg_sidemenu_top.png"]})` }}>
        <p className="name">{userInfo.name}</p>
        <div className="belong">
          <span>{userInfo.workspace?.name}</span>
          <span>{userInfo.department?.name}</span>
          <span>{userInfo.position?.name}</span>
        </div>
        <a className="btn-member-info" data-page-layer="edit-profile" onClick={() => setIsOpenModal((prev) => ({ ...prev, accountInfo: true }))}>
          회원정보
        </a>
      </div>
      <div className="in-scroll" style={{ padding: "146px 0 41px" }}>
        <div className="sec-mid">
          <div className="box-item active">
            <dl>
              <dt>출장 예약</dt>
              <dd>
                <Link to="/m/overseas/air/main">
                  <i className="ico-sidemenu abroad" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 해외 항공권
                </Link>
              </dd>
              <dd>
                <Link to="/m/domestic/air/main">
                  <i className="ico-sidemenu dome" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 국내 항공권
                </Link>
              </dd>
              <dd>
                <Link to="/m/hotel/main">
                  <i className="ico-sidemenu hotel" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 호텔
                </Link>
              </dd>
            </dl>
          </div>
          <div className="box-item active">
            <dl>
              <dt>부가서비스</dt>
              <dd>
                <a href="https://trvins.meritzfire.com/copr/tb?bizNo=MTI0ODY4NjM3MA==&travelPlace=WFg=&usrInpYn=WQ==" target="_blank">
                  <i className="ico-sidemenu hotel" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 해외여행보험
                </a>
              </dd>
            </dl>
          </div>
          <div className="box-item active">
            <dl>
              <dt>나의 예약정보</dt>
              <dd>
                <Link to="/m/reservation/travel/list">
                  <i className="ico-sidemenu booking_history" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 예약 내역
                </Link>
              </dd>
            </dl>
          </div>
          {userInfo.workspace?.company?.btmsSetting?.isOfflineUse && (
            <div className="box-item active">
              <dl>
                <dt>스케줄 요청내역</dt>
                <dd>
                  <Link to="/m/offline/travel/list">
                    <i className="ico-sidemenu booking_history" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 요청 내역
                  </Link>
                </dd>
              </dl>
            </div>
          )}
          <div className="box-item active">
            <dl>
              <dt>고객센터</dt>
              <dd>
                <a className="btn-member-info" data-page-layer="faq-list" onClick={() => setIsOpenModal((prev) => ({ ...prev, faqList: true }))}>
                  <i className="ico-sidemenu faq" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 자주 묻는 질문
                </a>
              </dd>
              <dd>
                <a data-page-layer="qna-list" onClick={() => setIsOpenModal((prev) => ({ ...prev, qnaList: true }))}>
                  <i className="ico-sidemenu qna" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 1:1 문의 내역
                </a>
              </dd>
              <dd>
                <a
                  className="btn-member-info"
                  data-page-layer="manager-info"
                  onClick={() => setIsOpenModal((prev) => ({ ...prev, managerInfo: true }))}
                >
                  <i className="ico-sidemenu tel" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 담당자 안내
                </a>
              </dd>
              <dd>
                <a
                  className="btn-member-info"
                  data-page-layer="notice-list"
                  onClick={() => setIsOpenModal((prev) => ({ ...prev, noticeList: true }))}
                >
                  <i className="ico-sidemenu notice" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} /> 공지사항
                </a>
              </dd>
            </dl>
          </div>
          <div className="box-item service active">
            <dl>
              <dt>서비스 정보</dt>
              <dd style={{ minWidth: "32.5%" }}>
                <a data-page-layer="condition-list" onClick={() => setIsOpenModal((prev) => ({ ...prev, conditionList: true }))}>
                  트립뷰 이용약관
                </a>
              </dd>
              <dd style={{ minWidth: "35.5%" }}>
                <a data-page-layer="privacy-policy-list" onClick={() => setIsOpenModal((prev) => ({ ...prev, privacyPolicyList: true }))}>
                  개인정보처리방침
                </a>
              </dd>
              <dd style={{ minWidth: "32%" }}>
                <a id="">라이센스</a>
              </dd>
            </dl>
          </div>
        </div>
        <div className="sec-bottom">
          <div className="cs-center">
            <p style={{ padding: "11px 0", color: "#757f92" }}>긴급 서비스</p>
            <p className="num">
              <i className="ico-sidemenu tel" style={{ backgroundImage: `url(${cmmImages["spr_sidemenu.png"]})` }} />
              {csCallNumber}
            </p>
            <p className="time">운영시간(평일 09:00 ~ 18:00) 외</p>
          </div>
          <div className="box-btn">
            <a href={`tel:${csCallNumber}`} className="btns-cmm round-basic color-w w-50">
              전화문의
            </a>
            <button type="button" className="btns-cmm round-basic color-w w-50" data-page-layer="qna-write" onClick={handleOpenQnaWrite}>
              1:1 문의
            </button>
          </div>
        </div>
        <button type="button" className="btn-logout" id="logoutBtn" onClick={() => navigate("/logout")}>
          로그아웃
        </button>
      </div>
      <button type="button" className="close-side" style={{ backgroundImage: `url(${cmmImages["btn_close_side.png"]})` }} onClick={onUnActive}>
        닫기
      </button>
      {isOpenModal.qnaList && (
        <QnaList
          data={qnaListData}
          onRetrieve={handleRetrieveQnaList}
          onClose={() => setIsOpenModal((prev) => ({ ...prev, qnaList: false }))}
          onOpenQnaWrite={handleOpenQnaWrite}
        />
      )}
      {isOpenModal.qnaWrite && (
        <QnaWrite
          onRetrieve={handleRetrieveQnaList}
          onClose={() => setIsOpenModal((prev) => ({ ...prev, qnaWrite: false }))}
          onOpenQnaRegistPop={() => setIsOpenModal((prev) => ({ ...prev, qnaRegistPop: true }))}
        />
      )}
      {isOpenModal.accountInfo && (
        <AccountInfo isOpen={isOpenModal.accountInfo} onClose={() => setIsOpenModal((prev) => ({ ...prev, accountInfo: false }))} />
      )}
      {isOpenModal.noticeList && <NoticeList onClose={() => setIsOpenModal((prev) => ({ ...prev, noticeList: false }))} />}
      {isOpenModal.managerInfo && <ManagerInfo onClose={() => setIsOpenModal((prev) => ({ ...prev, managerInfo: false }))} />}
      {isOpenModal.faqList && <FaqList onClose={() => setIsOpenModal((prev) => ({ ...prev, faqList: false }))} />}
      {isOpenModal.conditionList && <ConditionList onClose={() => setIsOpenModal((prev) => ({ ...prev, conditionList: false }))} />}
      {isOpenModal.privacyPolicyList && <PrivacyPolicyList onClose={() => setIsOpenModal((prev) => ({ ...prev, privacyPolicyList: false }))} />}
      <QnaRegistPop isOpen={isOpenModal.qnaRegistPop} onClose={() => setIsOpenModal((prev) => ({ ...prev, qnaRegistPop: false }))} />
    </div>
  );
}

Gnb.displayName = "MobileLayoutGnb";
export default Gnb;
