function IncludeInfoLayer() {
  return (
    <>
      <form id="infoForm" name="infoForm">
        <input type="hidden" id="userId" name="id" defaultValue="{{userInfo.id}}" />
        <input type="hidden" id="email" name="email" defaultValue="{{userInfo.email}}" />
        <input type="hidden" id="name" name="name" defaultValue="{{userInfo.name}}" />
        <input type="hidden" id="departmentId" name="departmentId" defaultValue="" />
        <input type="hidden" id="cellPhoneNumber" name="cellPhoneNumber" defaultValue="" />
        <input type="hidden" id="phoneNumber" name="phoneNumber" defaultValue="" />
        <div className="layer-pages edit-profile">
          <div className="layer-head">
            <p className="tit">회원정보</p>
          </div>
          <div className="layer-cotn">
            {/* 기본 정보 */}
            <div className="box-info default">
              <h2 className="title">기본 정보</h2>
              <p className="form-tit">회원 아이디</p>
              <div className="form-cn">
                <span className="box-val" id="email">
                  {"{"}
                  {"{"}userInfo.email{"}"}
                  {"}"}
                </span>
              </div>
              <p className="form-tit">이름</p>
              <div className="form-cn">
                <span className="box-val">
                  {"{"}
                  {"{"}userInfo.name{"}"}
                  {"}"}
                </span>
              </div>
              <p className="form-tit">
                현재 비밀번호<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="password" id="currentPassword" name="currentPassword" maxLength={20} autoComplete="off" />
                </span>
              </div>
              <p className="form-tit">
                신규 비밀번호<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="password" id="password" name="password" maxLength={20} autoComplete="off" />
                </span>
              </div>
              <p className="form-tit">
                신규 비밀번호 재입력<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="password" id="passwordCheck" maxLength={20} autoComplete="off" />
                </span>
              </div>
              <p className="form-tit">
                성별<span>*</span>
              </p>
              <div className="form-cn half-col">
                <label className="item-col form-radio">
                  <input type="radio" name="gender" id="genderMale" defaultValue="Male" />
                  <span>남성</span>
                </label>
                <label className="item-col form-radio">
                  <input type="radio" name="gender" id="genderFemale" defaultValue="Female" />
                  <span>여성</span>
                </label>
              </div>
              <p className="form-tit">
                생년월일<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="birthday" name="birthday" placeholder="예.19900531" maxLength={8} readOnly />
                </span>
              </div>
              <p className="form-tit">
                휴대전화<span>*</span>
              </p>
              <div className="form-cn cellphone">
                <span className="select-cmm">
                  <select id="cellPhoneNumber1">
                    <option value="010">010</option>
                    <option value="011">011</option>
                    <option value="016">016</option>
                    <option value="019">019</option>
                  </select>
                </span>
                <div className="box-right">
                  <span className="input-cmm num-front">
                    <input type="text" placeholder="" id="cellPhoneNumber2" defaultValue="" maxLength={4} />
                  </span>
                  <span className="input-cmm num-end">
                    <input type="text" placeholder="" id="cellPhoneNumber3" defaultValue="" maxLength={4} />
                  </span>
                </div>
              </div>
              <p className="form-tit ">마일리지 회원번호</p>
              <div className="form-cn mileage">
                <div className="content">
                  <div>
                    <span>항공사1</span>
                    <input type="hidden" id="mileageInfoId_0" name="mileageInfos[0].mileageInfoId" />
                    <input type="text" id="airline_0" name="mileageInfos[0].airline" placeholder="한글/영문 입력" />
                  </div>
                  <div>
                    <span>회원번호1</span>
                    <input type="text" id="mileageMemberNo_0" name="mileageInfos[0].mileageMemberNo" placeholder="숫자/알파벳 입력" />
                  </div>
                </div>
                <div className="content">
                  <div>
                    <span>항공사2</span>
                    <input type="hidden" id="mileageInfoId_1" name="mileageInfos[1].mileageInfoId" />
                    <input type="text" id="airline_1" name="mileageInfos[1].airline" placeholder="한글/영문 입력" />
                  </div>
                  <div>
                    <span>회원번호2</span>
                    <input type="text" id="mileageMemberNo_1" name="mileageInfos[1].mileageMemberNo" placeholder="숫자/알파벳 입력" />
                  </div>
                </div>
                <div className="content">
                  <div>
                    <span>항공사3</span>
                    <input type="hidden" id="mileageInfoId_2" name="mileageInfos[2].mileageInfoId" />
                    <input type="text" id="airline_2" name="mileageInfos[2].airline" placeholder="한글/영문 입력" />
                  </div>
                  <div>
                    <span>회원번호3</span>
                    <input type="text" id="mileageMemberNo_2" name="mileageInfos[2].mileageMemberNo" placeholder="숫자/알파벳 입력" />
                  </div>
                </div>
                <div className="content">
                  <div>
                    <span>항공사4</span>
                    <input type="hidden" id="mileageInfoId_3" name="mileageInfos[3].mileageInfoId" />
                    <input type="text" id="airline_3" name="mileageInfos[3].airline" placeholder="한글/영문 입력" />
                  </div>
                  <div>
                    <span>회원번호4</span>
                    <input type="text" id="mileageMemberNo_3" name="mileageInfos[3].mileageMemberNo" placeholder="숫자/알파벳 입력" />
                  </div>
                </div>
                <div className="content">
                  <div>
                    <span>항공사5</span>
                    <input type="hidden" id="mileageInfoId_4" name="mileageInfos[4].mileageInfoId" />
                    <input type="text" id="airline_4" name="mileageInfos[4].airline" placeholder="한글/영문 입력" />
                  </div>
                  <div>
                    <span>회원번호5</span>
                    <input type="text" id="mileageMemberNo_4" name="mileageInfos[4].mileageMemberNo" placeholder="숫자/알파벳 입력" />
                  </div>
                </div>
              </div>
              <div className="set-agree">
                <dl>
                  <dt>마케팅 수신 설정</dt>
                  <dd className="txt">회원정보, 예약 및 항공기 운항 정보, 서비스 주요 정책 관련 내용은 수신 동의 여부와 관계없이 발송됩니다.</dd>
                  <dd className="slide">
                    <span className="tit">메일 수신동의</span>
                    <label className="btns-toggle">
                      <input type="checkbox" id="isEmailReceive" />
                      <i />
                    </label>
                  </dd>
                  <dd className="slide">
                    <span className="tit">SMS 수신동의</span>
                    <label className="btns-toggle on">
                      <input type="checkbox" id="isSmsReceiveYn" />
                      <i />
                    </label>
                  </dd>
                </dl>
              </div>
            </div>
            {/* // 기본 정보 */}
            {/* 회사 정보 */}
            <div className="box-info company">
              <h2 className="title">회사 정보</h2>
              <p className="form-tit">회사명</p>
              <div className="form-cn">
                <span className="box-val">
                  {"{"}
                  {"{"}userInfo.workspace.company.name{"}"}
                  {"}"}
                </span>
              </div>
              <p className="form-tit">
                소속부서<span>*</span>
              </p>
              <div className="form-cn sel-multi-row">
                <span className="select-cmm" id="workspaceList"></span>
                <div id="departmentSelectBoxArea" />
              </div>
              <p className="form-tit">
                회계코드<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="accountingCode" name="accountingCode" defaultValue="" />
                </span>
              </div>
              <p className="form-tit">
                직급<span>*</span>
              </p>
              <div className="form-cn sel-multi-row">
                <span className="select-cmm" id="positionList">
                  <select name="position.id" id="setPositionId"></select>
                </span>
              </div>
              <p className="form-tit">사번</p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="employeeNo" name="employeeNo" />
                </span>
              </div>
              <p className="form-tit">회사전화</p>
              <div className="form-cn telephone">
                <span className="select-cmm">
                  <select id="phoneNumber1">
                    <option value="02">02</option>
                    <option value="031">031</option>
                    <option value="032">032</option>
                    <option value="033">033</option>
                    <option value="041">041</option>
                    <option value="042">042</option>
                    <option value="043">043</option>
                    <option value="044">044</option>
                    <option value="051">051</option>
                    <option value="052">052</option>
                    <option value="053">053</option>
                    <option value="054">054</option>
                    <option value="055">055</option>
                    <option value="061">061</option>
                    <option value="062">062</option>
                    <option value="063">063</option>
                    <option value="064">064</option>
                  </select>
                </span>
                <span className="input-cmm">
                  <input type="text" id="phoneNumber2" readOnly maxLength={8} />
                </span>
              </div>
            </div>
            {/* // 회사 정보 */}
            {/* 여권 정보 */}
            <div className="box-info passport">
              <h2 className="title">여권 정보</h2>
              <p className="form-tit">영문 성</p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="lastName" name="customerPassport.lastName" placeholder="예) HONG" maxLength={30} />
                </span>
              </div>
              <p className="form-tit">
                영문 이름<span>*</span>
              </p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="firstName" name="customerPassport.firstName" placeholder="예) GILDONG" maxLength={30} />
                </span>
              </div>
              <p className="form-tit">여권번호</p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="passportNumber" name="customerPassport.passportNumber" placeholder="예) M12345678" maxLength={9} />
                </span>
              </div>
              <p className="form-tit">여권 만료 기간</p>
              <div className="form-cn">
                <span className="input-cmm">
                  <input type="text" id="expireYmd" name="customerPassport.expireYmd" placeholder="예) 20200101" maxLength={8} readOnly />
                </span>
              </div>
              <p className="form-tit">여권 발행국가</p>
              <div className="form-cn">
                <span className="select-cmm" data-custom-class="passport-nation">
                  <select name="customerPassport.country.id" id="ui-id-1" style={{ display: "block" }}>
                    <optgroup label="otg01">
                      <option value="">선택</option>
                      <option value={23}>한국</option>
                      <option value={214}>미국</option>
                      <option value={19}>일본</option>
                      <option value={10}>중국</option>
                    </optgroup>
                    <optgroup label="otg02" id="countryList"></optgroup>
                  </select>
                </span>
              </div>
            </div>
            {/* // 여권 정보 */}
            <div className="sec-bottom-btns">
              <button id="modifyProcessBtn" onClick="javascript:modifyProcess();return false;" className="btns-cmm round-basic color-b w-90">
                정보 수정
              </button>
            </div>
          </div>
          <div className="box-btn-close layer-pages-close">
            <button type="button" className="btns-cmm" />
          </div>
        </div>
      </form>
    </>
  );
}

export default IncludeInfoLayer;
