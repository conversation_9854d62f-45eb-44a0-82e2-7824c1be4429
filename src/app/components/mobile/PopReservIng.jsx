import Box from "@mui/material";
import Modal from "@mui/material";
const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  border: "2px solid #000",
  boxShadow: 24,
  pt: 2,
  px: 4,
  pb: 3,
};
function PopReservIng() {
  return (
    <Modal open={false} aria-labelledby="child-modal-title" aria-describedby="child-modal-description">
      <Box sx={{ ...style, width: 200 }}>
        <div id="popReservIng" className="modal-wrap" style={{ paddingTop: 70 }}>
          <div className="modal-cotn">
            <p className="load-img">
              <img src="/static/mobile/images/loading/roading_search2.gif" alt="로딩 이미지" />
            </p>
            <dl>
              <dt>예약 진행 중입니다.</dt>
              <dd>
                새로고침 하시면 오류가 발생할 수 있으니 <br />
                잠시만 기다려주세요.
              </dd>
            </dl>
          </div>
        </div>
      </Box>
    </Modal>
  );
}

export default PopReservIng;
