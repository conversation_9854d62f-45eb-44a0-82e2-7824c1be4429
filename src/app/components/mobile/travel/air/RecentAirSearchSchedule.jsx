import { nvl } from "@/common";
import airLocalStorageUtil from "@/utils/airLocalStorageUtil";
import AirSessionUtil from "@/utils/airSessionUtil";
import AirUtil from "@/utils/airUtil";
import { generateMobileDynamicNavigateUrl } from "@/utils/common";
import DateUtil from "@/utils/dateUtil";
import { cloneDeep } from "lodash";
import { useNavigate } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import { useAppSelector } from "@/store";
import { selectUserInfo } from "@/store/userSlice";
import "@/styles/mobile/css/main.css";
import "swiper/css";

function RecentAirSearchSchedule(props) {
  const navigate = useNavigate();
  const { userInfo } = useAppSelector(selectUserInfo);
  const { isOverseas = 1, isMain } = props;
  const data = airLocalStorageUtil.get(userInfo?.id) ?? [];
  const dataRender = isMain ? data.reverse() : data.filter((item) => nvl(isOverseas, "") == "" || item.isOverseas == isOverseas).reverse();

  const recentAirSearch = (id) => {
    const params = cloneDeep(airLocalStorageUtil.getOne(id, userInfo?.id));
    delete params.id;
    AirSessionUtil.setSession("recentAirSearch", params);
    const url = generateMobileDynamicNavigateUrl(params);
    const navigateUrl = isOverseas === 1 ? `/m/overseas/air/search?${url}` : `/m/domestic/air/search?${url}`;
    navigate(navigateUrl);
  };

  return (
    <>
      {dataRender.length ? (
        <div className="swiper-container">
          <Swiper
            slidesPerView={1.3}
            spaceBetween={10}
            className="swiper-wrapper !p-0"
            effect="coverflow"
            coverflowEffect={{
              rotate: 50,
              stretch: 0,
              depth: 100,
              modifier: 1,
              slideShadows: true,
            }}
          >
            {dataRender.map((item) => {
              let arrivalAirportText = "";
              if (item.sectionType == "MultiCity") {
                arrivalAirportText =
                  item.departureAirportNames[item.departureAirportNames.length - 1] +
                  "(" +
                  item.departureAirportCodes[item.departureAirportNames.length - 1] +
                  ")";
              } else {
                if (item.sectionType == "RoundTrip") {
                  arrivalAirportText = item.departureAirportNames[1] + "(" + item.departureAirportCodes[1] + ")";
                } else {
                  arrivalAirportText = item.arrivalAirportNames[0] + "(" + item.arrivalAirportCodes[0] + ")";
                }
              }

              return (
                <SwiperSlide key={item.id}>
                  <li className={`swiper-slide ${item.sectionType == "MultiCity" ? "multi" : item.sectionType == "RoundTrip" ? "round" : "oneway"}`}>
                    <a className="item !h-[110px]" href="#none;" onClick={() => recentAirSearch(item.id)}>
                      <span className="label">{item.sectionType == "MultiCity" ? "다구간" : item.sectionType == "RoundTrip" ? "왕복" : "편도"}</span>
                      <span className="city">
                        <span className="arr">
                          {item.departureAirportNames[0]}({item.departureAirportCodes[0]})
                        </span>
                        <span className="arrow"></span>
                        <span className="dep">{arrivalAirportText}</span>
                        <span className="date">
                          {DateUtil.getPatternYmd(item.departureDays[0], ". ")} {DateUtil.getDayOfWeekName(item.departureDays[0])} -{" "}
                          {DateUtil.getPatternYmd(item.departureDays[item.departureDays.length - 1], ". ")}{" "}
                          {DateUtil.getDayOfWeekName(item.departureDays[item.departureDays.length - 1])}
                        </span>
                        <span className="etc">
                          승객 {item.adultCount} 명, {AirUtil.getSeatTypeName(item.departSeatTypeCode)}
                        </span>
                      </span>
                    </a>
                  </li>
                </SwiperSlide>
              );
            })}
          </Swiper>
        </div>
      ) : (
        <div className="side-padding" id="recentAirNoData">
          <div className="none-item">최근 검색한 스케줄이 없습니다.</div>
        </div>
      )}
    </>
  );
}

export default RecentAirSearchSchedule;
