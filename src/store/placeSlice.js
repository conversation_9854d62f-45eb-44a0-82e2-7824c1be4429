import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  place: {},
};

const slice = createSlice({
  name: "placeSlice",
  initialState,
  reducers: {
    choosePlace: (state, action) => {
      state.place = action.payload;
    },
    removePlace: (state) => {
      state.place = {};
    },
  },
});

export const selectPlace = (state) => state.placeSlice.place;

export const { choosePlace, removePlace } = slice.actions;

export default slice.reducer;
