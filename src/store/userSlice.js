import { get } from "lodash";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { getBtmsSetting } from "@/service/air";
import { getMainAirports, getMainAirTravelPlaces } from "@/service/mainAirTravelPlaceService";
import { getCountryList, getRewardMileSetting, getUserInfo } from "@/service/common";
import { normalizeAirports } from "@/utils/common";

const initialState = {
  btmsSetting: {},
  userAirports: {},
  rewardMileSettings: {},
  isKrReward: false,
  mainTravelPlaces: {},
  info: {
    userInfo: {},
    gnbUserInfo: {},
    menuAuthRole: {},
    homepageSettingInfo: {},
  },
  countryList: [],
};

export const actionGetBtmsSetting = createAsyncThunk("user/actionGetBtmsSetting", async (data, { rejectWithValue }) => {
  try {
    return getBtmsSetting(data);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetMainAirTravelPlaces = createAsyncThunk("user/actionGetMainAirTravelPlaces", async (data, { rejectWithValue }) => {
  try {
    return await getMainAirTravelPlaces();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetUserAirport = createAsyncThunk("user/actionGetUserAirport", async (data, { rejectWithValue }) => {
  try {
    return getMainAirports();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetUserInfo = createAsyncThunk("user/actionGetUserInfo", async (_, { rejectWithValue }) => {
  try {
    return getUserInfo();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetCountryList = createAsyncThunk("user/actionGetCountryList", async (_, { rejectWithValue }) => {
  try {
    return getCountryList();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetRewardMileSetting = createAsyncThunk("user/actionGetRewardMileSetting", async (_, { rejectWithValue }) => {
  try {
    return getRewardMileSetting();
  } catch (error) {
    return rejectWithValue(error);
  }
});

const slice = createSlice({
  name: "user",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(actionGetBtmsSetting.fulfilled, (state, action) => {
        state.btmsSetting = get(action, "payload.data");
      })
      .addCase(actionGetMainAirTravelPlaces.fulfilled, (state, action) => {
        state.mainTravelPlaces = get(action, "payload");
      })
      .addCase(actionGetUserAirport.fulfilled, (state, action) => {
        const data = get(action, "payload", {});
        state.userAirports = normalizeAirports(data);
      })

      .addCase(actionGetUserInfo.fulfilled, (state, action) => {
        state.info = get(action, "payload.data");
      })
      .addCase(actionGetUserInfo.rejected, (state) => {
        state.info = initialState.info;
      })

      .addCase(actionGetCountryList.fulfilled, (state, action) => {
        const data = get(action, "payload.data.data", []);
        const koreaItem = data.find((item) => item.code2 === "KR");
        const nextCountryList = koreaItem ? [koreaItem, ...data.filter((item) => item.code2 !== "KR")] : data;
        state.countryList = nextCountryList;
      })
      .addCase(actionGetCountryList.rejected, (state) => {
        state.countryList = [];
      })

      .addCase(actionGetRewardMileSetting.pending, (state) => {
        state.rewardMileSettings = {};
        state.isKrReward = false;
      })

      .addCase(actionGetRewardMileSetting.fulfilled, (state, action) => {
        const data = get(action, "payload.data.data", {});
        console.log(data);

        state.isKrReward = data.isUse && data.rewardMileType === "KE";
        state.rewardMileSettings = data;
      })

      .addCase(actionGetRewardMileSetting.rejected, (state) => {
        state.rewardMileSettings = {};
        state.isKrReward = false;
      });
  },
});

export const selectBtmsSetting = (state) => state.userSlice.btmsSetting;
export const selectUserAirport = (state) => state.userSlice.userAirports;
export const selectMainAirTravelPlaces = (state) => state.userSlice.mainTravelPlaces;
export const selectUserInfo = (state) => state.userSlice.info;
export const selectCountryList = (state) => state.userSlice.countryList;
export const selectRewardMileSettings = (state) => state.userSlice.rewardMileSettings;
export const selectIsKrReward = (state) => state.userSlice.isKrReward;

export default slice.reducer;
