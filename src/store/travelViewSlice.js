import qs from "qs";
import { get } from "lodash";
import { getBtmsManager, normalizeBookingData } from "@/utils/app";
import request, { requestV2 } from "@/utils/request";
import { createAsyncThunk, createSlice, createSelector } from "@reduxjs/toolkit";

export const actionReservationTravelView = createAsyncThunk("travelView/actionReservationTravelView", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/travel/overseas/${travelId}`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionBtmsManager = createAsyncThunk("travelView/actionBtmsManager", async ({ managerType }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/company/btmsManager`, method: "GET", params: { managerType } });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionCodeList = createAsyncThunk("travelView/actionCodeList", async (params, { rejectWithValue }) => {
  try {
    return await request({ url: `/api_v2/codes`, method: "GET", params });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionInvoiceList = createAsyncThunk("travelView/actionInvoiceList", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/travel/overseas/${travelId}/invoices`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionPayInfoList = createAsyncThunk("travelView/actionPayInfoList", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await request({ url: `/api_v2/user/overseas/air/payInfoList/${travelId}`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionETicketListMap = createAsyncThunk("travelView/actionETicketListMap", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await request({ url: `/api_v2/user/overseas/air/eticketListMap/${travelId}`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionCompareAirSchedule = createAsyncThunk("travelView/actionCompareAirSchedule", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/travel/overseas/${travelId}/compare-schedule`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionBookingAirTicket = createAsyncThunk("travelView/actionBookingAirTicket", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/travel/overseas/${travelId}/tickets`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionDocumentNumbers = createAsyncThunk("travelView/actionDocumentNumbers", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await request({ url: `api_v2/user/overseas/air/document-number/${travelId}`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionTravelHistories = createAsyncThunk("travelView/actionTravelHistories", async ({ travelId }, { rejectWithValue }) => {
  try {
    return await requestV2({ url: `/user/travel/${travelId}/histories`, method: "GET" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionAttachFileView = createAsyncThunk("travelView/actionAttachFileView", async ({ bookingAirId }, { rejectWithValue }) => {
  try {
    return await request({
      url: `/user/v2/reservation/travel/view/attachFile/view`,
      method: "POST",
      data: qs.stringify({ bookingAirId }, { arrayFormat: "repeat" }),
    });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionSearchUsaStayCity = createAsyncThunk("travelView/actionSearchUsaStayCity", async (params, { rejectWithValue }) => {
  try {
    return await requestV2({
      url: `/common/cities/search`,
      method: "GET",
      params: { countryId: 214, ...params },
    });
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionSearchUsaState = createAsyncThunk("travelView/actionSearchUsaState", async (params, { rejectWithValue }) => {
  try {
    return await requestV2({
      url: `/common/usa-states/search`,
      method: "GET",
      params,
    });
  } catch (error) {
    return rejectWithValue(error);
  }
});

const initialState = {
  data: {
    travel: {},
    bookingAirScheduleMap: {},
    bookingAirScheduleMapSize: null,
    travelModifyRequestRes: [],
    isAmericaSchedule: false,
    americaStayDTO: {},
  },
  usaStay: {
    isLoading: false,
    cities: [],
    states: [],
  },
  invoice: {
    invoiceMasterList: [],
  },
  payInfo: {
    receiptList: [],
    paymentList: [],
  },
  eticketListMap: {},
  codeList: [],
  compareAirSchedules: [],
  bookingAirTickets: [],
  documentNumbers: [],
  btmsManager: [],
  travelStatusHistories: [],
  viewFile: {
    attachFiles: [],
  },
};

const slice = createSlice({
  name: "travelView",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(actionReservationTravelView.fulfilled, (state, action) => {
        state.data = normalizeBookingData(get(action, "payload.data.data", {}));
      })
      .addCase(actionReservationTravelView.rejected, (state) => {
        state.data = initialState.data;
      })
      .addCase(actionCodeList.fulfilled, (state, action) => {
        state.codeList = get(action, "payload.data.codeList", []);
      })
      .addCase(actionCodeList.rejected, (state) => {
        state.codeList = initialState.codeList;
      })
      .addCase(actionInvoiceList.fulfilled, (state, action) => {
        state.invoice.invoiceMasterList = get(action, "payload.data.data.invoiceMasters", initialState.invoice.invoiceMasterList);
      })
      .addCase(actionInvoiceList.rejected, (state) => {
        state.invoice = initialState.invoice;
      })
      .addCase(actionPayInfoList.fulfilled, (state, action) => {
        state.payInfo = get(action, "payload.data", initialState.payInfo);
      })
      .addCase(actionPayInfoList.rejected, (state) => {
        state.payInfo = initialState.payInfo;
      })
      .addCase(actionETicketListMap.fulfilled, (state, action) => {
        state.eticketListMap = get(action, "payload.data", {});
      })
      .addCase(actionETicketListMap.rejected, (state) => {
        state.eticketListMap = initialState.eticketListMap;
      })
      .addCase(actionCompareAirSchedule.fulfilled, (state, action) => {
        state.compareAirSchedules = get(action, "payload.data.data", []);
      })
      .addCase(actionCompareAirSchedule.rejected, (state) => {
        state.compareAirSchedules = initialState.compareAirSchedules;
      })
      .addCase(actionBookingAirTicket.fulfilled, (state, action) => {
        state.bookingAirTickets = get(action, "payload.data.data", []);
      })
      .addCase(actionBookingAirTicket.rejected, (state) => {
        state.bookingAirTickets = initialState.bookingAirTickets;
      })
      .addCase(actionDocumentNumbers.fulfilled, (state, action) => {
        state.documentNumbers = get(action, "payload.data.documentNumbers", []);
      })
      .addCase(actionDocumentNumbers.rejected, (state) => {
        state.documentNumbers = initialState.documentNumbers;
      })
      .addCase(actionBtmsManager.fulfilled, (state, action) => {
        state.btmsManager = get(action, "payload.data", []);
      })
      .addCase(actionBtmsManager.rejected, (state) => {
        state.btmsManager = initialState.btmsManager;
      })
      .addCase(actionTravelHistories.fulfilled, (state, action) => {
        state.travelStatusHistories = get(action, "payload.data.data", []);
      })
      .addCase(actionTravelHistories.rejected, (state) => {
        state.travelStatusHistories = initialState.travelStatusHistories;
      })
      .addCase(actionAttachFileView.fulfilled, (state, action) => {
        state.viewFile.attachFiles = get(action, "payload.data.attachFiles", []);
      })
      .addCase(actionAttachFileView.rejected, (state) => {
        state.viewFile.attachFiles = initialState.viewFile.attachFiles;
      })
      .addCase(actionSearchUsaStayCity.pending, (state) => {
        state.usaStay.isLoading = true;
      })
      .addCase(actionSearchUsaStayCity.fulfilled, (state, action) => {
        const cities = get(action, "payload.data.data", []);
        state.usaStay.cities = cities.filter((item) => item.cityCode && item.name);
        state.usaStay.isLoading = false;
      })
      .addCase(actionSearchUsaStayCity.rejected, (state) => {
        state.usaStay.cities = initialState.usaStay.cities;
        state.usaStay.isLoading = false;
      })
      .addCase(actionSearchUsaState.pending, (state) => {
        state.usaStay.isLoading = true;
      })
      .addCase(actionSearchUsaState.fulfilled, (state, action) => {
        state.usaStay.states = get(action, "payload.data.data", []);
        state.usaStay.isLoading = false;
      })
      .addCase(actionSearchUsaState.rejected, (state) => {
        state.usaStay.states = initialState.usaStay.states;
        state.usaStay.isLoading = false;
      });
  },
});

export const selectReservationTravelView = (state) => state.travelViewSlice;
export const selectBtmsManager = (state) => state.travelViewSlice.btmsManager;
export const selectUsaStayLoading = (state) => state.travelViewSlice.usaStay.isLoading;
export const selectUsaCities = (state) => state.travelViewSlice.usaStay.cities;
export const selectUsaStates = (state) => state.travelViewSlice.usaStay.states;
export const selectBtmsManager4Visa = createSelector([selectBtmsManager], (btmsManagers) => getBtmsManager(btmsManagers));

export default slice.reducer;
