import { TYPE_MOBILE_LOADING } from "@/constants/app";

import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  appLoading: {
    type: TYPE_MOBILE_LOADING.NONE,
    message: "",
    open: false,
    data: {},
  },
  progressBar: {
    isOpen: false,
    type: "",
  },
};

const slice = createSlice({
  name: "loadingSliceMobile",
  initialState,
  reducers: {
    actionMobileLoading: (state, action) => {
      state.appLoading = action.payload;
    },
    actionProgressBar: (state, action) => {
      state.progressBar = action.payload;
    },
  },
});

export const { actionMobileLoading, actionProgressBar } = slice.actions;
export const selectAppLoading = (state) => state.loadingSliceMobile.appLoading;
export const selectProgressBar = (state) => state.loadingSliceMobile.progressBar;

export default slice.reducer;
