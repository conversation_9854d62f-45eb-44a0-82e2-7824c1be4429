import { cloneDeep, get, set } from "lodash";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { postAirSearch, getTravelRuleBase, getAirportCityList, getSavedFareRule } from "@/service/air";
import { postCalcCommission } from "@/service/common";

const initialState = {
  airSeach: {},
  travelRuleBase: {},
  airOverseasSessionData: {
    airCompareSchedules: [],
    data: {},
    lowestFareAndSchedule: {},
    filter: {},
  },
  fareRule: [],
  arrivalCityCodeMap: [],
};

export const actionAirSearch = createAsyncThunk("air/actionAirSearch:loadingSearchBg", async (data, { rejectWithValue }) => {
  try {
    const response = await postAirSearch(data);
    const clonedResponse = cloneDeep(response);

    // We call all postCalcCommission here
    const payload = response.data.data.journeyInfos[0].journeys.map((item) => {
      return {
        fareAmount: item.fares.paxTypeFares[0].airFare,
        discountAmount: 0,
        taxAmount: item.fares.paxTypeFares[0].airTax,
        seatTypeCode: data.cabinClass,
      };
    });
    const responseCaledCommission = await postCalcCommission(payload);
    const dataCaledCommission = responseCaledCommission.data.values;
    const newResponse = response.data.data.journeyInfos[0].journeys.map((flight, index) => {
      return set(flight, "fares.paxTypeFares[0].tasfAmount", dataCaledCommission[index] || 0);
    });

    // override the response
    clonedResponse.data.data.journeyInfos[0].journeys = newResponse;

    return clonedResponse;
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionTravelRuleBase = createAsyncThunk("air/actionTravelRuleBase", async (data, { rejectWithValue }) => {
  try {
    return getTravelRuleBase(data);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionAirportCity = createAsyncThunk("air/actionAirportCity", async (data, { rejectWithValue }) => {
  try {
    return getAirportCityList(data);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionSavedFareRule = createAsyncThunk("air/actionSavedFareRule", async (data, { rejectWithValue }) => {
  try {
    return getSavedFareRule(data);
  } catch (error) {
    return rejectWithValue(error);
  }
});

const slice = createSlice({
  name: "air",
  initialState,
  reducers: {
    actionSetAirOverseasSessionData: (state, action) => {
      state.airOverseasSessionData = { ...state.airOverseasSessionData, ...action.payload };
    },

    actionClearAirOverseasSessionData: (state) => {
      state.airOverseasSessionData = initialState.airOverseasSessionData;
    },

    actionClearAirSearch: (state) => {
      state.airSeach = initialState.airSeach;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(actionAirSearch.fulfilled, (state, action) => {
        state.airSeach = get(action, "payload.data");
      })
      .addCase(actionAirSearch.rejected, (state) => {
        state.airSeach = {};
      })

      .addCase(actionTravelRuleBase.fulfilled, (state, action) => {
        state.travelRuleBase = get(action, "payload.data");
      })
      .addCase(actionTravelRuleBase.rejected, (state) => {
        state.travelRuleBase = {};
      })

      .addCase(actionAirportCity.fulfilled, (state, action) => {
        state.arrivalCityCodeMap = get(action, "payload.data.airports", []);
      })
      .addCase(actionAirportCity.rejected, (state) => {
        state.arrivalCityCodeMap = [];
      })
      .addCase(actionSavedFareRule.fulfilled, (state, action) => {
        state.fareRule = get(action, "payload.data.data.travelRuleItems", []);
      })
      .addCase(actionSavedFareRule.rejected, (state) => {
        state.fareRule = [];
      });
  },
});

export const selectAirSearch = (state) => state.airSlice.airSeach;
export const selectTravelRuleBase = (state) => state.airSlice.travelRuleBase;
export const selectAirOverseasSessionData = (state) => state.airSlice.airOverseasSessionData;
export const selectAirportCity = (state) => state.airSlice.arrivalCityCodeMap;
export const selectSavedFareRule = (state) => state.airSlice.fareRule;

export const { actionSetAirOverseasSessionData, actionClearAirOverseasSessionData, actionClearAirSearch } = slice.actions;

export default slice.reducer;
