import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  loadingSearchCount: 0,
  loadingSearchBgCount: 0,
  loadingPaymentCount: 0,
  loadingPaymentBgCount: 0,
  loadingBookingNoticeCount: 0,
  loadingBookingCount: 0,
  loadingDefaultCount: 0,
  loadingHotelCount: 0,
  loadingNormalCount: 0,

  searchBgInfo: {
    desc: "",
    dayText: "",
    passengerText: "",
  },
};

function getKey(action) {
  return action.type.split(":")[1].split("/")[0] + "Count";
}

const slice = createSlice({
  name: "loadingUser",
  initialState,
  reducers: {
    actionIncreaseLoadingCount: (state, action) => {
      state[action.payload] += 1;
    },
    actionDecreaseLoadingCount: (state, action) => {
      state[action.payload] -= 1;
    },
    actionSetSearchBgInfo: (state, action) => {
      state.searchBgInfo = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addMatcher(
        (action) => action.type.includes("/pending") && action.type.includes("loading"),
        (state, action) => {
          const isAirSearch = action.type.includes("actionAirSearch");
          if (isAirSearch) {
            state[getKey(action)] += 2;
          } else {
            state[getKey(action)] += 1;
          }
        },
      )
      .addMatcher(
        (action) => action.type.includes("/fulfilled") && action.type.includes("loading"),
        (state, action) => {
          state[getKey(action)] -= 1;
        },
      )
      .addMatcher(
        (action) => action.type.includes("/rejected") && action.type.includes("loading"),
        (state, action) => {
          const isAirSearch = action.type.includes("actionAirSearch");
          if (isAirSearch) {
            state[getKey(action)] -= 2;
          }
          state[getKey(action)] -= 1;
        },
      );
  },
});

export const selectLoadingUser = (state) => state.loadingUserSlice;

export const { actionIncreaseLoadingCount, actionDecreaseLoadingCount, actionSetSearchBgInfo } = slice.actions;

export default slice.reducer;
