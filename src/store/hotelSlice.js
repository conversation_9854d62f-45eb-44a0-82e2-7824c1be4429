import { get } from "lodash";
import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { HOTEL_AUTOSEARCH_TYPE, HOTEL_RANGE_FARE_TYPE } from "@/constants/app";
import {
  getHotelCities,
  getHotelContent,
  getHotelDetailCodes,
  getHotelExchangeRate,
  getHotelFareList,
  getHotelLocation,
  getHotelTravelRule,
  getHotelViewAjax,
  postCancelCharges,
  postHotelNotice,
} from "@/service/hotel";
import { postHotelAutoSearch } from "@/service/common";
import { normalizeParamsHotelSearch } from "@/utils/app";

const initialState = {
  hotelLocation: null,
  hotelExchangeRate: 1,
  hotelSearchType: HOTEL_AUTOSEARCH_TYPE.CITY,
  hotelSearch: {},
  hotelCancelRes: {},
  hotelNotice: {},
  hotelView: {},
  hotelRoomView: {},
  hotelTravelRule: {},
  hotelCities: [],
  condition: {
    rangeFareType: Object.keys(HOTEL_RANGE_FARE_TYPE)[0],
    arrayGradeCode: [""],
    arrayFacilityCode: [""],
    fareRange: [0, 0],
    nightCount: 1,
    maxAveragePrice: 0,
    minSalePrice: 0,
    minAveragePrice: 0,
    maxSalePrice: 0,
    gradeCodeMap: {},
    facilityCodeMap: {},
  },
  hotelAutoSearch: [],
  hotelDetailCodes: {},
  filter: {},
  hotelList: [],
  sortBy: "recommend",
  hotelCompareList: [],
  selectedHotel: null,
};

export const actionHotelSearch = createAsyncThunk("hotel/actionHotelSearch", async (params, { rejectWithValue }) => {
  try {
    return getHotelFareList(normalizeParamsHotelSearch(params));
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelDetailCodes = createAsyncThunk("hotel/actionHotelDetailCodes", async (params, { rejectWithValue }) => {
  try {
    return getHotelDetailCodes(params);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelAutoSearch = createAsyncThunk("hotel/actionHotelAutoSearch", async (data, { rejectWithValue }) => {
  try {
    return postHotelAutoSearch(data);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelCancelRes = createAsyncThunk("hotel/actionHotelCancelRes:loadingDefault", async (data, { rejectWithValue }) => {
  try {
    const { htlMasterId, ...rest } = data;
    return postCancelCharges(rest, htlMasterId);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelNotice = createAsyncThunk("hotel/actionHotelNotice:loadingDefault", async (data, { rejectWithValue }) => {
  try {
    const { htlMasterId, ...rest } = data;
    return postHotelNotice(rest, htlMasterId);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelView = createAsyncThunk("hotel/actionHotelView", async (htlMasterId, { rejectWithValue }) => {
  try {
    return getHotelContent(htlMasterId);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelRoomView = createAsyncThunk("hotel/actionHotelRoomView", async (params, { rejectWithValue }) => {
  try {
    return getHotelViewAjax(params);
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelCities = createAsyncThunk("hotel/actionHotelCities", async (_, { rejectWithValue }) => {
  try {
    return getHotelCities();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelTravelRule = createAsyncThunk("hotel/actionHotelTravelRule", async (_, { rejectWithValue }) => {
  try {
    return getHotelTravelRule();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelExchangeRate = createAsyncThunk("hotel/actionHotelExchangeRate", async (_, { rejectWithValue }) => {
  try {
    return getHotelExchangeRate();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionHotelLocation = createAsyncThunk("hotel/actionHotelLocation", async (params, { rejectWithValue }) => {
  try {
    return getHotelLocation(params);
  } catch (error) {
    return rejectWithValue(error);
  }
});

const slice = createSlice({
  name: "hotel",
  initialState,
  reducers: {
    actionSelectHotel(state, action) {
      state.selectedHotel = action.payload;
    },
    actionSetHotelList(state, action) {
      state.hotelList = action.payload;
    },
    actionSetSortBy(state, action) {
      state.sortBy = action.payload;
    },
    actionClearSortBy(state) {
      state.sortBy = "recommend";
    },
    actionSetHotelFilter(state, action) {
      state.filter = { ...state.filter, ...action.payload };
    },
    actionSetHotelCondition(state, action) {
      state.condition = { ...state.condition, ...action.payload };
    },
    actionAddHotelCompare(state, action) {
      state.hotelCompareList = [...state.hotelCompareList, action.payload];
    },
    actionRemoveHotelCompareById(state, action) {
      state.hotelCompareList = state.hotelCompareList.filter((hotel) => hotel.htlDetailInfo?.htlMasterId !== action.payload);
    },
    actionClearHotelCompare(state) {
      state.hotelCompareList = [];
    },
    actionClearAutoSearch(state) {
      state.hotelAutoSearch = [];
    },
    actionClearExchangeRate(state) {
      state.hotelExchangeRate = 1;
    },
    actionSetHotelSearchType(state, action) {
      state.hotelSearchType = action.payload;
    },
    actionClearLocation(state) {
      state.hotelLocation = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(actionHotelSearch.fulfilled, (state, action) => {
        const hotelFareList = get(action, "payload.data.data", []);
        state.hotelSearch = {
          ...state.hotelSearch,
          hotelFareList: hotelFareList.map((hotel, index) => ({ ...hotel, segNo: index, id: `${hotel.htlMasterId}${hotel.hotelCode}${index}` })),
        };
      })
      .addCase(actionHotelSearch.rejected, (state) => {
        state.hotelSearch = {};
      })

      .addCase(actionHotelDetailCodes.fulfilled, (state, action) => {
        state.hotelDetailCodes = get(action, "payload.data.data", {});
      })
      .addCase(actionHotelDetailCodes.rejected, (state) => {
        state.hotelDetailCodes = {};
      })

      .addCase(actionHotelAutoSearch.fulfilled, (state, action) => {
        const keyword = action.meta.arg.keyword;
        const defaultKeyword = {
          regionId: "",
          regionName: keyword,
          regionNameLong: keyword,
          type: HOTEL_AUTOSEARCH_TYPE.GOOGLEMAP,
        };

        const cityList = get(action, "payload.data.cityList", []).map((el) => ({ ...el, type: HOTEL_AUTOSEARCH_TYPE.CITY }));
        const hotelList = get(action, "payload.data.hotelList", []).map((el) => ({ ...el, type: HOTEL_AUTOSEARCH_TYPE.HOTEL }));
        const landmarkList = get(action, "payload.data.landmarkList", []).map((el) => ({ ...el, type: HOTEL_AUTOSEARCH_TYPE.LANDMARK }));
        const googleMapPlaceList = get(action, "payload.data.googleMapPlaceList", []).map((el) => ({
          ...el,
          regionNameLong: el?.regionName || "",
          type: HOTEL_AUTOSEARCH_TYPE.GOOGLEMAP,
        }));

        let selectedHotelAutoSearchList = [...cityList, ...hotelList, ...landmarkList];
        if (cityList.length === 0 && hotelList.length === 0 && landmarkList.length === 0) {
          selectedHotelAutoSearchList = googleMapPlaceList;
        }

        if (keyword !== "" && keyword !== undefined && keyword !== null) {
          selectedHotelAutoSearchList.push(defaultKeyword);
        }

        state.hotelAutoSearch = selectedHotelAutoSearchList;
      })
      .addCase(actionHotelAutoSearch.rejected, (state) => {
        state.hotelAutoSearch = [];
      })

      .addCase(actionHotelCancelRes.fulfilled, (state, action) => {
        state.hotelCancelRes = get(action, "payload.data", {});
      })
      .addCase(actionHotelCancelRes.rejected, (state) => {
        state.hotelCancelRes = {};
      })

      .addCase(actionHotelNotice.fulfilled, (state, action) => {
        state.hotelNotice = get(action, "payload.data", {});
      })
      .addCase(actionHotelNotice.rejected, (state) => {
        state.hotelNotice = {};
      })

      .addCase(actionHotelView.fulfilled, (state, action) => {
        state.hotelView = {
          hotelContent: get(action, "payload.data.data", {}),
        };
      })
      .addCase(actionHotelView.rejected, (state) => {
        state.hotelView = {};
      })

      .addCase(actionHotelRoomView.fulfilled, (state, action) => {
        const roomFareList = get(action, "payload.data.roomFareList", []);
        state.hotelRoomView = get(action, "payload.data", {});
        state.hotelRoomView.roomFareList = roomFareList.map((room, index) => ({ ...room, id: `${room.accountCode}${index}` }));
      })
      .addCase(actionHotelRoomView.rejected, (state) => {
        state.hotelRoomView = {};
      })

      .addCase(actionHotelCities.fulfilled, (state, action) => {
        state.hotelCities = get(action, "payload.data.data", []);
      })
      .addCase(actionHotelCities.rejected, (state) => {
        state.hotelCities = [];
      })

      .addCase(actionHotelTravelRule.fulfilled, (state, action) => {
        state.hotelTravelRule = get(action, "payload.data.data", {});
      })
      .addCase(actionHotelTravelRule.rejected, (state) => {
        state.hotelTravelRule = {};
      })

      .addCase(actionHotelExchangeRate.fulfilled, (state, action) => {
        state.hotelExchangeRate = get(action, "payload.data.data", 1);
      })
      .addCase(actionHotelExchangeRate.rejected, (state) => {
        state.hotelExchangeRate = 1;
      })

      .addCase(actionHotelLocation.fulfilled, (state, action) => {
        state.hotelLocation = get(action, "payload.data.data", {});
      })
      .addCase(actionHotelLocation.rejected, (state) => {
        state.hotelLocation = {};
        state.hotelSearch = {};
      });
  },
});

export const selectHotelSearch = (state) => state.hotelSlice.hotelSearch;
export const selectSelectedHotel = (state) => state.hotelSlice.selectedHotel;
export const selectHotelList = (state) => state.hotelSlice.hotelList;
export const selectSortBy = (state) => state.hotelSlice.sortBy;
export const selectFilterHotel = (state) => state.hotelSlice.filter;
export const selectConditionHotel = (state) => state.hotelSlice.condition;
export const selectHotelCompareList = (state) => state.hotelSlice.hotelCompareList;
export const selectHotelView = (state) => state.hotelSlice.hotelView;
export const selectHotelRoomView = (state) => state.hotelSlice.hotelRoomView;
export const selectHotelDetailCodes = (state) => state.hotelSlice.hotelDetailCodes;
export const selectHotelAutoSearch = (state) => state.hotelSlice.hotelAutoSearch;
export const selectHotelCancelRes = (state) => state.hotelSlice.hotelCancelRes;
export const selectHotelNotice = (state) => state.hotelSlice.hotelNotice;
export const selectHotelCities = (state) => state.hotelSlice.hotelCities;
export const selectHotelTravelRule = (state) => state.hotelSlice.hotelTravelRule;
export const selectHotelExchangeRate = (state) => state.hotelSlice.hotelExchangeRate;
export const selectHotelSearchType = (state) => state.hotelSlice.hotelSearchType;
export const selectHotelLocation = (state) => state.hotelSlice.hotelLocation;

export const {
  actionSelectHotel,
  actionSetHotelList,
  actionSetSortBy,
  actionSetHotelFilter,
  actionSetHotelCondition,
  actionAddHotelCompare,
  actionRemoveHotelCompareById,
  actionClearAutoSearch,
  actionClearHotelCompare,
  actionClearExchangeRate,
  actionClearSortBy,
  actionSetHotelSearchType,
  actionClearLocation,
} = slice.actions;

export default slice.reducer;
