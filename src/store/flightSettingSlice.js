import { getFlightSetting } from "@/service/flightSetting";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

const initialState = {
  contents: null,
  isLoading: false,
};

export const actionGetFlightSetting = createAsyncThunk("flightSetting/getFlightSetting", async (_, { rejectWithValue }) => {
  try {
    return getFlightSetting();
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const flightSettingSlice = createSlice({
  name: "flightSetting",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(actionGetFlightSetting.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(actionGetFlightSetting.fulfilled, (state, action) => {
      state.isLoading = false;
      state.contents = action.payload?.data;
    });
    builder.addCase(actionGetFlightSetting.rejected, (state) => {
      state.isLoading = false;
      state.contents = null;
    });
  },
});

export const selectFlightSetting = (state) => state.flightSettingSlice.contents;
export const selectFlightSettingLoading = (state) => state.flightSettingSlice.isLoading;

export default flightSettingSlice.reducer;
