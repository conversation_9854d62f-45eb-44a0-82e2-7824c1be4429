import { getMainAirTravelPlaces, getMainAirports } from "@/service/mainAirTravelPlaceService";
import request from "@/utils/request";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import { get } from "lodash";

export const actionMain = createAsyncThunk("air/actionAirSearch", async (_, { rejectWithValue }) => {
  try {
    const [mainAirTravelPlaces, mainAirports] = await Promise.all([getMainAirTravelPlaces(), getMainAirports()]);
    return {
      ...mainAirTravelPlaces,
      ...mainAirports,
    };
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const actionGetAccountInfo = createAsyncThunk("air/actionGetAccountInfo", async (_, { rejectWithValue }) => {
  try {
    return await request({ url: `/customer/m/account/getAjax`, method: "POST" });
  } catch (error) {
    return rejectWithValue(error);
  }
});

const initialState = {
  isLoading: false,
  data: {
    airportMainSections: [],
    faqs: [],
    mainAirTravelPlaces: [],
    notices: [],
    domestic: [],
    overseas: [],
  },
  airCompareSchedule: {
    isOpen: false,
    isOnlyView: false,
    params: {},
    selectedFares: [],
    journeyInfos: [],
    onOpenDetailInfoSearch: () => {},
    onChangeSelectedFares: () => {},
    onOpenBookingNotice: () => {},
    onOpenTravelPolicy: () => {},
  },
  accountInfo: {
    customer: {},
    workspaceList: [],
    positionList: [],
    countryList: [],
  },
};

const slice = createSlice({
  name: "mainMobile",
  initialState,
  reducers: {
    actionOpenAirCompareSchedule: (state, action) => {
      const { payload } = action;
      const nextState = payload.isOnlyView ? { ...state.airCompareSchedule, ...payload } : { ...payload, isOnlyView: false };
      state.airCompareSchedule = { ...nextState, isOpen: true };
    },
    actionCloseAirCompareSchedule: (state) => {
      state.airCompareSchedule = { ...state.airCompareSchedule, isOpen: false };
    },
    actionUpdateAirCompareSchedule: (state, action) => {
      state.airCompareSchedule = { ...state.airCompareSchedule, selectedFares: action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(actionMain.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(actionMain.fulfilled, (state, action) => {
        localStorage.setItem("airMainData", JSON.stringify(action.payload));
        state.isLoading = false;
        state.data = action.payload;
      })
      .addCase(actionMain.rejected, (state) => {
        state.data = initialState.data;
        state.isLoading = false;
      })
      .addCase(actionGetAccountInfo.fulfilled, (state, action) => {
        state.accountInfo = get(action, "payload.data", initialState.accountInfo);
      })
      .addCase(actionGetAccountInfo.rejected, (state) => {
        state.accountInfo = initialState.accountInfo;
      });
  },
});

export const { actionOpenAirCompareSchedule, actionCloseAirCompareSchedule, actionUpdateAirCompareSchedule } = slice.actions;

export const selectDataMain = (state) => state.mainSliceMobile.data;
export const selectLoading = (state) => state.mainSliceMobile.isLoading;
export const selectAirCompareSchedule = (state) => state.mainSliceMobile.airCompareSchedule;
export const selectAccountInfo = (state) => state.mainSliceMobile.accountInfo;

export default slice.reducer;
