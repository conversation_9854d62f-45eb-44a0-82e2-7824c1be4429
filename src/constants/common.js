export const AIR_SEARCH_ORDER_BY = {
  recommend: "추천 (기업요금 낮은순+직항+총 소요시간)",
  lowDirectAmount: "요금낮은순+직항",
  lowBoardingTime: "탑승시간 짧은순",
  lowAmount: "요금 낮은순",
  corporateFareLowAmount: "기업운임 낮은순",
  fastDepartTime: "출발시간 빠른 순",
};
export const CONFIG_DATE = {
  autoApply: true,
  autoUpdateInput: true,
  locale: {
    format: "MM/DD/YYYY",
    separator: " - ",
    applyLabel: "Apply",
    cancelLabel: "Cancel",
    fromLabel: "From",
    toLabel: "To",
    customRangeLabel: "Custom",
    daysOfWeek: ["일", "월", "화", "수", "목", "금", "토"],
    monthNames: ["1월", "2월", "3월", "4월", "5월", "6월", "7월", "8월", "9월", "10월", "11월", "12월"],
    firstDay: 0,
  },
};

export const MAPPING_SECTION_TYPE_KR = {
  OneWay: "편도",
  RoundTrip: "왕복",
  MultiCity: "다구간",
};

export const MAPPING_SEAT_TYPE_CODE_KR = {
  M: "일반석",
  W: "프리미엄이코노미석",
  C: "비즈니스석",
  F: "일등석",
  Y: "이코노미",
};

export const MAPPING_UNIT = { EA: "개", PC: "개", KG: "KG" };

export const IGNORE_CREAT_PNR_FORM_LIST = [
  "reserverCheck",
  "ruleConfirmAllCheck",
  "documentNumber",
  "reservationRuleAgreementDTO.isAgreeReserverInfo",
  "reservationRuleAgreementDTO.isNameSamePassportRule",
];
export const BIRTHDAY_LIST = ["year", "month", "day"];

export const DEFAULT_COUNTRY = { code2: "", name: "국적" };

export const CONTACT_ADMIN_ALERT = "\n지속적으로 시스템 오류가 발생하면 <EMAIL> 해당 메일로 접수해주시기 바랍니다.";
