import { momentKR } from "@/utils/date";

export const DATE_FORMAT = "YYYY-MM-DD";
export const DEFAULT_LAYOUT = "default";
export const NONE_LAYOUT = "none";
export const MOBILE_LAYOUT = "mobile_layout";
export const ERROR_MESSAGE = "Error";
export const SERVER_ERROR_MESSAGE = "시스템 장애가 발생했습니다.\n다시 시도해주세요.";

export const SECTION_TYPE = {
  MULTICITY: "MultiCity",
  ROUNDTRIP: "RoundTrip",
  ONEWAY: "OneWay",
};
export const COP_CODE = "기업운임";

export const EXECPT_TIME_TYPE = {
  BORDING: "탑승시간",
  TOTAL: "총 소요시간",
};

export const GDS_TYPE = {
  AMADEUS: "AMADEUS",
};

export const TYPE_MOBILE_LOADING = Object.freeze({
  DEFAULT: "default",
  HOTEL: "hotel",
  SEARCH: "search",
  NONE: "none",
});

export const selectSectionType = Object.freeze({
  ROUND: "type-round",
  ONE_WAY: "type-oneway",
  MULTI: "type-multi",
});

export const AXIOS_STATUS_CODE = {
  SUCCESS: 200,
  ACCESS_DENIED: 403,
  LOGIN_EXPIRED: 401,
};

export const ALLIANCE_KR = Object.freeze({
  SkyTeam: "스카이팀",
  Star: "스타얼라이언스",
  OneWorld: "원월드",
});

export const MAPPED_SEAT_TYPE = {
  M: "일반석",
  W: "프리미엄이코노미석",
  C: "비즈니스석",
  F: "일등석",
};

export const MAPPED_STATUS_CODE = {
  Requested: { id: 392, name: "예약" },
  Rejected: { id: 393, name: "반려" },
  Approved: { id: 394, name: "승인" },
  TicketingConfirmed: { id: 395, name: "발권확인중" },
  Completed: { id: 396, name: "완료" },
  Cancelled: { id: 397, name: "취소" },
  ReservationRequested: { id: 209, name: "예약요청" },
  ReservationInProgress: { id: 210, name: "예약중" },
  ReservationCompleted: { id: 211, name: "예약완료" },
  TicketingRequested: { id: 212, name: "발권요청" },
  TicketingCompleted: { id: 213, name: "발권완료" },
  ReservationCancelled: { id: 214, name: "취소" },
  TicketingInProgress: { id: 348, name: "발권중" },
  ReservationCancelling: { id: 349, name: "취소중" },
};

export const CHARGE_TYPE = {
  TICKET: "발권금액",
  UP: "업금액",
  TASF: "취급수수료",
  PENALTY: "항공사패널티",
  REFUND_FEE: "항공사 환불수수료",
  VISA: "비자 금액",
  INSURANCE: "보험 금액",
  HOTEL: "호텔 금액",
  HOTEL_ETC: "호텔 기타매출 금액",
  BSP: "BSP",
  AIR_VI: "항공VI",
  DOMESTIC_VI: "국내선VI",
  DOMESTIC_REFUND: "국내선환급",
};

export const COMPANY_SITECODE = {
  KAKAO_COMPANY_SITECODE: "D76",
  MASTERCARD_COMPANY_SITECODE: "G78",
  KORTEK_COMPANY_SITECODE: "C78",
};

export const BTMS_MANAGER_CODE = {
  AIR: "Air",
  HOTEL_RENT_CAR: "Hotel_RentCar",
  PASSPORT_VISA: "Passport_Visa",
  BIZ_TRAINING: "BizTraining",
  SYSTEM_ETC: "System_Etc",
};

export const REGION_TYPE = { OVERSEAS: "OVERSEAS", DOMESTIC: "DOMESTIC" };

export const DIVIDER_DIRECTION = { VERTICAL: "VERTICAL", HORIZONTAL: "HORIZONTAL" };

export const HOTEL_SORT_CONDITIONS = {
  recommend: "추천순",
  lowAmount: "가격 낮은 순",
  highAmount: "가격 높은 순",
  lowGrade: "성급 낮은 순",
  highGrade: "성급 높은 순",
};

export const Y_N = { Y: "Y", N: "N" };

export const ROOM_HOTEL_FILTER_MAP = {
  all: "all",
  breakfastY: "breakfastY",
  breakfastN: "breakfastN",
  freeCancleYn: "freeCancleYn",
  fareRule: "fareRule",
};

export const ROOM_HOTEL_FILTER = [
  { id: 1, label: "전체", value: ROOM_HOTEL_FILTER_MAP.all },
  { id: 2, label: "조식포함", value: ROOM_HOTEL_FILTER_MAP.breakfastY },
  { id: 3, label: "조식불포함", value: ROOM_HOTEL_FILTER_MAP.breakfastN },
  { id: 4, label: "무료취소", value: ROOM_HOTEL_FILTER_MAP.freeCancleYn },
  { id: 5, label: "출장규정에 적합", value: ROOM_HOTEL_FILTER_MAP.fareRule },
];

export const HOTEL_CONDITION_MAP = {
  gradeCodeMap: "arrayGradeCode",
  categoryCodeMap: "categoryCodeMap",
  facilityCodeMap: "arrayFacilityCode",
  rangeFareType: "rangeFareType",
  fareRange: "fareRange",
};

export const NO_STAR = "성급없음";
export const INPUT_RANGE_TYPE = { MIN: "MIN", MAX: "MAX" };

export const HOTEL_RANGE_FARE_TYPE = {
  average: "1박당 요금 기준",
  total: "총 요금 기준",
};

export const DEAULT_POP_SLIDE_IMAGE_TITLE = "전체사진";
export const SNACK_BAR_TIMEOUT = 2500;
export const MAX_HOTEL_CLASS_MEMBER = 10;
export const HOTEL_CALENDAR_TEMPLATE = `<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-info" style="color:red; padding: 0 10px 10px 10px;">※ 28박 이상의 경우 <a href="/user/v2/board/qna/list" style="color:red;font-weight: bold;text-decoration: underline;">1:1 문의</a>를 이용하여 요청 주시기 바랍니다.</div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>`;
export const NOMAL_CALENDAR_TEMPLAYE = `<div class="daterangepicker"><div class="ranges"></div><div class="drp-calendar left"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-calendar right"><div class="calendar-table"></div><div class="calendar-time"></div></div><div class="drp-buttons"><span class="drp-selected"></span><button class="cancelBtn" type="button"></button><button class="applyBtn" disabled="disabled" type="button"></button> </div></div>`;
export const SNACK_BAR_TYPE = {
  INFO: "info",
  SUCCESS: "success",
  DANGER: "danger",
};

export const HOTEL_COMPARE_SECTION = {
  GENERAL: "GENERAL",
  LOCATION: "LOCATION",
  AMENITIES: "AMENITIES",
  INFORMATION: "INFORMATION",
  ACTION: "ACTION",
};

export const MAX_HOTEL_COMPARISION = 3;
export const SNACK_BAR_POSITION = {
  BOTTOM_LEFT_OUT: "BOTTOM_LEFT_OUT",
  BOTTOM_LEFT_IN: "BOTTOM_LEFT_IN",
};

export const HOTEL_DETAIL_TABS_VALUE = {
  ROOM_LIST: "ROOM_LIST",
  DETAIL_INFO: "DETAIL_INFO",
};

export const HOTEL_DETAIL_TABS = [
  { id: 1, label: "객실보기", value: HOTEL_DETAIL_TABS_VALUE.ROOM_LIST },
  { id: 2, label: "상세정보", value: HOTEL_DETAIL_TABS_VALUE.DETAIL_INFO },
];

export const HOTEL_TYPE_SEARCH = {
  BY_KEYWORD: "BY_KEYWORD",
  BY_MAP_LOCATION: "BY_MAP_LOCATION",
  BY_REGION_QUICKSEARCH: "BY_REGION_QUICKSEARCH",
  BY_HOTEL_ID: "BY_HOTEL_ID",
};

export const HOTEL_AUTOSEARCH_TYPE = {
  CITY: "city",
  HOTEL: "hotel",
  LANDMARK: "landmark",
  MAP: "map",
  GOOGLEMAP: "googlemap",
};

export const HOTEL_SELECT_ACTION_TYPE = {
  BOOKING: "BOOKING",
  PICTURES: "PICTURES",
  RULE: "RULE",
  NOTICE: "NOTICE",
};

export const HOTEL_MAX_SLICE_ROOM_ITEM = 6;

export const HOTEL_DEFAULT_CENTER_MAP = { lat: 33.2519836, lng: 126.5097 };

export const INPUT_NUMBER_MAX = 1000000000;

export const CANCEL_RES_RULE_CLASSES = {
  NORMAL: "NORMAL",
  DANGER: "DANGER",
};

export const MAX_HOTEL_ADDR_LENGTH = 40;

export const DEFAULT_ROOM_INFO = "1";

export const ZOOM_LEVEL = {
  DEFAULT: 16,
  DETAIL: 16,
  MAP: 3,
};

export const BADGE_TYPE = {
  CORLOR: {
    PRIMARY: "primary",
    SECONDARY: "secondary",
    SUCCESS: "success",
    DANGER: "danger",
    WARNING: "warning",
    INFO: "info",
    LIGHT: "light",
    DARK: "dark",
  },
  SIZE: {
    SM: "sm",
    MD: "md",
  },
};

export const MENU_USE = {
  ALL: "ALL",
  ONLY_AIR: "ONLY_AIR",
  ONLY_HOTEL: "ONLY_HOTEL",
};

export const MENU_PRIMARY = {
  AIR: "AIR",
  HOTEL: "HOTEL",
};
export const DEFAULT_DATE = `${momentKR().year()}-01-01`;

export const EMAIL_REGEX = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/i;

export const MAP_PASSWORD_CHECK_RESULT = {
  CONTINUOUS_NUMBER: "연속되는 숫자(문자)를 3자 이상 입력하실 수 없습니다.",
  PASSWORD_ONE_CHAR: "같은 숫자(문자)를 3번 이상 반복 입력하실 수 없습니다.",
  PASSWORD_LEN: "영문자, 숫자, 특수문자를 조합하여 8~20자로 다시 입력해 주세요.",
  DEFAULT: "비밀번호가 일치하지 않습니다. 다시 입력해주세요.",
};

export const BTMS_API_URL = import.meta.env.VITE_APP_API;
export const BTMS_API_V2_URL = import.meta.env.VITE_APP_API_V2;