import qs from "qs";
import request from "@/utils/request";

export const getHotelFareList = async (data) => {
  const response = await request({ url: "/api_v2/user/hotel/search", method: "POST", data });
  return response;
};

export const getHotelDetailCodes = async () => {
  const response = await request({ url: "/api_v2/user/hotel/detail-codes", method: "GET" });
  return response;
};

export const postCancelCharges = async (data, htlMasterId) => {
  const response = await request({
    url: `/user/v2/reservation/hotel/view/${htlMasterId}/cancel-charges`,
    method: "POST",
    data: qs.stringify(data),
  });
  return response;
};

export const postHotelNotice = async (data, htlMasterId) => {
  const response = await request({
    url: `/user/v2/reservation/hotel/view/${htlMasterId}/notice`,
    method: "POST",
    data: qs.stringify(data),
  });
  return response;
};

export const getHotelContent = async (htlMasterId) => {
  const response = await request({
    url: `/api_v2/user/hotel/${htlMasterId}`,
    method: "GET",
  });
  return response;
};

export const getHotelViewAjax = async (params) => {
  const response = await request({
    url: "/user/v2/hotel/viewAjax",
    method: "GET",
    params,
  });
  return response;
};

export const getHotelCities = async () => {
  const response = await request({
    url: "/api_v2/user/hotel/cities",
    method: "GET",
  });
  return response;
};

export const getHotelTravelRule = async () => {
  const response = await request({
    url: "/api_v2/user/hotel/travel-rule",
    method: "GET",
  });
  return response;
};

export const getHotelExchangeRate = async () => {
  const response = await request({
    url: "/api_v2/user/hotel/exchange-rate/USD",
    method: "GET",
  });
  return response;
};

export const getHotelLocation = async (params) => {
  const response = await request({
    url: "/api_v2/user/hotel/location",
    method: "GET",
    params,
  });
  return response;
};
