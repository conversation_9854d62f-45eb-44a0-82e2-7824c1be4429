import axios from "axios";

const URL = import.meta.env.VITE_APP_API_V2;

export const getCompanyByDomain = async (data) => {
  const url = `${URL}/public/company/get-by-domain`;
  const res = await axios.get(url, { params: data });

  return res.data;
};

export const login = async (data) => {
  const url = `${URL}/user/auth/login`;
  const res = await axios.post(url, data);

  return res.data;
};

export const getCompanyBySitecode = async (siteCode) => {
  const url = `${URL}/public/company/get-by-site-code`;
  const res = await axios.get(url, { params: { siteCode } });

  return res.data;
};
