import qs from "qs";
import request, { requestV2 } from "@/utils/request";
import axios from "axios";
import { BTMS_API_V2_URL } from "@/constants/app";

export const getUserInfo = async () => {
  const response = await request({ url: "/api_v2/user/info", method: "GET" });
  return response;
};

export const postCalcCommission = async (data) => {
  const response = await request({ url: "/api_v2/common/company/calculationCommission", method: "POST", data });
  return response;
};

export const postCustomerList = async (data) => {
  const response = await request({ url: "/common/v2/customer/list", method: "POST", data });
  return response;
};

export const getCountryList = async () => {
  const response = await requestV2({ url: "/common/countries", method: "GET" });
  return response;
};

export const postFileMultiUpload = async (data) => {
  const response = await request({ url: "/common/file/multiUpload", method: "POST", data });
  return response;
};

export const postFileDelete = async (data) => {
  const response = await request({
    url: "/common/file/delete",
    method: "POST",
    data: qs.stringify(data, { arrayFormat: "repeat", allowDots: true }),
  });
  return response;
};

export const postHotelAutoSearch = async (data) => {
  const response = await request({
    url: "/common/hotel/autoSearch",
    method: "POST",
    data: qs.stringify(data, { arrayFormat: "repeat", allowDots: true }),
  });
  return response;
};

export const getRewardMileSetting = async () => {
  const response = await requestV2({ url: "/user/company/reward-mile/setting", method: "GET" });
  return response;
};

export const confirmVerificationCode = async (data) => {
  const res = await axios.post(`${BTMS_API_V2_URL}/user/auth/register-verify`, data);
  return res;
};
