import axios from "axios";
import QueryString from "qs";

const URL = import.meta.env.VITE_APP_API_V2;

export const getCompanyList = async (companyId) => {
  const url = `${URL}/public/company/${companyId}/children`;
  const res = await axios.get(url);

  return res.data;
};

export const getPositionList = async (companyId) => {
  const url = `${URL}/public/position?companyId=${companyId}`;
  const res = await axios.get(url);

  return res.data;
};

export const getWorkspaceList = async (companyId) => {
  const url = `${URL}/public/workspace?companyId=${companyId}`;
  const res = await axios.get(url);

  return res.data;
};

export const getDepartmentList = async (workspaceId, parentDepartmentId) => {
  const url = `${URL}/public/department`;
  const res = await axios.get(url, {
    params: {
      workspaceId,
      parentDepartmentId,
    },
  });

  return res.data;
};
