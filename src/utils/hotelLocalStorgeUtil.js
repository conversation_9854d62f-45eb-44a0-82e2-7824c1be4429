import { momentKR } from "./date";

const HotelLocalStorageUtil = () => {
  const getOne = (id, userId) => {
    let data;
    const getData = get(userId);
    for (let index in getData) {
      if (getData[index].id === id) {
        data = getData[index];
        break;
      }
    }
    return data;
  };

  const get = (userId) => {
    return JSON.parse(localStorage.getItem(`hotelSearchData_${userId}`));
  };

  const set = (dataForm, userId) => {
    const { checkIn, checkOut } = dataForm;

    let data = [];
    const object = {};

    if (get(userId) != null) {
      data = get(userId);
      if (data.length >= 20) {
        data.splice(0, 1);
      }
    }

    const nightCount = momentKR(checkOut, "YYYYMMDD").diff(momentKR(checkIn, "YYYYMMDD"), "days");

    object.id = new Date().getTime();
    object.searchType = dataForm.searchType;
    object.regionId = dataForm.regionId;
    object.regionUpperId = dataForm.regionUpperId;
    object.regionNameLong = dataForm.regionNameLong;
    object.checkIn = dataForm.checkIn;
    object.checkInText = dataForm.checkInText;
    object.checkOut = dataForm.checkOut;
    object.checkOutText = dataForm.checkOutText;
    object.nightCount = nightCount;
    object.roomCount = dataForm.roomCount;
    object.adultCounts = dataForm.roomInfo.split(",");
    object.roomInfo = dataForm.roomInfo;
    object.totalAdultCount = dataForm.roomInfo.split(",").reduce((acc, item) => acc + Number(item), 0);
    object.googleMapPlaceId = dataForm.googleMapPlaceId || "";

    data.push(object);
    localStorage.setItem(`hotelSearchData_${userId}`, JSON.stringify(data));
  };

  const remove = (id, userId) => {
    let getData = get(userId);
    let data = [];
    for (let index in getData) {
      if (getData[index].id != id) {
        data.push(getData[index]);
      }
    }
    localStorage.setItem(`hotelSearchData_${userId}`, JSON.stringify(data));
  };

  return { get, set, remove, getOne };
};

const hotelLocalStorageUtil = HotelLocalStorageUtil();

export default hotelLocalStorageUtil;
