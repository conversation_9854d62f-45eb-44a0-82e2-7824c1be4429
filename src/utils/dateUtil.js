import { nvl } from "@/common";

const DateUtil = (function () {
  const getHourMin = function (hourMin) {
    const hour = Math.floor(hourMin / 60);
    const min = hourMin % 60;
    return (hour > 0 ? hour + "시간" : "") + "" + (min > 0 ? " " + min + "분" : "");
  };
  const getPatternYmd = function (ymd, pattern) {
    if (ymd != null && ymd.length > 7) {
      return ymd.substring(0, 4) + "" + pattern + "" + ymd.substring(4, 6) + "" + pattern + "" + ymd.substring(6, 8);
    } else {
      return "";
    }
  };
  const getPatternMd = function (ymd, pattern) {
    if (nvl(pattern, "") == "") {
      pattern = ".";
    }
    if (ymd != null && ymd.length > 4) {
      return ymd.substring(4, 6) + "" + pattern + "" + ymd.substring(6, 8);
    } else {
      return "";
    }
  };
  const getDayOfWeekName = function (ymd) {
    if (nvl(ymd, "") != "" && ymd.length == 8) {
      ymd = getPatternYmd(ymd, "-");
    }
    const weekName = new Array("일", "월", "화", "수", "목", "금", "토");
    return weekName[new Date(ymd).getDay()];
  };

  const getMdDayOfWeekName = function (ymd, weekName) {
    let result = "";
    if (nvl(ymd, "") != "" && ymd.length == 8) {
      if (ymd.substring(4, 5) == "0") {
        result += ymd.substring(5, 6);
      } else {
        result += ymd.substring(4, 6);
      }
      result += "월 ";
      if (ymd.substring(6, 7) == "0") {
        result += ymd.substring(7, 8);
      } else {
        result += ymd.substring(6, 8);
      }
      result += "일";
    }
    if (weekName) {
      result += " (";
      result += getDayOfWeekName(ymd);
      result += ")";
    }

    return result;
  };

  function plusHHMM(time1, time2) {
    if (!time1?.trim()) {
      time1 = "0000";
    }
    if (!time2?.trim()) {
      time2 = "0000";
    }
    const hour1 = parseInt(time1.substring(0, 2), 10) * 60;
    const min1 = parseInt(time1.substring(2, 4), 10);
    const hour2 = parseInt(time2.substring(0, 2), 10) * 60;
    const min2 = parseInt(time2.substring(2, 4), 10);
    const sumHour = Math.round((hour1 + hour2 + Math.floor((min1 + min2) / 60) * 60) / 60);
    const sumMin = (min1 + min2) % 60;

    return (sumHour < 10 ? "0" + sumHour : `${sumHour}`) + (sumMin < 10 ? "0" + sumMin : `${sumMin}`);
  }

  return {
    getHourMin: getHourMin,
    getPatternYmd: getPatternYmd,
    getPatternMd: getPatternMd,
    getDayOfWeekName: getDayOfWeekName,
    getMdDayOfWeekName: getMdDayOfWeekName,
    plusHHMM,
  };
})();

export default DateUtil;
