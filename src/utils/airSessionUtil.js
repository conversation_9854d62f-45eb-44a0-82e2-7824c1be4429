import { nvl } from "@/common";

const airSection = () => {
  const clear = () => {
    sessionStorage.clear();
  };

  const getOverseasData = () => {
    return JSON.parse(sessionStorage.getItem("airOverseasSessionData"));
  };

  const setMainAirTravelPlaceSession = (name, code) => {
    clear();
    sessionStorage.setItem("mainAirportName", name);
    sessionStorage.setItem("mainAirportCode", code);
  };

  const setSession = (type, data) => {
    const departureAirportCodes = [...data.departureAirportCodes.filter((i) => i !== "")];
    const departureAirportNames = [...data.departureAirportNames.filter((i) => i !== "")];
    const arrivalAirportCodes = [...data.arrivalAirportCodes.filter((i) => i !== "")];
    const arrivalAirportNames = [...data.arrivalAirportNames.filter((i) => i !== "")];
    const departureDays = [...data.departureDays.filter((i) => i !== "")];
    const departureDayTexts = [...data.departureDayTexts.filter((i) => i !== "")];

    clear();

    sessionStorage.setItem("departureAirportCodes", JSON.stringify(departureAirportCodes));
    sessionStorage.setItem("departureAirportNames", JSON.stringify(departureAirportNames));
    sessionStorage.setItem("arrivalAirportCodes", JSON.stringify(arrivalAirportCodes));
    sessionStorage.setItem("arrivalAirportNames", JSON.stringify(arrivalAirportNames));
    sessionStorage.setItem("departureDays", JSON.stringify(departureDays));
    sessionStorage.setItem("departureDayTexts", JSON.stringify(departureDayTexts));

    sessionStorage.setItem("headDepartAirport", data.headDepartAirport);
    sessionStorage.setItem("itineraryCount", data.itineraryCount);
    sessionStorage.setItem("adultCount", data.adultCount);
    // {{userInfo.position.name}}
    sessionStorage.setItem("positionName", nvl(data?.positionName, ""));

    if (type === "recentAirSearch") {
      sessionStorage.setItem("itineraryType", data.itineraryType);
      sessionStorage.setItem("departSeatTypeCode", data.departSeatTypeCode);
      sessionStorage.setItem("stopOverType", data.stopOverType);
      sessionStorage.setItem("sectionType", data.sectionType);
    } else {
      sessionStorage.setItem("itineraryType", data.sectionType);
      sessionStorage.setItem("departSeatTypeCode", data.departSeatTypeCode == "" ? " M" : data.departSeatTypeCode);
      sessionStorage.setItem("stopOverType", "");
      sessionStorage.setItem("sectionType", data.sectionType);
    }
  };

  // holding
  // const setOverseasData = (fareId) => {
  //   const data =
  // }

  return {
    clear,
    getOverseasData,
    setMainAirTravelPlaceSession,
    setSession,
  };
};

const AirSessionUtil = airSection();

export default AirSessionUtil;
