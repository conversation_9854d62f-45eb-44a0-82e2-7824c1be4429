const AirUtil = {
  getSeatWaitingName: function (seatWaiting) {
    return seatWaiting ? "대기" : "확정";
  },
  getFlightId: function (flightFare, itinerarySeq) {
    let flightId = "";
    switch (itinerarySeq) {
      case 1:
        flightId = flightFare.firstFlightId;
        break;
      case 2:
        flightId = flightFare.secondFlightId;
        break;
      case 3:
        flightId = flightFare.thirdFlightId;
        break;
      case 4:
        flightId = flightFare.fourthFlightId;
        break;
      case 5:
        flightId = flightFare.fifthFlightId;
        break;
      case 6:
        flightId = flightFare.sixthFlightId;
        break;
    }
    return flightId;
  },
  getBaggageAllowance: function (flightFare, itinerarySeq) {
    let baggageAllowance = "";
    switch (itinerarySeq) {
      case 1:
        baggageAllowance = flightFare.firstBaggageAllowance;
        break;
      case 2:
        baggageAllowance = flightFare.secondBaggageAllowance;
        break;
      case 3:
        baggageAllowance = flightFare.thirdBaggageAllowance;
        break;
      case 4:
        baggageAllowance = flightFare.fourthBaggageAllowance;
        break;
      case 5:
        baggageAllowance = flightFare.fifthBaggageAllowance;
        break;
      case 6:
        baggageAllowance = flightFare.sixthBaggageAllowance;
        break;
    }
    let filteredStr = "";
    if (baggageAllowance == "0") {
      filteredStr = "불포함";
    } else if (baggageAllowance) {
      filteredStr = baggageAllowance.replace("N", "PC").replace("W", "kg").replace("K", "kg");
    }
    return filteredStr;
  },
  getSeatTypeName: function (seatTypeCode) {
    if (seatTypeCode == "M") {
      return "일반석";
    } else if (seatTypeCode == "W") {
      // return '프리미엄이코노미석';
      return "프리미엄 이코노미";
    } else if (seatTypeCode == "C") {
      // return '비즈니스석';
      return "비즈니스";
    } else if (seatTypeCode == "F") {
      return "일등석";
    } else if (seatTypeCode == "Y") {
      return "이코노미";
    }
  },
  getExceptTimeType: function (exceptTimeType) {
    if (exceptTimeType == "BORDING") {
      return "최소 탑승시간 ";
    } else if (exceptTimeType == "TOTAL") {
      return "총 소요시간";
    }
  },
  getGroundTime: function (flightDetails) {
    let groundTime = 0;

    flightDetails.forEach(flightDetails, function (index, flightDetail) {
      if (flightDetail.groundTime != null) {
        groundTime += parseInt(flightDetail.groundTime);
      }
    });

    return groundTime;
  },
  getFlightTime: function (elapseFlyingTime, groundTime) {
    if (groundTime == 0) {
      return elapseFlyingTime;
    }
    const elapseFlyingTotalMin = Math.floor(elapseFlyingTime / 100) * 60 + (parseInt(elapseFlyingTime) % 100);
    const groundTotalMin = Math.floor(groundTime / 100) * 60 + (parseInt(groundTime) % 100);
    const flightTotalMin = elapseFlyingTotalMin - groundTotalMin;
    const hour = Math.floor(flightTotalMin / 60);
    const min = flightTotalMin % 60;

    return (hour > 9 ? hour : "0" + hour) + "" + (min > 9 ? min : "0" + min);
  },
  getSectionTypeName: function (sectionType) {
    if (sectionType == "MultiCity") {
      return "다구간";
    } else if (sectionType == "OneWay") {
      return "편도";
    } else if (sectionType == "RoundTrip") {
      return "왕복";
    }
  },
};

export default AirUtil;
