import { createElement, forwardRef, lazy, useRef } from "react";

function lazyWithPreload(factory) {
  const ReactLazyComponent = lazy(factory);
  let PreloadedComponent;
  let factoryPromise;

  const Component = forwardRef(function LazyWithPreload(props, ref) {
    const ComponentToRender = useRef(PreloadedComponent ?? ReactLazyComponent);
    return createElement(ComponentToRender.current, Object.assign(ref ? { ref } : {}, props));
  });

  const LazyWithPreload = Component;

  LazyWithPreload.preload = () => {
    if (!factoryPromise) {
      factoryPromise = factory().then((module) => {
        PreloadedComponent = module.default;
        return PreloadedComponent;
      });
    }

    return factoryPromise;
  };

  return LazyWithPreload;
}

export default lazyWithPreload;
