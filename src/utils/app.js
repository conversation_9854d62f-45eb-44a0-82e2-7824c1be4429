import moment from "moment";
import qs from "qs";
import { COP_CODE, HOTEL_SORT_CONDITIONS, MAPPED_STATUS_CODE, REGION_TYPE, TYPE_MOBILE_LOADING } from "@/constants/app";
import { momentKR } from "@/utils/date";
import { postCalcCommission } from "@/service/common";
import { store } from "@/store";
import { actionMobileLoading, actionProgressBar } from "@/store/loadingSlice.mobile";
import request from "@/utils/request";
import { reverseMap, stripHTML } from "@/utils/common";

import { cloneDeep, compact, get, intersection, isEmpty, omitBy, set, uniq } from "lodash";

function getMinElapseFlyingTime(journey) {
  const { flights } = journey;
  return flights
    .map((flight) => flight.segments.map((el) => el.flightTime + el.waitingTime))
    .flat()
    .reduce((a, b) => a + b, 0);
}

function hasIncludedCorporateFare(journey) {
  const fareTypes = journey.flights.map((flight) => flight.fares.fareTypes).flat();
  const includedCorporateFare = fareTypes.some((type) => type.includes(COP_CODE));
  return includedCorporateFare;
}

function getTotalFare(journey) {
  const paxTypeFaresDisplay = journey.fares.paxTypeFares[0];
  const airFare = Number(paxTypeFaresDisplay.airFare);
  const tax = Number(paxTypeFaresDisplay.airTax) + Number(paxTypeFaresDisplay.fuelChg);
  const ticketFare = Number(paxTypeFaresDisplay?.tasfAmount ?? 0); // Update later!
  return airFare + tax + ticketFare;
}

function getMaxStopsOfFlight(journey) {
  const flights = journey.flights;
  return Math.max(...flights.map((flight) => flight.stops));
}

function getMinFlightTime(journey) {
  const { flights } = journey;
  return flights
    .map((flight) => flight.segments.map((el) => el.flightTime))
    .flat()
    .reduce((a, b) => a + b, 0);
}

export function sortByCondition(journeyInfos, selectedMenuSort) {
  if (!isEmpty(journeyInfos)) {
    let clonedJourneyInfos = cloneDeep(journeyInfos);
    if (selectedMenuSort === "recommend") {
      clonedJourneyInfos.sort((a, b) => {
        /* 
          UPDATE 20250214: Priority of sortcondition recommend
          1. 기업운임
          2. 직항 (경유지 x)
          3. 요금 낮은 순
          4. 총 소요시간
          5. 출발시간 빠른 순
          6. 그래도 동일 순위가 존재하는 경우 pairkey번호가 낮은 순 
          7. 일반운임 직항(경유지 x)
          8. 일반운임 요금 낮은 순
          9. 일반운임 총 소요시간
          10. 일반운임 출발시간 빠른 순
        */
        const corporateFarePrev = hasIncludedCorporateFare(a);
        const corporateFareNext = hasIncludedCorporateFare(b);
        const totalFarePrev = getTotalFare(a);
        const totalFareNext = getTotalFare(b);
        const elapseTimePrev = getMinElapseFlyingTime(a);
        const elapseTimeNext = getMinElapseFlyingTime(b);
        const stopPrev = getMaxStopsOfFlight(a);
        const stopNext = getMaxStopsOfFlight(b);
        const departureDatePrev = moment(a.departureDate);
        const departureDateNext = moment(b.departureDate);
        const pairKeyPrev = a.pairKey;
        const pairKeyNext = b.pairKey;

        if (corporateFarePrev !== corporateFareNext) {
          return corporateFarePrev ? -1 : 1;
        }
        if (stopPrev !== stopNext) {
          return stopPrev - stopNext;
        }
        if (totalFarePrev !== totalFareNext) {
          return totalFarePrev - totalFareNext;
        }
        if (elapseTimePrev !== elapseTimeNext) {
          return elapseTimePrev - elapseTimeNext;
        }
        if (!departureDatePrev.isSame(departureDateNext)) {
          return departureDatePrev.isBefore(departureDateNext) ? -1 : 1;
        }
        return pairKeyPrev.localeCompare(pairKeyNext);
      });
    } else if (selectedMenuSort === "lowDirectAmount") {
      clonedJourneyInfos.sort((a, b) => {
        const totalFarePrev = getTotalFare(a);
        const totalFareNext = getTotalFare(b);
        const stopPrev = getMaxStopsOfFlight(a);
        const stopNext = getMaxStopsOfFlight(b);
        const pairKeyPrev = a.pairKey;
        const pairKeyNext = b.pairKey;

        if (stopPrev !== stopNext) {
          return stopPrev - stopNext;
        }
        if (totalFarePrev !== totalFareNext) {
          return totalFarePrev - totalFareNext;
        }
        return pairKeyPrev.localeCompare(pairKeyNext);
      });
    } else if (selectedMenuSort === "lowBoardingTime") {
      clonedJourneyInfos.sort((a, b) => {
        return getMinFlightTime(a) - getMinFlightTime(b);
      });
    } else if (selectedMenuSort === "lowAmount") {
      clonedJourneyInfos.sort((a, b) => {
        return getTotalFare(a) - getTotalFare(b);
      });
    } else if (selectedMenuSort === "fastDepartTime") {
      clonedJourneyInfos.sort((a, b) => {
        return momentKR(a.flights[0].departureDate).unix() - momentKR(b.flights[0].departureDate).unix();
      });
    } else if (selectedMenuSort === "corporateFareLowAmount") {
      clonedJourneyInfos.sort((a, b) => {
        if (hasIncludedCorporateFare(a) && !hasIncludedCorporateFare(b)) {
          return -1;
        } else if (!hasIncludedCorporateFare(a) && hasIncludedCorporateFare(b)) {
          return 1;
        } else {
          return getTotalFare(a) - getTotalFare(b);
        }
      });
    }
    return clonedJourneyInfos;
  }
  return [];
}

export async function calcCommission(journeyInfos, seatTypeCode, page = 1) {
  if (isEmpty(journeyInfos)) return journeyInfos;
  const clonedFlights = cloneDeep(journeyInfos);
  const payload = journeyInfos.slice((page - 1) * 10, page * 10).map((flight) => {
    const fare = flight.fares.paxTypeFares[0];
    return {
      fareAmount: fare.airFare,
      discountAmount: 0,
      taxAmount: fare.airTax,
      seatTypeCode,
    };
  });
  const response = await postCalcCommission(payload);
  const data = response.data.values;
  return clonedFlights.map((flight, index) => {
    if (index < (page - 1) * 10) return flight;
    return set(flight, "fares.paxTypeFares[0].tasfAmount", data[index - (page - 1) * 10] || 0);
  });
}

export function getSortAndCombineJourneyInfos(journeyInfos) {
  return journeyInfos[0].journeys.map((journey, index) => {
    const { pairKey, journeyKey } = journey;
    const id = journeyKey + index;
    const flights = [journey];
    journeyInfos.slice(1).forEach((item) => {
      item.journeys.forEach((el) => {
        if (el.pairKey === pairKey) {
          flights.push(el);
        }
      });
    });
    return { ...journey, flights, id };
  });
}

export const appMobileLoading = {
  on: ({ type = TYPE_MOBILE_LOADING.NONE, message = "", data = {} }) =>
    store.dispatch(
      actionMobileLoading({
        type,
        message,
        open: true,
        data,
      }),
    ),
  off: () =>
    store.dispatch(
      actionMobileLoading({
        type: TYPE_MOBILE_LOADING.NONE,
        message: "",
        open: false,
        data: {},
      }),
    ),
};

export const appProgressBar = {
  on: (type = "") => store.dispatch(actionProgressBar({ type, isOpen: true })),
  off: () => store.dispatch(actionProgressBar({ type: "", isOpen: false })),
};

export function requestWithMobileLoading(params) {
  const { success, fail, end, ...config } = params;
  appMobileLoading.on({ type: TYPE_MOBILE_LOADING.DEFAULT });
  return request(config)
    .then((res) => !!success && success(res))
    .catch((error) => !!fail && fail(error))
    .finally(() => {
      appMobileLoading.off();
      !!end && end();
    });
}

function convertDecimalToTime(decimal) {
  const hours = Math.floor(decimal);
  const minutes = Math.round((decimal - hours) * 60);
  const formattedHours = String(hours).padStart(2, "0");
  const formattedMinutes = String(minutes).padStart(2, "0");

  return `${formattedHours}${formattedMinutes}`;
}

export function getUniqueList(journeyInfos) {
  const flattened = journeyInfos.flatMap((obj) => Object.values(obj).flat());
  const airlines = flattened.map((obj) => {
    return { airline: obj.airline, name: obj.airlineName };
  });
  const uniqueAirlines = [...new Map(airlines.map((item) => [item["airline"], item])).values()];
  const alliances = flattened.map((obj) => obj.alliances);
  return { airlines: uniqueAirlines, alliances: uniq(alliances.flat()) };
}

export function filterFlights(filter, airSearch, condition) {
  const isDirect = filter?.stopOverType === "0";
  const { arrayCorporateFare, airlines, arrayStopOverCount, alliances, departureTime, arrivalTime, leadTime, stopOverTime } = condition;
  const rawData = getSortAndCombineJourneyInfos(airSearch.data.journeyInfos).filter((flight) => {
    if (isDirect) return flight.flights.every((el) => el.stops === 0);
    return true;
  });

  const isCorporateFareValid = (fareType) => {
    const hasCopCode = fareType.some((el) => el.includes(COP_CODE));
    if (arrayCorporateFare.length > 1) return true;
    if (arrayCorporateFare.length === 1 && arrayCorporateFare[0] === COP_CODE) return hasCopCode;
    if (arrayCorporateFare.length === 1 && arrayCorporateFare[0] !== COP_CODE) return !hasCopCode;
    return false;
  };
  const isStopsValid = (stops) => arrayStopOverCount.some((el) => Number(el) === stops);
  const isAlliancesValid = (alliance) => {
    if (isEmpty(alliance)) return true;
    return intersection(alliances, alliance).length > 0;
  };
  const isAirlineValid = (flight) => {
    return airlines.includes(flight.airline);
  };

  const isTimeInRange = (time, range) => {
    const formattedTime = momentKR(time).format("HHmm");
    return formattedTime >= convertDecimalToTime(range[0]) && formattedTime <= convertDecimalToTime(range[1]);
  };

  const isDepartureTimeValid = (flights) => {
    return departureTime.every((timeRange, index) => isTimeInRange(flights[index].segments[0].departureDate, timeRange.value));
  };

  const isArrivalTimeValid = (flights) => {
    return arrivalTime.every((timeRange, index) => isTimeInRange(flights[index].segments.slice(-1)[0].arrivalDate, timeRange.value));
  };

  const isDurationValid = (segments) => {
    const totalTime = segments.reduce((total, segment) => total + Number(segment.waitingTime) + Number(segment.flightTime), 0);
    return totalTime >= leadTime.value[0] * 60 && totalTime <= leadTime.value[1] * 60;
  };

  const isStopOverDurationValid = (flights) => {
    const stopOverDurations = flights.map((flight) => flight.segments.map((seg) => seg.waitingTime));
    const flatedStopOverDurations = stopOverDurations.flat();
    const maxOver = Math.max(...flatedStopOverDurations);
    return maxOver >= stopOverTime.value[0] * 60 && maxOver <= stopOverTime.value[1] * 60;
  };

  const filteredData = rawData.filter((el) => {
    const { flights, alliances } = el;
    const fareTypes = el.flights.map((flight) => flight.fares.fareTypes).flat();
    const stops = Math.max(...flights.map((flight) => JSON.stringify(flight.stops)));
    const firstSegments = flights[0].segments;
    return (
      isCorporateFareValid(fareTypes) &&
      isStopsValid(stops) &&
      isAlliancesValid(alliances) &&
      isAirlineValid(el) &&
      (!isEmpty(departureTime) ? isDepartureTimeValid(flights) : true) &&
      (!isEmpty(arrivalTime) ? isArrivalTimeValid(flights) : true) &&
      (!isEmpty(leadTime) ? isDurationValid(firstSegments) : true) &&
      (!isEmpty(stopOverTime) ? isStopOverDurationValid(flights) : true)
    );
  });
  return filteredData;
}

export function normalizeBookingData(rawData) {
  if (isEmpty(rawData)) return {};
  const clonedRawData = cloneDeep(rawData);
  const { americaStay } = clonedRawData;
  set(clonedRawData, "travel.statusCode", { id: MAPPED_STATUS_CODE[rawData.travel.status].id, name: MAPPED_STATUS_CODE[rawData.travel.status].name });
  set(clonedRawData, "travel.bookingAir.isViewTicketDateWarning", getIsViewTicketDateWarning(rawData.travel.bookingAir ?? {}));
  if (!isEmpty(americaStay)) {
    set(clonedRawData, "americaStayDTO", { ...americaStay, americaStayId: americaStay?.id ?? 0, zipcode: americaStay?.zipCode ?? "" });
  }

  return clonedRawData;
}

export function getIsViewTicketDateWarning(bookingAir) {
  const { bookingDate, viewTicketDate } = bookingAir;
  if (!viewTicketDate) {
    return true;
  }
  const isSameDate = isSameDateWithoutTime(bookingDate, viewTicketDate);
  const isWeekend = isTlDateWeekend(bookingDate, viewTicketDate);
  const isViewTicketDateBeforeBookingDate = bookingDate >= viewTicketDate;
  return isSameDate || isWeekend || isViewTicketDateBeforeBookingDate;
}

function isSameDateWithoutTime(bookingDate, viewTicketDate) {
  if (!bookingDate) return false;
  const dateString1 = moment(bookingDate).format("YYYYMMDD");
  const dateString2 = moment(viewTicketDate).format("YYYYMMDD");
  return dateString1 === dateString2;
}

function isTlDateWeekend(bookingDate, viewTicketDate) {
  if (!bookingDate) return false;
  const mapped_weekday = {
    friday: 5,
    saturday: 6,
    sunday: 7,
  };
  const bookingDay = moment(bookingDate).isoWeekday();
  const viewTicketDay = moment(viewTicketDate).isoWeekday();
  return bookingDay === mapped_weekday.friday && [mapped_weekday.saturday, mapped_weekday.sunday].includes(viewTicketDay);
}

export function getTravelCities(travel) {
  if (isEmpty(travel)) return [];
  const cities = travel?.bookingAir?.bookingAirSchedules?.map((el) => compact([el.fromAirport?.city?.name, el.toAirport?.city?.name]));
  const flatedCities = cities?.flat();
  const isMatchingFromToCity = flatedCities?.[0] === flatedCities?.[flatedCities.length - 1];
  const uniqueCities = uniq(cities?.flat());
  if (isMatchingFromToCity) uniqueCities.push(uniqueCities[0]);
  return uniqueCities;
}

export function normalizeCompareSchedule(userInfo, airCompareSchedules, lowestFareAndSchedule, data, checkIsViolation) {
  const isLowestPrice = userInfo?.workspace?.company?.btmsSetting?.isLowestPrice;
  return {
    compareSchedules: !isEmpty(airCompareSchedules)
      ? airCompareSchedules.map((schedule) => ({
          isRuleViolation: checkIsViolation(schedule),
          journeys: schedule.flights,
        }))
      : null,
    lowestSchedule:
      isLowestPrice && !isEmpty(lowestFareAndSchedule)
        ? {
            isRuleViolation: checkIsViolation(lowestFareAndSchedule),
            journeys: [lowestFareAndSchedule],
          }
        : null,
    selectedSchedule: airCompareSchedules.findIndex((schedule) => schedule.id === data.id),
  };
}

export function getBtmsManager(response, type = REGION_TYPE.OVERSEAS) {
  const btmsManagers = get(response, "data", []);

  const isOverseas = type === REGION_TYPE.OVERSEAS;
  const selectedManager = btmsManagers.find((manager) => {
    const { travelAgencyUser } = manager;
    const isValidPhoneNumber = !isEmpty(travelAgencyUser?.phoneNumber) ? (travelAgencyUser.phoneNumber.length > 3 ? true : false) : false;
    return manager.isOverseas === isOverseas && isValidPhoneNumber;
  });

  return selectedManager;
}

export function stripHTMLTagInAirportList(airportList, keyword = "name") {
  return airportList.map((airport) => {
    return { ...airport, [keyword]: stripHTML(airport[keyword]) };
  });
}

export function goBoard(boardType, categoryCodeName, boardId, subCategoryCodeId) {
  const url = "/user/v2/board/" + boardType + "/list";

  // Create a form element
  const form = document.createElement("form");
  form.action = url;
  form.method = "post";

  // Create and append hidden inputs
  const inputBoardId = document.createElement("input");
  inputBoardId.type = "hidden";
  inputBoardId.id = "setBoardId";
  inputBoardId.name = "setBoardId";
  inputBoardId.value = boardId;
  form.appendChild(inputBoardId);

  const inputCategory = document.createElement("input");
  inputCategory.type = "hidden";
  inputCategory.id = "setCategory";
  inputCategory.name = "setCategory";
  inputCategory.value = categoryCodeName;
  form.appendChild(inputCategory);

  const inputSubCategory = document.createElement("input");
  inputSubCategory.type = "hidden";
  inputSubCategory.id = "setSubCategory";
  inputSubCategory.name = "setSubCategory";
  inputSubCategory.value = subCategoryCodeId;
  form.appendChild(inputSubCategory);

  // Append the form to the body and submit it
  document.body.appendChild(form);
  form.submit();
}

export function sortHotelByCondition(hotelList, sortBy) {
  const clonedHotelList = cloneDeep(hotelList);
  const hotelSortReverse = reverseMap(HOTEL_SORT_CONDITIONS);

  clonedHotelList.sort((a, b) => {
    switch (sortBy) {
      case hotelSortReverse.recommend:
        return a.seqNo - b.seqNo;
      case hotelSortReverse.lowAmount:
        return a.salePrice - b.salePrice;
      case hotelSortReverse.highAmount:
        return b.salePrice - a.salePrice;
      case hotelSortReverse.lowGrade:
        return a.htlGrade - b.htlGrade;
      case hotelSortReverse.highGrade:
        return b.htlGrade - a.htlGrade;
    }
    return true;
  });
  return clonedHotelList;
}

function isMatchRangeFareType(hotel, filter) {
  const { nightCount, fareRange, rangeFareType } = filter;
  if (rangeFareType == "average") {
    if (hotel.salePrice / nightCount < fareRange[0] || hotel.salePrice / nightCount > fareRange[1]) {
      return false;
    }
  } else {
    if (hotel.salePrice < fareRange[0] || hotel.salePrice > fareRange[1]) {
      return false;
    }
  }
  return true;
}

function isIncludeHtlFacilities(hotel, filter) {
  const { htlFacilities } = hotel;
  const { arrayFacilityCode } = filter;
  if (isEmpty(htlFacilities)) return true;
  return intersection(arrayFacilityCode, htlFacilities).length > 0;
}

export function filterHotelByCondition(originalHotelFareList, filter) {
  const { arrayGradeCode } = filter;
  const filteredHotelList = originalHotelFareList.filter((hotel) => {
    const { htlGrade } = hotel;
    return arrayGradeCode.includes(htlGrade) && isIncludeHtlFacilities(hotel, filter) && isMatchRangeFareType(hotel, filter);
  });

  return filteredHotelList;
}

export function getPayloadHotelSearch(filter, additionalParams) {
  const { checkIn, checkOut, roomInfo } = filter;
  const payload = {
    checkIn: momentKR(checkIn, "YYYYMMDD").format("YYYY-MM-DD"),
    checkOut: momentKR(checkOut, "YYYYMMDD").format("YYYY-MM-DD"),
    roomInfo,
    ...additionalParams,
  };

  return cleanObject(payload);
}

export function cleanObject(obj) {
  return omitBy(obj, (v) => !v || v === 0 || v === "0" || (Array.isArray(v) && v.length === 0) || Number.isNaN(v));
}

export function getMaxAllowedStar(travelRuleBaseDTO) {
  const { isExceptUseHotelMaxStar, allowedHotelMaxStar1, allowedHotelMaxStar2, allowedHotelMaxStar3, allowedHotelMaxStar4, allowedHotelMaxStar5 } =
    travelRuleBaseDTO;
  if (!isExceptUseHotelMaxStar) return 0;
  if (allowedHotelMaxStar5) return 5;
  if (allowedHotelMaxStar4) return 4;
  if (allowedHotelMaxStar3) return 3;
  if (allowedHotelMaxStar2) return 2;
  if (allowedHotelMaxStar1) return 1;
  return 0;
}

export function getHotelViolation(room, travelRuleBaseDTO, nightCount, filter) {
  const { isExceptUseHotelMax, isExceptUseHotelMaxStar } = travelRuleBaseDTO;
  if (isEmpty(travelRuleBaseDTO)) return false;
  const roomLength = filter?.roomInfo ? filter.roomInfo.split(",").length : 1;
  const salePricePerNight = parseInt(room.salePrice) / nightCount;
  const maxAllowPerNight = parseInt(travelRuleBaseDTO.maxAllowPerNight) * roomLength;

  return (
    (isExceptUseHotelMax && salePricePerNight > maxAllowPerNight) ||
    (isExceptUseHotelMaxStar && Math.floor(Number(room.htlGrade)) > getMaxAllowedStar(travelRuleBaseDTO))
  );
}

export function hotelBooking(hotelContent, room, filter) {
  const { regionNameLong, checkIn, checkOut, roomInfo } = filter;
  const url = "/user/v2/hotel/booking";

  const form = document.createElement("form");
  form.action = url;
  form.method = "POST";

  function appendHiddenInput(id, name, value) {
    const inputHiddend = document.createElement("input");
    inputHiddend.type = "hidden";
    inputHiddend.id = id;
    inputHiddend.name = name;
    inputHiddend.value = value;
    form.appendChild(inputHiddend);
  }

  appendHiddenInput("jsonHotelDetailInfo", "jsonHotelDetailInfo", JSON.stringify(hotelContent.htlDetailInfo));
  appendHiddenInput("jsonRoomFare", "jsonRoomFare", JSON.stringify(room));
  appendHiddenInput("searchType", "searchType", "00000006");
  appendHiddenInput("regionId", "regionId", hotelContent.htlDetailInfo.htlMasterId);
  appendHiddenInput("regionUpperId", "regionUpperId", "");
  appendHiddenInput("regionNameLong", "regionNameLong", regionNameLong);
  appendHiddenInput("checkIn", "checkIn", checkIn);
  appendHiddenInput("checkOut", "checkOut", checkOut);
  appendHiddenInput("roomInfo", "roomInfo", roomInfo);

  document.body.appendChild(form);
  form.submit();
}

export function getPriceByExchangeRate(price, exchangeRate) {
  if (price === 0) return 0;
  return Math.floor(price / exchangeRate);
}

export function setQueryParams(searchParams) {
  const queryString = qs.stringify(searchParams, { arrayFormat: "repeat", skipNulls: true, encode: false });
  const newUrl = `${window.location.pathname}?${queryString}`;
  window.history.replaceState(null, "", newUrl);
}

export function normalizeParamsHotelSearch(params) {
  const paramsValue = cloneDeep(params);
  const keysToCheck = ["lat", "lng"];

  keysToCheck.forEach((key) => {
    if (Object.prototype.hasOwnProperty.call(paramsValue, key) && [null, undefined, 0, "0"].includes(paramsValue[key])) {
      delete paramsValue[key];
    }
  });

  return paramsValue;
}

export function getRoomMinSalePrice(hotelRoomView) {
  if (hotelRoomView?.roomFareList) {
    const clonedRoomFareList = cloneDeep(hotelRoomView).roomFareList;
    const roomList = clonedRoomFareList?.sort((a, b) => Number(a.salePrice) - Number(b.salePrice));
    return roomList?.[0]?.mileage || 0;
  }

  return 0;
}

export function getMapBounds(map) {
  const mapBounds = map?.getBounds();

  const x = mapBounds?.getNorthEast();
  const y = mapBounds?.getSouthWest();

  return {
    leftBottomLatitude: y?.lat() || "",
    leftBottomLongitude: y?.lng() || "",
    rightTopLatitude: x?.lat() || "",
    rightTopLongitude: x?.lng() || "",
  };
}
