import { cloneDeep, find, get, groupBy, isEmpty, set, uniq } from "lodash";
import { COP_CODE, SECTION_TYPE } from "@/constants/app";
import { momentKR } from "@/utils/date";
import AIR_ICONS_PATH from "@/assets/airIcon";

export function comma(number) {
  if (number === null) return 0;
  if (typeof number === "undefined") return "";
  let minusText = "";

  if (parseFloat(number) < 0) {
    minusText = "-";
  }

  number = "" + number;
  number = number.replace("-", "");
  if (number.length > 3) {
    const mod = number.length % 3;
    let output = mod > 0 ? number.substring(0, mod) : "";

    for (let i = 0; i < Math.floor(number.length / 3); i++) {
      if (mod == 0 && i == 0) {
        output += number.substring(mod + 3 * i, mod + 3 * i + 3);
      } else {
        output += "," + number.substring(mod + 3 * i, mod + 3 * i + 3);
      }
    }
    return minusText + "" + output;
  } else {
    return minusText + "" + number;
  }
}

export function getSeatTypeName(seatTypeCode) {
  const MAPPING_SEAT_TYPE = {
    M: "일반석",
    W: "프리미엄이코노미석",
    C: "비즈니스석",
    F: "일등석",
    Y: "이코노미",
  };
  return MAPPING_SEAT_TYPE[seatTypeCode];
}

export function nvl(str, defaultVal) {
  let defaultValue = "";

  if (typeof defaultVal != "undefined") {
    defaultValue = defaultVal;
  }

  if (typeof str == "undefined" || str == null || str == "" || str == "undefined") {
    return defaultValue;
  }

  return str;
}

export function getHourMin(hourMin) {
  const hour = Math.floor(hourMin / 60);
  const min = hourMin % 60;
  return (hour > 0 ? hour + "시간" : "") + "" + (min > 0 ? " " + min + "분 대기" : "");
}

export function getPatternYmd(ymd, pattern) {
  if (ymd != null && ymd.length > 7) {
    return ymd.substring(0, 4) + "" + pattern + "" + ymd.substring(4, 6) + "" + pattern + "" + ymd.substring(6, 8);
  } else {
    return "";
  }
}

export function getDayOfWeekName(ymd) {
  if (nvl(ymd, "") != "" && ymd.length == 8) {
    ymd = getPatternYmd(ymd, "-");
  }
  const weekName = new Array("일", "월", "화", "수", "목", "금", "토");
  return weekName[new Date(ymd).getDay()];
}

export function getKrTimeFromSegment(segments) {
  let totalTime = 0;
  segments.forEach((segment) => {
    totalTime += Number(segment.waitingTime) + Number(segment.flightTime);
  });
  const convertedTime = convertMinutesToHoursAndMinutes(totalTime);
  return `${convertedTime.hours}시간 ${convertedTime.minutes ? `${convertedTime.minutes}분` : ""}`;
}

function convertMinutesToHoursAndMinutes(totalMinutes) {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return { hours, minutes };
}

const airReservationValidate = () => {
  const koreanRegex = /[a-z0-9]|[ \[\]{}()<>?|`~!@#$%^&*-_+=,.;:\"'\\]/g;
  const koreanNenglishRegex = /[0-9]|[\[\]{}()<>?|`~!@#$%^&*-_+=,.;:\"'\\]/g;
  const englishRegex = /[^a-z]/gi;
  const korean = (value) => {
    let result = value,
      isValid = false;
    if (koreanRegex.test(result)) {
      result.replace(koreanRegex, "");
    }

    if (value.trim(nvl(value, "")) == "") isValid = false;
    else isValid = true;

    return { result, isValid };
  };
  const english = (value) => {
    let result = "",
      isValid = true;
    if (englishRegex.test(value.toLowerCase())) {
      result = value.slice(0, -1).toUpperCase();
    } else {
      result = value.toUpperCase();
    }

    if (result.trim(nvl(result, "")) == "") isValid = false;
    else isValid = true;
    return { result, isValid };
  };

  const koreanNenglish = (value) => {
    let result = "",
      isValid = true;
    if (koreanNenglishRegex.test(value.toLowerCase())) {
      result = value.slice(0, -1).toUpperCase();
    } else {
      result = value.toUpperCase();
    }

    if (result.trim(nvl(result, "")) == "") isValid = false;
    else isValid = true;
    return { result, isValid };
  };

  const koreanNenglishNnonespace = () => {};
  const number = () => {};
  const upperCaseEnglishAndNumber = () => {};
  const email = () => {};
  const select = () => {};

  return {
    korean,
    english,
    koreanNenglish,
    koreanNenglishNnonespace,
    number,
    upperCaseEnglishAndNumber,
    email,
    select,
  };
};

export const AirReservationValidate = airReservationValidate();

export function getCalculatedYmd(intervalDay, pattern) {
  let d = new Date();
  let day = d.getDate();
  d.setDate(day + intervalDay);

  let year = d.getFullYear();
  let month = d.getMonth() + 1;
  let date = d.getDate();
  if (month < 10) {
    month = "0" + month;
  }
  if (date < 10) {
    date = "0" + date;
  }

  return year + pattern + month + pattern + date;
}

export function getFilterFromSearchParams(searchParams, initialState = {}) {
  const params = Array.from(searchParams.entries()).reduce((acc, [key, value]) => {
    const multiValueKeys = [
      "departureAirportCodes",
      "departureAirportNames",
      "arrivalAirportCodes",
      "arrivalAirportNames",
      "departureDays",
      "departureDayTexts",
    ];

    if (multiValueKeys.includes(key)) {
      acc[key] = acc[key] ? [...acc[key], value] : [value];
    } else {
      acc[key] = value;
    }

    return acc;
  }, {});

  const newFilter = { ...initialState, ...params };
  return newFilter;
}

export function hasIncludedCorporateFare(journey) {
  if (isEmpty(journey)) return false;
  const fareTypes = journey.flights.map((flight) => flight.fares.fareTypes).flat();
  const includedCorporateFare = fareTypes.some((type) => type.includes(COP_CODE));
  return includedCorporateFare;
}

export function substring4IndexOf(originStr, compareStr) {
  if (isEmpty(originStr)) return "";
  let trimString = "";

  if (originStr !== null) {
    const index = originStr.indexOf(compareStr);
    if (index > -1) {
      trimString = originStr.substring(0, index);
    } else {
      trimString = originStr;
    }
  }

  return trimString;
}

export function hanldeInputChangeValidation(event) {
  const { value } = event.target;
  const inputValid = event.target.getAttribute("data-validateinput");
  let regular;
  let isValid = true;
  switch (inputValid) {
    case "koreanAndEnglishAndSpaceOnly":
    case "koreanNenglish":
      regular = /[0-9[\]{}()<>?|`~!@#$%^&*_\-+=,.;:"'\\\s]/g;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    case "koreanOnly":
      regular = /[a-z0-9]|[\]{}()<>?|`~!@#$%^&*-_+=,.;:"'\\]/g;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    case "numberOnly":
      regular = /[^0-9]/gi;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    case "english":
      regular = /[^a-z]/gi;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    case "englishAndNumberOnly":
      regular = /^[A-Za-z0-9+]*$/;
      isValid = !regular.test(value);
      if (isValid) {
        event.target.value = value.replace(regular, "");
        event.preventDefault();
      }
      break;
    case "email":
      regular = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/i;
      isValid = !regular.test(value);
      break;
    case "upperCaseEnglishAndNumber":
      regular = /[^A-Za-z0-9]/g;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    case "englishAndNumberAndSpecialOnly":
      regular = /^[0-9A-Za-z\s!@#$%^&*()_+\-=[\]{}\\|;:'",.<>/?]*$/;
      isValid = !regular.test(value);
      if (isValid) {
        event.target.value = value.replace(regular, "");
        event.preventDefault();
      }
      break;
    case "numberAndDotAndMinusOnly":
      regular = /[^0-9.-]/g;
      isValid = regular.test(value);
      event.target.value = value.replace(regular, "");
      event.preventDefault();
      break;
    default:
      return true;
  }
  return !isValid;
}

export function emailValidationCheck(email) {
  var email_regex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/i;

  if (!email_regex.test(email)) {
    return false;
  } else {
    return true;
  }
}

export function isValidationInput(event) {
  const inputValid = event.target.getAttribute("data-validateinput");
  if (inputValid) {
    return hanldeInputChangeValidation(event);
  }
  return true;
}

export function generateMobileDynamicNavigateUrl(params) {
  return Object.keys(params)
    .map((key) => {
      const value = params[key];
      if (Array.isArray(value)) {
        return value.map((v) => `${encodeURIComponent(key)}=${encodeURIComponent(v)}`).join("&");
      }
      return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
    })
    .join("&");
}

export function getJourneyAmount(journey) {
  const journeyAmount = { fareAmount: 0, taxAmount: 0, tasfAmount: 0 };
  if (!isEmpty(journey)) {
    const { flights } = journey;
    flights.forEach(({ fares }) => {
      journeyAmount.fareAmount += fares.paxTypeFares[0].airFare;
      journeyAmount.taxAmount += fares.paxTypeFares[0].airTax + fares.paxTypeFares[0].fuelChg;
      journeyAmount.tasfAmount += fares.paxTypeFares[0].tasfAmount ?? 0;
    });
  }
  return journeyAmount;
}

export function isIncludedCorporateFare(flights = []) {
  let isIncluded = false;
  flights.forEach(({ fares }) => {
    if (fares.fareTypes?.some((fareTypeItem) => fareTypeItem.includes(COP_CODE))) {
      isIncluded = true;
    }
  });
  return isIncluded;
}

export function trimString(originStr, startIdx, endIdx) {
  if (isEmpty(originStr)) return "";
  let trimString = "";

  if (originStr !== null && originStr !== undefined) {
    const strLen = originStr.length;

    if (endIdx === -1) {
      trimString = originStr.substring(startIdx);
    } else {
      if (strLen >= startIdx && strLen >= endIdx) {
        trimString = originStr.substring(startIdx, endIdx);
      }
    }
  }

  return trimString;
}

export function carriageReturn(contents) {
  if (contents && contents.trim().length > 0) {
    return contents.replace(/(\r\n|\r|\n|\n\r)/g, "<br>").replace(/&amp;/g, "&");
  }
  return "";
}

export function divisionComma(value, denominator) {
  return value && denominator ? comma(value / denominator) : 0;
}

export function returnPhoneNumber(value, position) {
  if (!value || value.trim() === "") {
    return "";
  }

  let first = value.substring(0, 2);
  let middle = null;
  let end = null;
  if (value.length < 7) {
    if (position === "FIRST") {
      return first === "02" ? "02" : value.substring(0, 3);
    }
    if (position === "MIDDLE") {
      return first === "02" ? value.substring(2) : value.substring(3);
    }
    if (position === "END") {
      return "";
    }
    if (position === "ALL") {
      return value;
    }
  }

  let endStartIdx = value.length - 4;
  if (first === "02") {
    middle = value.substring(2, endStartIdx);
  } else {
    first = value.substring(0, 3);
    middle = value.substring(3, endStartIdx);
  }
  end = value.substring(endStartIdx);

  if (position === "FIRST") {
    return first;
  }
  if (position === "MIDDLE") {
    return middle;
  }
  if (position === "END") {
    return end;
  }
  if (position === "ALL") {
    return `${first}-${middle}-${end}`;
  }
  return "";
}

export function getTLDate(bookingDate, createDate) {
  if (!bookingDate) {
    bookingDate = createDate;
  }
  const newTlDate = momentKR(bookingDate).add(3, "days").toDate();
  return newTlDate;
}

export function tlDate(bookingDate, createDate, pattern) {
  const tlDate = getTLDate(bookingDate, createDate);
  return momentKR(tlDate).format(pattern);
}

export function timeLimitTL(timeLimit) {
  const now = momentKR().format("YYYYMMDDHHmm");
  const viewTicketDate = momentKR(timeLimit).format("YYYYMMDD");
  const finalTimeLimit = viewTicketDate + "1700";
  return parseInt(now, 10) < parseInt(finalTimeLimit, 10);
}

export function inequalitySign(value) {
  if (value > 0) {
    return "+";
  }
  if (value < 0) {
    return "-";
  }
  return "";
}

export function pgReceiptView(tid, approvalNumber) {
  const receiptUrl = "https://iniweb.inicis.com/app/publication/apReceipt.jsp?noTid=" + tid;
  // receiptUrl += '&authFlg=1|0|2';

  //인증코드 3 : 승인번호 ==> https://iniweb.inicis.com/js/auth.js
  window.showReceipt(receiptUrl, "3", approvalNumber);
}

export function getCookie(name, valueOnly = false) {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) {
    const cookieValue = parts.pop().split(";").shift();
    return valueOnly ? cookieValue : `${name}=${cookieValue}`;
  }
  return "";
}

export function normalizeAirports(airports) {
  const { domestic = [], overseas = [] } = airports;
  return { domestic: groupBySection(domestic), overseas: groupBySection(overseas) };
}

function groupBySection(airports) {
  const groupedBySection = groupBy(airports, "airportSection");
  return Object.entries(groupedBySection)
    .map(([_, value], index) => {
      return value.map((el) => ({ ...el, sectionId: index + 1 }));
    })
    .flat();
}

export function swapArrayLoc(arr, from, to) {
  const temp = arr[to];
  arr[to] = arr[from];
  arr[from] = temp;
}

export function swapArrayPositionLoc(arr, from, to, position) {
  const temp = get(arr, `${from}[${position}]`);
  set(arr, `${from}[${position}]`, get(arr, `${to}[${position}]`));
  set(arr, `${to}[${position}]`, temp);
}

export function unescapeHtmlByDiv(inputData) {
  if (!inputData) {
    return "";
  }
  const parser = new DOMParser();
  const doc = parser.parseFromString(inputData, "text/html");
  return doc.documentElement.textContent;
}

export function onErrorImgAirline(event) {
  const { currentTarget } = event;
  currentTarget.onerror = null;
  currentTarget.src = AIR_ICONS_PATH.default_airline;
}

export function replaceCarriageReturns(text) {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  let displayText = text.replace(/(\r\n|\n|\r)/g, " <br/> ");
  return displayText.replace(urlRegex, (url) => {
    const href = url.startsWith("http") ? url : `http://${url}`;
    return `<a href="${href}" target="_blank">${url}</a>`;
  });
}

export function stripHTML(html) {
  if (isEmpty(html)) return "";
  return html.replace(/<[^>]*>/g, "");
}

export function getSlicedBySectionType(arr, sectionType) {
  if (sectionType === SECTION_TYPE.ONEWAY) return arr.slice(0, 1);
  if (sectionType === SECTION_TYPE.ROUNDTRIP) return arr.slice(0, 2);
  return arr; // MULTICITY
}

export function normalizeFilter(filter) {
  const clonedFilter = cloneDeep(filter);
  const isRoundTrip = filter.sectionType === SECTION_TYPE.ROUNDTRIP;
  if (isRoundTrip) {
    clonedFilter.arrivalAirportCodes[1] = clonedFilter.departureAirportCodes[0];
    clonedFilter.arrivalAirportNames[1] = clonedFilter.departureAirportNames[0];
    clonedFilter.departureAirportCodes[1] = clonedFilter.arrivalAirportCodes[0];
    clonedFilter.departureAirportNames[1] = clonedFilter.arrivalAirportNames[0];
  }

  return clonedFilter;
}

export function convertMapIntoSelectList(data) {
  return Object.entries(data).map(([key, value], index) => ({ id: index, name: value, value: key }));
}

export function reverseMap(data) {
  return Object.fromEntries(Object.entries(data).map(([key]) => [key, key]));
}

export function unescapeHtml(inputData) {
  if (!inputData || inputData.trim() === "") {
    return "";
  }

  return inputData
    .replace(/&amp;/g, "&")
    .replace(/&#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&gt;/g, ">")
    .replace(/&lt;/g, "<");
}

export function getAdditionalParams(filter, hotelSearch, hotelDetailCodes) {
  const hotelFareList = hotelSearch?.hotelFareList;
  if (hotelFareList?.length === 0 || !hotelFareList) {
    return { nightCount: 1, maxSalePrice: 0, minSalePrice: 0, maxAveragePrice: 0, minAveragePrice: 0, gradeCodeMap: {}, facilityCodeMap: {} };
  }

  const hotelSalePricesList = hotelFareList.map((hotel) => hotel.salePrice);

  const nightCount = getNightCount(filter);
  const maxSalePrice = Math.max(...hotelSalePricesList);
  const minSalePrice = Math.min(...hotelSalePricesList);
  const maxAveragePrice = Math.ceil(maxSalePrice / nightCount / 1000) * 1000;
  const minAveragePrice = Math.floor(minSalePrice / nightCount / 1000) * 1000;

  const gradeCodeMap = getMapByName(hotelFareList, "htlGrade", hotelDetailCodes.grade, "detailCodeMapping");
  const facilityCodeMap = getMapByName(
    uniq(hotelFareList.map((hotel) => hotel.htlFacilities).flat()),
    undefined,
    hotelDetailCodes.facility,
    "detailCode",
  );

  return {
    nightCount,
    maxSalePrice,
    minSalePrice,
    maxAveragePrice,
    minAveragePrice,
    gradeCodeMap,
    facilityCodeMap,
  };
}

function getMapByName(list, name, mappingNameList, mappingNameKey) {
  const map = {};
  for (const element of list) {
    const selectedElement = name ? element[name] : element;
    const founedMap = mappingNameList?.find((el) => el[mappingNameKey] === selectedElement);
    map[selectedElement] = founedMap?.detailCodeNameKr || "";
  }

  return map;
}

export function getNightCount(filter) {
  const { checkIn, checkOut } = filter;
  if (!checkIn || !checkOut) return 1;

  return momentKR(checkOut).diff(momentKR(checkIn), "days");
}

export function time(time) {
  if (time && time.trim() !== "" && time.length > 3) {
    return time.substring(0, 2) + ":" + time.substring(2, 4);
  }
  return "";
}

export function getTextDemension(text, font = "20px Noto Sans KR") {
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");
  context.font = font;
  if (text === null || text === undefined) {
    return { width: 1, height: 1 };
  }
  return {
    width: context.measureText(text).width,
    height: context.measureText(text).height,
  };
}

export function currencyFormat(num) {
  if (num === 0) return "0";

  const reg = /(^[+-]?\d+)(\d{3})/;
  let n = num.toString();

  while (reg.test(n)) n = n.replace(reg, "$1" + "," + "$2");

  return n;
}

export function getFirstValidValue(...values) {
  const invalidValues = ["0", 0, null, undefined, "undefined", "null", "false", false, ""];
  return find(values, (v) => !invalidValues.includes(v));
}

export function normalizeDangerHtmlHotelContent(rawHtml) {
  if (rawHtml) {
    return rawHtml
      .replaceAll("*", `<li class="inline ml-[8px] relative before:content-['•'] text-custom-gray-1000 before:absolute before:left-[-8px]"></li>`)
      .replaceAll("<ul>", "<ul class='list-disc pl-[16px] marker:text-custom-gray-1000'>")
      .replaceAll("<li>", "<li class='marker:text-[10px] before:ml-[-4px]'>")
      .replaceAll("<br>", "<p></p>");
  }

  return "";
}

export const validPassword = (password) => {
  const checkNum = ["012", "123", "234", "345", "456", "567", "678", "789"];
  const checkStr = [
    "abc",
    "bcd",
    "cde",
    "def",
    "efg",
    "fgh",
    "ghi",
    "hij",
    "ijk",
    "jkl",
    "klm",
    "lmn",
    "mno",
    "nop",
    "opq",
    "pqr",
    "qrs",
    "rst",
    "stu",
    "tuv",
    "uvw",
    "vwx",
    "wxy",
    "xyz",
    "ABC",
    "BCD",
    "CDE",
    "DEF",
    "EFG",
    "FGH",
    "GHI",
    "HIJ",
    "IJK",
    "JKL",
    "KLM",
    "LMN",
    "MNO",
    "NOP",
    "OPQ",
    "PQR",
    "QRS",
    "RST",
    "STU",
    "TUV",
    "UVW",
    "VWX",
    "WXY",
    "XYZ",
  ];

  const returnCode = ["OK", "PASSWORD_LEN", "PASSWORD_ONE_CHAR", "CONTINUOUS_NUMBER", ""];
  const passwordLen = password.length;

  if (passwordLen < 8) {
    return returnCode[1];
  }

  // Check for 3 or more repeated characters
  if (/([a-zA-Z])\1{2,}/.test(password)) {
    return returnCode[2];
  }

  // Check for 3 or more repeated numbers
  if (/(\d)\1{2,}/.test(password)) {
    return returnCode[2];
  }

  // Check for consecutive numbers
  for (let i = 0; i < checkNum.length; i++) {
    if (password.includes(checkNum[i])) {
      return returnCode[3];
    }
  }

  // Check for consecutive letters
  for (let i = 0; i < checkStr.length; i++) {
    if (password.includes(checkStr[i])) {
      return returnCode[2];
    }
  }

  // Check for required character types
  let matchCount = 0;
  // 1. Check for numbers
  if (/\d/.test(password)) {
    matchCount++;
  }
  // 2. Check for letters
  if (/[a-zA-Z]/.test(password)) {
    matchCount++;
  }
  // 3. Check for special characters
  if (/[!@#$%^&+=*()]/.test(password)) {
    matchCount++;
  }

  if (matchCount !== 3) {
    return returnCode[4];
  }

  return returnCode[0];
};
