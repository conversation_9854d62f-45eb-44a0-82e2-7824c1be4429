import axios from "axios";
import { defaultTo, get } from "lodash";
import { AXIOS_STATUS_CODE, ERROR_MESSAGE, SERVER_ERROR_MESSAGE } from "@/constants/app";
import { getCookie } from "@/utils/common";
import { CONTACT_ADMIN_ALERT } from "@/constants/common";

const IS_DEV = import.meta.env.VITE_ENVIRONMENT === "DEV";
const BASE_HEADER_OPTIONS = {
  ...(IS_DEV ? { "Cookie-Dev": getCookie("jwt") } : {}),
};

export const instanceAxios = axios.create({
  baseURL: IS_DEV ? import.meta.env.VITE_APP_API : window.location.origin,
  timeout: 0,
});
instanceAxios.interceptors.response.use(
  (response) => {
    const { code } = response.data;
    if (!code || Number(code) === AXIOS_STATUS_CODE.SUCCESS) {
      return response;
    }
    if (Number(code) === AXIOS_STATUS_CODE.ACCESS_DENIED) {
      location.href = "/accessDenied";
    }
    if (response.data.code && +response.data.code !== 200) {
      const message = defaultTo(get(response, "data.message"), SERVER_ERROR_MESSAGE);
      alert(`${message}${CONTACT_ADMIN_ALERT}`);
      return Promise.reject(response);
    }
    return Promise.reject(response);
  },
  (error) => {
    if (!axios.isCancel(error)) {
      const statusCode = defaultTo(get(error, "response.status"), 500);
      if (statusCode === AXIOS_STATUS_CODE.LOGIN_EXPIRED) {
        location.href = "/loginExpired";
      }
      const message = defaultTo(get(error, "response.data.message"), ERROR_MESSAGE);
      alert(`${message}${CONTACT_ADMIN_ALERT}`);
    }
    return Promise.reject(error);
  },
);

export default function request(options) {
  return instanceAxios({ ...options, headers: BASE_HEADER_OPTIONS });
}

export function requestV2(options) {
  return instanceAxios({
    ...options,
    baseURL: import.meta.env.VITE_APP_API_V2,
    headers: {
      Authorization: `Bearer ${getCookie("jwt", true)}`,
    },
  });
}
