const AirLocalStorageUtil = () => {
  const getOne = (id, userId) => {
    let data;
    const getData = get(userId);
    for (let index in getData) {
      if (getData[index].id === id) {
        data = getData[index];
        break;
      }
    }
    return data;
  };

  const get = (userId) => {
    return JSON.parse(localStorage.getItem(`airSearchData_${userId}`));
  };

  /**
   * 
   * @param {*} dataForm 
   * 
   * {
   *  departureAirportCodes: ["SEL", ""],
      departureAirportNames: ["서울", ""],
      arrivalAirportCodes: ["", ""],
      arrivalAirportNames: ["", ""],
      adultCount: 1,
      departSeatTypeCode: "M",
      departureDays: ["", ""],
      departureDayTexts: ["", ""],
      itineraryType: "RoundTrip",
      itineraryCount: 2,
      labelPassenger: "일반석",
      sectionType: "RoundTrip",
      isOverseas: "departure",
      stopOverType: 0,
      headDepartAirport: 1,
   * 
   * }
   * 
   * 
   */
  const set = (dataForm, userId) => {
    const departureAirportCodes = [];
    const departureAirportNames = [];
    const arrivalAirportCodes = [];
    const arrivalAirportNames = [];
    const departureDays = [];
    const departureDayTexts = [];
    let data = [];
    const object = {};

    dataForm.departureAirportCodes.forEach((item) => departureAirportCodes.push(item));
    dataForm.departureAirportNames.forEach((item) => departureAirportNames.push(item));
    dataForm.arrivalAirportCodes.forEach((item) => arrivalAirportCodes.push(item));
    dataForm.arrivalAirportNames.forEach((item) => arrivalAirportNames.push(item));
    dataForm.departureDays.forEach((item) => departureDays.push(item));
    dataForm.departureDayTexts.forEach((item) => departureDayTexts.push(item));

    if (get(userId) != null) {
      data = get(userId);
      if (data.length >= 20) {
        data.splice(0, 1);
      }
    }

    object.id = new Date().getTime();
    object.isOverseas = dataForm.isOverseas;
    object.departureAirportCodes = departureAirportCodes;
    object.departureAirportNames = departureAirportNames;
    object.arrivalAirportCodes = arrivalAirportCodes;
    object.arrivalAirportNames = arrivalAirportNames;
    object.departureDays = departureDays;
    object.departureDayTexts = departureDayTexts;
    object.itineraryType = dataForm.sectionType;
    object.departSeatTypeCode = dataForm.departSeatTypeCode;
    object.itineraryCount = dataForm.itineraryCount;
    object.adultCount = dataForm.adultCount;
    object.stopOverType = dataForm.stopOverType;
    object.sectionType = dataForm.sectionType;
    object.headDepartAirport = dataForm.headDepartAirport;

    data.push(object);
    localStorage.setItem(`airSearchData_${userId}`, JSON.stringify(data));
  };

  const remove = (id, userId) => {
    let getData = get(userId);
    let data = [];
    for (let index in getData) {
      if (getData[index].id != id) {
        data.push(getData[index]);
      }
    }
    localStorage.setItem(`airSearchData_${userId}`, JSON.stringify(data));
  };

  return { get, set, remove, getOne };
};

const airLocalStorageUtil = AirLocalStorageUtil();

export default airLocalStorageUtil;
