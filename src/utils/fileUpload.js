import { CONTACT_ADMIN_ALERT } from "@/constants/common";
import { requestWithMobileLoading } from "@/utils/app";
import QueryString from "qs";

export function uploadFile(params) {
  const { file, uploadConfig, uploadedFiles, formData, onUpdateUploadedFiles } = params;
  if (uploadedFiles.length >= uploadConfig.MAX_COUNT) {
    alert("파일 개수는 " + uploadConfig.MAX_COUNT + "를 초과할 수 없습니다.");
    return;
  }
  if (file.size > uploadConfig.MAX_SIZE * 1024 * 1024) {
    alert("파일 용량은 " + uploadConfig.MAX_SIZE + "MB를 초과할 수 없습니다.");
    return;
  }
  if (uploadConfig.FILE_TYPE === "img") {
    var ext = file.name.split(".").pop().toLowerCase();
    if (["gif", "png", "jpg", "jpeg"].indexOf(ext) === -1) {
      alert("gif, png, jpg, jpeg 파일만 업로드 할수 있습니다.");
      return;
    }
  }
  const payload = new FormData();
  Object.keys(formData).forEach((key) => {
    payload.append(key, formData[key]);
  });
  payload.append("File", file);
  requestWithMobileLoading({
    url: "/common/file/upload",
    method: "POST",
    data: payload,
    success: (res) => {
      onUpdateUploadedFiles((prev) => [...prev, res.data])
    },
  });
}

export function deleteFile(params) {
  const { file, onUpdateUploadedFiles } = params;
  requestWithMobileLoading({
    url: "/common/file/delete",
    method: "POST",
    data: QueryString.stringify({ filePath: `${file.fileUploadPath}${file.tempFileName}` }),
    success: (res) => {
      if (!res.data) {
        alert(`파일 삭제 중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
        return;
      }
      onUpdateUploadedFiles((prev) => prev.filter((item) => item.createDate !== file.createDate));
    },
    fail: (error) => {
      if (error.response?.status === 401) {
        location.href("/loginExpired");
        return;
      }
      alert(`파일 삭제 중 장애가 발생했습니다.\n다시 시도해주세요.${CONTACT_ADMIN_ALERT}`);
    },
  });
}
