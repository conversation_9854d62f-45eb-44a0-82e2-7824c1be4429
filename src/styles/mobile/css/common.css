﻿@charset "utf-8";
.clearfix{ position: relative; }
.clearfix::after{ content: ''; display: block; clear: both; }
.contents{ position: relative;  }
#wrap{  }
.sec-pay-to{ z-index: 50; background-color: #fff;}
.ta-l{ text-align: left; }
.ta-r{ text-align: right; }
.ta-c{ text-align: center; }
/* 아이폰 텍스트박스 라운드 처리 */
input, textarea, button { -webkit-appearance:none; -moz-appearance:none; appearance:none; }
input, textarea, button, select { -webkit-border-radius:0; -moz-border-radius:0; -o-border-radius:0; border-radius:0; }
input:focus, textarea:focus { -webkit-tap-highlight-color: transparent; outline:none; }
/*=========================================*/
/* ################ Layout ############### */
/*=========================================*/

/* header */
#header{ position: relative; border-bottom: 1px solid #e2e4e8; z-index: 10; background-color: #fff;}
.page-header{ position: relative; height: 55px; border-bottom: 1px solid #ebeef3; }
.page-header .pg-title{ font-size: 15px; font-weight: 500; line-height: 55px; text-align: center; color: #000; border-bottom: 1px solid #ebeef3; }
.page-header .btn-prev-page{position: absolute; top: 4px; left: 0; width: 46px; height: 46px; background: url(../../../assets/mobile/images/cmm/btn-prev-head.png) 50% 50% no-repeat; -webkit-background-size: 7px 14px; background-size: 7px 14px;}
.page-header .btn-close-x{position: absolute; top: 4px; right: 0; width: 46px; height: 46px; background: url(../../../assets/mobile/images/cmm/btn_close_page_b.png) 50% 50% no-repeat; -webkit-background-size: 14px 14px; background-size: 14px 14px;}

.page-header.type-white { background-color: #6f98fe; border-bottom: 0 none;}
.page-header.type-white .btn-prev-page{ background-image: url(../../../assets/mobile/images/cmm/icn_prev_page_w.png); -webkit-background-size: 9px 16px; background-size: 9px 16px;}
.page-header.type-black { background-color: #fff; height: auto;}
.page-header.type-black .btn-prev-page{ background-image: url(../../../assets/mobile/images/cmm/btn_page_back.png); -webkit-background-size: 9px 16px; background-size: 9px 16px;}
.page-header .air-plan{ position: relative; padding: 10px 0 10px 45px; border-bottom: 1px solid #ebeef3;}
.page-header .air-plan .city{ overflow: hidden; display: inline-block; font-size: 14px; line-height: 16px; font-family: Roboto; font-weight: 700; color: #000; ;}
.page-header .air-plan .city .arr::after{ content: ''; display: inline-block; vertical-align: top; width: 30px; height: 22px; background: url(../../../assets/mobile/images/search/icn_roundtrip_small.png) 50% 45% no-repeat; -webkit-background-size: 12px 8px; background-size: 12px 8px; }
.page-header .air-plan .city p{ float: left; }
.page-header .air-plan .city.oneway .arr::after{ background: url(../../../assets/mobile/images/search/icn_oneway_s.png) 47% 50% no-repeat; -webkit-background-size: 13px 4px; background-size: 13px 4px; background-position-y: 40%;}
.page-header .air-plan .date{   font-size: 12px; line-height: 14px; font-family: Roboto;}
.page-header .air-plan.domestic{ padding-top: 17px; padding-bottom: 17px; text-align: center; padding-right: 45px;}
.page-header .air-plan.domestic .tit-di{ margin-right: 8px; display: inline-block; line-height: 22px; vertical-align: top; font-size: 15px; font-weight: 500; color: #4e81ff;}
.page-header .air-plan.domestic .city{ line-height: 22px; vertical-align: top; font-family: "Noto Sans KR"; font-weight: 300; font-size: 15px;}
.page-header .air-plan.domestic .city.oneway .arr::after{ background-position-y: 50% ; }
.page-header .air-plan.domestic ~ .btn-prev-page{ padding-top: 2px; }
.page-header .remain-time{ text-align: center; line-height: 40px; background-color: #4e81ff; color: #fff;}
.page-header .loading-bar.domestic{ position: absolute; top: 57px; left: 0; width: 100%; }
.page-header .loading-bar{ position: relative; z-index: 10}
.page-header .loading-bar .ing{ height: 2px; background-color: #4e81ff; }
.page-header .hotel-booking{ position: relative; height: 56px; padding-left: 43px; padding-right: 82px; background-color: #fff; }
.page-header .hotel-booking .location{ padding-top: 10px; margin-bottom: 2px;  font-weight: 700; line-height: 17px; letter-spacing: -0.5px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap}
.page-header .hotel-booking .location span{ font-family: Roboto; }
.page-header .hotel-booking .info { font-size: 12px; line-height: 15px;}
.page-header .hotel-booking .info::after{ content: ''; clear: both; display: block; }
.page-header .hotel-booking .info p{ padding: 0 8px; font-family: Roboto; }
.page-header .hotel-booking .info .date{ position: relative; display: inline-block; padding-left: 0; padding-right: 8px; }
.page-header .hotel-booking .info .date::after{ content: ''; position: absolute; top: 50%; right: 0; height: 10px; margin-top: -5px; border-left: 1px solid #e2e4e8;}
.page-header .hotel-booking .info .room{ display: inline-block; padding-left: 6px;}
.page-header .btn-research{ position: absolute; top: 15px; right: 16px; width: 57px; text-align: center; line-height: 24px; border: 1px solid #4e81ff; border-radius: 16px; font-size: 12px; text-align: center; color: #4e81ff;}

#footer{ position: relative; padding:16px; background-color: #f2f4f9; }
#footer .cs-center{ color: #7f848d; margin-bottom: 12px; }
#footer .cs-center:first-child{ padding-top: 9px; }
#footer .cs-center + .cs-center{ margin-bottom: 24px; }
#footer .cs-center .tel{ font-size: 18px; line-height: 27px; font-weight: 700; }
#footer .cs-center .operate{ font-size: 13px; line-height: 19px; }
#footer .box-lnb{padding-bottom: 24px; margin-bottom: 23px; border-bottom: 1px solid #e2e4e8; color: #8d9199;}
#footer .box-lnb .col:first-child{ margin-bottom: 6px; font-size: 10px; }
#footer .box-lnb .col a{ vertical-align: middle; color: #8d9199; font-size: 12px; line-height: 18px;}
#footer .box-lnb .col .bar{display: inline-block; margin: 0 5px; font-size: 10px; color: #c9cfd8; vertical-align: top;}
#footer .info{ margin-bottom: 21px; color: #8d9199; font-size: 12px;line-height: 18px; font-weight: 300; }
#footer .copyright{ padding-bottom: 10px; font-size: 11px; font-family: Roboto; color: #8d9199}
#footer .copyright strong{ font-weight: 500;}

#btn-top{ text-align: right; }

#container{ position: relative; }
#dimContents{ position: absolute; top: 89px; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.2);}
.contents{ position: relative; }
.contents.dim-contents::after{ content: '';  position: absolute; top: 0; left: -99999em; right: -999999em; bottom: 0; background-color: #fff;}

/* col Commmon */
.col-left{ float: left; }
.col-right{ float: right; }

.h-100{ height: 100%; }

/* cmm Css */
#header #gnb .more:hover i,
.select-ticket .sel .ico
#footer .family-site:hover .ico{
-webkit-transform: rotate(180deg);
-ms-transform: rotate(180deg);
-o-transform: rotate(180deg);
transform: rotate(180deg);
}
.ico-qustion-circle{ display: inline-block; margin-left: 2px; width: 13px; height: 13px; background: url(../../../assets/mobile/images/cmm/icn-qm-line-copy.png) 0 0 no-repeat; -webkit-background-size: 100%; background-size: 100%;}
.default-wd{ width: 1080px; margin: 0 auto; }
.none-pd-bottom{ padding-bottom: 0 !important; }

/* background color */
.bg-blue{ background-color: #6f98fe; }
.bg-white{ background-color: #fff; }
.bg-c01{ background-color: #f2f4f9; }
.bg-gray { background-color: #f2f4f9; }
/* btutton-default */
.btn-default{ cursor: pointer; display: inline-block; font-size: 14px; color: #000; outline: none; box-sizing: border-box; }
.btn-default.bg-blue{ width: 140px; text-align: center; font-size: 16px; font-weight: 500; color: #fff; background-color: #4e81ff; line-height: 50px; border-radius: 5px;}
.btn-datepicker{ cursor: pointer; outline: none; }
.btn-filter{ position: fixed; bottom: 30px; left: 50%; margin-left: -48px;}
.btn-filter.type-domestic{ margin-left: -65px; }
.btn-filter .btns-cmm{ display: block; width: 96px; line-height: 40px; border-radius: 20px; border-color: #34446e; box-shadow: 0 2px 6px 0 rgba(62, 76, 112, 0.4); background-color: #34446e;}
.btn-filter .btns-cmm span{ display: block; padding-left: 31px; font-size: 13px; font-weight: 500; background: url(../../../assets/mobile/images/cmm/icn_filter.png) 16px 55% no-repeat; -webkit-background-size: 30px 26px; background-size: 30px 26px; }
.btn-filter.type-domestic .btns-cmm{ width: 130px; }
.btn-filter.type-hotel{margin-left: -100px;}
.btn-filter.type-hotel .box-wrap{ display: inline-block; line-height: 40px; border-radius: 20px; border-color: #34446e; box-shadow: 0 2px 6px 0 rgba(62, 76, 112, 0.4); background-color: #34446e; }
.btn-filter.type-hotel .box-wrap .btns-cmm{ display: inline-block; color: #fff; border: 0 none; vertical-align: top;}
.btn-filter.type-hotel .box-wrap .btns-cmm.map{ width: 84px; position: relative; }
.btn-filter.type-hotel .box-wrap .btns-cmm.map span{ padding-left: 30px; background: url(../../../assets/mobile/images/cmm/icn-map.png) 16px 50% no-repeat; -webkit-background-size: 17px 17px; background-size: 17px 17px; }
.btn-filter.type-hotel .box-wrap .btns-cmm.map::after{ content: ''; height: 14px; position: absolute; top: 50%; right: 0; margin-top: -7px; border-right: 1px solid rgba(250, 250, 250, 0.3); }
.btn-filter.type-hotel .box-wrap .btns-cmm.list{ display: none; width: 84px; position: relative; }
.btn-filter.type-hotel .box-wrap .btns-cmm.list span{ padding-left: 30px; background: url(../../../assets/mobile/images/cmm/icn_list.png) 16px 50% no-repeat; -webkit-background-size: 17px 17px; background-size: 17px 17px; }
.btn-filter.type-hotel .box-wrap .btns-cmm.list::after{ content: ''; height: 14px; position: absolute; top: 50%; right: 0; margin-top: -7px; border-right: 1px solid rgba(250, 250, 250, 0.3); }
.btn-filter.type-hotel .box-wrap .btns-cmm.align{ width: 116px; }
.btn-filter.type-hotel .box-wrap .btns-cmm.align span{ padding-left: 15px; background: url(../../../assets/mobile/images/cmm/icn_filter.png) 5px 55% no-repeat; -webkit-background-size: 30px 26px; background-size: 30px 26px;}

.btn-page-top{ position: fixed; bottom: 30px; right: 20px;  }
.btn-page-top .btns-cmm{ display: block; width: 44px; height: 44px; background-color: #fff; border: 0 none; border-radius: 50%; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15); background: #fff url(../../../assets/mobile/images/cmm/icn_top_arrow.png) 50% 50% no-repeat; -webkit-background-size: 10px 17px; background-size: 10px 17px; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15);}
.btn-tip-compare{position: absolute; top: 21px; right: 16px; width: 18px; height: 18px; cursor: pointer; background: url(../../../assets/mobile/images/cmm/icn_question.png) 0 0 no-repeat; -webkit-background-size: 18px; background-size: 18px;}
.btn-tip-compare span{display: none; position: absolute; top: 150%; right: -6px; width: 160px; line-height: 19px; padding: 12px 16px; background-color: #f0f4ff; border: 1px solid #4e81ff; color: #4e81ff; font-size: 13px; border-radius: 4px;}
.btn-tip-compare span:after,
.btn-tip-compare span:before {
	bottom: 100%;
	right: 7px;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
}
.btn-tip-compare span:after {
	border-color: rgba(136, 183, 213, 0);
	border-bottom-color: #f0f4ff;
	border-width: 6px;
	margin-right: 0px;
}
.btn-tip-compare span:before {
	border-color: rgba(194, 225, 245, 0);
	border-bottom-color: #4e81ff;
	border-width: 8px;
	margin-right: -2px;
}
.btn-tip-compare.active span{ display: block; }

#ticketSearch .btn-datepicker.in .default{ display: none;  }
#ticketSearch .btn-datepicker.in .val{ display: block; }

/* 1줄 말즐일 표*/
.txt-short{ text-overflow: ellipsis; white-space: nowrap; overflow: hidden }

.paginate{ position: relative; padding-top: 20px;}
.paginate *{ vertical-align: top; }
.paginate .btns{ display: inline-block; font-size: 0; }
.paginate .btns .btn-default{ width: 20px; height: 20px; background-position: 0 0; background-repeat: no-repeat; }
.paginate .btns .step-all{ background: url(../../../assets/mobile/images/cmm/btn_navi_ll.png) 50% 7px no-repeat; }
.paginate .btns .step-one{ background: url(../../../assets/mobile/images/cmm/btn_navi_l.png) 50% 7px no-repeat; }
.paginate .btns.next .btn-default {
    background-position: 50% 6px;
     -webkit-transform: rotate(180deg);
     -ms-transform: rotate(180deg);
     -o-transform: rotate(180deg);
     transform: rotate(180deg);
}
.paginate ol{ display: inline-block; margin: 0 10px;}
.paginate ol::after{ content: ''; clear: both; display: block; }
.paginate li{ float: left; }
.paginate li a{ display: block; width: 20px; line-height: 20px; color: #babdc3; text-align: center;}
.paginate li.active{ font-weight: 700; }
.paginate li.active a{color: #000}

.box-saerch-cmm{ position: relative; margin-bottom: 40px; border: 1px solid #e2e4e8; border-radius: 5px; overflow: hidden;}
.box-saerch-cmm input{width: 748px; height: 44px; border: 0 none; padding:0  56px 0 20px; outline: none;}
.box-saerch-cmm .btn-search{ position: absolute; top: 0; right: 5px; width: 44px; height: 44px; background: url(../../../assets/mobile/images/cmm/btn_search.png) 50% 50% no-repeat; cursor: pointer; outline: none;}


.plan-info .table-cmm.type-schedule{ margin-bottom: 10px; }
.plan-info .table-cmm.type-schedule th{ border-bottom-color: #fff; }
.plan-info .table-cmm.type-schedule th .box-in{ font-size: 14px; }
.plan-info .table-cmm.type-schedule td{ border-bottom-color: #fff; }
.plan-info .table-cmm.type-schedule td .box-in{ line-height: 52px; border-top: 1px solid #e2e4e8; border-bottom: 1px solid #e2e4e8; font-size: 13px; letter-spacing: -1px;}
.plan-info .table-cmm.type-schedule td strong{ font-weight: 700; }
.plan-info .table-cmm.type-schedule img{ height: 15px; position: relative; top: -2px; margin-right: 2px; vertical-align: middle;}
.plan-info .table-cmm.type-schedule td:first-child .box-in{ border-left: 1px solid #e2e4e8; }
.plan-info .table-cmm.type-schedule td:last-child .box-in{ border-right: 1px solid #e2e4e8; }
.plan-info .box-btns{ overflow: hidden; margin-bottom: 43px; }
.plan-info .box-btns .btn-default{ float: left; width: 120px; margin-right: 8px; line-height: 40px; font-weight: 500; text-align: center; border: 1px solid #4e81ff; border-radius: 5px; color: #4e81ff;}
.plan-info .box-btns .btn-default.change-air{ text-indent: 5px; background: url(../../../assets/mobile/images/cmm/ico_btn_blue.png) 12px 50% no-repeat;}

/*=========================================*/
/* ################ form ############### */
/*=========================================*/
.form-tit{display: block; font-size: 13px; line-height: 19px; color: #757f92; }
.form-tit span{ color: #4e81ff; }
.input-cmm{ display: block; margin-bottom: 24px; }
.input-cmm input{ width: 100%; height: 40px; padding: 0; margin: 0; border: 0 none; border-bottom: 1px solid #ebeef3; font-size: 15px; outline: none;}
.input-cmm input:focus{ border-color: #4e81ff; }
.input-cmm input::placeholder{ color: #c9cfd8 }
.select-cmm{ position: relative; display: block;}
.select-cmm::before{content: ''; position: absolute; top: 50%; right: 8px; width: 8px; height: 4px; margin-top: -2px; background: url(../../../assets/mobile/images/cmm/ico_select.png) 0 0 no-repeat; -webkit-background-size: 8px 4px; background-size: 8px 4px;}
.select-cmm select{ width: 100%; position: relative; border: 0 none; margin: 0; padding: 0; height: 40px; border-bottom: 1px solid #ebeef3; background-color: transparent; outline: none;}
.custom-sel{ margin-bottom: 24px; border-bottom: 1px solid #ebeef3; }
.custom-sel .ui-selectmenu-button { position: relative; display: block; width: 100%; padding-left: 0; box-sizing: border-box; outline: none; }
.custom-sel .ui-selectmenu-button::before{content: ''; position: absolute; top: 50%; right: 8px; width: 8px; height: 4px; margin-top: -2px; background: url(../../../assets/mobile/images/cmm/ico_select.png) 0 0 no-repeat; -webkit-background-size: 8px 4px; background-size: 8px 4px;}
.ui-selectmenu-menu{ z-index: 49; }
.ui-selectmenu-menu .ui-menu{ margin-top: 4px; background-color: #fff; border: 1px solid #e2e4e8; border-radius: 5px;}
.ui-selectmenu-menu .ui-menu .ui-menu-item-wrapper{ padding: 0 14px; line-height: 46px; }
.ui-selectmenu-menu .ui-menu .ui-menu-item-wrapper.ui-state-active{ background-color: rgba(78, 129, 255, 0.07); color: #4e81ff; }

.form-cn{ margin-bottom: 24px; }
.form-cn .input-cmm{ margin-bottom: 0; }
.form-cn .form-desc{ padding-top: 4px; font-size: 12px; line-height: 18px; color: #9da9be; }
.form-cn .form-radio,
.form-cn .form-chkbox{ margin-top: 9px; }
.form-cn .validation{ display: block; margin-top: 5px; font-size: 12px; color: #ff4e50; }
.form-cn .box-val{ display: inline-block; line-height: 24px; }
.form-cn.telephone{ overflow: hidden; }
.form-cn.telephone .select-cmm{ float: left; width: 100px; }
.form-cn.telephone .select-cmm select{ height: 41px; }
.form-cn.telephone .input-cmm{ float: left; width: calc( 100% - 100px);}
.form-cn.cellphone{ overflow: hidden; }
.form-cn.cellphone .select-cmm select{ height: 41px; }
.form-cn.cellphone .select-cmm{ float: left; width: 100px; }
.form-cn.cellphone .box-right{ position: relative; float: right; width: calc( 100% - 100px); }
.form-cn.cellphone .box-right::before{ content: '-'; position: absolute; top: 0; left: 50%; line-height: 38px;  }
.form-cn.cellphone .input-cmm{ float: left; width: 50%; text-align: center;}
.form-cn.cellphone .input-cmm input{ text-align: center; }

.form-chkbox{ display: inline-block;}
.form-chkbox span{position: relative; display: inline-block; padding-left: 26px; vertical-align: top; font-size: 14px; letter-spacing: -0.3px;}
.form-chkbox input[type="checkbox"]{ visibility: hidden; width: 0; height: 0; opacity: 0; position: absolute; padding: 0; margin: 0;}
.form-chkbox input[type="checkbox"]:checked + span::after{ background-image: url(../../../assets/mobile/images/cmm/check_sel.png); }
.form-chkbox span::after{ content: ''; position: absolute; top: 0px; left: 0; width: 18px; height: 18px; background: url(../../../assets/mobile/images/cmm/check_dis.png) 0 0 no-repeat; -webkit-background-size: 18px;background-size: 18px;}
.form-radio{ display: inline-block; position: relative;}
.form-radio span{position: relative; display: inline-block; padding-left: 26px; vertical-align: top; min-height: 20px;}
.form-radio input[type="radio"]{ visibility: hidden; width: 0; height: 0; opacity: 0; }
.form-radio span::before{ content: ''; position: absolute; top: 0px; left: 0; width: 16px; height: 16px; border-radius: 100%; background-color: #c9cfd8;  }
.form-radio span::after{ content: ''; position: absolute; top: 5px; left: 5px; width: 6px; height: 6px; border-radius: 100%; background-color: #fff;}
.form-radio input[type="radio"]:checked + span::before{ background-color: #4e81ff; border-color:#4e81ff; }
.form-select { position: relative; display: inline-block; }
.form-select.w100{ width: 100%; }
.form-select.w100 select{ box-sizing: border-box; width: 100%; display: block; }
.form-select select{text-align: right; height: 22px; font-size: 15px; border: 0 none; padding: 0 20px 0 0 ; box-sizing: content-box; background: url(../../../assets/mobile/images/cmm/ico_arrow_select.png) no-repeat 96% 50%; outline: none;}
.select-ticket { position: relative; z-index: 10; }
.form-select.has-scroll{ background-color: #fff; line-height: 34px;   border-radius: 5px;}
.form-select.has-scroll .ui-selectmenu-button.ui-button{ padding: 0; line-height: 36px; outline: none;}
.form-select.has-scroll .ui-icon-triangle-1-s{top: 13px; right: 13px; background: url(../../../assets/mobile/images/cmm/ico_arrow_select.png) no-repeat 0% 0%;}
.form-select select option{ text-align: right; }
input.text-focus{ height: 34px; border: 0 none; border-bottom: 1px solid #e2e4e8; outline: none; }
input.text-focus:focus{ border-color: #4e81ff;  }
.box-col input[type="text"],
.box-col input[type="password"]{ width: 100%; height: 34px; border: 0 none; border-bottom: 1px solid #ebeef3; outline: none;  }
.box-col input[type="text"]:focus{ border-color: #4e81ff !important;  }
.box-col input[type="password"]:focus{ border-color: #4e81ff !important;  }
.box-col .desc.none-bor{ padding-bottom: 0 !important; }
.box-col .box-ymd{ padding-bottom: 0 !important; }
.box-col .box-ymd .form-select{ border-bottom: 0 none; }
.box-col .form-select{ border-bottom: 1px solid #e2e4e8; }
.box-col .form-select select{ height: 34px; }
.box-col .form-radio{ line-height: 28px; }
.box-col .form-radio span::before{ top: 50%; margin-top: -8px; }
.box-col .form-radio span::after{ top: 50%; margin-top: -3px; }

.none-bor{ border: 0 none !important; }

.select-ticket .list{ display: none; position: absolute; top: 0; left: 0; }
.select-ticket.active .list{ display: block; background-color: #fff;}
.in-slide .ui-widget.ui-widget-content{ border: 0 none; height: 3px; background-color: #c9cfd8; border-radius: 3px; }
.in-slide .ui-slider-horizontal .ui-slider-range{ background-color: #4e81ff; }
.in-slide .ui-slider .ui-slider-handle{ top: -5px; width: 14px; height: 14px; border-radius: 100%; border: 0 none; background-color: #4e81ff;}

.box-col.box-ymd .form-select.yy{ width: 54px; }
.box-col.box-ymd .form-select.mm{ width: 51px; }
.box-col.box-ymd .form-select.dd{ width: 51px; }

.box-yymmdd{overflow: hidden; position: relative; margin-bottom: 24px;}
.box-yymmdd .select-cmm{ float: left; width: 33.3%; }
.box-yymmdd .select-cmm:first-child{ width: 33.4%; }
.box-yymmdd .select-cmm::before{ right: 20px; }

/*=========================================*/
/* ################ datepicer ############### */
/*=========================================*/



.ui-selectmenu-menu .slimScrollDiv{ margin-top: 4px; }
.ui-selectmenu-menu .slimScrollDiv .slimScrollRail{ background-color: transparent !important; }
.ui-selectmenu-menu .scrollbar-inner{ background-color: #fff; border: 1px solid #e2e4e8; border-radius: 5px; }
.ui-selectmenu-menu .scrollbar-inner .ui-menu-item-wrapper{ padding:0 14px; line-height: 34px;color: #000;  box-sizing: border-box;  }
.ui-selectmenu-menu .scrollbar-inner .ui-menu-item-wrapper:hover{ background-color: rgba(78, 129, 255, 0.07); color: #4e81ff; }
.ui-selectmenu-menu .scrollbar-inner .ui-menu-divider{ text-indent: -999em; height: 0; border-top: 1px solid #e2e4e8;}
.ui-selectmenu-menu .scrollbar-inner .ui-menu-divider:first-child{ border-top: 0 none; }
.ui-selectmenu-menu .scrollbar-inner.passport-nation {height: 268px; }
.ui-selectmenu-menu .scrollbar-inner > .scroll-element.scroll-y{ right: 7px; }
.ui-selectmenu-menu .scrollbar-inner .scroll-element_track{ background-color: #fff;  }
.ui-selectmenu-menu .scrollbar-inner > .scroll-element .scroll-bar{ background-color: #c9cfd8; }
.ui-selectmenu-menu .scrollbar-inner .scroll-element_outer{ top: 12px; bottom: 12px; height: auto; }
.ui-selectmenu-menu .slimScrollDiv .slimScrollBar{ right: 10px !important; width: 3px !important; background-color: #c9cfd8 !important;}

.box-tit.type-cmm{position: relative; overflow: hidden; padding:18px 0 18px 21px; margin-bottom: 20px; border-radius: 5px; background-color: #f1f5ff; }
.box-tit.type-cmm::before{ content: ''; position: absolute; top: 0; left: 0; bottom: 0; width: 5px;  background-color: #4e81ff; }
.box-tit.type-cmm dt{margin-bottom: 6px; font-size: 20px; font-weight: 700; line-height: 29px; color: #4e81ff; }
.box-tit.type-cmm dd{ font-size: 13px; line-height: 19px; color: #596a92; }

.validate{ padding: 6px 0 3px; font-size: 11px; line-height: 17px; color: #ff4e50; }

/*=========================================*/
/* ################ table ############### */
/*=========================================*/
.tbl-cmm{ position: relative; border-top: 2px solid #ebeef3; border-bottom: 2px solid #ebeef3;}
.tbl-cmm table{ width: 100%; }
.tbl-cmm tbody th{ background-color: #f2f4f9; padding:12px 0 12px 15px; font-size: 13px; font-weight: 500; line-height: 19px; text-align: left; color: #293036; border-top: 1px solid #f2f4f9;}
.tbl-cmm tbody td{ padding:12px 0 12px 15px; font-size: 13px; line-height: 19px; border-top: 1px solid #ebeef3;background-color: #fff;}
.tbl-cmm tbody tr:first-child th,
.tbl-cmm tbody tr:first-child td{ border-top: 0 none; }

.table-cmm{ position: relative; }
.table-cmm table{ width: 100%; }
.table-cmm thead th{ font-weight: 500; }
.table-cmm tbody th{}
.table-cmm tbody td{}
.table-cmm .left{ text-align: left !important; }
.table-cmm .center{ text-align: center !important; }
.table-cmm .right{ text-align: right !important; }
.table-cmm.type-schedule .box-in{ display: block; }
.table-cmm.type-schedule th{  }
.table-cmm.type-schedule th:first-child .box-in{ border-radius: 5px 0 0 5px; }
.table-cmm.type-schedule th:last-child .box-in{ border-radius: 0 5px 5px 0; }
.table-cmm.type-schedule td:first-child .box-in{ border-radius: 5px 0 0 5px; }
.table-cmm.type-schedule td:last-child .box-in{ border-radius: 0 5px 5px 0; }
.table-cmm.type-schedule thead th{ height: 46px; border-bottom: 8px solid #f2f4f9;  font-size: 15px; font-weight: 400;  color: #3f4e73;}
.table-cmm.type-schedule thead th { line-height: 46px;  background: url(../../../assets/mobile/images/cmm/bg_tbl_schedule_center.png) repeat-x 0 0; color: #fff;}
.table-cmm.type-schedule thead th:first-child { background: url(../../../assets/mobile/images/cmm/bg_tbl_schedule_left.png) repeat-x 0 0;}
.table-cmm.type-schedule thead th:last-child { background: url(../../../assets/mobile/images/cmm/bg_tbl_schedule_right.png) repeat-x 100% 0;}
.table-cmm.type-schedule tbody td{ border-bottom: 4px solid #f2f4f9;  text-align: center; font-size: 15px; color: #333;}
.table-cmm.type-schedule tbody td .box-in{ line-height: 54px; background-color: #fff;}
.table-cmm.fixed .fix-head{ margin-right: 9px; }
.table-cmm.fixed .fix-body{ max-height: 296px;overflow-y:auto; }
.table-cmm.fixed .fix-body.scroll-wrapper{ padding-right: 9px !important; }
.table-cmm.fixed thead th{}
.table-cmm.fixed tbody th{}
.table-cmm.fixed tbody td{}
.table-cmm.type-fare{ border-top: 2px solid #9da9be; border-bottom: 2px solid #9da9be; }
.table-cmm.type-fare thead th{ height: 46px; font-size: 15px; font-weight: 500; }
.table-cmm.type-fare tbody td{ height: 57px; border-top: 1px solid rgba(157, 169, 190, 0.6); font-size: 15px; color: #333; text-align: center;}




/*=========================================*/
/* ################ modal popup ############### */
/*=========================================*/
/* modal popup*/
.blocker {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    z-index: 25;
    padding: 30px;
    box-sizing: border-box;
    background-color: #000;
    background-color: rgba(0,0,0,0.5);
    text-align: center;
    z-index: 115;

}
.modal a.close-modal{ top: 34px; right: 40px; width: 20px; height: 20px; display: none;}
.modal-wrap{ display: none; max-width: none; padding: 0; margin: 0; box-shadow: 0 2px 10px 4px rgba(41, 48, 54, 0.2); min-height: 50px; width: 100%;}
.modal-wrap .modal-head h2{ font-size: 26px; line-height: 38px; font-weight: 500; letter-spacing: -1px;}
.modal-wrap.type-alrt { text-align: center; }
.modal-wrap.type-alrt .desc{ padding: 40px 0 32px; font-size: 16px; line-height: 24px; font-weight: 500; }
.modal-wrap.type-alrt .box-btn{ padding-bottom: 20px; }
.modal-wrap.type-alrt .modal-head{ padding-left: 16px; text-align: left; font-size: 16px; line-height: 56px; border-bottom: 1px solid #ebeef3; font-weight: 500;}
.modal-wrap.type-alrt .btn-close{ position: absolute; top: 18px; right: 16px; width: 14px; height: 14px; background: url(../../../assets/mobile/images/cmm/btn_close_page_b.png) 0 0 no-repeat; -webkit-background-size: 14px 14px; background-size: 14px 14px; }

/* 예약 취소 완료 */
.modal-wrap.reserv-cancel-complete dl.desc dt{ margin-bottom: 12px; font-size: 16px; font-weight: 500; line-height: 24px; }
.modal-wrap.reserv-cancel-complete dl.desc dd{ font-weight: 300; line-height: 20px; }
.modal-wrap.reserv-cancel-complete dl.desc .mail{ font-weight: 500; color: #4e81ff; }
.modal-wrap.reserv-cancel-complete .box-btn{ margin-bottom: 24px; }

/* 문의 접수 완료 */
.modal-wrap.inquery-receive-complete dl.desc dt{ margin-bottom: 12px; font-size: 16px; font-weight: 500; line-height: 24px; }
.modal-wrap.inquery-receive-complete dl.desc dd{ font-weight: 300; line-height: 20px; }
.modal-wrap.inquery-receive-complete dl.desc .mail{ font-weight: 500; color: #4e81ff; }
.modal-wrap.inquery-receive-complete .box-btn{ margin-bottom: 24px; }


.box-guide{ padding: 20px; margin-bottom: 20px; border-radius: 5px; background-color: #f2f4f9; }
.box-guide dt{ margin-bottom: 15px; font-weight: 700; }
.box-guide dd{ line-height: 19px; text-indent: -9px; margin-left: 9px; }
.box-guide dd span{ color: #4e81ff; }


/* 항공 스케줄 해외 공통*/
.info-booking .plan{ display: none; position: relative; padding:0 22px ; }
.info-booking .plan dt{position: relative; overflow: hidden; padding: 9px; margin-bottom: 20px; background-color: #f2f4f9; }
.info-booking .plan .type{ float: left; width: 55px; line-height: 28px; border-radius: 2px; background-color: #3f4e73; font-size: 12px; font-weight: 500; color: #fff; text-align: center;}
.info-booking .plan div.city{ float: left; margin: 4px 0 0 15px; font-size: 15px; font-weight: 500; line-height: 22px; font-weight: 500;}
.info-booking .plan div.city span{ float: left; }
.info-booking .plan div.city span.dep{ margin-right: 15px; }
.info-booking .plan div.city span.arr{ padding-left: 34px; background: url(../../../assets/mobile/images/cmm/icn_arrow_city_s.png) no-repeat 0 50%;}
.info-booking .plan div.time{ float: right; margin:5px 0 0 15px; font-size: 13px; line-height: 19px; color: #000;  }
.info-booking .plan .boarding{ position: relative; height: 10px; }
.info-booking .plan .boarding span{ position: absolute; top: -25px; left: 27px; font-size: 12px; line-height: 18px; color: #4b75bf; }
.info-booking .plan .plane{ overflow: hidden; margin-bottom: 28px; padding-left: 4px;}
.info-booking .plan .plane span{ float: left; position: relative; padding:0 8px; font-size: 13px; line-height: 19px; color: #293036;}
.info-booking .plan .plane span::before{content: ''; position: absolute; top: 50%; left: 0; width: 1px; height: 14px; margin-top: -7px; background-color: #babdc3; }
.info-booking .plan .plane span:first-child{ padding-left: 0; }
.info-booking .plan .plane span:first-child img{ margin-right: 4px; }
.info-booking .plan .plane span:first-child::before{ display: none; }
.info-booking .plan .schdule{ position: relative; line-height: 22px; }
.info-booking .plan .schdule:last-child{ margin-bottom: 29px; }
.info-booking .plan .schdule .date{ float: left; width: 136px; text-align: right; }
.info-booking .plan .schdule .time{ float: left; width: 82px; text-align: right;}
.info-booking .plan .schdule .sign{ float: left; width: 55px; height: 22px;}
.info-booking .plan .schdule .sign::before{ content: ''; display: block; width: 13px; height: 13px; border-radius: 100%;  margin: 3px auto 0; border: 1px solid #9da9be; }
.info-booking .plan .schdule .sign.point::before{  background-color: #9da9be; }
.info-booking .plan .schdule .sign.ing::before{   background-color: #fff; }
.info-booking .plan .schdule .city{float: left; width: 450px;}
.info-booking .plan .schdule .city .en{ float: left; margin-right: 15px; font-size: 15px; font-weight: bold; color: #282d5c; }
.info-booking .plan .schdule .city .kr{ float: left; color: #293036;}
.info-booking .plan .schdule .time-plan{position: relative; margin-left: 245px; padding:7px 0 7px 27px;  font-size: 12px; line-height: 19px; color: #9da9be;}
.info-booking .plan .schdule .time-plan::before{content: ''; position: absolute; top: -4px; left: 0; bottom: -3px; width: 1px; background-color: #9da9be; }
.info-booking .plan .schdule .overstop{position: relative; margin:8px 0 20px 244px; padding:9px 0 9px 27px;  font-size: 12px; line-height: 18px; color: #49b999;}
.info-booking .plan .schdule .overstop::before{content: ''; position: absolute; top: -4px; left: 0; bottom: -4px; width: 4px; background: url(../../../assets/mobile/images/cmm/dot_line_ready.gif) repeat-y 0 0;}
.info-booking .plan .schdule .etc{ position: absolute; top: -30px; right: 12px; font-size: 13px; line-height: 19px; text-align: right; color: #757f92;}
.info-booking .plan .schdule .etc strong{ font-weight: 700; color: #3b7ff3; }





.btns-cmm{ display: inline-block; border: 1px solid #4e81ff; font-size: 15px; line-height: 48px; font-weight: 500; text-align: center; color: #fff; outline: none; cursor: pointer;}
.btns-cmm.color-b{ background-color: #4e81ff; color: #fff; }
.btns-cmm.color-w{ background-color: #fff; color: #4e81ff; }
.btns-toggle{ display: inline-block; position: relative; }
.btns-toggle input{ width: 0; height: 0; font-size: 0; padding: 0; margin: 0; border: 0 none; opacity: 0; visibility: hidden; }
.btns-toggle label{ display: inline-block; }
.btns-toggle i{ display: inline-block; position: relative; width: 50px; height: 30px; border-radius: 15px; background-color: #ebeef3; }
.btns-toggle i::after{ content: ''; position: absolute; top: 2px; left: 2px; width: 26px; height: 26px; border-radius: 100%; background-color: #fff;}
.btns-toggle input:checked ~ i{ background-color: #4e81ff; }
.btns-toggle input:checked ~ i::after{ right: 2px; left: initial; }

.round-basic{ border-radius: 26px; }
.round-sm{ border-radius: 4px; }

.w-100{ width: 100%; }
.w-90{ width: 90%; }
.w-75{ width: 75%; }
.w-50{ width: 50%; }
.w-49{ width: 49%; }
.w-48{ width: 48%; }

.side-padding{ padding-left: 16px; padding-right: 16px; }

/* DT-12908 비활성화 & 버튼두개 */
.btns-cmm.disabled, .btns-cmm:disabled{ background-color: #fff !important; color: #9da9be !important; border-color: #9da9be !important; }
.btn-double{ overflow: hidden; }
.btn-double .btns-cmm:first-child{ float: left; width: 49%; }
.btn-double .btns-cmm:last-child{ float: right; width: 49%; }
/* // DT-12908 */

/**/
.resent-search-sld{ position: relative; margin-bottom: 30px;}
.resent-search-sld .tit-size01{ margin-bottom: 5px; }
.resent-search-sld .swiper-container{  }
.resent-search-sld .swiper-wrapper{padding: 12px 16px 16px 16px; box-sizing: border-box;}
.resent-search-sld .tit-size01{ margin-bottom: 17px; }
.resent-search-sld li{min-width: 219px; }
.resent-search-sld li:first-child{ margin-left: 0; }
.resent-search-sld li .label{ color: #4e81ff; border: 1px solid #4e81ff;}
/*.resent-search-sld li.round .label{ background-color: #ff9268; }
.resent-search-sld li.round .date{ color: #ff9268; }
/*.resent-search-sld li.oneway .label{ background-color: #8184e4; }*/
/*.resent-search-sld li.oneway .date{ color: #8184e4; }*/
/*.resent-search-sld li.multi .label{ background-color: #61c7cb; }*/
/*.resent-search-sld li.multi .date{ color: #61c7cb; }*/
.resent-search-sld .item{ position: relative; display: block;  height: 84px; padding: 13px; border-radius: 5px;  box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2);   background-color: #ffffff;}
.resent-search-sld .label{ display: inline-block; width: 38px; line-height: 18px; text-align: center; font-size: 11px;  border-radius: 9px; color: #fff;}
.resent-search-sld .city{ display: block; margin: 6px 0 3px;  font-size: 14px; font-weight: 500; color: #000; overflow: hidden;}
.resent-search-sld .city .arrow{ display: inline-block; width: 15px; height: 8px; vertical-align: middle; background: url(../../../assets/mobile/images/cmm/icn_arrow_roundtrip.png) 0 0 no-repeat; -webkit-background-size: 15px 8px; background-size: 15px 8px;}
.resent-search-sld .date{ display: block; font-size: 12px; line-height: 18px; color: #9da9be;}
.resent-search-sld .etc{ display: block; font-size: 12px; line-height: 18px; color: #000;}
.resent-search-sld .none-item{position: relative; display: block; line-height: 84px; padding: 13px; border-radius: 5px; text-align: center; box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2); background-color: #ffffff; color: #c9cfd8; }


.pop-hidden{ overflow: hidden; }
.popup-fixed-bottom{ display: none; position: fixed; top: 0; left: 0; right: 0; height: 100%; background-color: rgba(0, 0, 0, 0.7); z-index: 100;}
.popup-fixed-bottom .modal-wrap{display: block; position: fixed; bottom: 24px; left: 16px; right: 16px; width: auto; padding: 20px; background-color: #fff; border-radius: 15px;}

.popup-fixed-bottom .last-destination .title{margin-bottom: 27px; text-align: center; font-size: 18px; line-height: 26px; color: #000; font-weight: 700; }
.popup-fixed-bottom .last-destination ul{ padding-bottom: 9px; }
.popup-fixed-bottom .last-destination li{ position: relative; overflow: hidden; margin-bottom: 21px; }
.popup-fixed-bottom .last-destination li label{ position: absolute; top: 0; left: 0;}
.popup-fixed-bottom .last-destination li label span{ padding-left: 32px; font-weight: 500; color: #000;}
.popup-fixed-bottom .last-destination li .desc{margin-left: 82px;line-height: 20px; font-weight: 300;  text-overflow: ellipsis; white-space: nowrap; overflow: hidden  }
.popup-fixed-bottom .last-destination .box-btn{ text-align: center; }
.popup-fixed-bottom .last-destination .tool-tip{ position: absolute; top: 24px; right: 24px; }
.popup-fixed-bottom .last-destination .tool-tip .btns-cmm{ width: 18px; height: 18px; background: url(../../../assets/mobile/images/cmm/icn_qustion_36.png) 0 0 no-repeat; -webkit-background-size: 18px 18px; background-size: 18px 18px; border:0 none; }
.popup-fixed-bottom .last-destination .tool-tip .box-cotn{display: none; position: absolute; bottom: 140%; right: -12px; width: 160px; min-height: 110px; padding: 16px; background-color: #f0f4ff; border: 1px solid #4e81ff; color: #4e81ff; font-size: 13px; border-radius: 4px;}
.popup-fixed-bottom .last-destination .tool-tip.active .box-cotn{ display: block; }
/*.cssarrow {position: relative;    background: #f0f4ff; border: 1px solid #4e81ff; }*/
.popup-fixed-bottom .last-destination .tool-tip.active .box-cotn:after,
.popup-fixed-bottom .last-destination .tool-tip.active .box-cotn:before {top: 100%; right: 0; border: solid transparent; content: " "; height: 0; width: 0; position: absolute; pointer-events: none; }
.popup-fixed-bottom .last-destination .tool-tip.active .box-cotn:after {border-color: rgba(136, 183, 213, 0); border-top-color: #f0f4ff; border-width: 8px; margin-right: 12px; margin-top: -1px;}
.popup-fixed-bottom .last-destination .tool-tip.active .box-cotn:before {border-color: rgba(194, 225, 245, 0); border-top-color: #4e81ff; border-width: 8px; margin-right: 12px; }



.layer-pages{ position: fixed; top: 0; left: 0; right: 0; height: 100%; background-color: #fff; z-index: 110; display: none;}
.layer-pages .layer-head{ position: absolute; top: 0; left: 0; width: 100%; border-bottom: 2px solid #ebeef3;background-color: #fff; z-index: 20;}
.layer-pages .layer-head .tit{padding: 0 16px; font-size: 16px; font-weight: 700; color: #000; line-height: 56px;  }
.layer-pages .layer-cotn{ position: relative; height: 90%; padding-top: 58px; box-sizing:border-box; overflow-y: auto;}
.layer-pages .box-btn-close{ position: absolute; top: 12px; right: 6px; z-index: 20;}
.layer-pages .box-btn-close .btns-cmm{ overflow: hidden; text-indent: -9999em; width: 34px; height: 34px; background: url(../../../assets/mobile/images/cmm/btn_close_page_b.png) 50% 50% no-repeat; -webkit-background-size: 14px; background-size: 14px; border: 0 none;}
.layer-pages .box-btn-close.type-arrow { top: 5px; left: 0; right: initial; }
.layer-pages .box-btn-close.type-arrow .btns-cmm{display: block; width: 46px; height: 46px; background: url(../../../assets/mobile/images/cmm/btn_page_back.png) no-repeat 50% 50%; -webkit-background-size: 9px 16px; 	background-size: 9px 16px;}
.layer-pages .btn-prev-page{position: absolute; top: 4px; left: 0; z-index: 20;}
.layer-pages .btn-prev-page .btns-cmm{ width: 46px; height: 46px; background: url(../../../assets/mobile/images/cmm/btn-prev-head.png) 50% 50% no-repeat; -webkit-background-size: 7px 14px; background-size: 7px 14px; border: 0 none;}
.layer-pages.type-bottom{ background-color: rgba(0, 0, 0, 0.7);}
.layer-pages.type-bottom .box-wrap{ position: fixed; bottom: 0; left: 0; right: 0; background-color: #fff; /*max-height: 83%;*/ border-radius: 12px 12px 0 0; overflow: hidden;
-webkit-transform: translateY(100%);
-ms-transform: translateY(100%);
-o-transform: translateY(100%);
transform: translateY(100%);
-webkit-transition-duration: 0.4s;
-moz-transition-duration: 0.4s;
-ms-transition-duration: 0.4s;
-o-transition-duration: 0.4s;
transition-duration: 0.4s;
-webkit-transition-delay: 0.1s;
-moz-transition-delay: 0.1s;
-ms-transition-delay: 0.1s;
-o-transition-delay: 0.1s;
transition-delay: 0.1s;
}
.layer-pages.type-bottom .box-wrap.active{
	-webkit-transform: translateY(0%);
-ms-transform: translateY(0%);
-o-transform: translateY(0%);
transform: translateY(0%);
}
.layer-pages.type-bottom .box-wrap .layer-cotn{
	max-height: calc(83vh);
	overflow-y:auto;
}
.layer-pages.type-bottom .layer-cotn{ padding-left: 16px; padding-right: 16px; padding-bottom: 30px;}

.layer-pages.special-price .desc{ padding-top: 17px; line-height: 19px; }
.layer-pages.note-info .desc{ padding-top: 17px; line-height: 19px; }

.layer-pages.select-day .sec-top{position: fixed; top: 58px; left: 0; right: 0; padding: 20px 0 0; overflow: hidden; background-color: #fff; z-index: 10;}
.layer-pages.select-day .sec-top .clearfix{margin-bottom: 26px; padding-left: 16px; padding-right: 16px; }
.layer-pages.select-day .sec-top .dep{ float: left; width: 50%; text-align: left;}
.layer-pages.select-day .sec-top .arr{ float: right; width: 50%; text-align: right;}
.layer-pages.select-day .sec-top .arr input{ text-align: right; }
.layer-pages.select-day .sec-top .city{ font-size: 13px; line-height: 19px; }
.layer-pages.select-day .sec-top .city span{ display: inline-block; margin-left: 5px; }
.layer-pages.select-day .sec-top input{ border: 0 none; outline: none; height: 24px; font-size: 16px; font-weight: 500;}
.layer-pages.select-day .sec-top input::placeholder{ color: #c9cfd8; }
.layer-pages.select-day .sec-top .week{ display: table; width: 100%; padding:0 16px 8px; border-bottom: 1px solid #ebeef3; box-sizing: border-box;}
.layer-pages.select-day .sec-top .week p{ display: table-cell; text-align: center; font-size: 13px;}
.layer-pages.select-day .sec-calendar{ position: relative; padding: 83px 16px 0 16px; }
.layer-pages.select-day .sec-calendar .date-input{ visibility: hidden; opacity: 0; width: 0; height: 0; border: 0; margin: 0; }
.layer-pages.select-day .sec-calendar .box-btn{ position: fixed; bottom:24px; left: 0; width: 100%; z-index: 10; text-align: center;}
.layer-pages.select-day.type-hotel .date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected:before{content: '체크인';}
.layer-pages.select-day.type-hotel .date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected:before{content: '체크아웃'; letter-spacing: -0.5px;}

/* 좌석 & 인원 선택 */
.layer-pages.saet-class .layer-cotn .tit{ font-size: 16px; line-height: 24px; font-weight: 500; }
.layer-pages.saet-class .box-btn{ position: fixed; bottom:24px; left: 0; width: 100%; z-index: 10; text-align: center;}
.layer-pages.saet-class .member{ position: relative; padding: 21px 16px;  border-bottom: 1px solid #ebeef3;}
.layer-pages.saet-class .member .tit{ margin-bottom: 12px; }
.layer-pages.saet-class .member dl{ float: left; }
.layer-pages.saet-class .member dt{ font-size: 14px; line-height: 20px; font-weight: 300; color: #000; padding-top: 8px;}
.layer-pages.saet-class .member dd{ font-size: 12px; line-height: 18px; color: #9da9be; }
.layer-pages.saet-class .member .clearfix{ margin-bottom: 5px; }
.layer-pages.saet-class .member .clearfix:last-child{ margin-bottom: 0; }
.layer-pages.saet-class .member .box-calc{ float: right; padding-top: 5px;}
.layer-pages.saet-class .member .box-calc input{ width: 50px; height: 28px; text-align: center; border: 0 none; vertical-align: top; font-size: 20px; font-weight: 500;}
.layer-pages.saet-class .member .box-calc .btn-calc{display: inline-block; vertical-align: top; width: 28px; height: 28px; -webkit-background-size:100% !important ; background-size:100% !important ;}
.layer-pages.saet-class .member .box-calc .btn-calc.minus{ background: url(../../../assets/mobile/images/cmm/btn_calc_minus_on.png) 0 0 no-repeat; }
.layer-pages.saet-class .member .box-calc .btn-calc.minus[disabled]{ background-image: url(../../../assets/mobile/images/cmm/btn_calc_minus_off.png); }
.layer-pages.saet-class .member .box-calc .btn-calc.plus{ background: url(../../../assets/mobile/images/cmm/btn_calc_plus_on.png) 0 0 no-repeat; }
.layer-pages.saet-class .member .box-calc .btn-calc.plus[disabled]{ background-image: url(../../../assets/mobile/images/cmm/btn_calc_plus_off.png); }
.layer-pages.saet-class .member .btn-delete-cell{ position: absolute; top: 23px; right: 16px; width: 36px; line-height: 20px; background-color: #f2f4f9; text-align: center; border-radius: 11px; font-size: 12px; font-weight: 500; color: #9da9be;}
.layer-pages.saet-class .member:first-child .btn-delete-cell{display: none;}
.layer-pages.saet-class .btn-add-room{ display: block; width: 106px; margin: 25px auto 0; font-size: 14px; letter-spacing: -0.3px; color: #4e81ff; line-height: 36px; text-indent: 20px; background: url(../../../assets/mobile/images/search/icn-plus-b-s.png) 16px 50% no-repeat;}
.layer-pages.saet-class .seat{ position: relative; padding: 19px 16px; }
.layer-pages.saet-class .seat .tit{ margin-bottom: 12px; }
.layer-pages.saet-class .seat .sel{
display: -webkit-flex;
 display: -moz-flex;
 display: -ms-flex;
 display: -o-flex;
 display: flex;
justify-content: center;
}
.layer-pages.saet-class .seat .sel img{ width: 100%; }
.layer-pages.saet-class .seat .sel .img .on{ display: none; }
.layer-pages.saet-class .seat .box-grade{ width: calc( 25% - 8px); margin-left: 8px;}
.layer-pages.saet-class .seat .box-grade:first-child{  margin-left: 0; }
.layer-pages.saet-class .seat .txt{display: block; padding-top: 10px; font-size: 13px; font-weight: 300; line-height: 16px; text-align: center; }
.layer-pages.saet-class .seat .txt .on{ display: none; }
.layer-pages.saet-class .seat input[type="radio"]{ visibility: hidden; font-size: 0; width: 0; height: 0; opacity: 0; }
.layer-pages.saet-class .seat input[type="radio"]:checked ~  .txt{ color: #4e81ff; font-weight: 500; }
.layer-pages.saet-class .seat input[type="radio"]:checked ~  .img .on{display: block;}
.layer-pages.saet-class .seat input[type="radio"]:checked ~  .img .off{display: none;}

/* 도시검색  */
.layer-pages.city-search{  }
.layer-pages.city-search .search-box{ width: calc(100% - 32px); height: 40px; padding: 0 10px 0 46px; margin: 20px 16px; border: 0 none; outline: none;  border-radius: 22px; background:#f2f6ff url(../../../assets/mobile/images/cmm/icn_search_city.png) 15px 50% no-repeat; -webkit-background-size: 18px; background-size: 18px; color: #7fa3ff;}
.layer-pages.city-search .search-box:valid ~ .box-default{ display: none; }
.layer-pages.city-search .search-box:valid ~ .self-position{ display: none; }
.layer-pages.city-search .search-box:valid ~ .box-result{ display: block; }
.layer-pages.city-search .self-position{ margin:0 16px 30px;  }
.layer-pages.city-search .self-position .btn-pin{ background-color: #4e81ff; display: block; padding-left: 19px; line-height: 20px; background: url(../../../assets/mobile/images/cmm/icn_location_pin.png) 0 50% no-repeat; -webkit-background-size: 13px 16px; background-size: 13px 16px; color: #4e81ff;}
.layer-pages.city-search .box-default{ position: relative; }
.layer-pages.city-search .box-default dt{ margin: 0 16px; line-height: 20px; font-weight: 500; color: #000;}
.layer-pages.city-search .box-default .resently{ padding-bottom: 15px; }
.layer-pages.city-search .box-default .resently dt{ margin-bottom: 10px; }
.layer-pages.city-search .box-default .resently dd{ margin: 0 16px; }
.layer-pages.city-search .box-default .resently button{ padding: 0 10px; margin-right: 3px; font-size: 13px; line-height: 26px; border-radius: 16px; outline: none; border: 1px solid #4e81ff; color: #4e81ff; }
.layer-pages.city-search .box-default .sec-list{}
.layer-pages.city-search .box-default .sec-list dl{border-top: 10px solid #f2f4f9;
display: -webkit-flex;
display: -moz-flex;
display: -ms-flex;
display: -o-flex;
display: flex;
-webkit-flex-wrap: wrap;
-moz-flex-wrap: wrap;
-ms-flex-wrap: wrap;
-o-flex-wrap: wrap;
flex-wrap: wrap;
}
.layer-pages.city-search .box-default .sec-list dl{ padding-bottom: 10px; }
.layer-pages.city-search .box-default .sec-list dt{ padding: 14px 0 10px ; width: 100%; }
.layer-pages.city-search .box-default .sec-list dd{position: relative; width: 33.3%; text-align: center; }
.layer-pages.city-search .box-default .sec-list dd::after{content: ''; position: absolute; top: 50%; left: 0; height: 15px; width: 1px; background-color: #ebeef3; margin-top: -7px;}
.layer-pages.city-search .box-default .sec-list dd:nth-of-type(3n +1 )::after{ display: none; }
.layer-pages.city-search .box-default .sec-list button{ width: 100%; line-height: 40px; }
.layer-pages.city-search .box-result{ display: none; height: calc(100vh - 140px); overflow-y: auto; }
.layer-pages.city-search .box-result ul{ padding: 10px 0 0; }
.layer-pages.city-search .box-result button{display: block; width: 100%; padding:7px 20px ; text-align: left;}
.layer-pages.city-search .box-result button span{color: #4e81ff;}
.layer-pages.city-search .box-result .city{ margin-bottom: 2px; line-height: 20px; }
.layer-pages.city-search .box-result .nation{ font-size: 12px; line-height: 18px; color: #9da9be;}

/* 필터 */
.layer-pages.filter .layer-head .tit{ text-align: center; }
.layer-pages.filter .layer-cotn{ padding-left: 16px; padding-right: 16px; padding-bottom: 30px; }
.layer-pages.filter .layer-cotn dl{ padding: 16px 0; border-bottom: 1px solid #ebeef3;}
.layer-pages.filter .layer-cotn dt{ margin-bottom: 18px; font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.filter .layer-cotn dd{ padding-bottom: 18px; }
.layer-pages.filter .box-aircraft{ position: relative; padding-bottom: 20px; border-bottom: 1px solid #ebeef3;}
.layer-pages.filter .box-aircraft dl{ border-bottom: 0 none; padding-bottom: 0;}
.layer-pages.filter .box-aircraft .btn-view-all{ display: inline-block; position: relative; padding-right: 18px; background: url(../../../assets/mobile/images/cmm/btn_dropdown_s_gray.png) 100% 50% no-repeat; -webkit-background-size: 10px 4px; background-size: 10px 4px; font-size: 12px; line-height: 18px; font-weight: 500; color: #9da9be;}
.layer-pages.filter .type-time{ position: relative; overflow: hidden;	 }
.layer-pages.filter .type-time .txt{ display: block; border: 1px solid #c9cfd8; font-size: 13px; line-height: 40px; text-align: center; background-color: #fff; color: #babdc3; }
.layer-pages.filter .type-time label{ float: left; width: 50%; margin-left: -1px;}
.layer-pages.filter .type-time input{ position: absolute; top: 0; left: 0; width: 0; height: 0; visibility: hidden; line-height: 0; font-size: 0; opacity: 0; }
.layer-pages.filter .type-time label:nth-child(1){ margin-left: 0; }
.layer-pages.filter .type-time label:nth-child(1) .txt{ border-radius: 5px 0 0 5px; }
.layer-pages.filter .type-time label:nth-child(2) .txt{ border-radius: 0 5px 5px 0 ; }
.layer-pages.filter .type-time input:checked ~ .txt{ position: relative; font-weight: 500; color: #4e81ff; border-color: #4e81ff; background-color: rgba(78, 129, 255, 0.07); }
.layer-pages.filter .type-condition{ position: relative; overflow: hidden; padding-bottom: 4px !important; }
.layer-pages.filter .type-condition .txt{ display: block; border: 1px solid #c9cfd8;  border-radius: 18px;  font-size: 13px; line-height: 36px; text-align: center; background-color: #fff; color: #babdc3; }
.layer-pages.filter .type-condition label{ float: left; width: 49%;}
.layer-pages.filter .type-condition label:first-child{ float: left; }
.layer-pages.filter .type-condition label:last-child{ float: right; }
.layer-pages.filter .type-condition input{ position: absolute; top: 0; left: 0; width: 0; height: 0; visibility: hidden; line-height: 0; font-size: 0; opacity: 0; }
.layer-pages.filter .type-condition input:checked ~ .txt{ position: relative; font-weight: 500; color: #fff; border-color: #4e81ff; background-color: #4e81ff; }
.layer-pages.filter .type-condition .select-cmm::before{ right: 20px; }
.layer-pages.filter .type-condition .select-cmm select{ height: 40px; padding: 0 20px; border-radius: 22px; border: 1px solid #e2e4e8; }
.layer-pages.filter .set-time .tit{ margin-bottom: 4px; font-size: 14px; font-weight: 500; }
.layer-pages.filter .set-time .val{ font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.filter .set-time:last-child .slider-range{ margin-bottom: 0;  }
.layer-pages.filter .slider-range{ margin: 20px 8px 5px; }
.layer-pages.filter .box-saerch{overflow: hidden;}
.layer-pages.filter .box-saerch input{ height: 40px; padding:0 15px 0 46px; border: 1px solid #e2e4e8; width: 100%; box-sizing: border-box; border-radius: 22px; background: url(../../../assets/mobile/images/search/icn-search.png) 15px 50% no-repeat;  -webkit-background-size: 18px 18px; background-size: 18px 18px; outline: none}
.layer-pages.filter .box-saerch input:focus{ border-color: #4e81ff; color: #4e81ff; }
.layer-pages.filter .box-saerch input::placeholder{ color: #c9cfd8; }
.layer-pages.filter .grade-star{ position: relative; display: -webkit-flex;
display: -moz-flex;
display: -ms-flex;
display: -o-flex;
display: flex;
justify-content: space-between;
}
.layer-pages.filter .grade-star label{ position: relative; width: 18%;}
.layer-pages.filter .grade-star input{ position: absolute; visibility: hidden;opacity: 0; width: 0; height: 0; padding: 0; margin: 0 }
.layer-pages.filter .grade-star span.star{ display: block; border: 1px solid #e2e4e8; border-radius: 5px; background-color: #fff; line-height: 40px; text-align: center; text-indent: -19px; background: url(../../../assets/mobile/images/search/icn-star-y.png) calc(50% + 9px) 50% no-repeat; -webkit-background-size: 15px 14px; background-size: 15px 14px;}
.layer-pages.filter .grade-star span.noStar{ display: block; border: 1px solid #e2e4e8; border-radius: 5px; background-color: #fff; line-height: 40px; text-align: center; text-indent: 0px; }
.layer-pages.filter .grade-star input:checked ~ span.star{ background-color: #4e81ff; color: #fff; background-image: url(../../../assets/mobile/images/search/icn-star-w.png);}
.layer-pages.filter .grade-star input:checked ~ span.noStar{ background-color: #4e81ff; color: #fff; text-indent: 0px; }
.layer-pages.filter .free-service{
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	justify-content: space-between;
	flex-flow: wrap;
}
.layer-pages.filter .free-service label{ width: 23%; }
.layer-pages.filter .free-service label:nth-child(1n + 5){ margin-top: 20px; }
.layer-pages.filter .free-service i{ position: relative; display: block;width: 66px; height: 66px;  background-color: #f2f4f9; border-radius: 10px; margin: 0 auto 5px;}
.layer-pages.filter .free-service i::after{ content: ''; position: absolute; top: 50%; left: 50%; background: url(../../../assets/mobile/images/search/spr-ico-freeservice.png) 0 0 no-repeat; -webkit-background-size: 32px 492px ; background-size: 32px 492px; width: 32px; height: 32px; margin-top: -16px; margin-left: -16px;}
.layer-pages.filter .free-service input{ position: absolute; visibility: hidden; width: 0; height: 0; opacity: 0; padding: 0; margin: 0; }
.layer-pages.filter .free-service span{ display: block; font-size: 12px; letter-spacing: -0.3px; text-align: center;}
.layer-pages.filter .free-service input:checked ~ i{background-color: #4e81ff;}
.layer-pages.filter .free-service .wifi i::after{ height: 23px; margin-top: -11.5px; background-position: 0 0; }
.layer-pages.filter .free-service .pool i::after{ height: 32px; margin-top: -16px; background-position: 0 -108px; }
.layer-pages.filter .free-service .coast i::after{ height: 32px; margin-top: -16px; background-position: 0 -234px; }
.layer-pages.filter .free-service .parking i::after{  height: 32px; margin-top: -16px; background-position: 0 -298px; }
.layer-pages.filter .free-service .language i::after{ height: 33px; margin-top: -16.5px; background-position: 0 -426px; }
.layer-pages.filter .free-service .baby i::after{ height: 30px; margin-top: -15px; background-position: 0 -48px; }
.layer-pages.filter .free-service .hour24 i::after{ height: 32px; margin-top: -16px; background-position: 0 -170px; }
.layer-pages.filter .free-service .restaurant i::after{ width: 22px; height: 32px; margin-top: -16px; margin-left: -11px; background-position: 0 -362px; }
.layer-pages.filter .free-service .wifi input:checked ~ i::after{  background-position: 0 -24px; }
.layer-pages.filter .free-service .pool input:checked ~ i::after{  background-position: 0 -139px; }
.layer-pages.filter .free-service .coast input:checked ~ i::after{  background-position: 0 -266px; }
.layer-pages.filter .free-service .parking input:checked ~ i::after{  background-position: 0 -330px; }
.layer-pages.filter .free-service .language input:checked ~ i::after{  background-position: 0 -460px; }
.layer-pages.filter .free-service .baby input:checked ~ i::after{  background-position: 0 -78px; }
.layer-pages.filter .free-service .hour24 input:checked ~ i::after{  background-position: 0 -202px; }
.layer-pages.filter .free-service .restaurant input:checked ~ i::after{ background-position: 0 -394px; }

.layer-pages.filter .ui-widget.ui-widget-content{ border: 0 none; height: 3px; background-color: #c9cfd8; border-radius: 3px; }
.layer-pages.filter .ui-slider-horizontal .ui-slider-range{ background-color: #4e81ff; }
.layer-pages.filter .ui-slider .ui-slider-handle{ top: -9px; width: 18px; height: 18px; border: 1px solid #4e81ff; border-radius: 100%; background-color: #fff; box-shadow: 0 2px 4px 0 rgba(157, 169, 190, 0.2);}
.layer-pages.filter .price-range .range-val{ padding-top: 9px; overflow: hidden; font-size: 13px; line-height: 19px;}
.layer-pages.filter .price-range .range-val .min{ float: left; }
.layer-pages.filter .price-range .range-val .max{ float: right; }
/*.layer-pages.filter .type-condition*/
.layer-pages.filter .box-btn{ padding-top: 30px; text-align: center;}
.layer-pages.filter .btn-reset{ position: fixed; top: 15px; left: 16px; z-index: 30;}
.layer-pages.filter .btn-reset .btns-cmm{ width: 55px; line-height: 24px; text-align: center; font-size: 12px; color: #4e81ff; border-radius: 16px;}

/* 검색 결과 상세보기 */
.layer-pages.detail-info{}
.layer-pages.detail-info .layer-cotn{ padding-top: 104px; }
.layer-pages.detail-info .tab-cotn{ background-color: #f2f4f9; }
.layer-pages.detail-info .box-tab{ display: none; position: relative; padding-bottom: 88px; margin-top: 9px;}
.layer-pages.detail-info .box-tab.active{ display: block; }
.layer-pages.detail-info .fare-condition{ background-color: #fff; padding-left: 16px; padding-right: 16px; padding-top: 20px; }
.layer-pages.detail-info .trip-rule{ position: relative; background-color: #fff; padding-top: 10px; padding-bottom: 150px; padding-left: 16px; padding-right: 16px;  }
.layer-pages.detail-info .trip-rule .result{ position: relative; margin: 0 0 21px; padding: 10px 14px 10px 40px; font-size: 14px; font-weight: 500; letter-spacing: -0.5px; line-height: 18px; border-radius: 5px;}
.layer-pages.detail-info .trip-rule .result::after{ content: ''; position: absolute; top: 50%; left: 10px; margin-top: -10px; width: 20px; height: 20px; -webkit-background-size: 20px !important;
background-size: 20px !important;}
.layer-pages.detail-info .trip-rule .result.success{ background-color: #e5f8f3; color: #49b999;}
.layer-pages.detail-info .trip-rule .result.success::after{ background: url(../../../assets/mobile/images/cmm/icn_inpolicy.png) 0 0 no-repeat;}
.layer-pages.detail-info .trip-rule .result.fail{ background-color: #fff2f3;  color: #ff4e50;}
.layer-pages.detail-info .trip-rule .result.fail::after{ background: url(../../../assets/mobile/images/cmm/icn_outpolicy.png) 0 0 no-repeat; }
.layer-pages.detail-info .desc{ margin-bottom: 20px; font-size: 15px; text-align: center; line-height: 20px; color: #293036; }
.layer-pages.detail-info .box-info{ padding: 20px 16px; border: 1px solid #e2e4e8; border-radius: 5px; }
.layer-pages.detail-info .box-info .tit{ margin-bottom: 20px; text-align: center; color: #757f92; letter-spacing: -1px;}
.layer-pages.detail-info .box-info .tit span{ color: #4e81ff; }
.layer-pages.detail-info .box-info li{ position: relative; padding-left: 18px; }
.layer-pages.detail-info .box-info li::before{ content: ''; position: absolute; top: 4px; left: 0; width: 8px; height: 8px;  border: 1px solid #000; border-radius: 100%; }
.layer-pages.detail-info .box-info li.success::before{ border-color: #49b999; background-color: #49b999;}
.layer-pages.detail-info .box-info li.fail::before{ border-color: #ff4e50; background-color: #ff4e50;}
.layer-pages.detail-info .box-info li.default::before{ border-color: #babdc3; background-color: #fff;}
.layer-pages.detail-info .box-info li.etc::before{ border-color: #e2e4e8; background-color: #e2e4e8;}
.layer-pages.detail-info .box-info li:last-child dl{ margin-bottom: 0; }
.layer-pages.detail-info .box-info dl{ margin-bottom: 15px;
display: -webkit-flex;
display: -moz-flex;
display: -ms-flex;
display: -o-flex;
display: flex; }
.layer-pages.detail-info .box-info dt{ min-width: 120px; font-size: 13px; font-weight: 700; color: #293036;}
.layer-pages.detail-info .box-info dd{ font-size: 13px; line-height: 19px; }
.layer-pages.detail-info.type-rule .layer-head .tit{ text-align: center; }
.layer-pages.detail-info.type-rule .layer-cotn{ margin-top: 0; padding-top: 58px ; padding-bottom: 30px; }
.layer-pages.detail-info.type-rule .tab-btm-fare a{ width: 95px; }

/* 탑승객 정보 확인 */
.layer-pages.passenger-confirm{  }
.layer-pages.passenger-confirm .layer-head{ border-bottom: 0 none; }
.layer-pages.passenger-confirm .desc{ margin-bottom: 25px; padding:0 16px;  font-size: 13px; line-height: 19px; color: #293036;}
.layer-pages.passenger-confirm .tbl{ margin:0 16px; }
.layer-pages.passenger-confirm .tbl table{ width: 100%; }
.layer-pages.passenger-confirm .tbl th{padding: 8px 0; border-bottom: 2px solid #ebeef3; text-align: center; font-weight: 14; font-weight: 500; color: #34446e; }
.layer-pages.passenger-confirm .tbl td{ border-bottom: 1px solid #ebeef3; padding: 15px 0; text-align: center; line-height: 20px;}
.layer-pages.passenger-confirm .tbl .left{ text-align: left; }
.layer-pages.passenger-confirm .bottom-btn{ position: fixed; bottom: 0; left: 0; width: calc( 100% - 32px); margin:0 16px; overflow: hidden; padding-bottom: 24px;	 }
.layer-pages.passenger-confirm .bottom-btn .btns-cmm{ float: left; width: 48%; }
.layer-pages.passenger-confirm .bottom-btn .btns-cmm:last-child{ float: right; }
.layer-pages.passenger-confirm .domestic .desc span{ display: block; color: #ff4e50;}
.layer-pages.passenger-confirm .domestic .box-dl{ padding: 0 16px; }
.layer-pages.passenger-confirm .domestic .box-dt{ margin-bottom: 10px; font-size: 15px; font-weight: 700; }
.layer-pages.passenger-confirm .domestic .box-dd { margin-bottom: 30px; }
.layer-pages.passenger-confirm .domestic .box-dd .tbl{margin: 0;}

/* 탑승객 검색 */
.layer-pages.passenger-search{ }
.layer-pages.passenger-search .layer-head{ border-bottom: 0 none; }
.layer-pages.passenger-search .layer-head .tit{ border-bottom: 1px solid #ebeef3; }
.layer-pages.passenger-search .layer-cotn { padding-top: 140px; padding-left: 12px; padding-right: 12px; background-color: #fff; }
.layer-pages.passenger-search .box-search{ padding: 16px;  }
.layer-pages.passenger-search .box-search input{height: 40px; padding:0 10px 0 46px; border-radius: 22px; background: #f2f6ff url(../../../assets/mobile/images/cmm/icn_search_city.png) no-repeat 15px 50%; -webkit-background-size: 18px; background-size: 18px; border: 0 none ; margin: 0; width: 100%; box-sizing: border-box; color: #4e81ff;}
.layer-pages.passenger-search .box-search input::placeholder{ color: #7fa3ff }
.layer-pages.passenger-search .box-list{ position: relative; }
.layer-pages.passenger-search .box-list li{ position: relative; margin-bottom: 10px; padding: 12px 12px 12px 79px; border-radius: 8px; background-color: #fff; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15);}
.layer-pages.passenger-search .box-list .profile{ position: absolute; top: 12px; left: 12px; width: 52px; height: 52px; background: url(../../../assets/mobile/images/cmm/ico_profile.png) 0 0 no-repeat; -webkit-background-size: 52px; background-size: 52px;}
.layer-pages.passenger-search .box-list .info{ padding-top: 3px; margin-bottom: 2px; line-height: 20px; font-weight: 300;}
.layer-pages.passenger-search .box-list .info > *{ padding-left: 3px; padding-right: 3px; }
.layer-pages.passenger-search .box-list .name{ display: inline-block; padding-left: 0; font-size: 16px; font-weight: 500; }
.layer-pages.passenger-search .box-list .code{ display: inline-block; padding-right: 5px;}
.layer-pages.passenger-search .box-list .position{ position: relative; display: inline-block; padding-left: 10px; }
.layer-pages.passenger-search .box-list .position::before{ content: ''; position: absolute; top: 50%; left: 0; height: 10px; margin-top: -5px; border-left: 1px solid #e2e4e8;}
.layer-pages.passenger-search .box-list .email{ margin-bottom: 8px;  line-height: 20px; }
.layer-pages.passenger-search .box-list .belong{ font-size: 12px; line-height: 18px; color: #757f92; font-weight: 300;}
.layer-pages.passenger-search .box-list.none p{ padding-top: 240px; text-align: center; background: url(../../../assets/mobile/images/cmm/bg_error.png) 50% 70px no-repeat; -webkit-background-size: 70px 150px; background-size: 70px 150px; color: #9da9be;}



/* 회원 정보 */
.layer-pages.edit-profile{}
.layer-pages.edit-profile .layer-head{ text-align: center; }
.layer-pages.edit-profile .layer-cotn{ padding-bottom: 110px; }
.layer-pages.edit-profile h2.title{ margin-bottom: 25px; font-size: 18px; font-weight: 500;line-height: 26px; }
.layer-pages.edit-profile .form-tit { margin-bottom: 5px; }
.layer-pages.edit-profile .box-info{ padding: 20px 16px; border-bottom: 12px solid #f2f4f9; }
.layer-pages.edit-profile .box-info.default{}
.layer-pages.edit-profile .box-info.default .set-agree{ position: relative; }
.layer-pages.edit-profile .box-info.default .set-agree dt{ margin-bottom: 6px; font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.edit-profile .box-info.default .set-agree dd{ border-bottom: 1px solid #ebeef3; }
.layer-pages.edit-profile .box-info.default .set-agree .txt{ padding-bottom: 13px; font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.edit-profile .box-info.default .set-agree .slide{ padding: 10px 0; overflow: hidden; }
.layer-pages.edit-profile .box-info.default .set-agree .slide .tit{ float: left; line-height: 30px; }
.layer-pages.edit-profile .box-info.default .set-agree .btns-toggle{ float: right; }
.layer-pages.edit-profile .box-info.company{}
.layer-pages.edit-profile .box-info.passport{ border-bottom: 0 none; }
.layer-pages.edit-profile .box-info *:last-child{ margin-bottom: 0; }

/* 미주 내 체류지 정보 */
.layer-pages.stay-usa{}
.layer-pages.stay-usa .layer-head{ text-align: center; }
.layer-pages.stay-usa .layer-cotn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.stay-usa .form-tit:first-child{ padding-top: 29px; }
.layer-pages.stay-usa .search-form{ position: relative; z-index: 10;}
.layer-pages.stay-usa .search-form .box-city-list{ display: none; position: absolute; top: 50px; left: 0; width: 100%; height:calc(100vh - 200px); background-color: #fff; overflow-y: auto;}
.layer-pages.stay-usa .search-form .box-city-list li{ }
.layer-pages.stay-usa .search-form .box-city-list li button{ outline: none; display: block; width: 100%; line-height: 40px; font-size: 15px; text-align: left;}
.layer-pages.stay-usa .search-form .box-city-list .point{ color: #4e81ff; }
.layer-pages.stay-usa .box-btn{ text-align: center; }

/* 취소/변경 요청 */
.layer-pages.cancel-request .layer-head{ text-align: center; }
.layer-pages.cancel-request .layer-cotn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.cancel-request .layer-cotn dl.desc{ padding: 24px 0 20px; }
.layer-pages.cancel-request .layer-cotn dl.desc dt{ margin-bottom: 12px; font-size: 15px; line-height: 22px; font-weight: 500; color: #4e81ff; }
.layer-pages.cancel-request .layer-cotn dl.desc dd{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.cancel-request .layer-cotn .passenger{ position: relative; padding-top: 20px; margin-bottom: 20px;}
.layer-pages.cancel-request .layer-cotn .passenger dt{ padding-bottom: 11px; border-bottom: 1px solid #ebeef3; font-size: 15px; font-weight: 700; }
.layer-pages.cancel-request .layer-cotn .passenger dd{ padding: 14px 0;border-bottom: 1px solid #ebeef3; font-weight: 300; line-height: 20px;}
.layer-pages.cancel-request .layer-cotn .passenger dd span{ display: inline-block; margin-left: 5px; }
.layer-pages.cancel-request .layer-cotn .box-textarea{ position: relative; margin-bottom: 87px;}
.layer-pages.cancel-request .layer-cotn .box-textarea textarea{ padding: 16px; width: 100%; -webkit-box-sizing: border-box;
-moz-box-sizing: border-box; box-sizing: border-box; border-color: #ebeef3; border-radius: 5px; height: 180px; }
.layer-pages.cancel-request .layer-cotn .box-textarea .desc{ padding-top: 20px; font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.cancel-request .layer-cotn .box-btn{ overflow: hidden; padding-bottom: 25px;}
.layer-pages.cancel-request .layer-cotn .box-btn .btns-cmm:first-child{ float: left; width: 49%; }
.layer-pages.cancel-request .layer-cotn .box-btn .btns-cmm:last-child{ float: right; width: 49%; }

.layer-pages.request-cmm .layer-head{ text-align: center; }
.layer-pages.request-cmm .layer-cotn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.request-cmm .layer-cotn dl.desc{ padding: 24px 0 20px; }
.layer-pages.request-cmm .layer-cotn dl.desc dt{ margin-bottom: 12px; font-size: 15px; line-height: 22px; font-weight: 500; color: #4e81ff; }
.layer-pages.request-cmm .layer-cotn dl.desc dd{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.request-cmm .layer-cotn .passenger{ position: relative; padding-top: 20px; margin-bottom: 20px;}
.layer-pages.request-cmm .layer-cotn .passenger dt{ padding-bottom: 11px; border-bottom: 1px solid #ebeef3; font-size: 15px; font-weight: 700; }
.layer-pages.request-cmm .layer-cotn .passenger dd{ padding: 14px 0;border-bottom: 1px solid #ebeef3; font-weight: 300; line-height: 20px;}
.layer-pages.request-cmm .layer-cotn .passenger dd span{ display: inline-block; margin-left: 5px; }
.layer-pages.request-cmm .layer-cotn .box-textarea{ position: relative; margin-bottom: 87px;}
.layer-pages.request-cmm .layer-cotn .box-textarea textarea{ padding: 16px; width: 100%; -webkit-box-sizing: border-box;
-moz-box-sizing: border-box; box-sizing: border-box; border-color: #ebeef3; border-radius: 5px; height: 180px; }
.layer-pages.request-cmm .layer-cotn .box-textarea .desc{ padding-top: 20px; font-size: 13px; line-height: 19px; color: #757f92; }
.layer-pages.request-cmm .layer-cotn .box-btn{ overflow: hidden; padding-bottom: 25px;}
.layer-pages.request-cmm .layer-cotn .box-btn .btns-cmm:first-child{ float: left; width: 49%; }
.layer-pages.request-cmm .layer-cotn .box-btn .btns-cmm:last-child{ float: right; width: 49%; }
.layer-pages.request-cmm .process{ padding: 24px 16px 20px;  margin: 0 -16px;  border-bottom: 12px solid #ebeef3;}
.layer-pages.request-cmm .process dt{ margin-bottom: 18px; font-size: 16px; font-weight: 500; line-height: 24px; }
.layer-pages.request-cmm .process dd{ margin-bottom: 10px; font-size: 13px; line-height: 19px; color: #ff4e50; }
.layer-pages.request-cmm .process .btns-rule{  display: block; width: 77px; border: 1px solid #757f92; border-radius: 4px; line-height: 29px; color: #757f92; text-indent: 8px; font-size: 12px; background: url(../../../assets/mobile/images/reserv/arrow-grey.png) 90% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px;}
.layer-pages.request-cmm .booking-info{ position: relative; padding: 20px 0;}
.layer-pages.request-cmm .booking-info .tit{ margin-bottom: 25px; font-size: 16px; font-weight: 500; line-height: 24px; }
.layer-pages.request-cmm .booking-info .ht-name{ margin-bottom: 6px; font-size: 18px; font-weight: 700; line-height: 27px; }
.layer-pages.request-cmm .booking-info .etc{ margin-bottom: 25px; font-size: 13px; font-weight: 300; color: #757f92; }
.layer-pages.request-cmm .booking-info .rm-name{ padding-bottom: 13px; border-bottom: 1px solid #ebeef3; font-size: 15px; font-weight: 500; }
.layer-pages.request-cmm .booking-info .room{ margin-bottom: 18px;}
.layer-pages.request-cmm .booking-info .room .box-in{overflow: hidden; border-bottom: 1px solid #ebeef3;}
.layer-pages.request-cmm .booking-info .room p{ font-size: 13px; font-weight: 500; line-height: 45px;}
.layer-pages.request-cmm .booking-info .room .num{ float: left; }
.layer-pages.request-cmm .booking-info .room .person{ float: right; }
.layer-pages.request-cmm .booking-info .box-textarea{ margin-bottom: 24px; }
.layer-pages.request-cmm .booking-info .box-textarea .desc{ padding-bottom: 20px; border-bottom: 1px solid #ebeef3; }
.layer-pages.request-cmm .booking-info .box-textarea:last-child .desc{ border-bottom: 0 none; }
.layer-pages.request-cmm .booking-info .box-agree{ padding-bottom: 15px; }
.layer-pages.request-cmm .booking-info .box-agree .desc{ padding-bottom: 16px; }
.layer-pages.request-cmm .booking-info .box-agree .form-chkbox span{ font-size: 14px; font-weight: 500; }

/* 1:1 문의 작성 */
.layer-pages.qna-write .layer-cotn{ padding-left: 16px; padding-right: 16px; padding-bottom: 40px; }
/*.layer-pages.qna-write .btn-prev-page{position: absolute; top: 4px; left: 0; z-index: 20;}*/
/*.layer-pages.qna-write .btn-prev-page .btns-cmm{ width: 46px; height: 46px; background: url(../../../assets/mobile/images/cmm/btn-prev-head.png) 50% 50% no-repeat; -webkit-background-size: 7px 14px; background-size: 7px 14px; border: 0 none;}*/
.layer-pages.qna-write .layer-head .tit{ text-align: center; }
.layer-pages.qna-write .custom-sel:first-child{ margin-top: 20px; }
.layer-pages.qna-write .custom-sel{ margin-bottom: 10px; border: 1px solid #ebeef3; border-radius: 5px;}
.layer-pages.qna-write .custom-sel .ui-selectmenu-button{ padding-top: 10px; padding-bottom: 10px; padding-left: 10px; }
.layer-pages.qna-write .custom-sel .ui-selectmenu-text{}
.layer-pages.qna-write .select-cmm{ margin-bottom: 10px; text-indent: 10px;}
.layer-pages.qna-write .select-cmm:first-child{ margin-top: 20px; }
.layer-pages.qna-write .select-cmm select{ border: 1px solid #ebeef3; text-indent: 10px; }
.layer-pages.qna-write .form-cn textarea{ display: block; width: 100%; height: 209px; padding: 10px; border: 1px solid #ebeef3; border-radius: 5px; box-sizing: border-box;}
.layer-pages.qna-write .form-cn{ margin-bottom: 10px; }
.layer-pages.qna-write .form-cn .input-cmm input{ padding-left: 10px; padding-right: 10px; border: 1px solid #ebeef3; border-radius: 5px; box-sizing: border-box;}
.layer-pages.qna-write .form-cn.file{ position: relative; margin-bottom: 40px;}
.layer-pages.qna-write .form-cn.file .clearfix{ position: relative; }
.layer-pages.qna-write .form-cn.file .desc{ float: left; padding-top: 10px; margin-right: 110px; font-size: 12px; line-height: 18px; color: #9da9be;}
.layer-pages.qna-write .form-cn.file .box-file-upload{ position: absolute; top: 10px; right: 0; width: 98px; height: 30px; border-radius: 21px; line-height: 30px; border: 1px solid #4e81ff; text-align: center; font-weight: 500; color: #4e81ff;}
.layer-pages.qna-write .form-cn.file .box-file-upload input{ visibility: hidden; width: 0; height: 0; padding: 0; opacity: 0 }
.layer-pages.qna-write .alram-feedback{ position: relative; margin-bottom: 40px;}
.layer-pages.qna-write .alram-feedback dl{ margin-bottom: 30px; }
.layer-pages.qna-write .alram-feedback dt{margin-bottom: 17px; font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.qna-write .alram-feedback dd{ margin-bottom: 22px; }
.layer-pages.qna-write .alram-feedback dd:last-child{ margin-bottom: 0; }
.layer-pages.qna-write .alram-feedback .form-chkbox span{ width: 65px; }
.layer-pages.qna-write .alram-feedback .val{ display: inline-block; }
.layer-pages.qna-write .alram-feedback .desc{padding: 12px 16px; border-radius: 5px; background-color: #f2f4f9; }
.layer-pages.qna-write .alram-feedback .desc p{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92;}
.layer-pages.qna-write .box-btn{  overflow: hidden;}
.layer-pages.qna-write .box-btn .btns-cmm{ float: left; width: 49%; }
.layer-pages.qna-write .box-btn .btns-cmm:last-child{ float: right; }

/* 1:1 문의 내역 */
.layer-pages.qna-list .layer-head{ text-align: center; }
.layer-pages.qna-list .layer-cotn{ position: relative; padding-left: 16px; padding-right: 16px; }
.layer-pages.qna-list .layer-cotn .desc{ padding-top: 25px; }
.layer-pages.qna-list .layer-cotn .desc dt{ margin-bottom: 6px; font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.qna-list .layer-cotn .desc dd{ margin-bottom: 24px; font-size: 13px; font-weight: 300; line-height: 19px; }
.layer-pages.qna-list .layer-cotn .box-btn{ margin-left: -16px; margin-right: -16px; text-align: center; padding-bottom: 18px; border-bottom: 1px solid #ebeef3;}
.layer-pages.qna-list .layer-cotn .sec-qna-list { padding-bottom: 50px; }
.layer-pages.qna-list .layer-cotn .sec-qna-list .title{ padding: 18px 0; font-size: 16px; font-weight: 500; line-height: 24px; border-bottom: 1px solid #ebeef3;}
.layer-pages.qna-list .layer-cotn .sec-qna-list .box-none{ padding: 50px 0; font-size: 14px; font-weight: 300; color: #babdc3; text-align: center; }

/* 자주 묻는 질문 */
.layer-pages.faq-list .layer-head{ text-align: center; }
.layer-pages.faq-list .box-search{ padding: 16px; border-bottom: 12px solid #ebeef3;  }
.layer-pages.faq-list .box-search input{height: 40px; padding:0 10px 0 46px; border-radius: 22px; background: #f2f6ff url(../../../assets/mobile/images/cmm/icn_search_city.png) no-repeat 15px 50%; -webkit-background-size: 18px; background-size: 18px; border: 0 none ; margin: 0; width: 100%; box-sizing: border-box;}
.layer-pages.faq-list .box-search input::placeholder{ color: #7fa3ff }
.layer-pages.faq-list .box-tab-item{ position: relative; padding: 15px 16px; }
.layer-pages.faq-list .box-tab-item ul{ overflow: hidden; border: 1px solid #e2e4e8; border-radius: 5px;}
.layer-pages.faq-list .box-tab-item li{ position: relative; float: left; width: 33.33%; height: 40px; text-align: center; border-left: 1px solid #e2e4e8; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}
.layer-pages.faq-list .box-tab-item li:nth-child( 3n + 1){ border-left: 0 none; }
.layer-pages.faq-list .box-tab-item li:nth-child( 1n + 4 ){ border-top: 1px solid #e2e4e8; }
.layer-pages.faq-list .box-tab-item label{ display: block; position: relative; line-height: 40px;}
.layer-pages.faq-list .box-tab-item input{ float: left; width: 0; height: 0; padding: 0; margin: 0; visibility: hidden; opacity: 0; }
.layer-pages.faq-list .box-tab-item .txt{ clear: both; position: absolute; top: 0; left: 0; width: 100%; text-align: center; font-weight: 300;}
.layer-pages.faq-list .box-tab-item input:checked ~ .txt{ background-color: #4e81ff; color: #fff; font-weight: 500;}
.layer-pages.faq-list .box-tab-sub{padding-bottom: 15px; white-space: nowrap;transform: translate3d(0,0,0); overflow-x: auto;}
.layer-pages.faq-list .box-tab-sub::-webkit-scrollbar {  display: none; }
.layer-pages.faq-list .box-tab-sub ul{ display: inline-block; padding: 0 16px;}
.layer-pages.faq-list .box-tab-sub li{ display: inline-block; }
.layer-pages.faq-list .box-tab-sub label{display: inline-block; line-height: 24px; border: 1px solid #4e81ff; border-radius: 16px; background-color: #fff; overflow: hidden; }
.layer-pages.faq-list .box-tab-sub input[type="radio"]{ visibility: hidden;width: 0; height: 0; outline: none; border: 0 none; opacity: 0; margin: 0; padding: 0; }
.layer-pages.faq-list .box-tab-sub .txt{ display: inline-block; padding: 0 12px;  font-size: 12px; color: #4e81ff; vertical-align: top;}
.layer-pages.faq-list .box-tab-sub input[type="radio"]:checked ~ .txt{ background-color: #4e81ff; color: #fff; font-weight: 500; }
.layer-pages.faq-list .box-accodi{ padding:0 16px 25px; }
.layer-pages.faq-list .box-accodi .box-item{ position: relative; border-bottom: 1px solid #ebeef3; }
.layer-pages.faq-list .box-accodi .box-item:first-child{border-top: 1px solid #ebeef3;}
.layer-pages.faq-list .box-accodi dt{ padding: 15px 35px 15px 0; font-weight: 300; line-height: 20px; }
.layer-pages.faq-list .box-accodi dd{ display: none; padding-bottom: 16px; padding-right: 35px; font-weight: 300; line-height: 20px; }
.layer-pages.faq-list .box-accodi .btn-arrow{position: absolute; top: 11px; right: 8px; width: 30px; height: 30px; background: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat;    -webkit-background-size: 10px 6px;     background-size: 10px 6px;}
.layer-pages.faq-list .box-accodi .box-item.active dt{ font-weight: 500; }
.layer-pages.faq-list .box-accodi .box-item.active dd{ display: block; }
.layer-pages.faq-list .box-accodi .box-item.active .btn-arrow{
	 -webkit-transform: rotateZ(180deg);
	 -ms-transform: rotateZ(180deg);
	 -o-transform: rotateZ(180deg);
	 transform: rotateZ(180deg);
}

/* 공지사항 */
.layer-pages.notice-list .layer-head{ text-align: center; }
.layer-pages.notice-list .box-accodi{ margin-top: 25px; padding:0 18px 25px; }
.layer-pages.notice-list .box-accodi .box-item{ position: relative; border-bottom: 1px solid #ebeef3; }
.layer-pages.notice-list .box-accodi .box-item:first-child{border-top: 1px solid #ebeef3;}
.layer-pages.notice-list .box-accodi dt{ padding: 15px 35px 15px 0; font-weight: 300; line-height: 20px; }
.layer-pages.notice-list .box-accodi dt .date{ display: block; font-size: 13px;line-height: 19px; color: #757f92; font-weight: 400; }
.layer-pages.notice-list .box-accodi dd{ display: none; padding-bottom: 16px; font-weight: 300; line-height: 20px; }
.layer-pages.notice-list .box-accodi dd .txt{ padding-right: 35px; }
.layer-pages.notice-list .box-accodi dd img{ margin-top: 15px; width: 100%; }
.layer-pages.notice-list .box-accodi .btn-arrow{position: absolute; top: 11px; right: 8px; width: 30px; height: 30px; background: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat;    -webkit-background-size: 10px 6px;     background-size: 10px 6px;}
.layer-pages.notice-list .box-accodi .box-item.active dt{ font-weight: 500; }
.layer-pages.notice-list .box-accodi .box-item.active dd{ display: block; }
.layer-pages.notice-list .box-accodi .box-item.active .btn-arrow{
	 -webkit-transform: rotateZ(180deg);
	 -ms-transform: rotateZ(180deg);
	 -o-transform: rotateZ(180deg);
	 transform: rotateZ(180deg);
}

/* 약관동의 */
.layer-pages.policy-agree { padding-left: 20px; padding-right: 20px; }
.layer-pages.policy-agree .title{ margin-bottom: 43px; font-size: 22px; line-height: 33px; font-weight: 500; letter-spacing: -1px; }
.layer-pages.policy-agree .box-agree{ position: relative; padding-bottom: 20px;}
.layer-pages.policy-agree .box-agree .all-agree{ padding-bottom: 20px; margin-bottom: 20px; border-bottom: 1px solid #ebeef3; line-height: 24px; font-size: 16px; font-weight: 500; }
.layer-pages.policy-agree .box-agree .all-agree .form-chkbox span::after{ top: 3px; }
.layer-pages.policy-agree .box-agree .box-cell{ position: relative; margin-bottom: 20px;}
.layer-pages.policy-agree .box-agree .box-cell dt{ margin-bottom: 11px; }
.layer-pages.policy-agree .box-agree .box-cell dd{ display: none; margin-left: 27px; margin-right: 10px;}
.layer-pages.policy-agree .box-agree .box-cell  .form-chkbox span::after{ top: 2px; }
.layer-pages.policy-agree .box-agree .btn-arrow {position: absolute; top: 0px; right: 0; width: 20px; height: 20px; background: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat;    -webkit-background-size: 10px 6px;     background-size: 10px 6px;}
.layer-pages.policy-agree .box-agree .box-cell.active dd{ display: block; }
.layer-pages.policy-agree .box-agree .box-cell.active .btn-arrow{
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg);
}
.layer-pages.policy-agree .box-btn{ text-align: center; padding-bottom: 25px;}

/* 법인단체 견적 문의 내역 */
.layer-pages.group-estimate{}
.layer-pages.group-estimate .layer-head .tit{ text-align: center; }
.layer-pages.group-estimate .cs-time{ padding: 24px 16px; }
.layer-pages.group-estimate .cs-time dt{ font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.group-estimate .cs-time dd{ font-size: 13px; font-weight: 300; line-height: 19px; }
.layer-pages.group-estimate .btn-request{ padding-bottom: 19px; border-bottom: 1px solid #ebeef3; }
.layer-pages.group-estimate .sec-list{ height: calc( 100vh - 217px); }
.layer-pages.group-estimate .sec-list.has-item{ background-color: #f2f4f9; padding-top: 10px;}
.layer-pages.group-estimate .sec-list .box-item{ position: relative; padding:0 16px 16px; margin-bottom: 10px; background-color: #fff; }
.layer-pages.group-estimate .sec-list .box-item .num{ padding: 14px 0 12px 25px; border-bottom: 1px solid #ebeef3;  line-height: 19px; background: url(../../../assets/mobile/images/cmm/icn_miceb.png) 0 50% no-repeat; -webkit-background-size: 18px 13px; background-size: 18px 13px; font-weight: 300;}
.layer-pages.group-estimate .sec-list .box-item .num span{ font-weight: 500; }
.layer-pages.group-estimate .sec-list .box-item .tit a{ display: inline-block; padding: 13px 0; font-size: 14px; font-weight: 500; }
.layer-pages.group-estimate .sec-list .box-item .etc{ position: relative; overflow: hidden; }
.layer-pages.group-estimate .sec-list .box-item .etc span{position: relative; float: left; padding: 0 8px; font-size: 12px; line-height: 18px; color: #757f92;}
.layer-pages.group-estimate .sec-list .box-item .etc span:first-child{ padding-left: 0; }
.layer-pages.group-estimate .sec-list .box-item .etc span::before{ content: ''; position: absolute; top: 50%; left: 0; border-left: 1px solid #e2e4e8; height: 10px; margin-top: -5px; }
.layer-pages.group-estimate .sec-list .box-item .status{ position: absolute; top: 14px; right: 16px; font-size: 14px; font-weight: 500; line-height: 20px;}
.layer-pages.group-estimate .sec-list .box-item .status.reserv{ color: #61c7cb }
.layer-pages.group-estimate .sec-list .box-item .status.end{ color: #757f92 }
.layer-pages.group-estimate .sec-list .box-item .status.cancel{ color: #babdc3 }
.layer-pages.group-estimate .sec-list .none-item{ padding-top: 267px; text-align: center; background: url(../../../assets/mobile/images/cmm/bg_error.png) 50% 100px no-repeat; -webkit-background-size: 70px 150px; background-size: 70px 150px; font-size: 14px; font-weight: 300;line-height: 20px; color: #9da9be;}
.layer-pages.group-estimate .sec-view{ position: relative; padding-bottom: 95px;}
.layer-pages.group-estimate .sec-view .box-item{ padding: 20px 16px 25px; border-top: 10px solid #f2f4f9; }
.layer-pages.group-estimate .sec-view .box-item:first-child{ border-top: 0 none; }
.layer-pages.group-estimate .sec-view .box-item .tit{ margin-bottom: 25px; font-size: 18px; line-height: 26px; font-weight: 500;  }
.layer-pages.group-estimate .sec-view .box-item dl{overflow: hidden; line-height: 22px; }
.layer-pages.group-estimate .sec-view .box-item dt{ float: left; width: 90px; font-size: 13px; color: #757f92;}
.layer-pages.group-estimate .sec-view .box-item dd{ float: left; margin-bottom: 10px; width: calc( 100vw - 122px ); font-size: 15px;}
.layer-pages.group-estimate .sec-view .box-item dd:last-child{ margin-bottom: 0; }
.layer-pages.group-estimate .sec-view .box-cotn{padding: 16px; margin-bottom: 25px;}
.layer-pages.group-estimate .sec-view .box-cotn dl{ padding:0 8px;  border-radius: 5px; border: 1px solid #e2e4e8;}
.layer-pages.group-estimate .sec-view .box-cotn dt{ padding: 14px 6px; border-bottom: 1px solid #ebeef3; font-size: 15px; line-height: 22px; font-weight: 500; }
.layer-pages.group-estimate .sec-view .box-cotn dd{ padding: 12px 6px 30px; font-size: 14px; line-height: 20px; }
.layer-pages.group-estimate .sec-view .box-btn{ position: fixed; bottom: 24px; left: 0; right: 0; z-index: 10;}
.layer-pages.group-estimate .search-auto{ position: relative;  z-index: 1;}
.layer-pages.group-estimate .search-auto input[type="search"]:valid ~ .box-nation{ display: block; }
.layer-pages.group-estimate .search-auto .box-nation{ display: none; position: absolute; top: 100%; left: 0; width: 100%; max-height: 300px; margin-top: 4px; box-sizing: border-box; overflow-y: auto; background-color: #fff; border: 1px solid #e2e4e8; border-radius: 5px;}
.layer-pages.group-estimate .search-auto .box-nation a{ display: block; padding: 0 14px; line-height: 46px; font-size: 14px; }
.layer-pages.group-estimate .search-auto .box-nation span{ color: #4e81ff; }
.layer-pages.group-estimate .sec-write .box-item{ position: relative; }
.layer-pages.group-estimate .sec-write .box-item .form-tit {  float: none; width: auto; margin-bottom: 10px;}
.layer-pages.group-estimate .sec-write .box-item .form-tit ~ dd{ float: none; width: auto; margin-bottom: 20px; }
.layer-pages.group-estimate .sec-write .add-property .box-chk{ overflow: hidden; padding-top: 5px; margin-bottom: 20px;}
.layer-pages.group-estimate .sec-write .add-property .box-chk .form-chkbox{ float: left; margin-left: 24px; line-height: 18px;}
.layer-pages.group-estimate .sec-write .add-property .box-chk .form-chkbox:first-child{ margin-left: 0; }
.layer-pages.group-estimate .sec-write .add-property .form-cn{ margin-bottom: 10px; }
.layer-pages.group-estimate .sec-write .add-property input[type=text]{ padding-left: 14px; padding-right: 14px; box-sizing: border-box; border: 1px solid #e2e4e8; border-radius: 5px; }
.layer-pages.group-estimate .sec-write .add-property textarea{ display: block; width: 100%; height: 209px; padding: 10px; border: 1px solid #ebeef3; border-radius: 5px; box-sizing: border-box;}
.layer-pages.group-estimate .sec-write .add-property .desc{margin-top: 25px; padding: 12px 16px; border-radius: 5px; background-color: #f2f4f9; }
.layer-pages.group-estimate .sec-write .add-property .desc p{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92;}
.layer-pages.group-estimate .sec-write .box-btn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.group-estimate .sec-write .box-btn .btns-cmm{ 	float: left; width: 49%; }
.layer-pages.group-estimate .sec-write .box-btn .btns-cmm:nth-child(2){ float: right; }
.layer-pages.group-estimate .sec-write .essential{ position: absolute; top: 22px; right: 16px;  font-size: 13px; line-height: 19px; color: #757f92;}
.layer-pages.group-estimate .sec-write .essential span{ color: #4e81ff; }

/* VISA 신청 내역 */
.layer-pages.visa-apply{}
.layer-pages.visa-apply .layer-head .tit{ text-align: center; }
.layer-pages.visa-apply .cs-time{ padding: 24px 16px; }
.layer-pages.visa-apply .cs-time dt{ font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.visa-apply .cs-time dd{ font-size: 13px; font-weight: 300; line-height: 19px; }
.layer-pages.visa-apply .btn-request{ padding-bottom: 19px; border-bottom: 1px solid #ebeef3; }
.layer-pages.visa-apply .sec-list{ height: calc( 100vh - 217px); }
.layer-pages.visa-apply .sec-list.has-item{ background-color: #f2f4f9; padding-top: 10px;}
.layer-pages.visa-apply .sec-list .box-item{ position: relative; padding:0 16px 16px; margin-bottom: 10px; background-color: #fff; }
.layer-pages.visa-apply .sec-list .box-item .num{ padding: 14px 0 12px 23px; border-bottom: 1px solid #ebeef3;  line-height: 19px; background: url(../../../assets/mobile/images/cmm/icn_visa_requestb.png) 0 50% no-repeat; -webkit-background-size: 14px 16px; background-size: 14px 16px; font-weight: 300;}
.layer-pages.visa-apply .sec-list .box-item .num span{ font-weight: 500; }
.layer-pages.visa-apply .sec-list .box-item .tit a{ display: inline-block; overflow: hidden; padding: 13px 0; font-size: 14px; font-weight: 500; }
.layer-pages.visa-apply .sec-list .box-item .tit span{ float: left; }
.layer-pages.visa-apply .sec-list .box-item .tit span.nation{ position: relative; padding-right: 7px; }
.layer-pages.visa-apply .sec-list .box-item .tit span.nation::after{ content: ''; position: absolute; top: 50%; right: 0; height: 14px; border-left: 1px solid #c9cfd8; margin-top: -7px;}
.layer-pages.visa-apply .sec-list .box-item .tit span.txt{ padding-left: 7px; }
.layer-pages.visa-apply .sec-list .box-item .date{ font-size: 12px; line-height: 18px; color: #757f92; }
.layer-pages.visa-apply .sec-list .box-item .status{ position: absolute; top: 14px; right: 16px; font-size: 14px; font-weight: 500; line-height: 20px;}
.layer-pages.visa-apply .sec-list .box-item .status.reserv{ color: #61c7cb }
.layer-pages.visa-apply .sec-list .box-item .status.complete{ color: #4e81ff }
.layer-pages.visa-apply .sec-list .none-item{ padding-top: 267px; text-align: center; background: url(../../../assets/mobile/images/cmm/bg_error.png) 50% 100px no-repeat; -webkit-background-size: 70px 150px; background-size: 70px 150px; font-size: 14px; font-weight: 300;line-height: 20px; color: #9da9be;}
.layer-pages.visa-apply .sec-view{ position: relative; padding-bottom: 95px;}
.layer-pages.visa-apply .sec-view .box-item{ padding: 20px 16px 25px; border-top: 10px solid #f2f4f9; }
.layer-pages.visa-apply .sec-view .box-item:first-child{ border-top: 0 none; }
.layer-pages.visa-apply .sec-view .box-item .tit{ margin-bottom: 25px; font-size: 18px; line-height: 26px; font-weight: 500;  }
.layer-pages.visa-apply .sec-view .box-item dl{overflow: hidden; line-height: 22px; }
.layer-pages.visa-apply .sec-view .box-item dt{ float: left; width: 90px; font-size: 13px; color: #757f92;}
.layer-pages.visa-apply .sec-view .box-item dd{ float: left; margin-bottom: 10px; width: calc( 100vw - 122px ); font-size: 15px;}
.layer-pages.visa-apply .sec-view .box-item dd:last-child{ margin-bottom: 0; }
.layer-pages.visa-apply .sec-view .box-btn{ position: fixed; bottom: 24px; left: 0; right: 0; z-index: 10;}
.layer-pages.visa-apply .sec-write .box-item{ position: relative; }
.layer-pages.visa-apply .sec-write .box-item .form-tit {  float: none; width: auto; margin-bottom: 10px;}
.layer-pages.visa-apply .sec-write .box-item .form-tit ~ dd{ float: none; width: auto; margin-bottom: 20px; }
.layer-pages.visa-apply .sec-write .add-property .desc{margin-top: 25px; padding: 12px 16px; border-radius: 5px; background-color: #f2f4f9; }
.layer-pages.visa-apply .sec-write .add-property .desc p{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92;}
.layer-pages.visa-apply .sec-write .box-btn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.visa-apply .sec-write .box-btn .btns-cmm{ 	float: left; width: 49%; }
.layer-pages.visa-apply .sec-write .box-btn .btns-cmm:nth-child(2){ float: right; }
.layer-pages.visa-apply .sec-write .essential{ position: absolute; top: 22px; right: 16px;  font-size: 13px; line-height: 19px; color: #757f92;}
.layer-pages.visa-apply .sec-write .essential span{ color: #4e81ff; }
.layer-pages.visa-apply .saerch-auto{ position: relative;  z-index: 1;}
.layer-pages.visa-apply .saerch-auto input[type="search"]:valid ~ .box-nation{ display: block; }
.layer-pages.visa-apply .saerch-auto .box-nation{ display: none; position: absolute; top: 100%; left: 0; width: 100%; max-height: 300px; margin-top: 4px; box-sizing: border-box; overflow-y: auto; background-color: #fff; border: 1px solid #e2e4e8; border-radius: 5px;}
.layer-pages.visa-apply .saerch-auto .box-nation a{ display: block; padding: 0 14px; line-height: 46px; font-size: 14px; }
.layer-pages.visa-apply .saerch-auto .box-nation span{ color: #4e81ff; }

/* 담당자 안내 */
.layer-pages.manager-info .layer-cotn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.manager-info .cs-call{ padding: 24px 0; }
.layer-pages.manager-info .cs-call dt{ margin-bottom: 6px; font-size: 15px; font-weight: 500; line-height: 22px; }
.layer-pages.manager-info .cs-call dd{ font-size: 13px; line-height: 19px; font-weight: 300; }
.layer-pages.manager-info .tit .box-ico{position: relative; display: inline-block; width: 30px; height: 22px; margin-right: 6px; background-image: url(../../../assets/mobile/images/cmm/spr_sidemenu.png); 	background-repeat: no-repeat; 	-webkit-background-size: 30px 328px;
	background-size: 30px 328px; text-indent: -999em;}
.layer-pages.manager-info .tit .box-ico.plan{ height: 26px; background-position: 0 -6px;}
.layer-pages.manager-info .tit .box-ico.hotel{background-position: 0 -114px;}
.layer-pages.manager-info .tit .box-ico.mice{height: 19px;background-position: 0 -135px;}
.layer-pages.manager-info .tit .box-ico.visa{background-position: 0 -309px;}
.layer-pages.manager-info .list-manager{ margin-bottom: 43px; }
.layer-pages.manager-info .list-manager .box-item{ margin-top: 30px; }
.layer-pages.manager-info .list-manager .box-item:first-child{ margin-top: 0; }
.layer-pages.manager-info .list-manager .tit{ margin-bottom: 9px; padding-bottom: 14px; border-bottom: 1px solid #ebeef3; font-size: 16px; font-weight: 500; line-height: 24px; }
.layer-pages.manager-info .list-manager .tit .type{ color: #4e81ff; }
.layer-pages.manager-info .list-manager dl{ margin-bottom: 36px; margin-bottom: 20px; border-bottom: 1px solid #ebeef3; padding-bottom: 5px;}
.layer-pages.manager-info .list-manager dt{ margin-bottom: 10px; font-size: 14px; font-weight: bold; }
.layer-pages.manager-info .list-manager dd{ margin-bottom: 5px; line-height: 20px; }
.layer-pages.manager-info .list-manager dd .box-ico{ display: inline-block; margin-right: 10px; width: 12px; text-indent: -999px; position: relative;  vertical-align: top;}
.layer-pages.manager-info .list-manager dd .box-ico.call{ top: 3px; background:  url(../../../assets/mobile/images/cmm/icn_tel_gray.png) 0 0 no-repeat; width: 14px; height: 15px; -webkit-background-size: 14px 15px; background-size: 14px 15px;}
.layer-pages.manager-info .list-manager dd .box-ico.email{ top: 7px; background:  url(../../../assets/mobile/images/cmm/icn_mail_gray.png) 0 0 no-repeat; width: 14px; height: 10px; -webkit-background-size: 13px 10px; background-size: 13px 10px;}
.layer-pages.manager-info .list-manager dd span{}
.layer-pages.manager-info .special-call{ padding-bottom: 20px; margin-bottom: 20px; border-bottom: 1px solid #ebeef3; }
.layer-pages.manager-info .special-call dt{margin-bottom: 12px; font-size: 15px; font-weight: 500; color: #757f92; }
.layer-pages.manager-info .special-call dd.num{ margin-bottom: 4px;font-size: 18px; line-height: 27px; font-weight: 500; color: #4e81ff; }
.layer-pages.manager-info .special-call dd.time{ font-size: 13px; line-height: 19px; }
.layer-pages.manager-info .special-call dd .box-ico{ width: 20px !important; top: 1px; width: 30px; height: 17px;	background-position: 0 -269px;}
.layer-pages.manager-info .send-email{ position: relative; padding-bottom: 40px;}
.layer-pages.manager-info .send-email dt{ font-size: 15px; font-weight: 500; color: #757f92; line-height: 22px; }
.layer-pages.manager-info .send-email .box-col{ overflow: hidden; }
.layer-pages.manager-info .send-email .btns-cmm { width: 140px; line-height: 40px; }
.layer-pages.manager-info .send-email .col-left{ line-height: 42px; }

/* 검색조건 수정 (재검색) */
.layer-pages.resaerch-edit{ background: rgba(0, 0, 0, 0.7); }
.layer-pages.resaerch-edit .layer-head { border-bottom: 0 none; }
.layer-pages.resaerch-edit .layer-cotn{ height: auto; background-color: #fff; padding-left: 20px; padding-right: 20px; padding-bottom: 0; border-radius: 0 0 20px 20px; box-shadow: 0 2px 10px 0 rgba(157, 169, 190, 0.25);}
.layer-pages.resaerch-edit .layer-cotn dl{ border-bottom: 1px solid #ebeef3; }
.layer-pages.resaerch-edit .layer-cotn dt{ padding-top: 17px; font-size: 13px; line-height: 19px; color: #4e81ff; }
.layer-pages.resaerch-edit .layer-cotn dd{ padding-bottom: 12px; font-size: 16px; line-height: 24px; }
.layer-pages.resaerch-edit .layer-cotn .clearfix dl{ width: 50%; float: left;}
.layer-pages.resaerch-edit .layer-cotn .clearfix dl:last-child{ text-align: right; }
.layer-pages.resaerch-edit .box-btn{ padding: 24px 0; text-align: center; }
.layer-pages.resaerch-edit .sec-day{ padding-bottom: 0; margin: 0; border-bottom: 0 none; }
.layer-pages.resaerch-edit .sec-day button{ float: none; }
.layer-pages.resaerch-edit .sec-day .val{ color: #000 }

/*  숙소 상세 정보 팝업 */
.layer-pages.rooms-detail .tit{ text-align: center; }
.layer-pages.rooms-detail .layer-cotn{ padding-left: 16px; padding-right: 16px; }
.layer-pages.rooms-detail .box-hotel-item{ margin-bottom: 20px; padding-top: 20px; }
.layer-pages.rooms-detail .box-hotel-item .name .en{ color: #000; }
.layer-pages.rooms-detail .box-btns{ position: relative;  overflow: hidden; margin-bottom: 10px;}
.layer-pages.rooms-detail .box-btns .btn-go-home{ float: left; width: 125px; margin-top: 8px; padding-left: 8px; font-size: 12px; box-sizing: border-box;  line-height: 22px; border: 1px solid #757f92; color: #757f92; border-radius: 4px; background: url(../../../assets/mobile/images/search/arrow_darkgray.png) 95% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px;}
.layer-pages.rooms-detail .box-btns .box-right{ float: right; }
.layer-pages.rooms-detail .box-btns .box-right .btns{ float: left;width: 38px; height: 38px; margin-left: 11px; background-color: #f2f6ff; background-repeat: no-repeat; background-position: 50%; border-radius: 50%; }
.layer-pages.rooms-detail .box-btns .btn-call{ background-image: url(../../../assets/mobile/images/search/icn_tel_blue_solid.png); -webkit-background-size: 14px 15px; background-size: 14px 15px; }
.layer-pages.rooms-detail .box-btns .btn-here{ background-image: url(../../../assets/mobile/images/search/icn_marker_blue_solid.png); -webkit-background-size: 14px 18px; background-size: 14px 18px; }
.layer-pages.rooms-detail .info-here{ margin-bottom: 36px; padding-left: 18px;  font-size: 12px; line-height: 18px; color: #757f92;  background: url(../../../assets/mobile/images/search/icn-marker-gray-solid.png) 0 50% no-repeat;
	-webkit-background-size: 10px 13px; 	background-size: 10px 13px;}
.layer-pages.rooms-detail .box-desc{ position: relative; margin-bottom: 30px; }
.layer-pages.rooms-detail .box-desc dt{ margin-bottom: 11px; font-size: 15px; font-weight: 700; line-height: 22px; }
.layer-pages.rooms-detail .box-desc dd{ line-height: 19px; }
.layer-pages.rooms-detail .box-desc .txt-more{ position: relative; }
.layer-pages.rooms-detail .box-desc .txt-more input{ position: absolute; top: 0;left: 0; visibility: hidden; opacity: 0; width: 0; height: 0; border: 0 none; padding: 0; margin: 0; }
.layer-pages.rooms-detail .box-desc .txt-more .txt{ margin-bottom: 10px;  overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 6; -webkit-box-orient: vertical; word-wrap:break-word; }
.layer-pages.rooms-detail .box-desc .txt-more label{ cursor: pointer; font-size: 12px; font-weight: 500; line-height: 18px; color: #4e81ff;}
.layer-pages.rooms-detail .box-desc .txt-more input:checked ~ .txt{ -webkit-line-clamp: initial; }
.layer-pages.rooms-detail .check-time{ position: relative; }
.layer-pages.rooms-detail .check-time .box-col{ margin-top: 10px; }
.layer-pages.rooms-detail .check-time .box-col,
.layer-pages.rooms-detail .check-time .box-col p{
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	justify-content: space-between;
}
.layer-pages.rooms-detail .check-time .box-col p{ width: 49%; padding:0 18px; border: 1px solid #c9cfd8; -webkit-box-sizing: border-box; box-sizing: border-box; border-radius: 5px; line-height: 48px;}
.layer-pages.rooms-detail .check-time .box-col span{font-weight: 500;}
.layer-pages.rooms-detail .check-time .box-col strong{ font-size: 16px; font-weight: 500; color: #4e81ff; }
.layer-pages.rooms-detail .box-desc.note dt{ font-size: 13px; }

/* 편의시설 팝업 */
.layer-pages.comport-option .layer-cotn{  padding-bottom: 36px;}
.layer-pages.comport-option .tit{ text-align: center; }
.layer-pages.comport-option .comport-list{ padding: 30px 0; }
.layer-pages.comport-option .comport-list li:nth-child(1n + 5){  margin-top: 30px; }
.layer-pages.comport-option .etc-list{ padding: 20px 16px; border-top: 1px solid #ebeef3; }
.layer-pages.comport-option .etc-list li{ margin-bottom: 14px; line-height: 20px; }
.layer-pages.comport-option .box-desc{ padding:0 16px; }
.layer-pages.comport-option .box-desc dt{ margin-bottom: 10px; font-size: 13px; line-height: 19px; font-weight: 700; }
.layer-pages.comport-option .box-desc dd{ line-height: 19px; }

/* 취소 및 예약규정 */
.layer-pages.cancel-reserv-rule .layer-head .tit{ text-align: center; }
.layer-pages.cancel-reserv-rule .layer-cotn .p-tit{ padding-left: 16px; padding-right: 16px; margin-bottom: 14px; font-size: 15px; line-height: 22px; font-weight: 700;}
.layer-pages.cancel-reserv-rule .layer-cotn .p-tit:first-child{ padding-top: 20px; }
.layer-pages.cancel-reserv-rule .layer-cotn .p-tit.cancel-desc { padding-left: 22px; background: url(../../../assets/mobile/images/cmm/icn-notice-red.png) 0 50% no-repeat; }
.layer-pages.cancel-reserv-rule .layer-cotn .p-tit.cancel-desc strong { color: #ff4e50; }
.layer-pages.cancel-reserv-rule .charge_tbl{ margin-bottom: 20px; padding-left: 16px; padding-right: 16px; }
.layer-pages.cancel-reserv-rule .charge_tbl table{ width: 100%; }
.layer-pages.cancel-reserv-rule .charge_tbl th{ height: 38px; border-bottom: 2px solid #ebeef3; }
.layer-pages.cancel-reserv-rule .charge_tbl td{ padding: 15px 0; border-bottom: 1px solid #ebeef3; line-height: 20px; text-align: center;}
.layer-pages.cancel-reserv-rule .rule-list{ padding-left: 16px; padding-right: 16px; padding-bottom: 10px;}
.layer-pages.cancel-reserv-rule .rule-list li{position: relative; margin-bottom: 20px; padding-left: 14px; line-height: 22px; font-weight: 300;}
.layer-pages.cancel-reserv-rule .rule-list li::before{ content: ''; position: absolute; top: 8px; left: 0; width: 5px; height: 5px; background-color: #000; }
.layer-pages.cancel-reserv-rule .rule-list li strong{ font-weight: 500; }
.layer-pages.cancel-reserv-rule hr.line{ margin-bottom: 20px; border: 0 none; height: 10px; background-color: #f2f4f9; }


/* DT-12908 SKYPASS 등록 추가로 인한 수정 */
/* 예약자 정보 수정 & SKYPASS 등록 공통 */
.layer-pages.subscriber-info .layer-head .tit,
.layer-pages.skypass-info .layer-head .tit{ text-align: center; }
.layer-pages.subscriber-info .layer-cotn dl,
.layer-pages.skypass-info .layer-cotn dl{ padding: 30px 16px 0; }
.layer-pages.subscriber-info dt,
.layer-pages.skypass-info dt{ font-size: 13px; color: #757f92; }
.layer-pages.subscriber-info dt .essential,
.layer-pages.skypass-info dt .essential{ display: inline-block; margin-left: 2px;color: #4e81ff;}
.layer-pages.subscriber-info .btns-cmm,
.layer-pages.skypass-info .btns-cmm{display: block; margin: 0 auto; }
/* 예약자 정보 수정 */
.layer-pages.subscriber-info .layer-cotn dl{ margin-bottom: 30px; }
/* SKYPASS 등록 */
.layer-pages.skypass-info .layer-cotn dl{ margin-bottom: 20px; }
.layer-pages.skypass-info .layer-cotn .desc-list{ position: relative; margin-bottom: 40px; padding: 0 16px; }
.layer-pages.skypass-info .layer-cotn .desc-list li{ margin-bottom: 15px; position: relative; padding-left: 13px; font-size: 13px; color: #000; }
.layer-pages.skypass-info .layer-cotn .desc-list li::after{ content: ''; position: absolute; top: 5px; left: 0; width: 5px; height: 5px; background-color: #000; border-radius: 1px;}
/* // DT-12908 */

/* 요청사항 */
.layer-pages.reserv-request{}
.layer-pages.reserv-request .layer-head .tit{ text-align: center; }
.layer-pages.reserv-request .layer-cotn { padding-left: 16px; padding-right: 16px;  padding-bottom: 30px;}
.layer-pages.reserv-request .layer-cotn .box-item hr.line{ border: 0 none; margin-left: -16px; margin-right: -16px; border-bottom: 12px solid #f2f4f9; }
.layer-pages.reserv-request .layer-cotn .box-item dl{ padding-bottom: 20px; }
.layer-pages.reserv-request .layer-cotn .box-item dt{ font-size: 15px; font-weight: 500; line-height: 22px; margin-bottom: 16px; }
.layer-pages.reserv-request .layer-cotn .box-item.bed{ padding-top: 20px; }
.layer-pages.reserv-request .layer-cotn .box-item.bed p{ margin-bottom: 8px; font-size: 13px; color: #757f92; }
.layer-pages.reserv-request .select-cmm select { border: 1px solid #c9cfd8; padding-left: 20px; border-radius: 22px; }
.layer-pages.reserv-request .layer-cotn .select-more li{ margin-bottom: 16px; }
.layer-pages.reserv-request .layer-cotn textarea{ width: 100%; height: 180px; padding: 16px; box-sizing: border-box; border: 1px solid #ebeef3; border-radius: 5px; }
.layer-pages.reserv-request .layer-cotn textarea:placeholder{ color: #babdc3; }
.layer-pages.reserv-request .select-cmm::before{ right: 20px; }
.layer-pages.reserv-request .btns-cmm{display: block; margin: 0 auto; }

.side-menu-wrap{ position: fixed; top: 0; left: 0; bottom: 0; width: 100%; z-index: 100; ;
    transform: translateX(-100%);
    transition: 0.3s;
    background-color: #fff;
 }
.side-menu-wrap.active {transform: translateX(0);}
.side-menu-wrap .sec-top{ position: absolute; top: 0; left: 0; right: 0; z-index: 10; padding: 59px 16px 18px; border-radius: 0 0 29px 0; background: url(../../../assets/mobile/images/cmm/bg_sidemenu_top.png) 0 0 no-repeat; -webkit-background-size: cover; background-size: cover;}
.side-menu-wrap .sec-top .name{ margin-bottom: 12px; font-size: 24px; line-height: 27px; color: #fff; letter-spacing: -1px;}
.side-menu-wrap .sec-top .belong{position: relative; margin-bottom: 30px; font-size: 13px; font-weight: 300; line-height: 16px; color: #fff; }
.side-menu-wrap .sec-top .belong span{position: relative; display: inline-block;padding-left: 9px; margin-left: 5px; }
.side-menu-wrap .sec-top .belong span::after{ content: ''; position: absolute; top: 50%; left: 0;  height: 10px; margin-top: -5px; border-left: 1px solid rgba(255, 255, 255, 0.3);}
.side-menu-wrap .sec-top .belong span:first-child{ padding-left: 0; margin-left: 0; }
.side-menu-wrap .sec-top .btn-bussiness-plan{ display: block; line-height: 46px; border: solid 1px #4e81ff; border-radius: 24px; font-size: 15px; font-weight: 500; text-align: center; color: #4e81ff; background-color: #fff;}
.side-menu-wrap .sec-top .btn-bussiness-plan .ico-sidemenu { display: inline-block; vertical-align: middle; position: relative; top: -2px; margin-right: 5px;}
.side-menu-wrap .sec-top .btn-member-info{ position: absolute; top: 58px; right: 16px; width: 72px; line-height: 26px; border: 1px solid #fff; border-radius: 14px; font-size: 12px; color: #fff; text-align: center;}

.side-menu-wrap .in-scroll{padding: 210px 0 41px; height: 100%; overflow-y: auto; -webkit-box-sizing: border-box;
-moz-box-sizing: border-box;
box-sizing: border-box; }
.side-menu-wrap .sec-mid{ position: relative; padding-top: 10px; }
.side-menu-wrap .sec-mid .box-item.active dd{ display: block; }
.side-menu-wrap .sec-mid .box-item{position: relative;}
.side-menu-wrap .sec-mid .box-item dl{ overflow: hidden; padding:0 16px 10px;
display: -webkit-flex;
display: -moz-flex;
display: -ms-flex;
display: -o-flex;
display: flex;
flex-wrap: wrap;
}
.side-menu-wrap .sec-mid .box-item dt{ min-width: 100%;  padding: 11px 0; color: #757f92; }
.side-menu-wrap .sec-mid .box-item dd{ position: relative; min-width: 50%;  }
.side-menu-wrap .sec-mid .box-item dd a{ padding: 10px 0;  font-size: 13px;  line-height: 28px; color: #000;
 display: -webkit-flex;
 display: -moz-flex;
 display: -ms-flex;
 display: -o-flex;
 display: flex;
 align-items: center;
}
.side-menu-wrap .sec-mid .box-item dd .ico-sidemenu { width: 40px; }
.side-menu-wrap .sec-mid .box-item .btn-cell{ position: absolute; top: 7px; right: 6px; padding: 10px; }
.side-menu-wrap .sec-mid .box-item.service dd{ min-width: 33.333%; position: relative; }
.side-menu-wrap .sec-mid .box-item.service a::after{content: ''; position: absolute; top: 50%; left: -10%; height: 18px; margin-top: -9px; border-left: 1px solid #ebeef3; }
.side-menu-wrap .sec-mid .box-item.service dd:nth-of-type(1) a::after{ display: none; }
.side-menu-wrap .sec-bottom{ position: relative;  margin:0 16px; border-top: 1px solid #ebeef3; border-bottom: 1px solid #ebeef3;  }
.side-menu-wrap .sec-bottom .cs-center{ padding: 20px 0 0; }
.side-menu-wrap .sec-bottom .cs-center .num{margin-bottom: 4px; font-size: 18px; line-height: 27px; font-weight: 700; color: #4e81ff; }
.side-menu-wrap .sec-bottom .cs-center .ico-sidemenu { display: inline-block; margin-right: 3px; position: relative; top: 2px; }
.side-menu-wrap .sec-bottom .cs-center .time{ padding-bottom: 25px; font-size: 13px; line-height: 19px; color: #757f92; }
.side-menu-wrap .sec-bottom .box-btn{ overflow: hidden; padding-bottom: 20px;}
.side-menu-wrap .sec-bottom .box-btn .btns-cmm { width: 49%; line-height: 38px;}
.side-menu-wrap .sec-bottom .box-btn .btns-cmm:nth-child(1){ float: left; }
.side-menu-wrap .sec-bottom .box-btn .btns-cmm:nth-child(2){ float: right; }
.side-menu-wrap .btn-logout{ margin: 25px 16px 0; font-size: 13px; line-height: 19px; color: #757f92; }
.side-menu-wrap .close-side{ position: absolute; top: 12px; left: 6px; overflow: hidden; width: 12px; height: 12px; padding: 10px; background: url(../../../assets/mobile/images/cmm/btn_close_side.png) 50% 50% no-repeat; -webkit-background-size: 14px; background-size: 14px; z-index: 10; text-indent: -999em; -webkit-box-sizing: content-box;
-moz-box-sizing: content-box;
box-sizing: content-box; }

/* 0823 스케줄 아이콘 추가 */
 .ico-schedule{
    background-image: url(../../../assets/mobile/images/cmm/icn_schedule.png);
    background-repeat: no-repeat;
    display: block;
    -webkit-background-size: 17px 20px;
    background-size: 17px 20px;
    width: 40px;
    height: 21px;
 }
/*-- 0823 스케줄 아이콘 추가 */

.ico-sidemenu  {
    background-image: url(../../../assets/mobile/images/cmm/spr_sidemenu.png);
    background-repeat: no-repeat;
    display: block;
    -webkit-background-size: 30px 328px;
    background-size: 30px 328px;
}


.ico-sidemenu.dropdown {
    width: 12px;
    height: 6px;
    background-position: 0 0;
}

.ico-sidemenu.abroad{
    width: 30px;
    height: 26px;
    background-position: 0 -6px;
}

.ico-sidemenu.booking_history{
    width: 16px;
    height: 19px;
    background-position: 0 -32px;
}

.ico-sidemenu.call{
    width: 27px;
    height: 22px;
    background-position: 0 -154px;
}

.ico-sidemenu.dome{
    width: 26px;
    height: 21px;
    background-position: 0 -71px;
}

.ico-sidemenu.faq{
    width: 22px;
    height: 22px;
    background-position: 0 -92px;
}

.ico-sidemenu.hotel{
    width: 22px;
    height: 22px;
    background-position: 0 -114px;
}

.ico-sidemenu.mice{
    width: 24px;
    height: 18px;
    background-position: 0 -136px;
}

.ico-sidemenu.mice_history{
    width: 27px;
    height: 22px;
    background-position: 0 -154px;
}

.ico-sidemenu.notice{
    width: 19px;
    height: 17px;
    background-position: 0 -176px;
}

.ico-sidemenu.qna{
    width: 22px;
    height: 22px;
    background-position: 0 -193px;
}

.ico-sidemenu.rentcar{
    width: 21px;
    height: 18px;
    background-position: 0 -215px;
}

.ico-sidemenu.schedule{
    width: 18px;
    height: 18px;
    background-position: 0 -233px;
}

.ico-sidemenu.schedule_copy{
    width: 18px;
    height: 18px;
    background-position: 0 -251px;
}

.ico-sidemenu.tel{
    width: 16px;
    height: 17px;
    background-position: 0 -269px;
}

.ico-sidemenu.visa_history{
    width: 21px;
    height: 23px;
    background-position: 0 -286px;
}

.ico-sidemenu.visa_request{
    width: 16px;
    height: 19px;
    background-position: 0 -309px;
}


.head-fixed{ height: 100%; }
.head-fixed .page-header { position: fixed;  top: 0; left: 0; width: 100%; z-index: 30;  }
/*.head-fixed #container{ height: 100%; }*/
/*.head-fixed .contents{ height: 100%; }*/
/*.head-fixed .saerch-result{ }*/

.half-col{ position: relative; }
.half-col::after{ content: ''; display: block; clear: both; }
.half-col .item-col {float: left; width: 50%; }


/* 여정 정보 */
.sec-schedule-info{ position: relative; }
.sec-schedule-info .plan{ position: relative; padding:0 16px; border-bottom: 1px solid #ebeef3; margin-bottom: 9px; background-color: #fff;}
.sec-schedule-info .plan:first-child{ margin-top: 9px; }
.sec-schedule-info .sec-top{ position: relative; overflow: hidden; padding: 17px 0 16px; line-height: 19px;}
.sec-schedule-info .sec-top .left-col{ float: left; }
.sec-schedule-info .sec-top .right-col{ float: right; }
.sec-schedule-info .sec-top .label{ float: left;  width: 44px; margin-right: 8px; border-radius: 16px; background-color: #34446e; color: #fff; font-size: 11px; text-align: center;}
.sec-schedule-info .sec-top .date{ float: left;}
.sec-schedule-info .sec-top .total-time{ float: left; padding-left: 21px; margin-right: 6px; font-size: 13px; color: #4e81ff; font-weight: 500; background: url(../../../assets/mobile/images/cmm/icn_clock.png) 0 50% no-repeat; -webkit-background-size: 15px ; background-size: 15px ;}
.sec-schedule-info .sec-top .status{ float: left; margin-right: 21px; }
.sec-schedule-info .sec-mid{ display: none; padding: 20px 0; border-top: 1px solid #ebeef3; }
.sec-schedule-info .sec-mid.active{ display: block; }
.sec-schedule-info .sec-mid .aircraft{ overflow: hidden; margin-bottom: 10px;}
.sec-schedule-info .sec-mid .aircraft img{ float: left; width: 30px; height: 20px; margin-right: 6px; }
.sec-schedule-info .sec-mid .aircraft .name{ float: left; line-height: 18px; }
.sec-schedule-info .sec-mid .aircraft .etc{ clear: both; margin-left: 38px; overflow: hidden; font-size: 12px; color: #757f92; line-height: 18px; }
.sec-schedule-info .sec-mid .aircraft .etc span{ position: relative; padding:0 8px; }
.sec-schedule-info .sec-mid .aircraft .etc span::after{ content: ''; position: absolute; top: 50%; left: 0; height: 12px; margin-top: -6px; border-left: 1px solid #babdc3; }
.sec-schedule-info .sec-mid .aircraft .etc span:first-child{ padding-left: 0; }
.sec-schedule-info .sec-mid .aircraft .etc span:first-child::after{ display: none; }
.sec-schedule-info .sec-mid .schedule{ margin:0 28px 10px 38px;  }
.sec-schedule-info .sec-mid .schedule .box-group{ position: relative; overflow: hidden; padding-top: 17px; line-height: 22px;}
.sec-schedule-info .sec-mid .schedule .box-group + .box-group{ margin-top: 15px; }
.sec-schedule-info .sec-mid .schedule .date{ position: absolute; top: 0; left: 0; font-size: 10px; line-height: 15px; color: #9da9be; }
.sec-schedule-info .sec-mid .schedule .time{ float: left; width: 57px;  font-size: 15px; font-weight: 700;}
.sec-schedule-info .sec-mid .schedule .sign{ float: left; height: 20px; }
.sec-schedule-info .sec-mid .schedule .sign::after{ content: ''; display: block; margin-top: 7px; width: 7px; height: 7px; border-radius: 100%; border: 1px solid #9da9be;}
.sec-schedule-info .sec-mid .schedule .sign.dep::after{background-color: #9da9be;}
.sec-schedule-info .sec-mid .schedule .sign.arr::after{ background-color: #fff; }
.sec-schedule-info .sec-mid .schedule .city{ float: left; margin-left: 10px; font-size: 15px; font-weight: 700;}
.sec-schedule-info .sec-mid .schedule .airport{ float: left; margin-left: 12px; }
.sec-schedule-info .sec-mid .schedule .box-etc{ position: relative; padding: 4px 0 4px 52px; margin-left: 61px; font-size: 12px; line-height: 18px; color: #757f92;}
.sec-schedule-info .sec-mid .schedule .box-etc::after{ content: ''; position: absolute; top: -6px; bottom: -27px;left: 0; width: 1px; background-color: #9da9be;}
.sec-schedule-info .sec-mid .overstop{padding: 0 0 0 16px; margin: 10px 0 15px 98px; font-size: 12px; line-height: 33px; color: #49b999; background: url(../../../assets/mobile/images/cmm/schedule_overstop.png) 0 0 no-repeat; -webkit-background-size: 3px 33px; background-size: 3px 33px; letter-spacing: -1px;}
.sec-schedule-info .box-btn{ position: absolute; top: 17px; right: 13px; }
.sec-schedule-info .box-btn .btns-cmm{display: block; width: 20px; height: 20px; border: 0 none; background: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat; -webkit-background-size: 10px 6px; background-size: 10px 6px;}
.sec-schedule-info .box-btn .btns-cmm.active{ -webkit-transform: rotate(180deg);
-ms-transform: rotate(180deg);
-o-transform: rotate(180deg);
transform: rotate(180deg); }

.sec-table-fare{ position: relative; }
.sec-tab-top{ position: relative; z-index: 1;}
.sec-tab-top::after{ content: ''; clear: both; display: block; }
.sec-tab-top li{ float: left; }
.sec-tab-top.n03 li{width: 33.33%; }
.sec-tab-top.n02 li{width: 50%; }
.sec-tab-top button{ position: relative; outline: none; display: block; width: 80%; margin: 0 auto; line-height: 46px;  font-size: 14px; color: #9da9be;}
.sec-tab-top button::after{ display: none; content: ''; position: absolute; bottom: -1px; left: 0; width: 100%;  height: 2px; background-color: #4e81ff}
.sec-tab-top .active button{ font-weight: 500; color: #4e81ff; }
.sec-tab-top .active button::after{ display: block; }


.sec-bottom-price{ position: fixed; bottom: 0; left: 0; z-index: 50; width: 100%; height: 88px; box-shadow: 0 -3px 9px 0 rgba(0, 0, 0, 0.06);  background-color: #ffffff; }
.sec-bottom-price .total-price{ float: left; padding: 16px 0 0 13px; }
.sec-bottom-price .total-price .tit{ font-size: 12px; line-height: 18px; }
.sec-bottom-price .total-price .fare{ font-size: 18px; line-height: 27px; font-weight: 700; color: #4e81ff; }
.sec-bottom-price .total-price .type{ font-size: 12px; line-height: 18px; color: #9da9be; }
.sec-bottom-price .right-col{ float:right; padding-top: 20px; width: 63%; text-align: center;}

.sec-bottom-btns{ position: fixed; bottom: 0; left: 0; z-index: 50; width: 100%; padding:30px 0; text-align: center; box-shadow: 0 -3px 9px 0 rgba(0, 0, 0, 0.06);  background-color: #ffffff; box-sizing: border-box;}
.sec-bottom-btns .btns-cmm{}

.contents .sec-result-sel{ background-color: #fff; display: block;}
.sec-result-sel{ display: none; position: relative; }
.sec-result-sel .box-group{ position: relative; padding: 17px 16px; border-bottom: 1px solid #ebeef3;}
.sec-result-sel .clearfix{ margin-top: 10px; }
.sec-result-sel .clearfix:first-child{ margin-top: 0px; }
.sec-result-sel .label{ float: left; width: 46px; margin-right: 8px; line-height: 19px; border-radius: 16px; background-color: #34446e; text-align: center; color: #fff; font-size: 11px;}
.sec-result-sel .date-person{ float: left; line-height: 19px;}
.sec-result-sel .time{ float: left; margin-right: 10px; font-size: 15px;line-height: 22px;font-weight: 500;}
.sec-result-sel .share{ float: left; line-height: 22px; font-size: 12px;}
.sec-result-sel .air-seat{ float: left; font-size: 12px; line-height: 18px; color: #757f92;}
.sec-result-sel .air-seat .aircraft{margin-right: 4px; margin-top: -3px; width: 30px; height: 20px; vertical-align: top; position: relative; top: 2px;}
.sec-result-sel .fare{ float: right; font-size: 12px; line-height: 18px;}
.sec-result-sel .fare strong{ font-size: 14px; }
.sec-result-sel .btn-change{ position: absolute; top: 12px; right: 17px; width: 50px; line-height: 25px; border-color: #34446e; border-radius: 3px; font-size: 13px; font-weight: 300; text-align: center; color: #34446e;}

.sec-bills-info{ position: relative; padding: 20px 16px 0; background-color: #fff;}
.sec-bills-info .title{ margin-bottom: 25px; font-size: 16px; line-height: 24px; font-weight: 500; }
.sec-bills-info .box-item{ padding-bottom: 5px; }
.sec-bills-info .clearfix{ padding-bottom: 7px; }
.sec-bills-info .tit{ margin-bottom: 10px; font-size: 15px; font-weight: bold; line-height: 22px; font-weight: 500; }
.sec-bills-info .tit span{ font-weight: 500; }
.sec-bills-info .tit strong{ font-weight: 700; }
.sec-bills-info .tit .days{ color: #9da9be; margin-left: 10px;}

.sec-bills-info .val,
.sec-bills-info .txt { float: left; width: 50%; font-size: 13px; line-height: 19px; color: #757f92; }
.sec-bills-info .val{ text-align: right; }
.sec-bills-info .total{ overflow: hidden; padding:16px 0; border-top: 1px solid #ebeef3;}
.sec-bills-info .total .txt{ font-size: 14px; line-height: 20px; color: #000; }
.sec-bills-info .total .val{ font-size: 16px; line-height: 20px; font-weight: 700; color: #4e81ff; }

/* 문의 답변 섹션*/
.sec-qna-list{ position: relative; }
.sec-qna-list .box-item{ position: relative; border-bottom: 1px solid #ebeef3;}
.sec-qna-list .box-top{ position: relative; padding: 16px 0; }
.sec-qna-list .box-top .status{ position: absolute; top: 16px; left: 0; width: 52px;font-size: 11px; line-height: 22px; border-radius: 11px; text-align: center;}
.sec-qna-list .box-top .status.ready{ background-color: #f2f4f9; color: #757f92; }
.sec-qna-list .box-top .status.complete{ background-color: rgba(78, 129, 255, 0.07); color: #4e81ff; }
.sec-qna-list .box-top .tit{ margin-left: 61px; margin-right: 90px; line-height: 22px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden }
.sec-qna-list .box-top .cotn{display: none; padding-top: 13px; font-size: 14px; font-weight: 300; line-height: 19px; }
.sec-qna-list .box-top .date{ position: absolute; top: 13px; right: 22px; margin-top: 6px; font-size: 12px; line-height: 18px; color: #9da9be; }
.sec-qna-list .box-bottom{ display: none; position: relative; margin-bottom: 12px;}
.sec-qna-list .box-bottom .label{ margin-bottom: 10px; }
.sec-qna-list .box-bottom .label span{ display: block; width: 34px; height: 20px; line-height: 20px; border: 1px solid #4e81ff; text-align: center;  font-size: 11px; line-height: 17px; color: #4e81ff; border-radius: 16px;}
.sec-qna-list .box-bottom .date{ position: absolute; top: 12px; right: 12px; font-size: 12px; line-height: 18px; color: #9da9be; }
.sec-qna-list .btn-arrow {position: absolute; top: 25px; right: 0; width: 10px; height: 6px; background: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png) 0 0 no-repeat;    -webkit-background-size: 10px 6px;     background-size: 10px 6px;}
.sec-qna-list .box-item.active .box-top .cotn{ display: block; }
.sec-qna-list .box-item.active .box-top .tit{ overflow: visible; white-space: normal; }
.sec-qna-list .box-item.active .box-bottom{ display: block; padding: 10px 12px; background-color: rgba(78, 129, 255, 0.07); border-radius: 5px;}
.sec-qna-list .box-item.active .btn-arrow {
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg);
}

.tab-btm-fare { overflow: hidden; margin-bottom: 14px; }
.tab-btm-fare li{ float: left; margin-right: 6px;}
.tab-btm-fare li.ui-tabs-active a{ background-color: #3e4d91; color: #fff; background-color: #fff;}
.tab-btm-fare li.active a{ background-color: #3e4d91; color: #fff;}
.tab-btm-fare a{ display: block; width: 86px; height: 32px; border: 1px solid #3e4d91; border-radius: 16px; text-align: center; font-size: 13px; line-height: 32px; font-weight: 500; color: #3e4d91; background-color: #fff;}
#popCompareBox .modal-cotn{ padding-top: 47px; padding-bottom: 30px; }
#popCompareBox .box-ico{ position: relative; margin: 0 auto 20px; width: 54px; height: 54px; background: url(../../../assets/mobile/images/cmm/icn_comparison_l.png) 0 0 no-repeat; -webkit-background-size: 54px 52px; background-size: 54px 52px;}
#popCompareBox .box-ico .num{ position: absolute; top: -7px; right: -7px; width: 32px; line-height: 32px; border-radius: 100%; font-size: 16px; font-weight: 700; text-align: center; color: #fff; background-color: #4e81ff; }
#popCompareBox .desc{ margin-bottom: 40px; font-size: 15px; line-height: 22px; text-align: center; }
#popCompareBox .desc span{ font-weight: 700; color: #4e81ff; }
#popCompareBox .box-btn{text-align: center;}
#popCompareBox .box-btn .btns-cmm{ margin-bottom: 6px; }

/* 예약 진행전 안내 팝업 */
#popReservNoti { padding: 0px; }
#popReservNoti .modal-cotn{ padding: 15px 10px; text-align: center;}
#popReservNoti .modal-cotn dt{ margin-bottom: 11px; font-size: 15px; font-weight: 400; line-height: 22px; }
#popReservNoti .modal-cotn dl{ margin-bottom: 11px; font-size: 15px; font-weight: 400; line-height: 22px; }
#popReservNoti .modal-cotn #noti-confirm-btn{ border: 1px solid #4e81ff; padding: 10px 25px; border-radius: 10px; color: #4e81ff; }

/* 예약 진행중 로딩 팝업*/
#popReservIng .modal-cotn{ padding-bottom: 30px; text-align: center;}
#popReservIng .modal-cotn .load-img{ margin: -66px 0 12px; }
#popReservIng .modal-cotn dt{ margin-bottom: 11px; font-size: 18px; font-weight: 500; line-height: 27px; }
#popReservIng .modal-cotn dd{ font-weight: 300; line-height: 20px; }

/* 결제 진행 팝업 */
#popPayIng { background-color: #4e81ff; border-radius: 10px; overflow: hidden; text-align: center;}
#popPayIng .modal-cotn{ padding-top: 26px; padding-bottom: 30px; }
#popPayIng .modal-cotn .load-img{ margin-bottom: 10px; }
#popPayIng .modal-cotn .tit{ margin-bottom: 12px; font-size: 16px; font-weight: 500; line-height: 22px;  color: #fff;}
#popPayIng .modal-cotn dt{ margin-bottom: 21px;  font-weight: 300; color: #fff;}
#popPayIng .modal-cotn dd{ font-size: 13px; line-height: 18px; color: #f5e179;}

/* 결제 시간 만료 */
#payTimeOver .modal-cotn{ padding-top: 40px; text-align: center;}
#payTimeOver .modal-cotn dt{ padding-top: 80px; margin-bottom: 12px; font-size: 16px; line-height: 22px; font-weight: 500; background: url(../../../assets/mobile/images/cmm/icn_notice.png) 50% 0 no-repeat; -webkit-background-size: 55px; background-size: 55px; }
#payTimeOver .modal-cotn dd{ margin-bottom: 30px;  font-weight: 300; }
#payTimeOver .modal-cotn .box-btn{ overflow: hidden; padding:0 26px 30px;}
#payTimeOver .modal-cotn .box-btn .btns-cmm{ width: 48%; }
#payTimeOver .modal-cotn .box-btn .btns-cmm:first-child{ float: left; }
#payTimeOver .modal-cotn .box-btn .btns-cmm:last-child{ float: right; }

/* 대기 좌석 안내 */
#popReadySeatGuide .modal-cotn{ text-align: center; padding: 30px 0;}
#popReadySeatGuide .modal-cotn dt{ font-size: 17px; line-height: 25px; font-weight: 500; }
#popReadySeatGuide .modal-cotn dd{ margin-bottom: 20px; line-height: 20px; font-weight: 300; }
#popReadySeatGuide .modal-cotn dd span{ color: #4e81ff; font-weight: 500; }
#popReadySeatGuide .modal-cotn .num{ margin-bottom: 30px; font-size: 15px; font-weight: 500; }
#popReadySeatGuide .modal-cotn .num .tel{ display: inline-block; vertical-align: middle;  margin-right: 3px;}

/* 영수증 / 인보이스 */
#receiptInvoicePop .modal-body{ max-height: calc(100vh - 117px); overflow-y: auto; }
#receiptInvoicePop .box-acoodi{ position: relative; padding: 16px;}
#receiptInvoicePop .box-acoodi .tit{text-align: left; font-size: 16px; font-weight: 500; line-height: 26px;}
#receiptInvoicePop .box-acoodi .tbl-cmm{ display: none; border-top: 0 none; border-bottom: 0 none;}
#receiptInvoicePop .box-acoodi .tbl-cmm th{ height: 36px; border-bottom: 2px solid #ebeef3; font-size: 14px; color: #34446e; font-weight: 500;}
#receiptInvoicePop .box-acoodi .tbl-cmm td{ height: 50px; padding: 0; border-top: 0 none; border-bottom: 1px solid #ebeef3;}
#receiptInvoicePop .box-acoodi .tbl-cmm td[colspan]{ height: auto; padding-top: 20px; padding-bottom: 20px; border-bottom: 0 none; font-size: 14px; font-weight: 300; color: #babdc3; }
#receiptInvoicePop .box-acoodi .tbl-cmm .btns-cmm{  padding-left: 14px; padding-right: 14px; font-size: 13px; line-height: 26px; border-radius: 14px; background-color: #4e81ff; font-weight: 300;}
#receiptInvoicePop .box-item{ position: relative; }
#receiptInvoicePop .box-item.receipt{ padding-bottom: 30px; margin-bottom: 16px;}
#receiptInvoicePop .box-item.receipt .tbl-cmm{ margin-top: 10px; }
#receiptInvoicePop .box-item.receipt::after{ content: ''; height: 10px; position: absolute; bottom: 0; left: -16px; right: -16px;background-color: #f2f4f9;}
#receiptInvoicePop .box-item.receipt.active{ padding-bottom: 40px; }
#receiptInvoicePop .box-item.active{ min-height: 200px; }
#receiptInvoicePop .box-item.active .tbl-cmm{ display: block; }
#receiptInvoicePop .box-item.active .btn-arrow{
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg);
}
#receiptInvoicePop .btn-arrow{ position: absolute; top: 6px; right: 0; width: 15px; height: 15px; background: url(../../../assets/mobile/images/cmm/btn_dropdown_l.png) 50% 50% no-repeat; -webkit-background-size: 13px 7px; background-size: 13px 7px;}

/*국내선 항공권 변경 불가 안내 팝업*/
#domesticDisablePop .desc{ -ms-text-align-last: auto;
text-align: left; font-weight: normal; font-size: 13px; line-height: 18px; padding: 16px; }
#domesticDisablePop .box-btn{ padding-top: 10px; }
#domesticDisablePop .btns-cmm{ width: 120px; border-radius: 21px; line-height: 40px; font-size: 14px;}

/* 세션완료 팝업 */
#roomSsesionEnd dl{ padding-top: 121px; text-align: center; background: url(../../../assets/mobile/images/search/icn_clock.png) 50% 40px no-repeat; -webkit-background-size: 57px 61px; background-size: 57px 61px;}
#roomSsesionEnd dt{margin-bottom: 12px; font-size: 17px; font-weight: 500; line-height: 25px; }
#roomSsesionEnd dd{ font-weight: 300; margin-bottom: 30px; }
#roomSsesionEnd .box-btn{ padding-bottom: 24px; text-align: center;}
#roomSsesionEnd .btns-cmm { width: 120px; line-height: 42px; border-radius: 21px;}


/* 취소규정안내 (무료취소기한이 지난 예약) */
#freeCancelClose .modal-cotn{ padding-bottom: 24px; }
#freeCancelClose dl{ text-align: center; padding: 20px 16px ;  text-align: left;}
#freeCancelClose dt{ margin-bottom: 16px; line-height: 20px; }
#freeCancelClose dt strong{ font-weight: bold; color: #4e81ff;}
#freeCancelClose dd{ position: relative; padding-left: 13px; font-size: 13px; margin-bottom: 19px; color: #757f92; }
#freeCancelClose dd::after{ content: ''; position: absolute; top: 6px; left: 0; width: 5px; height: 5px; background-color: #757f92;  }
#freeCancelClose .box-btn{ overflow: hidden; padding: 0 26px;}
#freeCancelClose .box-btn .btns-cmm{ width: 48%; }
#freeCancelClose .box-btn .btn-ing{ float: right; }
#freeCancelClose .box-btn .btn-cancel{ float: left; }

.box-hotel-item{display: block; position: relative;}
.box-hotel-item .name{ position: relative; }
.box-hotel-item .name .kr{ position: relative; font-size: 16px; line-height: 24px; font-weight: 500; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical}
.box-hotel-item .name .en{ margin-bottom: 4px; font-size: 12px; font-weight: 300; line-height: 18px; color: #9da9be;}
.box-hotel-item .grade{ position: relative; margin-bottom: 7px; }
.box-hotel-item .grade .tit{ display: inline-block; margin-right: 5px; vertical-align: middle; width: 50px; text-align: center; border-radius: 9px; background-color: #f2f6ff; font-size: 11px; color: #4e81ff;}
.box-hotel-item .grade .start { display: inline-block; vertical-align: middle;}
.box-hotel-item .grade .start img{ position: relative; top: 3px; width: 12px; height: 12px;  vertical-align: top;}
.box-hotel-item .price{ position: relative; line-height: 27px; text-align: right;}
.box-hotel-item .price .day{ font-size: 12px; color: #9da9be; }
.box-hotel-item .price .val{ display: inline-block; margin-left: 10px; font-size: 18px; font-weight: 700;  color: #4e81ff;}

/* 검색 결과 상세보기 */
.layer-pages.detail-info{}
.layer-pages.detail-info .layer-cotn{ padding-top: 104px; }
.layer-pages.detail-info .tab-cotn{ background-color: #f2f4f9; }
.layer-pages.detail-info .box-tab{ display: none; position: relative; padding-bottom: 88px; margin-top: 9px;}
.layer-pages.detail-info .box-tab.active{ display: block; }
.layer-pages.detail-info .fare-condition{ background-color: #fff; padding-left: 16px; padding-right: 16px; padding-top: 20px; }
.layer-pages.detail-info .desc{ margin-bottom: 20px; font-size: 15px; text-align: center; line-height: 20px; color: #293036; }
.layer-pages .trip-rule{ position: relative; background-color: #fff; padding-top: 20px; padding-bottom: 150px; padding-left: 16px; padding-right: 16px;  }
.layer-pages .trip-rule .result{ position: relative; margin: 0 0 21px; padding: 10px 14px 10px 40px; font-size: 14px; font-weight: 500; letter-spacing: -0.5px; line-height: 18px; border-radius: 5px;}
.layer-pages .trip-rule .result::after{ content: ''; position: absolute; top: 50%; left: 10px; margin-top: -10px; width: 20px; height: 20px; -webkit-background-size: 20px !important; background-size: 20px !important;}
.layer-pages .trip-rule .result.success{ background-color: #e5f8f3; color: #49b999;}
.layer-pages .trip-rule .result.success::after{ background: url(../../../assets/mobile/images/cmm/icn_inpolicy.png) 0 0 no-repeat;}
.layer-pages .trip-rule .result.fail{ background-color: #fff2f3;  color: #ff4e50;}
.layer-pages .trip-rule .result.fail::after{ background: url(../../../assets/mobile/images/cmm/icn_outpolicy.png) 0 0 no-repeat; }
.layer-pages .trip-rule .box-info{ padding: 20px 16px; border: 1px solid #e2e4e8; border-radius: 5px; }
.layer-pages .trip-rule .box-info .tit{ margin-bottom: 20px; text-align: center; color: #757f92; letter-spacing: -1px;}
.layer-pages .trip-rule .box-info .tit span{ color: #4e81ff; }
.layer-pages .trip-rule .box-info li{ position: relative; padding-left: 18px; }
.layer-pages .trip-rule .box-info li::before{ content: ''; position: absolute; top: 4px; left: 0; width: 8px; height: 8px;  border: 1px solid #000; border-radius: 100%; }
.layer-pages .trip-rule .box-info li.success::before{ border-color: #49b999; background-color: #49b999;}
.layer-pages .trip-rule .box-info li.fail::before{ border-color: #ff4e50; background-color: #ff4e50;}
.layer-pages .trip-rule .box-info li.default::before{ border-color: #babdc3; background-color: #fff;}
.layer-pages .trip-rule .box-info li.etc::before{ border-color: #e2e4e8; background-color: #e2e4e8;}
.layer-pages .trip-rule .box-info li:last-child dl{ margin-bottom: 0; }
.layer-pages .trip-rule .box-info dl{ margin-bottom: 15px; display: -webkit-flex; display: -moz-flex; display: -ms-flex; display: -o-flex; display: flex; }
.layer-pages .trip-rule .box-info dt{ min-width: 120px; font-size: 13px; font-weight: 700; color: #293036;}
.layer-pages .trip-rule .box-info dd{ font-size: 13px; line-height: 19px; }

/* 출장규정 팝업 */
.layer-pages.trip-rule-pop .desc{ margin-bottom: 20px; font-size: 15px; text-align: center; line-height: 20px; color: #293036; }
.layer-pages.trip-rule-pop .layer-head .tit{ text-align: center; }

/* 요청내역 */
.layer-pages.detail-info.type-rule .layer-head .tit{ text-align: center; }
.layer-pages.detail-info.type-rule .layer-cotn{ margin-top: 0; padding-top: 58px ; padding-bottom: 30px; }
.layer-pages.detail-info.type-rule .tab-btm-fare a{ width: 95px; }

/* 최근 결제 카드 내역 */
.btn-u-inline { position: absolute; top: 0; right: 0; text-decoration: underline; font-size: 13px; font-weight: 400; line-height: normal; letter-spacing: -.3px; color: #757f92; }

.md-recent-card { padding: 20px 16px 30px; }
.md-recent-card .scrollbar-inner { max-height: 297px; overflow-y: auto; overflow-x: hidden; }
.md-recent-card .total{ margin-bottom: 10px; text-align: left; font-size: 14px; font-weight: 500; letter-spacing: -.3px; color: #34446E; }
.md-recent-card .table-cmm.type-line{ border-top: 0 none; border-bottom: 0 none;}
.md-recent-card .table-cmm.type-line th{ padding: 20px 0; border-bottom: solid 2px #EBEEF3; border-top: solid 2px #EBEEF3; text-align: center; font-size: 14px; font-weight: 500; letter-spacing: -.3px; color: #34446E; }
.md-recent-card .table-cmm.type-line td{ padding: 20px 0; text-align: center; font-size: 14px; border-bottom: 1px solid #EBEEF3; font-weight: 300; letter-spacing: -.3px; }

/* 대기 좌석 안내 */
#noticeBeforeBooking .modal-cotn{ text-align: center; padding: 30px 0;}
#noticeBeforeBooking .modal-cotn dt{ font-size: 17px; line-height: 25px; font-weight: 500; }
#noticeBeforeBooking .modal-cotn dd{ margin-bottom: 20px; line-height: 20px; font-weight: 300; }
#noticeBeforeBooking .modal-cotn dd span{ color: #4e81ff; font-weight: 500; }
#noticeBeforeBooking .modal-cotn .num{ margin-bottom: 30px; font-size: 15px; font-weight: 500; }
#noticeBeforeBooking .modal-cotn .num .tel{ display: inline-block; vertical-align: middle;  margin-right: 3px;}

/* DT-24190 마스터카드 유입 고객 할인 표기 */
.btn-arrow-rate { display: inline-flex; gap: 5px; align-items: center; font-size: 12px; font-weight: 400; vertical-align: middle; color: #333;}
.btn-arrow-rate .btn-default { display: block; width: 10px; height: 10px; background: url(../../../assets/mobile/images/cmm/btn_dropdown_l.png) no-repeat center / contain; }
.btn-arrow-rate .cn { display: none; position: absolute; top: calc(100% + 5px); right: 0; z-index: 1; padding: 11px 13px 13px; width: 164px; height: auto; background-color: #34446e; border-radius: 5px; text-align: left; font-size: 12px; line-height: 26px; font-weight: 400; }
.btn-arrow-rate.active .btn-default { transform: rotate(180deg); }
.btn-arrow-rate.active .cn { display: block; }
.ul-table { display: flex; justify-content: space-between; padding-top: 12px; width: 100%; }
.ul-table > li { letter-spacing: -0.22px; font-size: 12px; font-weight: 400; line-height: 1.4; color: #ffffff; }
.info-date { font-size: 12px; font-weight: 400; line-height: 1.4; color: #E2E4E8; }
.hotel .box-hotel-item .info + .val.group.sales-price{margin-top: 6px;}
.val.group.sales-price{display: block;}
.val.group.sales-price .prev,
.val.group.sales-price .result{display: block;}
.val.group.sales-price .del{text-decoration: line-through;}
.val.group.sales-price .result{color: #ff4e50;}
.val.group.sales-price .prev{color: #757f92; font-size: 13px;}
.val.group.sales-price .result{color: #4e81ff;}
.val.group.sales-price .prev{display: flex; justify-content: flex-end; gap: 0px;}
.val.group.sales-price .prev .sale{display: inline-flex; align-items: center; justify-content: center; padding: 2px 3px; border-radius: 2px; font-size: 10px; font-weight: 500; color: #fff; line-height: 14px; background-color: #4E81FF; letter-spacing: 0; margin-right: 5px;}

.val.group.sales-price .price{ display:flex; gap:12px; position: relative; font-size: 16px; line-height: 23px; font-weight: 700; color: #000; justify-content: flex-end; margin-top: 3px;}
.val.group.sales-price .price .val{font-size: 18px; font-weight: 700; line-height: 26px; color: #4e81ff; float: none; width: auto;}

.fare-info.sales .clearfix{display: flex; justify-content: space-between;}
.fare-info.sales .clearfix:after{display: none;}
.fare-info.sales .txt .sale{color: #ff4e50;}
.fare-info.sales .val.group{display: block;}
.fare-info.sales .val.group .prev,
.fare-info.sales .val.group .result{display: block;}
.fare-info.sales .val.group .del{text-decoration: line-through;}
.fare-info.sales .val.group .result{color: #ff4e50;}
.fare-info.sales .total .val.group .prev{color: #757f92; font-size: 13px;}
.fare-info.sales .total .val.group .result{color: #4e81ff;}
.fare-info.sales .total{display: flex; justify-content: space-between;}

.sec-bills-info.sales .clearfix{display: flex; justify-content: space-between;}
.sec-bills-info.sales .clearfix:after{display: none;}
.sec-bills-info.sales .txt .sale{color: #ff4e50;}
.sec-bills-info.sales .val.group{display: block;}
.sec-bills-info.sales .val.group .prev,
.sec-bills-info.sales .val.group .result{display: block;}
.sec-bills-info.sales .val.group .del{text-decoration: line-through;}
.sec-bills-info.sales .val.group .result{color: #ff4e50;}
.sec-bills-info.sales .total .val.group .prev{color: #757f92; font-size: 13px;}
.sec-bills-info.sales .total .val.group .result{color: #4e81ff;}
.sec-bills-info.sales .total{display: flex; justify-content: space-between; overflow: visible;}
.sec-bills-info.sales .val.group .prev{display: flex; justify-content: flex-end; gap: 0px;}
.sec-bills-info.sales .val.group .prev .sale{display: inline-flex; align-items: center; justify-content: center; padding: 2px 3px; border-radius: 2px; font-size: 10px; font-weight: 500; color: #fff; line-height: 14px; background-color: #4E81FF; letter-spacing: 0; margin-right: 5px;}
.sec-bills-info.sales .total .txt{font-weight: 500;}