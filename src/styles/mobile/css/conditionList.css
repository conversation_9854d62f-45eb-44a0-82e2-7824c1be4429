.layer-pages.condition-list .layer-head {
  text-align: center;
}
.layer-pages.condition-list .box-accodi {
  margin-top: 25px;
  padding: 0 18px 25px;
}
.layer-pages.condition-list .box-accodi .box-item {
  position: relative;
  border-bottom: 1px solid #ebeef3;
}
.layer-pages.condition-list .box-accodi .box-item:first-child {
  border-top: 1px solid #ebeef3;
}
.layer-pages.condition-list .box-accodi dt {
  padding: 15px 35px 15px 0;
  font-weight: 300;
  line-height: 20px;
}
.layer-pages.condition-list .box-accodi dt .date {
  display: block;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
  font-weight: 400;
}
.layer-pages.condition-list .box-accodi dd {
  display: none;
  padding-bottom: 16px;
  font-weight: 300;
  line-height: 20px;
}
.layer-pages.condition-list .box-accodi dd .txt {
  padding-right: 35px;
}
.layer-pages.condition-list .box-accodi dd img {
  margin-top: 15px;
  width: 100%;
}
.layer-pages.condition-list .box-accodi .btn-arrow {
  position: absolute;
  top: 11px;
  right: 8px;
  width: 30px;
  height: 30px;
  background: url(@/assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat;
  -webkit-background-size: 10px 6px;
  background-size: 10px 6px;
}
.layer-pages.condition-list .box-accodi .box-item.active dt {
  font-weight: 500;
}
.layer-pages.condition-list .box-accodi .box-item.active dd {
  display: block;
}
.layer-pages.condition-list .box-accodi .box-item.active .btn-arrow {
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
