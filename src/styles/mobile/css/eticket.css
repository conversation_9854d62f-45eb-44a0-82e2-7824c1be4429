#eTicketLayerPop .modal-body {
  max-height: calc(100vh - 117px);
  overflow-y: auto;
}
#eTicketLayerPop .box-acoodi {
  position: relative;
  padding: 16px;
}
#eTicketLayerPop .box-acoodi .tit {
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  line-height: 26px;
}
#eTicketLayerPop .box-acoodi .tbl-cmm {
  display: none;
  border-top: 0 none;
  border-bottom: 0 none;
}
#eTicketLayerPop .box-acoodi .tbl-cmm th {
  height: 36px;
  border-bottom: 2px solid #ebeef3;
  font-size: 14px;
  color: #34446e;
  font-weight: 500;
}
#eTicketLayerPop .box-acoodi .tbl-cmm td {
  height: 50px;
  padding: 0;
  border-top: 0 none;
  border-bottom: 1px solid #ebeef3;
}
#eTicketLayerPop .box-acoodi .tbl-cmm td[colspan] {
  height: auto;
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 0 none;
  font-size: 14px;
  font-weight: 300;
  color: #babdc3;
}
#eTicketLayerPop .box-acoodi .tbl-cmm .btns-cmm {
  padding-left: 14px;
  padding-right: 14px;
  font-size: 13px;
  line-height: 26px;
  border-radius: 14px;
  background-color: #4e81ff;
  font-weight: 300;
}
#eTicketLayerPop .box-item {
  position: relative;
}
#eTicketLayerPop .box-item.receipt {
  padding-bottom: 30px;
  margin-bottom: 16px;
}
#eTicketLayerPop .box-item.receipt .tbl-cmm {
  margin-top: 10px;
}
#eTicketLayerPop .box-item.receipt::after {
  content: "";
  height: 10px;
  position: absolute;
  bottom: 0;
  left: -16px;
  right: -16px;
  background-color: #f2f4f9;
}
#eTicketLayerPop .box-item.receipt.active {
  padding-bottom: 40px;
}
#eTicketLayerPop .box-item.active {
  min-height: 200px;
}
#eTicketLayerPop .box-item.active .tbl-cmm {
  display: block;
}
#eTicketLayerPop .box-item.active .btn-arrow {
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
#eTicketLayerPop .btn-arrow {
  position: absolute;
  top: 6px;
  right: 0;
  width: 15px;
  height: 15px;
  background: url(../images/cmm/btn_dropdown_l.png) 50% 50% no-repeat;
  -webkit-background-size: 13px 7px;
  background-size: 13px 7px;
}
