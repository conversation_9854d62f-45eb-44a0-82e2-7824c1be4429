﻿@charset "utf-8";
.pg-login{ position: relative; min-height: 600px;}
.pg-login h1{ padding: 40px 0; text-align: center;}
.pg-login h2.title{ margin-bottom: 50px; font-size: 26px; font-weight: 500; line-height: 38px; }
.pg-login .contents{ }
.pg-login input::placeholder{color: #c9cfd8; font-weight: 300;}
.pg-login .id{ position: relative; margin-bottom: 15px; }
.pg-login .id input{ height: 40px; padding-right: 35%; -webkit-box-sizing: border-box; width: 40%;
-moz-box-sizing: border-box;
box-sizing: border-box;}
.pg-login .id .mail{ position: absolute; top: 10px; right: 0; }
.pg-login .btn-func{ padding-top: 40px; text-align: center; }
.pg-login .validate{ padding-top: 20px; font-size: 12px; }

.login-cotn{ position: relative; }
.login-cotn .pw{ position: relative; }
.login-cotn .pw input{ height: 40px;}
.login-cotn .etc-col{ overflow: hidden; }
.login-cotn .etc-col .btns{ text-align: center; padding-top: 34px;
display: -webkit-flex;
display: -moz-flex;
display: -ms-flex;
display: -o-flex;
display: flex;
}
.login-cotn .etc-col .btns a{ position: relative; width: 50%; font-size: 13px; font-weight: 300; line-height: 19px; color: #9da9be; }
.login-cotn .etc-col .btns a:first-child::after{ content: ''; position: absolute; top: 50%; right: 0; width: 1px; height: 12px; margin-top: -6px; background-color: #9da9be;}

.pw-reset-cotn{ position: relative;}
.pw-reset-cotn .id{ margin-bottom: 0; }
.pw-reset-cotn .new-pw input{ height: 40px; }
.pw-reset-cotn .new-pw.in02{ margin-top: 20px; }

.policy-agree-cotn{ position: relative; width: 400px;  margin: 0 auto; }
.policy-agree-cotn h2.title{ line-height: 38px; }
.policy-agree-cotn .form-chkbox{ line-height: 22px; }
.policy-agree-cotn .form-chkbox span{ font-size: 15px; }
.policy-agree-cotn .form-chkbox span::after{ border-radius: 100%; }
.policy-agree-cotn .all .desc{ padding: 0 0 21px 28px; margin-bottom: 20px; border-bottom: 1px solid #e2e4e8; padding-top: 20px; font-size: 13px; line-height: 19px; font-weight: 300; color: #7f848d; }
.policy-agree-cotn .cell{ margin-bottom: 20px; }
.policy-agree-cotn .btns{ padding-top: 10px;}
.policy-agree-cotn .btn-confirm{ width: 100%; border-radius: 5px; font-size: 16px; font-weight: 500; line-height: 50px; background-color: #4e81ff;  color: #fff;}

.join-cotn{ position: relative; padding: 40px 0 0 0 !important ; width: 1080px; margin: 0 auto;}
.join-cotn .box-division{ position: relative; padding: 18px 0 40px; border-top: solid 2px #ebeef3;}
.join-cotn .box-division:first-child{ border-top: solid 2px #ebeef3;}
.join-cotn .box-division .tit{margin-bottom: 32px; line-height: 25px; font-size: 17px; font-weight: 500; }
.join-cotn .box-division dl{ position: relative; }
.join-cotn .box-division dl::after{ content: ''; display: block; clear: both; }
.join-cotn .box-division dt .essential{ display: inline-block; color: #4e81ff; }
.join-cotn .box-division dt{ clear: both; float: left; width: 130px; margin-top: 13px; color: #757f92; line-height: 32px;}
.join-cotn .box-division dd{ float: left; width: 950px; margin-top: 13px; font-size: 15px; line-height: 32px;}
.join-cotn .box-division dt:first-child,
.join-cotn .box-division dt:first-child + dd{ margin-top: 0; }
.join-cotn .box-division dd *{ font-size: 15px; line-height: 32px;}
.join-cotn .box-division dd.gender .form-radio{ float: left; margin-right: 40px;}
.join-cotn .box-division .mail span{ float: left; }
.join-cotn .box-division .mail .btn-default{ float: left; margin-left: 14px;}
.join-cotn .box-division .mail .time{ margin-left: 10px; font-size: 12px; line-height: 36px; color:#4e81ff; }
.join-cotn .box-division .mail .validate{ clear: both; font-size: 12px;}
.join-cotn .box-division .code .btn-default{ margin-left: 3px; }
.join-cotn .box-division .btn-white{ width: 98px; line-height: 34px; border: 1px solid #4e81ff; border-radius: 5px; text-align: center;color: #4e81ff; font-size: 13px; font-weight: 500;}
.join-cotn .box-division .btn-gray{ width: 98px; line-height: 34px; border: 1px solid #babdc3; border-radius: 5px; text-align: center;color: #fff; font-size: 13px; font-weight: 500; background-color: #babdc3;}
.join-cotn .box-col{ float: left; width: 210px; margin-left: 33px; }
.join-cotn .box-col:first-child{ margin-left: 0; }
.join-cotn .box-col.box-ymd{ border-bottom: 1px solid #e2e4e8; }
.join-cotn .box-col.box-ymd .form-select{ border-bottom: 0 none; margin-right: 15px;}
.join-cotn .box-col .form-chkbox span::after{ top: 50%; }
.join-cotn .box-col.tel{border-bottom: 1px solid #e2e4e8; }
.join-cotn .box-col.tel .form-select{ border-bottom: 0 none; }
.join-cotn .box-col.tel input{ width: 80px; padding-left: 15px; border-bottom: 0 none;}
.join-cotn .desc-essential{ position: absolute; top: 20px; right: 0; line-height: 20px; color: #757f92;}
.join-cotn .desc-essential span{ color: #4e81ff; }
.join-cotn .form-chkbox span::after{ top: 50%; margin-top: -9px; }
.join-cotn .btns-bottom { text-align: center; padding-top: 40px; padding-bottom: 100px; }
.join-cotn .btns-bottom .btn-blue{ width: 140px; line-height: 50px; border-radius: 5px; background-color: #4e81ff; font-size: 16px; font-weight: 500; color: #fff; }

.member-join-cotn h2.title{ margin-bottom: 25px; font-size: 18px; font-weight: 500;line-height: 26px; }
.member-join-cotn .box-info .btns-cmm { line-height: 40px; box-sizing:  border-box; font-size: 14px;}
.member-join-cotn .form-tit { margin-bottom: 5px; }
.member-join-cotn .validate{ padding-top: 0; }
.member-join-cotn .box-info{ padding: 20px 16px; border-bottom: 12px solid #f2f4f9; }
.member-join-cotn .box-info.company{ border-bottom: 0 none; }
.member-join-cotn .box-email-id{position: relative; margin-bottom: 24px;}
.member-join-cotn .box-email-id .form-cn{ margin-bottom: 0; }
.member-join-cotn .box-email-id input{ box-sizing: border-box; }
.member-join-cotn .box-email-id .address{ position: absolute; top: 9px; right: 0; font-size: 15px;  line-height: 22px; }
.member-join-cotn .box-email-id .btns-cmm{ margin-top: 10px; }
.member-join-cotn .box-email-id .return-time{ padding-top: 2px; font-size: 12px; line-height: 18px; color: #4e81ff; }
.member-join-cotn .box-certify{ position: relative; }
.member-join-cotn .box-certify .form-cn .input-cmm{ display: block; margin-right: 105px; }
.member-join-cotn .box-certify .form-cn .val{ font-size: 12px;line-height: 18px; color: #9da9be; }
.member-join-cotn .box-certify .btns-cmm{ position: absolute; top: 0; right: 0; width: 100px;}
.member-join-cotn .box-certify .btns-cmm[disabled]{ background-color: #9da9be; border-color: #9da9be; color: #fff;}
.member-join-cotn .box-btn{ text-align: center; padding-bottom: 25px; }

/* 회원가입 완료 */
.complete-join-cotn{ position: relative; padding: 20px;}
.complete-join-cotn dt{ padding: 40px 0 43px; font-size: 22px; font-weight: 500; line-height: 33px; }
.complete-join-cotn dd{ margin-bottom: 50px; font-size: 13px; line-height: 19px; color: #757f92; }
.complete-join-cotn dd span{ color: #ff4e50; }
.complete-join-cotn .box-btn{ text-align: center; }


/* RVYN-976
비밀번호 변경 */
.pw-change-cont{padding:0 20px; padding-bottom: 42px;}
.pw-change-cont .pw-ch_title{margin-top:19px; }
.pw-change-cont .pw-ch_title dt{font-size: 18px; font-weight: 500; margin-bottom: 14px;}
.pw-change-cont .pw-ch_title dd p{font-size: 14px;}
.pw-change-cont .pw-form{margin-top: 30px;}
.pw-change-cont .pw-form .form-cn:nth-last-of-type(1){margin-bottom: 0;}
.pw-change-cont .form-tit{color:#757F92;font-size: 13px;position: relative;display: inline-block;padding-right: 10px;}
.pw-change-cont em.necessaray{color:#4E81FF;position: absolute;right: 0;top:0;}
.pw-change-cont .list-style{margin-top:20px}
.pw-change-cont .list-style li{padding-left: 7px;position: relative;margin-bottom: 10px; font-size: 12px;}
.pw-change-cont .list-style li:nth-last-of-type(1){margin-bottom: 0px;}
.pw-change-cont .list-style li::before{content: '';display: block; position: absolute;top:9px;left:0;width: 2px;height: 2px;border-radius: 100%;background-color: #000;}
.pw-change-cont .box-btn{text-align: center;margin-top:30px}
.pw-change-cont .box-btn button{width: 230px;margin-bottom: 10px;}
.form-chkbox span::after{border: none !important;}