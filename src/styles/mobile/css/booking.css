input.upper {
  text-transform: uppercase;
}
.box-cell div {
  white-space: pre-line;
}
.reserv-request .policy-agree .box-desc {
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}
.line-table.first-table {
  margin-top: -37px;
}
.line-table thead th,
.line-table tbody th,
.line-table tbody td {
  border-bottom: 1px solid #dddddd;
  text-align: left;
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}
.borderR thead th,
.borderR tbody td {
  border: 1px solid #dddddd;
  padding: 5px 3px;
}
.borderR thead th {
  text-align: center;
}
.policy-agree a {
  color: inherit;
  text-decoration: underline;
  font-size: 12px;
  font-weight: 400;
}

@media (max-width: 480px) {
  .mileage .content {
    display: block;
  }
}

@media (min-width: 480px) {
  .mileage .content {
    display: flex;
  }
}

.mileage .content span {
  width: 50px;
  margin-right: 5px;
  margin-left: 5px;
  font-size: 13px;
}

.mileage .content input[type="text"] {
  height: 34px;
  border: 0 none;
  border-bottom: 1px solid #e2e4e8;
  outline: none;
  padding-left: 5px;
  margin-right: 5px;
}

.passenger-info .form-chkbox span::after {
  top: 8px;
  left: 1px;
}
.passenger-info .form-chkbox span {
  padding-top: 2px;
}
