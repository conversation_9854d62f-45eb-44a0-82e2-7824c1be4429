﻿@charset "utf-8";
.pg-main{ position: relative; }
.pg-main .box-top{ position: relative; }
.pg-main .box-top .btn-sidemenu{ width: 56px; height: 56px; background: url(../images/main/btn_sidemenu.png) 50% 50% no-repeat; -webkit-background-size: 21px 15px; background-size: 21px 15px;}
.pg-main .contents{ padding: 13px 0; }
.pg-main .tit-size01{ font-size: 16px; line-height: 24px; font-weight: 500; color: #000; }

.pg-main .copy{margin-bottom: 30px; font-size: 28px; line-height: 32px; color: #4e81ff;}

.pg-main .airticket-type{margin-bottom: 40px;
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	-moz-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	-o-flex-wrap: wrap;
	flex-wrap: wrap;
	justify-content: space-between;
}
.pg-main .airticket-type a{width: 48%; position: relative;}
.pg-main .airticket-type img{ width: 100%;  box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2); border-radius: 16px;}
.pg-main .airticket-type .txt{position: absolute; top: 15px; left: 15px; font-size: 18px; line-height: 22px; font-weight: 500; color: #fff;}

.pg-main .business-manage{ position: relative; }
.pg-main .business-manage dl{ margin-bottom: 20px; }
.pg-main .business-manage dt{ margin-bottom: 4px; font-size: 16px; line-height: 24px; font-weight: 500; color: #000;}
.pg-main .business-manage dd{ font-size: 13px; line-height: 19px; color: #7f848d;}
.pg-main .business-manage .cate{ margin-bottom: 40px;
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flex;
	display: -o-flex;
	display: flex;
	-webkit-flex-wrap: wrap;
	-moz-flex-wrap: wrap;
	-ms-flex-wrap: wrap;
	-o-flex-wrap: wrap;
	flex-wrap: wrap;
	justify-content: space-between;
}
.pg-main .business-manage .cate li{ width: 32%; text-align: center;}
.pg-main .business-manage .cate li a{display: block; height: 110px; border-radius: 5px; box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2); background-color: #fff;}
.pg-main .business-manage .cate li.hotel a{ color: #8184e4; }
.pg-main .business-manage .cate li.car a{ color: #ff9268; }
.pg-main .business-manage .cate li.visa a{ color: #61c7cb; }
.pg-main .business-manage .cate li.hotel i{ margin-top: 21px; }
.pg-main .business-manage .cate li.car i{ margin-top: 30px;}
.pg-main .business-manage .cate li.visa i{ margin-top: 24px; }
.pg-main .business-manage .cate li i{ display: inline-block; }
.pg-main .business-manage .cate li .txt{display: block; padding-top: 10px;}
.pg-main .business-manage .group{ position: relative; margin-bottom: 40px; border-radius: 8px;   box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2);   background-color: #ffffff;}
.pg-main .business-manage .group a{ display: block; height: 110px; background: url(../images/main/img_mice.png) 90% 100% no-repeat; -webkit-background-size: 95px 90px; background-size: 95px 90px;}
.pg-main .business-manage .group .txt{ display: block; padding:22px 22px 17px 0; margin-left: 22px; font-size: 20px; font-weight: 300; line-height: 23px; color: #4e81ff;  background: url(../images/main/icn_arrow_l.png) 0 100% no-repeat; -webkit-background-size: 40px 4px; background-size: 40px 4px;}

.pg-main .favor-trip-city{ position: relative; margin-bottom: 40px; }
.pg-main .favor-trip-city .tit-size01{ margin-bottom: 13px; }
.pg-main .favor-trip-city .swiper-container{ padding-left: 16px; }
.pg-main .favor-trip-city .swiper-slide{ position: relative; border-radius: 10px; overflow: hidden;}
.pg-main .favor-trip-city .swiper-slide img{ width: 100%; border-radius: 10px;}
.pg-main .favor-trip-city .swiper-slide .txt{ position: absolute; top: 15px; left: 15px; }
.pg-main .favor-trip-city .swiper-slide .txt span{display: block;  color: #fff; }
.pg-main .favor-trip-city .swiper-slide .txt .kr{ font-size: 16px; line-height: 20px; font-weight: 700; }
.pg-main .favor-trip-city .swiper-slide .txt .en{ font-size: 12px; color: #fff; }


.pg-main .inquiry-service{ position: relative; margin-bottom: 30px;}
.pg-main .inquiry-service .tit-size01{ margin-bottom: 13px; }
.pg-main .inquiry-service .box-item{ position: relative; height: 98px; padding-right: 22px; margin-bottom: 10px; border-radius: 8px; box-shadow: 4px 2px 12px 0 rgba(157, 169, 190, 0.2); background-color: #fff; text-align: right;}
.pg-main .inquiry-service .box-item .spr-service{ position: absolute; top: 0; left: 27px; }
.pg-main .inquiry-service .box-item .spr-service.air{ top: 21px; }
.pg-main .inquiry-service .box-item .spr-service.hotel{ top: 20px; }
.pg-main .inquiry-service .box-item .spr-service.mice{ top: 23px; }
.pg-main .inquiry-service .box-item .txt{ display: block; padding-top: 25px; margin-bottom: 2px; font-size: 14px; line-height: 20px; color: #9da9be; }
.pg-main .inquiry-service .box-item .tel{ display: block; font-size: 14px; line-height: 26px; color: #4e81ff; font-weight: 700;}

.spr-booking {
    background-image: url(../images/main/spr_booking.png);
    background-repeat: no-repeat;
    display: block;
    -webkit-background-size: 95px 78px;
    background-size: 95px 78px;
}

.spr-booking.car {
    width: 42px;
    height: 32px;
    background-position: 0 0;
}

.spr-booking.hotel {
    width: 53px;
    height: 41px;
    background-position: -42px 0;
}

.spr-booking.visa {
    width: 30px;
    height: 37px;
    background-position: 0 -41px;
}


.spr-service {
    background-image: url(../images/main/spr_service.png);
    background-repeat: no-repeat;
    display: block;
    -webkit-background-size: 59px 166px;
    background-size: 59px 166px;
}

.spr-service.air {
    width: 59px;
    height: 56px;
    background-position: 0 0;
}

.spr-service.hotel {
    width: 54px;
    height: 57px;
    background-position: 0 -56px;
}

.spr-service.mice {
    width: 51px;
    height: 52px;
    background-position: 0 -114px;
}

.pg-main .inquiry-service .box-item .spr-service.visa{display: block;top: 21px;width: 48px;height: 57px; background-repeat: no-repeat;background-position: 0 0;background-size: 48px 57px;}

.pg-main .airticket-type a.w100{ width: 100%; }
.pg-main .airticket-type a.w100 .txt{ top: unset; bottom: 50%; left: 5%; }