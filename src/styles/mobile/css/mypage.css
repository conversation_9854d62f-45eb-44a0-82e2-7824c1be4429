﻿@charset "utf-8";
.pg-mypage{ position: relative; }

.sec-detail-search{ border-top: 1px solid #ebeef3; position: relative;  }
.sec-detail-search .box-top{text-align: center; padding: 16px;}
.sec-detail-search .box-bottom{ display: none; padding: 0 16px 20px;}
.sec-detail-search .btns-toggle-cotn{ position: relative; display: inline-block; padding-right: 24px; font-size: 13px; font-weight: 500; line-height: 16px; outline: none;}
.sec-detail-search .btns-toggle-cotn i{ position: absolute; top: 0; right: 0; width: 16px; height: 16px; background: url(@/assets/mobile/images/member/btn-detail-search.png) 0 0 no-repeat; -webkit-background-size: 16px; background-size: 16px; }
.sec-detail-search .box-saerch{ margin-bottom: 15px; }
.sec-detail-search .box-saerch input{ width: 100%;  height: 40px; border-radius: 22px; border: 0 none; padding: 0 20px 0 45px; box-sizing: border-box; outline: none; background:#f2f6ff url(@/assets/mobile/images/cmm/icn_search_city.png) 15px 50% no-repeat; -webkit-background-size: 18px;background-size: 18px;}
.sec-detail-search .box-saerch.box-saerch input::placeholder{ color: #7fa3ff; }
.sec-detail-search .reserv-status{ overflow: hidden; margin-bottom: 15px;}
.sec-detail-search .reserv-status .tit{ float: left; width: 104px; font-weight: 500; line-height: 18px; }
.sec-detail-search .reserv-status .box-gruop{ float: left; padding-top: 2px;}
.sec-detail-search .reserv-status .box-gruop .form-chkbox{ margin-left: 30px; }
.sec-detail-search .reserv-status .box-gruop .form-chkbox:first-child{ margin-left: 0; }
.sec-detail-search .reserv-status .btn-reset{ float: right; width: 55px; line-height: 24px; border: 1px solid #4e81ff; border-radius: 16px; font-size: 12px; text-align: center; color: #4e81ff;}
.sec-detail-search .box-bottom { text-align: center; }
.sec-detail-search .box-bottom .box-btn{overflow: hidden;}
.sec-detail-search .box-bottom .btn-act{ line-height: 40px; font-size: 14px; width: 50%; }
.sec-detail-search .box-bottom.type-hotel .form-chkbox{ margin-left: 16px; }
.sec-detail-search .box-bottom.type-hotel .reserv-status{ margin-bottom: 25px; }
.sec-detail-search .box-bottom.type-hotel .btns-cmm{ width: 120px; }
.sec-detail-search.active .box-bottom{ display: block; }
.sec-detail-search.active .btns-toggle-cotn i{
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg);
}

.none-reserv-list{ position: relative; }
.none-reserv-list .innner{  padding-top: 233px; background: url(@/assets/mobile/images/cmm/bg_error.png) 50% 46px no-repeat; -webkit-background-size: 70px 150px; background-size: 70px 150px; text-align: center; font-weight: 300; color: #9da9be;}

.reserv-list-cotn{ position: relative; padding: 16px 0;}
.reserv-list-cotn .box-item{ position: relative; padding: 16px; margin-bottom: 12px; background-color: #fff;}
.reserv-list-cotn .box-item:last-child{ margin-bottom: 0; }
.reserv-list-cotn .tit{ padding: 0 0 12px 20px; border-bottom: 1px solid #ebeef3; font-size: 13px; font-weight: 300; line-height: 19px; background: url(@/assets/mobile/images/member/icn_airplaneb.png) 0 3px no-repeat; -webkit-background-size: 15px; background-size: 15px;}
.reserv-list-cotn .info{ padding: 12px 0; }
.reserv-list-cotn .info p{ margin-bottom: 2px; }
.reserv-list-cotn .info p:last-child{ margin-bottom: 0; }
.reserv-list-cotn .info p strong{ margin-right: 13px; display: inline-block; font-weight: 500; line-height: 20px; }
.reserv-list-cotn .info p span{ display: inline-block; font-size: 12px; line-height: 20px; }
.reserv-list-cotn .etc{overflow: hidden; font-size: 12px; color: #757f92; }
.reserv-list-cotn .etc .person{ position: relative; float: left; padding-right: 8px;}
.reserv-list-cotn .etc .person::after{ content: ''; position: absolute; top: 50%; right: 0; height: 10px; margin-top: -5px; border-left: 1px solid #ebeef3 }
.reserv-list-cotn .etc .date { float: left; padding-left: 8px; }
.reserv-list-cotn .status{ position: absolute; top: 16px; right: 20px; font-weight: bold; }
.reserv-list-cotn .status.reserv{ color: #61c7cb; }
.reserv-list-cotn .status.cancel{ color: #ff4e50; }
/* 호텔 */
.reserv-list-cotn.type-hotel .tit{ background-image: url(@/assets/mobile/images/member/icn_hotel_black.png); }
.reserv-list-cotn.type-hotel .etc .person span{ display: inline-block; margin-left: 7px; }
.reserv-list-cotn.type-hotel .status{ font-weight: 500; }
.reserv-list-cotn.type-hotel .status.reserv{ color: #4e81ff; }
.reserv-list-cotn.type-hotel .status.cancel{ color: #9da9be; }

/* 항공권 예약 */
.reserv-complete-top{ position: relative; padding: 20px 16px; background-color: #4e81ff; color: #fff;}
.reserv-complete-top.type-cancel{ background-color: #9da9be; }
.reserv-complete-top dt{ margin-bottom: 15px; font-size: 18px; font-weight: 500; line-height: 27px; }
.reserv-complete-top dd{ margin-bottom: 2px; font-size: 13px; line-height: 19px; font-weight: 300;  }
.reserv-complete-top dd:last-child{ margin-bottom: 0; }
.reserv-complete-top .date_status{ overflow: hidden;  padding: 14px 0 0 0; margin-top: 14px; border-top: 1px solid rgba(250,250,250,0.3); }
.reserv-complete-top .date_status p{ position: relative; float: left; width: 50%; font-size: 13px; }
.reserv-complete-top .date_status p span{ float: left; font-weight: 300; }
.reserv-complete-top .date_status p strong{ float: right; }
.reserv-complete-top .date_status p:first-child strong{ padding-right: 18px; }
.reserv-complete-top .date_status p:last-child span{ padding-left: 17px; }
.reserv-complete-top .date_status p:last-child::before{ content: ''; position: absolute; top: 0; left: 0; height: 24px; border-left: 1px solid rgba(250,250,250,0.3);;}

.reserv-complete-cotn{ background-color: #fff; padding-bottom: 110px;}
.reserv-complete-cotn .box-info{ position: relative; border-bottom: 12px solid #ebeef3;}
.reserv-complete-cotn .box-dt{ padding: 20px 16px; font-size: 16px; font-weight: 500; line-height: 24px;}
.reserv-complete-cotn .box-dd{ display: none; padding: 0 16px 20px; }
.reserv-complete-cotn .btn-box-arrow{ position: absolute; top: 20px; right: 20px;  width: 20px; height: 20px; border: 0 none; background: url(@/assets/mobile/images/cmm/btn_dropdown_l.png) 50% 50% no-repeat; -webkit-background-size: 13px 7px; background-size: 13px 7px; }
.reserv-complete-cotn .box-info .form-tit { margin-bottom: 5px; }
.reserv-complete-cotn .box-info .form-cn{ margin-bottom: 17px; }
.reserv-complete-cotn .box-info.active .box-dd{ display: block; }
.reserv-complete-cotn .box-info.active .btn-box-arrow{
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg); }
.reserv-complete-cotn .box-info.plan .box-dt{ padding-top: 25px; padding-bottom: 10px; border-bottom: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.plan .box-dd{ padding: 0; }
.reserv-complete-cotn .box-info.plan .box-group{ position: relative; padding: 18px 16px; border-bottom: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.plan .box-top{ position: relative; overflow: hidden; margin-bottom: 20px;}
.reserv-complete-cotn .box-info.plan .label{ float: left; width: 46px; margin-right: 8px; line-height: 19px; border-radius: 16px; background-color: #34446e; font-size: 11px; text-align: center; color: #fff;}
.reserv-complete-cotn .box-info.plan .date{ line-height: 19px; }
.reserv-complete-cotn .box-info.plan .aircraft{ overflow: hidden; line-height: 20px; }
.reserv-complete-cotn .box-info.plan .aircraft img{float: left; width: 30px; height: 20px; margin-right: 8px; }
.reserv-complete-cotn .box-info.plan .aircraft .name{ float: left; margin-right: 6px; }
.reserv-complete-cotn .box-info.plan .aircraft .seat{ float: left; position: relative; padding-left: 7px; font-size: 12px; color: #757f92; }
.reserv-complete-cotn .box-info.plan .aircraft .seat::before{ content: ''; position: absolute; top: 50%; left: 0; height: 10px; margin-top: -5px; border-left: 1px solid #757f92;}
.reserv-complete-cotn .box-info.plan .time-city{ overflow: hidden; padding-left: 38px; }
.reserv-complete-cotn .box-info.plan .time-city p{ float: left; font-size: 15px; font-weight: 700; line-height: 22px; }
.reserv-complete-cotn .box-info.plan .time-city p:first-child{ padding-right: 21px; margin-right: 9px; background: url(@/assets/mobile/images/cmm/ico_plan_arrow_gray.png) 100% 50% no-repeat; -webkit-background-size: 12px 5px; background-size: 12px 5px;}
.reserv-complete-cotn .box-info.plan .time-city p.share{ margin-left: 10px; font-size: 12px; font-weight: 400;}
.reserv-complete-cotn .box-info.plan .box-right{ position: absolute; top: 18px; right: 18px; overflow: hidden; line-height: 19px;}
.reserv-complete-cotn .box-info.plan .box-right .total-time{ float: left; padding-left: 16px; font-size: 13px; font-weight: 500; color: #4e81ff; background: url(@/assets/mobile/images/cmm/icn_clock_blue.png) 0 50% no-repeat; -webkit-background-size: 13px;background-size: 13px;}
.reserv-complete-cotn .box-info.plan .box-right .btns-arrow{ position: relative; float: left; padding-right: 21px; margin-left: 7px; outline: none; font-size: 13px; line-height: 19px;}
.reserv-complete-cotn .box-info.plan .box-right .btns-arrow i{ position: absolute; top: 50%; right: 0; width: 10px; height: 6px; margin-top: -2px; background: url(@/assets/mobile/images/cmm/icn_list_dropdown.png) 50% 50% no-repeat;    -webkit-background-size: 10px 6px;     background-size: 10px 6px;}
.reserv-complete-cotn .box-info.plan .btn-compare-bill{display: block;padding: 18px 0; font-size: 16px; line-height: 24px; color: #4e81ff; text-align: center;}
.reserv-complete-cotn .box-info.user .form-cn:last-child{ margin-bottom: 0; }
.reserv-complete-cotn .box-info.passenger{ position: relative; }
.reserv-complete-cotn .box-info.passenger .box-dd{ padding-bottom: 0; }
.reserv-complete-cotn .box-info.passenger .box-group{ border-bottom: 1px solid #ebeef3; margin-bottom: 20px;}
.reserv-complete-cotn .box-info.passenger .box-group:last-child{ border-bottom: 0 none; margin-bottom: 0;}
.reserv-complete-cotn .box-info.passenger .box-group .tit{margin-bottom: 16px; font-size: 15px; line-height: 22px; font-weight: 700;}
.reserv-complete-cotn .box-info.passenger .box-group .tit span{ font-family: Roboto; }
.reserv-complete-cotn .box-info.passenger .item-col{ float: left; width: 33.33%; }
.reserv-complete-cotn .box-info.passenger .clearfix.col-02 .item-col:nth-child(2){ width: 66%; }
.reserv-complete-cotn .box-info.stay .desc{ margin-bottom: 20px; font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-complete-cotn .box-info.stay .btn-stay-regist{}
.reserv-complete-cotn .box-info.payment .desc-rule{ position: relative; margin-bottom: 15px; padding-left: 11px; line-height: 20px;}
.reserv-complete-cotn .box-info.payment .desc-rule::before{ content: ''; position: absolute; top: 50%; left: 0; width: 8px; height: 8px; margin-top: -4px; border-radius: 100%; background-color: #ff4e50; }
.reserv-complete-cotn .box-info.payment .desc-rule strong{ text-decoration: underline; }
/* DT-12860 */
.ticket-complete .desc-rule{ padding: 20px 16px; margin-bottom: 10px; text-align: center; background-color: #fff; }
/* DT-12860 */

/* DT-12908 수정 */
.reserv-complete-cotn .box-info.payment .desc-request{ padding-bottom: 15px; line-height: 20px; }
.reserv-complete-cotn .box-info.payment .box-status{ position: relative; margin-top: 15px; padding-top: 18px; border-top: 1px solid #ebeef3; }
/* // DT-12908 */
.reserv-complete-cotn .box-info.payment .box-status{ position: relative; padding-top: 18px; border-top: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.payment .box-status .tit{margin-bottom: 4px; font-weight: 500; line-height: 20px; }
.reserv-complete-cotn .box-info.payment .box-status .date{ font-size: 13px; line-height: 19px; color: #9da9be; }
.reserv-complete-cotn .box-info.payment .box-status::after{ position: absolute; top: 18px; right: 0; width: 40px; height: 40px; line-height: 40px; border: 1px solid #ff4e50; border-radius: 100%; text-align: center; font-size: 16px; font-weight: 500; color: #ff4e50;}
.reserv-complete-cotn .box-info.payment .box-status.approval::after{ content: '승인';border-color: #61c7cb; color: #61c7cb; }
.reserv-complete-cotn .box-info.payment .box-status.rejcet::after{ content: '반려'; border-color: #ff4e50; color: #ff4e50; }
.reserv-complete-cotn .box-info.reserv{ border-bottom: 0 none; }
.reserv-complete-cotn .box-info.reserv ul{ border-top: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.reserv li{ position: relative; padding: 12px 0; border-bottom: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.reserv .tit{ font-weight: 300; line-height: 20px; }
.reserv-complete-cotn .box-info.reserv .name-date{ overflow: hidden; font-size: 12px; line-height: 18px; color: #757f92;}
.reserv-complete-cotn .box-info.reserv .name-date span{position: relative; float: left; }
.reserv-complete-cotn .box-info.reserv .name-date span:first-child{ padding-right: 7px; margin-right: 8px; }
.reserv-complete-cotn .box-info.reserv .name-date span:first-child::after{ content: ''; position: absolute; top: 50%; right: 0; height: 10px; margin-top: -5px; border-left: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.reserv .status{ position: absolute; top: 50%; right: 0; margin-top: -10px; font-size: 13px; line-height: 20px;  }
.reserv-complete-cotn .box-info.reserv .status.blue{color: #4e81ff;}
.reserv-complete-cotn .box-info.reserv .status.gray{ color: #9da9be; }
.reserv-complete-cotn .box-info.reserv .domestic-disable{ padding-left: 16px; padding-right: 16px; }
.reserv-complete-cotn .box-info.reserv .domestic-disable .btn-modal{display: block; padding-left: 34px; border-radius: 5px; font-size: 14px; font-weight: 500; line-height: 40px; color: #ff4e50;  background: #fff2f3 url(@/assets/mobile/images/cmm/icn_outpolicy.png) 12px 50% no-repeat; -webkit-background-size: 16px; background-size: 16px;}
.reserv-complete-cotn .sec-bottom-btns{ box-shadow: none; padding: 0; bottom: 24px; background: none;}
/* DT-12860 */
.reserv-complete-cotn.in-domestic{ padding-bottom: 0px; }
/* DT-12860 */
.reserv-complete-cotn.in-domestic .box-info.plan .time-city{ padding-left: 0; margin-bottom: 10px;}
.reserv-complete-cotn.in-domestic .box-info.plan .aircraft .name{ font-size: 12px; line-height: 20px; color: #757f92; }
/* 호텔 - 예약 정보 */
.reserv-complete-cotn .box-info.booking { position: relative; }
.reserv-complete-cotn .box-info.booking .name{ position: relative; }
.reserv-complete-cotn .box-info.booking .name .kr{ position: relative; padding-right: 12px; font-weight: 700; line-height: 26px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical}
.reserv-complete-cotn .box-info.booking .name .kr::after{ content: ''; position: absolute; top: 6px; right: 0; width: 6px; height: 12px; background: url(@/assets/mobile/images/reserv/btn-more.png) 0 0 no-repeat; -webkit-background-size: 6px 12px;background-size: 6px 12px;}
.reserv-complete-cotn .box-info.booking .name .kr a{font-size: 18px; }
.reserv-complete-cotn .box-info.booking .name .en{ margin-bottom: 6px; font-size: 12px; font-weight: 300; line-height: 18px; }
.reserv-complete-cotn .box-info.booking .grade{ position: relative; margin-bottom: 15px; }
.reserv-complete-cotn .box-info.booking .grade .tit{ display: inline-block; margin-right: 5px; vertical-align: middle; width: 40px; text-align: center; border-radius: 9px; background-color: #f2f6ff; font-size: 11px; color: #4e81ff;}
.reserv-complete-cotn .box-info.booking .grade .start { display: inline-block; vertical-align: middle;}
.reserv-complete-cotn .box-info.booking .grade .start img{ position: relative; top: 3px; width: 12px; height: 12px;  vertical-align: top;}
.reserv-complete-cotn .box-info.booking .address{padding-left: 18px; margin-bottom: 9px; font-size: 12px; color: #757f92; background: url(@/assets/mobile/images/reserv/icn-marker-gray-solid.png) 0 50% no-repeat; -webkit-background-size: 10px 13px; background-size: 10px 13px;}
.reserv-complete-cotn .box-info.booking .tel{margin-bottom: 10px; padding-left: 18px; font-size: 12px; color: #757f92; background: url(@/assets/mobile/images/reserv/icn-tel-gray-solid.png) 0 50% no-repeat; -webkit-background-size: 10px 12px; background-size: 10px 12px;}
.reserv-complete-cotn .box-info.booking .etc-btn{ overflow: hidden; margin-bottom: 30px;}
.reserv-complete-cotn .box-info.booking .etc-btn .btn{ float: left; width: 48.5%; line-height: 39px; border-radius: 5px; border: 1px solid #4e81ff; text-align: center; font-size: 13px; color: #4e81ff; box-sizing: border-box;}
.reserv-complete-cotn .box-info.booking .etc-btn .btn:last-child{ float: right; }
.reserv-complete-cotn .box-info.booking .check-in-out{ overflow: hidden; }
.reserv-complete-cotn .box-info.booking .check-in-out dl{ float: left; width: 50%; margin-bottom: 15px; box-sizing: border-box;}
.reserv-complete-cotn .box-info.booking .check-in-out dl:nth-child(2){position: relative; padding-left: 17px; float: right; }
.reserv-complete-cotn .box-info.booking .check-in-out dl::before{ content: ''; position: absolute; top: 50%; left: 0; border-left: 1px solid #e2e4e8; height: 40px; margin-top: -20px;}
.reserv-complete-cotn .box-info.booking .check-in-out dd{ margin-bottom: 0; font-weight: 500;}
.reserv-complete-cotn .box-info.booking .check-in-out .desc{ clear: both; font-size: 13px; line-height: 19px; color: #757f92;}
.reserv-complete-cotn .box-info.booking .in-request{padding-top: 20px; }
.reserv-complete-cotn .box-info.booking .in-request .name .kr{ font-size: 18px; font-weight: 700; line-height: 27px; }
.reserv-complete-cotn .box-info.booking .in-request .name .kr::after{ display: none; }
.reserv-complete-cotn .box-info.booking .in-request .grade{ padding-bottom: 16px; border-bottom: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.booking .in-request .room-info{ position: relative;  font-size: 13px;line-height: 19px; overflow: hidden;}
.reserv-complete-cotn .box-info.booking .in-request .room-info dl{ overflow: hidden; margin-bottom: 8px;}
.reserv-complete-cotn .box-info.booking .in-request .room-info dt{ float: left; width: 62px; padding-left: 23px;}
.reserv-complete-cotn .box-info.booking .in-request .room-info dd{ float: left; }
.reserv-complete-cotn .box-info.booking .in-request .room-info .name{ margin-bottom: 12px; font-size: 16px;font-weight: 500; line-height: 24px; }
.reserv-complete-cotn .box-info.booking .in-request .room-info .date{}
.reserv-complete-cotn .box-info.booking .in-request .room-info .date dt{ background: url(@/assets/mobile/images/reserv/icn-calendar-lg.png) 0 50% no-repeat; -webkit-background-size: 15px 14px; background-size: 15px 14px;  }
.reserv-complete-cotn .box-info.booking .in-request .room-info .length{}
.reserv-complete-cotn .box-info.booking .in-request .room-info .length dt{ background: url(@/assets/mobile/images/reserv/icn-room.png) 0 50% no-repeat; -webkit-background-size: 17px 13px; background-size: 17px 13px; }
.reserv-complete-cotn .box-info.booking .in-request .room-info .person{}
.reserv-complete-cotn .box-info.booking .in-request .room-info .person dt{background: url(@/assets/mobile/images/reserv/icn-person-lg.png) 2px 50% no-repeat; -webkit-background-size: 11px 12px; background-size: 11px 12px; }
.reserv-complete-cotn .box-info.booking .in-request .room-info .meal{}
.reserv-complete-cotn .box-info.booking .in-request .room-info .meal dt{ background: url(@/assets/mobile/images/reserv/icn-breakfast.png) 2px 50% no-repeat; -webkit-background-size: 11px 13px; background-size: 11px 13px; }
.reserv-complete-cotn .box-info.booking .in-request .free-cancel{ padding-top: 10px; color: #ff4e50; line-height: 19px; }


/* 호텔 - 총 결제금액 */
.reserv-complete-cotn .total-price{ position: relative; border-bottom: 0 none;}
.reserv-complete-cotn .total-price .box-dt{overflow: hidden;}
.reserv-complete-cotn .total-price .box-dt .tit{float: left;}
.reserv-complete-cotn .total-price .box-dt .tit .sub{ position: relative; top: -2px; margin-left: 3px; display: inline-block; font-size: 12px; color: #757f92; vertical-align: middle;}
.reserv-complete-cotn .total-price .box-dt .val{ float: right; font-size: 18px; line-height: 27px; color: #4e81ff; font-weight: 700;}
.reserv-complete-cotn .total-price .box-note{ position: relative; padding:0 14px; margin-bottom: 20px; border: 1px solid #e2e4e8; border-radius: 5px;}
.reserv-complete-cotn .total-price .box-note dt{ padding-top: 14px; padding-bottom: 15px; }
.reserv-complete-cotn .total-price .box-note dd{ display: none;padding-bottom: 14px; }
.reserv-complete-cotn .total-price .box-note .btn{ position: absolute; top: 13px; right: 11px; width: 20px; height: 20px; background: url(@/assets/mobile/images/cmm/btn-dropdown-s.png) 50% 50% no-repeat; -webkit-background-size: 10px 4px;background-size: 10px 4px;}
.reserv-complete-cotn .total-price .box-note.active dd{ display: block; }
.reserv-complete-cotn .total-price .box-note.active .btn{
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	-o-transform: rotate(180deg);
	transform: rotate(180deg);
}

.reserv-complete-cotn .box-info .desc-list{ position: relative; margin-bottom: 40px; }
.reserv-complete-cotn .box-info .desc-list li{ margin-bottom: 15px; position: relative; padding-left: 13px; font-size: 13px; color: #757f92; }
.reserv-complete-cotn .box-info .desc-list li::after{ content: ''; position: absolute; top: 5px; left: 0; width: 5px; height: 5px; background-color: #757f92; border-radius: 1px;}
.reserv-complete-cotn .box-info .desc-list li.point{ color: #4e81ff; }
.reserv-complete-cotn .box-info .desc-list li.point::after{ background-color: #4e81ff; }
.reserv-complete-cotn .box-info .btn-next-step{display: block; margin: 0 auto;}
.reserv-complete-cotn .box-info .essential-tit{ display: inline-block; margin-left: 3px; font-size: 12px; color: #4e81ff; }

.reserv-complete-cotn .box-info.subscriber{ position: relative; }
.reserv-complete-cotn .box-info.subscriber .box-dd{ padding-bottom: 0; }
.reserv-complete-cotn .box-info.subscriber .btn-edit{ position: absolute; top: 21px; right: 16px; width: 46px; line-height: 24px; text-align: center; font-size: 12px; color: #9da9be; border: 1px solid #9da9be; border-radius: 16px; }
.reserv-complete-cotn .box-info.subscriber .info{ position: relative; }
.reserv-complete-cotn .box-info.subscriber .info dt{ margin-bottom: 5px; font-size: 13px; line-height: 19px; color: #757f92;}
.reserv-complete-cotn .box-info.subscriber .info dd{ margin-bottom: 17px; }
.reserv-complete-cotn .box-info.subscriber .box-agree{ padding: 16px; margin-left: -16px; margin-right: -16px; background-color: #4e81ff; color: #fff;}
.reserv-complete-cotn .box-info.subscriber .box-agree p{ margin-bottom: 19px; font-size: 13px; font-weight: 400; }
.reserv-complete-cotn .box-info.subscriber .box-agree .form-chkbox span{ font-size: 14px; font-weight: 500; line-height: 18px; }
.reserv-complete-cotn .box-info.subscriber .box-agree .form-chkbox span::after{ background-image: url(@/assets/mobile/images/cmm/check-dis-w.png); }
.reserv-complete-cotn .box-info.subscriber .box-agree .form-chkbox input:checked ~ span::after{ background-image: url(@/assets/mobile/images/cmm/check-w.png); }

.reserv-complete-cotn .box-info.guest-info { position: relative; }
.reserv-complete-cotn .box-info.guest-info .box-item{}
.reserv-complete-cotn .box-info.guest-info .box-item:last-child dd{ margin-bottom: 0; }
.reserv-complete-cotn .box-info.guest-info .box-item .tit{ padding-top: 5px; margin-bottom: 14px; line-height: 24px; }
.reserv-complete-cotn .box-info.guest-info .box-item .tit span{ display: inline-block; }
.reserv-complete-cotn .box-info.guest-info .box-item .tit .size01{ position: relative; padding-right: 10px; font-size: 16px; font-weight: 500;  }
.reserv-complete-cotn .box-info.guest-info .box-item .tit .size01::after{ content: ''; position: absolute; top: 50%; right: 0; height: 16px; margin-top: -8px; border-left: 1px solid #c9cfd8; }
.reserv-complete-cotn .box-info.guest-info .box-item .tit .size02{padding-left: 10px; font-size: 14px; color: #757f92; }
.reserv-complete-cotn .box-info.guest-info .box-item dt{ position: relative; margin-bottom: 5px; font-size: 12px; font-weight: 700; color: #757f92; }
.reserv-complete-cotn .box-info.guest-info .box-item dd{ overflow: hidden; margin-bottom: 17px; }
.reserv-complete-cotn .box-info.guest-info .box-item input{ height: 46px; border: 0 none; border-bottom: 1px solid #ebeef3; width: 49%;}
.reserv-complete-cotn .box-info.guest-info .box-item input::placeholder{ color: #c9cfd8; }
.reserv-complete-cotn .box-info.guest-info .box-item input.name01{ float: left; }
.reserv-complete-cotn .box-info.guest-info .box-item input.name02{ float: right; }
.reserv-complete-cotn .box-info.guest-info .btn-search-person{ position: absolute; top: 3px; right: 0; width: 46px; line-height: 24px; border-radius: 16px; background-color: #4e81ff; text-align: center; font-size: 12px; color: #fff;}
.reserv-complete-cotn .box-info.req-list{ position: relative; padding-bottom: 30px; border-bottom: 0 none;}
.reserv-complete-cotn .box-info.req-list .desc-list{ margin-top: 24px; padding-top: 20px; border-top: 1px solid #ebeef3; }
.reserv-complete-cotn .box-info.req-list .list{ position: relative; }
.reserv-complete-cotn .box-info.req-list .list dt{ margin-bottom: 5px; font-size: 13px; color: #757f92;}
.reserv-complete-cotn .box-info.req-list .list dd{ margin-bottom: 17px }


/* 호텔 - 객실 정보 */
.reserv-complete-cotn .box-info.room{ position: relative; }
.reserv-complete-cotn .box-info.room .box-option{ overflow: hidden; margin-bottom: 8px; font-size: 13px; color: #757f92;}
.reserv-complete-cotn .box-info.room .box-option .rm{ position: relative; float: left; padding:0 11px 0 21px; background: url(@/assets/mobile/images/reserv/icn-room.png) 0 50% no-repeat; -webkit-background-size: 17px 13px; background-size: 17px 13px;}
.reserv-complete-cotn .box-info.room .box-option .rm::after{ content: ''; position: absolute; top: 2px; right: 0; height: 15px; border-left: 1px solid #e2e4e8; }
.reserv-complete-cotn .box-info.room .box-option .meal{ float: left; margin-left: 12px; padding-left: 17px; background: url(@/assets/mobile/images/reserv/icn-breakfast.png) 0 50% no-repeat; -webkit-background-size: 11px 13px; background-size: 11px 13px;}
.reserv-complete-cotn .box-info.room .name{ margin-bottom: 6px; font-size: 18px; font-weight: 500; line-height: 26px; }
.reserv-complete-cotn .box-info.room .free-cancel{ margin-bottom: 20px; font-size: 13px; line-height: 19px; color: #ff4e50; }
.reserv-complete-cotn .box-info.room .etc-btn{ overflow: hidden; margin-bottom: 15px;}
.reserv-complete-cotn .box-info.room .etc-btn .btn{ float: left; border: 1px solid #000; border-radius: 4px; text-indent: 8px; font-size: 12px; line-height: 20px; margin-right: 10px; background-repeat: no-repeat; background-position: 90% 50%; -webkit-background-size: 6px 8px; background-size: 6px 8px;}
.reserv-complete-cotn .box-info.room .etc-btn .btn.notice{ width: 79px; border-color: #f59c79; background-image: url(@/assets/mobile/images/reserv/arrow-orange.png); color: #f59c79;}
.reserv-complete-cotn .box-info.room .etc-btn .btn.special{ width: 58px; border-color: #4e81ff; background-image: url(@/assets/mobile/images/reserv/arrow-blue.png);  color: #4e81ff;}
.reserv-complete-cotn .box-info.room .box-desc{ position: relative; margin-bottom: 20px;}
.reserv-complete-cotn .box-info.room .box-desc p{ position: relative; padding-left: 14px; font-size: 13px; line-height: 19px; color: #757f92;}
.reserv-complete-cotn .box-info.room .box-desc p::before{ content: ''; position: absolute; top: 8px; left: 0; width: 5px; height: 5px; background-color: #757f92; }
.reserv-complete-cotn .box-info.room .total{ position: relative; padding: 22px 0 0;}
.reserv-complete-cotn .box-info.room .total::before{ content: ''; position: absolute; top: 0; left: -16px; right: -16px; border-top: 1px solid #ebeef3;}
.reserv-complete-cotn .box-info.room .total::after{ content: ''; clear: both; display: block; }
.reserv-complete-cotn .box-info.room .total .tit{ float: left; font-size: 16px; font-weight: 500;}
.reserv-complete-cotn .box-info.room .total .val{ float: right; font-size: 18px; font-weight: 700; color: #4e81ff;}
.reserv-complete-cotn .box-info.room-detail{ position: relative; }
.reserv-complete-cotn .box-info.room-detail .box-dt{ position: relative; padding: 20px 0 13px; margin: 0 16px 15px;  border-bottom: 1px solid #ebeef3;}
.reserv-complete-cotn .box-info.room-detail .box-dt .member{ position: absolute; top: 50%; left: 83px; margin-top: -7px; font-size: 14px; line-height: 20px;  color: #757f92; }
.reserv-complete-cotn .box-info.room-detail .box-dd dl{ overflow: hidden; }
.reserv-complete-cotn .box-info.room-detail .box-dd dt{ float: left; width: 67px; font-size: 13px; line-height: 19px; color: #9da9be;}
.reserv-complete-cotn .box-info.room-detail .box-dd dd{ float: left; width: calc(100% - 67px); margin-bottom: 10px; line-height: 19px;}
.reserv-complete-cotn .box-info.room-detail .box-dd dl:last-child dd{ margin-bottom: 0; }

.reserv-payment-cotn{ background-color: #fff; }
.reserv-payment-cotn .sec-bills-info .total{ margin-top: 5px; }
.reserv-payment-cotn .desc-date{ padding-top: 18px;  font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-payment-cotn .desc-date:last-child{ padding-top: 5px; margin-bottom: 40px; }
.reserv-payment-cotn .box-btn{ padding:0 16px 20px; }
.reserv-payment-cotn .box-btn .btns-cmm{ margin-bottom: 5px; }
.reserv-payment-cotn .bil-invoice{ position: fixed; bottom: 0; left: 0; right: 0; text-align: center;}

.reserv-payment-cancel{ position: relative; }
.reserv-payment-cancel .sec-tit{ margin-bottom: 18px; font-size: 16px; font-weight: 500; line-height: 24px; }
.reserv-payment-cancel .sec-penalty{ position: relative; padding: 25px 16px; border-bottom: 12px solid #ebeef3;}
.reserv-payment-cancel .sec-penalty .desc{ margin-bottom: 30px; font-weight: 300; line-height: 20px; }
.reserv-payment-cancel .sec-penalty .btn-request:not(:last-child){ margin-bottom: 30px; }
.reserv-payment-cancel .sec-penalty .onetoone{ font-size: 13px;line-height: 19px; color: #757f92; }
.reserv-payment-cancel .sec-penalty .onetoone .more{ color: #4e81ff; text-decoration: underline; }
.reserv-payment-cancel .sec-penalty .onetoone ~ .box-btns { margin-top: 35px; }
.reserv-payment-cancel .sec-penalty .btn-detail{ position: absolute; top: 28px; right: 16px; font-size: 13px; line-height: 19px; color: #757f92; text-decoration: underline;}
.reserv-payment-cancel .sec-penalty .box-btns:not(:last-child) { margin-bottom: 20px; }
.reserv-payment-cancel .sec-cancel{ position: relative; padding: 25px 16px; }
.reserv-payment-cancel .sec-cancel .box-none{ padding: 50px 0; font-size: 14px; font-weight: 300; color: #babdc3; text-align: center;}


.reserv-payment-cancel{ position: relative; }
.reserv-payment-cancel .sec-tit{ margin-bottom: 18px; font-size: 16px; font-weight: 500; line-height: 24px; }
.reserv-payment-cancel .sec-guide{ position: relative; padding: 20px 16px 25px; border-bottom: 12px solid #ebeef3}
.reserv-payment-cancel .sec-guide .desc{ margin-bottom: 15px; font-weight: 300; line-height: 20px; }
.reserv-payment-cancel .sec-guide .box-rule{ position: relative;  margin-bottom: 28px;}
.reserv-payment-cancel .sec-guide .box-rule .info{ margin-bottom: 10px; font-size: 13px; line-height: 19px; color: #ff4e50; }
.reserv-payment-cancel .sec-guide .box-rule .btns{ display: block; width: 77px; border: 1px solid #757f92; border-radius: 4px; line-height: 29px; color: #757f92; text-indent: 8px; font-size: 12px; background: url(@/assets/mobile/images/reserv/arrow-grey.png) 90% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px;}
.reserv-payment-cancel

/* 1:1문의 작성 */
.qna-write-cotn{ position: relative; padding: 25px 16px; }
.qna-write-cotn .custom-sel{ margin-bottom: 10px; border: 1px solid #ebeef3; border-radius: 5px;}
.qna-write-cotn .custom-sel .ui-selectmenu-button{ padding-top: 10px; padding-bottom: 10px; padding-left: 10px; }
.qna-write-cotn .custom-sel .ui-selectmenu-text{}
.qna-write-cotn .custom-sel select{}
.qna-write-cotn .form-cn textarea{ display: block; width: 100%; height: 209px; padding: 10px; border: 1px solid #ebeef3; border-radius: 5px; box-sizing: border-box;}
.qna-write-cotn .form-cn{ margin-bottom: 10px; }
.qna-write-cotn .form-cn .input-cmm input{ padding-left: 10px; padding-right: 10px; border: 1px solid #ebeef3; border-radius: 5px; box-sizing: border-box;}
.qna-write-cotn .form-cn.file{ position: relative; margin-bottom: 40px;}
.qna-write-cotn .form-cn.file .clearfix{ position: relative; }
.qna-write-cotn .form-cn.file .desc{ float: left; padding-top: 13px; margin-right: 110px; font-size: 12px; line-height: 18px; color: #9da9be;}
.qna-write-cotn .form-cn.file .box-file-upload{ position: absolute; top: 10px; right: 0; width: 98px; height: 40px; border-radius: 5px; line-height: 38px; border: 1px solid #4e81ff; text-align: center; font-weight: 500; color: #4e81ff;}
.qna-write-cotn .form-cn.file .box-file-upload input{ visibility: hidden; width: 0; height: 0; padding: 0; opacity: 0 }
.qna-write-cotn .alram-feedback{ position: relative; margin-bottom: 40px;}
.qna-write-cotn .alram-feedback dl{ margin-bottom: 30px; }
.qna-write-cotn .alram-feedback dt{margin-bottom: 17px; font-size: 15px; font-weight: 500; line-height: 22px; }
.qna-write-cotn .alram-feedback dd{ margin-bottom: 22px; }
.qna-write-cotn .alram-feedback dd:last-child{ margin-bottom: 0; }
.qna-write-cotn .alram-feedback .form-chkbox span{ width: 65px; }
.qna-write-cotn .alram-feedback .val{ display: inline-block; }
.qna-write-cotn .alram-feedback .desc{padding: 12px 16px; border-radius: 5px; background-color: #f2f4f9; }
.qna-write-cotn .alram-feedback .desc p{ margin-left: 8px; text-indent: -8px; font-size: 13px; line-height: 19px; color: #757f92;}
.qna-write-cotn .box-btn{  overflow: hidden;}
.qna-write-cotn .box-btn .btns-cmm{ float: left; width: 49%; }
.qna-write-cotn .box-btn .btns-cmm:last-child{ float: right; }

/* .호텔 예약 규정 약관 동의 */
.rule-agree-hotel .sec-wrap{ padding-left: 16px; padding-right: 16px;  }
.rule-agree-hotel .sec-wrap:first-child{ padding-top: 20px; }
.rule-agree-hotel .pg-tit{ margin-bottom: 20px; font-size: 16px; line-height: 24px; font-weight: 500; }
.rule-agree-hotel hr.line{ margin-top: 20px; margin-bottom: 20px; margin-left: -16px; margin-right: -16px; border: 0 none; border-bottom: 10px solid #f2f4f9; }
.rule-agree-hotel .info-pay{ position: relative; }
.rule-agree-hotel .info-pay .prod-item{ margin-bottom: 10px; overflow: hidden; font-size: 15px; line-height: 22px;}
.rule-agree-hotel .info-pay .prod-item .room{ display: inline-block; font-weight: 500; }
.rule-agree-hotel .info-pay .prod-item .date{ margin-left: 3px; display: inline-block; color: #9da9be; }
.rule-agree-hotel .info-pay .prod-item .multi{ margin-left: 3px; display: inline-block; color: #9da9be;}
.rule-agree-hotel .info-pay .prod-item strong{ font-weight: 700; }
.rule-agree-hotel .info-pay .col-items{ margin-bottom: 7px; overflow: hidden; font-size: 13px; line-height: 19px; color: #757f92;}
.rule-agree-hotel .info-pay .col-items .left-col{ float: left; }
.rule-agree-hotel .info-pay .col-items .right-col{ float: right; }
.rule-agree-hotel .info-pay .col-total{ overflow: hidden; margin-top: 5px; padding-top: 16px; border-top: 1px solid #ebeef3; }
.rule-agree-hotel .info-pay .col-total .tit{ float: left; }
.rule-agree-hotel .info-pay .col-total .price{ float: right; font-size: 16px; font-weight: 700; color: #4e81ff;}
.rule-agree-hotel .opinion-pay{}
.rule-agree-hotel .opinion-pay .essential{ font-size: 12px; color: #4e81ff; }
.rule-agree-hotel .opinion-pay .items{ position: relative; margin-bottom: 13px;}
.rule-agree-hotel .opinion-pay .items p{ position: relative; padding-left: 16px; font-size: 14px; line-height: 20px;}
.rule-agree-hotel .opinion-pay .items p::before{ content: ''; position: absolute; top: 50%; left: 0; width: 8px; height: 8px; border-radius: 50%; background-color: #ff4e50; margin-top: -4px; }
.rule-agree-hotel .opinion-pay .items p span{display: inline-block; margin-left: 5px; text-decoration: underline; font-weight: 700;}
.rule-agree-hotel .opinion-pay .box-text{ border: 1px solid #ebeef3; border-radius: 5px; overflow: hidden; }
.rule-agree-hotel .opinion-pay .box-text textarea{ box-sizing: border-box; height: 150px; padding: 12px; border: 0 none; width: 100%; }
.rule-agree-hotel .opinion-pay .box-text textarea:placeholder{ color: #c9cfd8; }

.rule-agree-hotel .policy-agree{ position: relative; background-color: #fff; margin-bottom: 20px;}
.rule-agree-hotel .policy-agree .box-all-chk{ position: absolute; top: 3px; right: 16px; }
.rule-agree-hotel .policy-agree .box-all-chk span{ font-weight: 500; }
.rule-agree-hotel .policy-agree .form-chkbox span{ padding-left: 30px; }
.rule-agree-hotel .policy-agree .box-cell{ position: relative; border-bottom: 1px solid #ebeef3;  }
.rule-agree-hotel .policy-agree .box-cell:first-child{ margin-top: 5px; border-top: 1px solid #ebeef3;  }
.rule-agree-hotel .policy-agree .box-tit{ padding-top: 16px; padding-bottom: 16px; }
.rule-agree-hotel .policy-agree .box-desc{ display: none; padding: 0 0 15px 30px; font-size: 13px; font-weight: 300; line-height: 19px;}
.rule-agree-hotel .policy-agree .btns-acoodi{ position: absolute; top: 15px; right: 0; width: 20px; height: 20px; background: url(@/assets/mobile/images/cmm/ico_select.png) 50% 50% no-repeat; -webkit-background-size: 8px 4px; background-size: 8px 4px;}
.rule-agree-hotel .policy-agree .all-check{ padding: 19px 0 0;  font-weight: 500;}
.rule-agree-hotel .policy-agree .box-cell.active .box-desc{ display: block; }
.rule-agree-hotel .policy-agree .box-cell.active .btns-acoodi{
-webkit-transform: rotateZ(180deg);
-ms-transform: rotateZ(180deg);
-o-transform: rotateZ(180deg);
transform: rotateZ(180deg);
}
.rule-agree-hotel .agree-pay{ margin-bottom: 40px; }
.rule-agree-hotel .agree-pay .inner{ padding: 16px; background-color: #4e81ff; }
.rule-agree-hotel .agree-pay .inner .desc{ margin-bottom: 19px; font-size: 13px; color: #fff; }
.rule-agree-hotel .agree-pay .inner .form-chkbox span{ color: #fff; font-weight: 500; }
.rule-agree-hotel .agree-pay .inner .form-chkbox span::after {background-image: url(@/assets/mobile/images/cmm/check-dis-w.png);}
.rule-agree-hotel .agree-pay .inner .form-chkbox input:checked ~ span::after {background-image: url(@/assets/mobile/images/cmm/check-w.png);}
.rule-agree-hotel .agree-pay .etc-desc{ padding: 10px 0; border-bottom: 1px solid #fff; background-color: #3f4e73; font-size: 13px; text-align: center;color: #fff;}
.rule-agree-hotel .agree-pay .etc-desc span{ color: #ffd53f; font-weight: 700;}
.rule-agree-hotel .btn-reserv-request{ display: block; margin: 0 auto 25px; }

/* 선택한 스케줄 상세 정보  RVYN-1036*/
.box-info.comment{border-bottom: 0;}
.reserv-selected-schedule .sec-qna-list .box-top{border-top: 1px solid #EBEEF3}
.reserv-selected-schedule.reserv-complete-cotn{padding-bottom: 0;}
.box-dt .comment-info{font-size: 14px; font-weight: 350;margin-top:10px;margin-bottom: 5px;}
/* -- 선택한 스케줄 상세 정보*/

/* DT-12908 */
/* 체크박스 추가 */
.reserv-complete-cotn .box-info.guest-info .box-item .box-agree{ margin-bottom: 15px; }

/* 결제수단 추가 */
.rule-agree-hotel .payment-info .pg-tit{ margin-bottom: 16px; }
.rule-agree-hotel .payment-info .items p{ margin-bottom: 8px; font-size: 13px; color: #757f92; }
.rule-agree-hotel .payment-info .select-cmm select { border: 1px solid #c9cfd8; padding-left: 20px; border-radius: 22px; }
.rule-agree-hotel .payment-info .select-cmm::before{ right: 20px; }

.reserv-complete-cotn .box-info.passenger .btn-edit-passenger{ margin-bottom: 20px; }

.sec-bottom-btns.btn-double{ padding: 0 16px; }
/* // DT-12908 */


/* DT-18341 - 20231107 */
.view-state { position: absolute; top: 24px; right: 16px; font-size: 0; font-weight: 500; line-height: 0; letter-spacing: -.3px; color: #ffffff; }
.view-state .pay { display: inline-block; padding-right: 8px; padding-left: 0; font-size: 13px; line-height: 15px; }
.view-state .state { position: relative; display: inline-block; padding-left: 8px; font-size: 13px; line-height: 15px; }
.view-state .pay + .state::after { content: ''; position: absolute; top: 50%; left: 0; height: 12px; transform: translateY(-50%); border-left: 1px solid #EBEEF3; }

.reserv-complete-cotn .box-info.reserv .btn-wrap { margin-top: 40px; text-align: center; }
.reserv-complete-cotn .box-info.reserv .btn-wrap .btns-cmm { width: 210px; }
/* // DT-18341 - 20231107 */

/* DT-25030 예약요청 */
.reserv-complete-cotn .box-dt .txt-info {
	margin-top: 8px;
	font-size: 13px;
	line-height: 19px;
	font-weight: 400;
	color: #757f92;
}

.reserv-complete-cotn .added {
	position: relative;
}

.reserv-complete-cotn .added .box-dt .txt-info {
	color: #557ffe;
}

.reserv-complete-cotn .added .box-dt {
	padding-bottom: 8px;
}

.reserv-complete-cotn .btn-file-group label {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	font-size: 15px;
	font-weight: 500;
	line-height: 48px;
	border: 1px solid #4E81FF;
	background-color: #fff;
	color: #4E81FF;
	width: 100%;
	box-sizing: border-box;
	cursor: pointer;
	border-radius: 26px;
	-webkit-appearance: button;
}

.reserv-complete-cotn .btn-file-group .btn-file {
	position: absolute;
	width: 0;
	height: 0;
	padding: 0;
	overflow: hidden;
	border: 0;
}

.reserv-complete-cotn .btn-edit-document {
	position: absolute;
	top: 20px;
	right: 16px;
	border: 1px solid #3f4e73;
	border-radius: 2px;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 62px;
	height: 22px;
	box-sizing: border-box;
	font-size: 12px;
	line-height: 17px;
	font-weight: 500;
	color: #3f4e73;
}

.reserv-complete-cotn .file-list {
	overflow-y: auto;
	padding-top: 8px;
	border-top: 1px solid #ebeef3;
	max-height: 207px;
	margin-bottom: 22px;
}

.reserv-complete-cotn .file-list li {
	padding: 10px 0;
	border-bottom: 1px solid #ebeef3;
}

.reserv-complete-cotn .file-list .file-name {
	font-size: 13px;
	line-height: 19px;
	font-weight: 400;
	color: #222;
	letter-spacing: -0.28px;
}

.reserv-complete-cotn .box-info.req-list .btns-cmm + .list {
	margin-top: 24px;
}

.layer-pages.edit-document .document-list-outer {
	margin-left: -16px;
	margin-right: -16px;
	padding: 15px 16px;
	border-bottom: 1px solid #ebebeb;
	margin-bottom: 15px;
	overflow-y: auto;
	max-height: 250px;
}

.layer-pages.edit-document .document-list .form-chkbox {
	display: block;
}

.layer-pages.edit-document .document-list .form-chkbox span {
	display: block;
	padding: 16px 42px 16px 8px;
	border-radius: 5px;
	font-size: 13px;
	font-weight: 400;
	color: #757f92;
	background-color: #fff;
}

.layer-pages.edit-document .document-list .form-chkbox span:after {
	right: 8px;
	left: auto;
	top: 50%;
	margin-top: -9px;
}

.layer-pages.edit-document .document-list .form-chkbox input[type="checkbox"]:checked + span {
	background-color: #F2F5FF;
}

.form-doc-number li .doc-number {
	display: flex;
	align-items: center;
	width: 100%;
	height: 40px;
	padding: 0;
	margin: 0;
	border: 0 none;
	border-bottom: 1px solid #ebeef3;
	font-size: 15px;
}

.form-doc-number li .input-cmm, .form-doc-number li .doc-number {
	margin-bottom: 16px;
}

.form-doc-number li:last-of-type .input-cmm, .form-doc-number li:last-of-type .doc-number {
	margin-bottom: 6px;
}

.form-doc-number + .btns-cmm {
	margin-top: 16px;
}

.reserv-complete-cotn .total-price .box-dt .val.group.sales-price .prev {
	line-height: 20px;
}

.reserv-complete-cotn .total-price .box-dt .tit .sub.block {
	display: flex;
	margin-top: 3px;
	margin-left: 0;
	line-height: 17px;
	align-items: center;
	gap: 4px;
}

.reserv-complete-cotn .total-price .box-dt .tit .sub.block + .ico-qustion-circle {
	margin-left: 0;
}

.reserv-complete-cotn .total-price .box-dt.visible {
	overflow: visible;
}

.reserv-complete-cotn .total-price .box-dt.visible:after {
	content: '';
	display: block;
	clear: both;
}

.rule-agree-hotel .info-pay .col-total.sales {
	overflow: visible;
}

.rule-agree-hotel .info-pay .col-total.sales:after {
	content: '';
	display: block;
	clear: both;
}

.rule-agree-hotel .info-pay .col-total.sales .tit {
	font-weight: 500;
}

.stay-autocomplete-paper {
	background-color: #fff;
	color: rgba(0, 0, 0, 0.87);
	-webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
	border-radius: 4px;
	box-shadow:
			0px 2px 1px -1px rgba(0, 0, 0, 0.2),
			0px 1px 1px 0px rgba(0, 0, 0, 0.14),
			0px 1px 3px 0px rgba(0, 0, 0, 0.12);
	font-family: "Roboto", "Helvetica", "Arial", sans-serif;
	font-weight: 400;
	font-size: 1rem;
	line-height: 1.5;
	letter-spacing: 0.00938em;
	overflow: auto;
}
.stay-autocomplete-listbox {
	list-style: none;
	margin: 0;
	padding: 8px 0;
	max-height: 40vh;
	overflow: auto;
	position: relative;
}
.stay-autocomplete-option {
	padding: 5px 10px;
	cursor: pointer;
}
.stay-autocomplete-option:hover {
	background-color: #0000000a;
}
.clear-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
}