/* 사용자 지정 css */
@charset "utf-8";
.pg-login{ position: relative; min-height: 600px;}
.pg-login h1{ padding: 40px 0; text-align: center;}
.pg-login h2.title{ margin-bottom: 50px; font-size: 26px; font-weight: 500; line-height: 38px; }
.pg-login .contents{ }
.pg-login input::placeholder{color: #c9cfd8; font-weight: 300;}
.pg-login .id{ position: relative; margin-bottom: 15px; }
.pg-login .id input{ height: 40px; padding-right: 35%; -webkit-box-sizing: border-box; width: 40%;
    -moz-box-sizing: border-box;
    box-sizing: border-box;}
.pg-login .emailBox input{ height: 40px; padding-right: 35%; -webkit-box-sizing: border-box; width: 100%;
    -moz-box-sizing: border-box;
    box-sizing: border-box;}
.pg-login .id .mail{ position: absolute; top: 10px; right: 0; }
.pg-login .btn-func{ padding-top: 40px; text-align: center; }
.pg-login .validate{ padding-top: 20px; font-size: 12px; }

.login-cotn{ position: relative; }
.login-cotn .pw{ position: relative; }
.login-cotn .pw input{ height: 40px;}
.login-cotn .etc-col{ overflow: hidden; }
.login-cotn .etc-col .btns{ text-align: center; padding-top: 34px;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
}
.login-cotn .etc-col .btns a{ position: relative; width: 50%; font-size: 13px; font-weight: 300; line-height: 19px; color: #9da9be; }
.login-cotn .etc-col .btns a:first-child::after{ content: ''; position: absolute; top: 50%; right: 0; width: 1px; height: 12px; margin-top: -6px; background-color: #9da9be;}

.join-cotn{ position: relative; padding: 40px 0 0 0 !important ; width: 1080px; margin: 0 auto;}
.join-cotn .box-division{ position: relative; padding: 18px 0 40px; border-top: solid 2px #ebeef3;}
.join-cotn .box-division:first-child{ border-top: solid 2px #ebeef3;}
.join-cotn .box-division .tit{margin-bottom: 32px; line-height: 25px; font-size: 17px; font-weight: 500; }
.join-cotn .box-division dl{ position: relative; }
.join-cotn .box-division dl::after{ content: ''; display: block; clear: both; }
.join-cotn .box-division dt .essential{ display: inline-block; color: #4e81ff; }
.join-cotn .box-division dt{ clear: both; float: left; width: 130px; margin-top: 13px; color: #757f92; line-height: 32px;}
.join-cotn .box-division dd{ float: left; width: 950px; margin-top: 13px; font-size: 15px; line-height: 32px;}
.join-cotn .box-division dt:first-child,
.join-cotn .box-division dt:first-child + dd{ margin-top: 0; }
.join-cotn .box-division dd *{ font-size: 15px; line-height: 32px;}
.join-cotn .box-division dd.gender .form-radio{ float: left; margin-right: 40px;}
.join-cotn .box-division .mail span{ float: left; }
.join-cotn .box-division .mail .btn-default{ float: left; margin-left: 14px;}
.join-cotn .box-division .mail .time{ margin-left: 10px; font-size: 12px; line-height: 36px; color:#4e81ff; }
.join-cotn .box-division .mail .validate{ clear: both; font-size: 12px;}
.join-cotn .box-division .code .btn-default{ margin-left: 3px; }
.join-cotn .box-division .btn-white{ width: 98px; line-height: 34px; border: 1px solid #4e81ff; border-radius: 5px; text-align: center;color: #4e81ff; font-size: 13px; font-weight: 500;}
.join-cotn .box-division .btn-gray{ width: 98px; line-height: 34px; border: 1px solid #babdc3; border-radius: 5px; text-align: center;color: #fff; font-size: 13px; font-weight: 500; background-color: #babdc3;}
.join-cotn .box-col{ float: left; width: 210px; margin-left: 33px; }
.join-cotn .box-col:first-child{ margin-left: 0; }
.join-cotn .box-col.box-ymd{ border-bottom: 1px solid #e2e4e8; }
.join-cotn .box-col.box-ymd .form-select{ border-bottom: 0 none; margin-right: 15px;}
.join-cotn .box-col .form-chkbox span::after{ top: 50%; }
.join-cotn .box-col.tel{border-bottom: 1px solid #e2e4e8; }
.join-cotn .box-col.tel .form-select{ border-bottom: 0 none; }
.join-cotn .box-col.tel input{ width: 80px; padding-left: 15px; border-bottom: 0 none;}
.join-cotn .desc-essential{ position: absolute; top: 20px; right: 0; line-height: 20px; color: #757f92;}
.join-cotn .desc-essential span{ color: #4e81ff; }
.join-cotn .form-chkbox span::after{ top: 50%; margin-top: -9px; }
.join-cotn .btns-bottom { text-align: center; padding-top: 40px; padding-bottom: 100px; }
.join-cotn .btns-bottom .btn-blue{ width: 140px; line-height: 50px; border-radius: 5px; background-color: #4e81ff; font-size: 16px; font-weight: 500; color: #fff; }

.member-join-cotn h2.title{ margin-bottom: 25px; font-size: 18px; font-weight: 500;line-height: 26px; }
.member-join-cotn .box-info .btns-cmm { line-height: 40px; box-sizing:  border-box; font-size: 14px;}
.member-join-cotn .form-tit { margin-bottom: 5px; }
.member-join-cotn .validate{ padding-top: 0; }
.member-join-cotn .box-info{ padding: 20px 16px; border-bottom: 12px solid #f2f4f9; }
.member-join-cotn .box-info.company{ border-bottom: 0 none; }
.member-join-cotn .box-email{position: relative; margin-bottom: 24px;}
.member-join-cotn .box-email .form-cn{ margin-bottom: 0; }
.member-join-cotn .box-email input{ box-sizing: border-box; }
.member-join-cotn .box-email .address{ position: absolute; top: 9px; right: 0; font-size: 15px;  line-height: 22px; }
.member-join-cotn .box-email .btns-cmm{ margin-top: 10px; }
.member-join-cotn .box-email .return-time{ padding-top: 2px; font-size: 12px; line-height: 18px; color: #4e81ff; }
.member-join-cotn .box-certify{ position: relative; }
.member-join-cotn .box-certify .form-cn .input-cmm{ display: block; margin-right: 105px; }
.member-join-cotn .box-certify .form-cn .val{ font-size: 12px;line-height: 18px; color: #9da9be; }
.member-join-cotn .box-certify .btns-cmm{ position: absolute; top: 0; right: 0; width: 100px;}
.member-join-cotn .box-certify .btns-cmm[disabled]{ background-color: #9da9be; border-color: #9da9be; color: #fff;}
.member-join-cotn .box-btn{ text-align: center; padding-bottom: 25px; }

/* 회원가입 완료 */
.complete-join-cotn{ position: relative; padding: 20px;}
.complete-join-cotn dt{ padding: 40px 0 43px; font-size: 22px; font-weight: 500; line-height: 33px; }
.complete-join-cotn dd{ margin-bottom: 50px; font-size: 13px; line-height: 19px; color: #757f92; }
.complete-join-cotn dd span{ color: #ff4e50; }
.complete-join-cotn .box-btn{ text-align: center; }


.contents .pay-card{ padding:0 16px 20px; background-color: #fff; }
.contents .pay-card .tit{ margin-bottom: 24px; font-size: 18px; font-weight: 500; line-height: 26px; }
.contents .pay-card .form-tit{ position: relative; }
.contents .pay-card .form-cn{}
.contents .pay-card .box-tab{ overflow: hidden; padding-top: 12px; margin-bottom: 24px;}
.contents .pay-card .box-tab label{   }
.contents .pay-card .box-tab input{ width: 0; height: 0; margin: 0; padding: 0; opacity: 0;visibility: hidden; }
.contents .pay-card .box-tab .txt{ display: inline-block; width: 100%; font-size: 13px; font-weight: 500; letter-spacing: -0.5px; color: #babdc3; line-height: 40px; border: 1px solid #c9cfd8; text-align: center; box-sizing: border-box;}
.contents .pay-card .box-tab .personal{ border-radius: 5px; }
.contents .pay-card .box-tab .personal .txt{ border-radius: 5px; }
.contents .pay-card .box-tab .law{  }
.contents .pay-card .box-tab .law.pr .txt{border-radius: 5px 0 0 5px; }
.contents .pay-card .box-tab .law.no .txt{ border-radius: 0 5px 5px 0; }
.contents .pay-card .box-left{ float: left; width: 33%;}
.contents .pay-card .box-right{ float: right; width: 65%;}
.contents .pay-card .box-right label{ float: left; width: 50%; display: block;}
.contents .pay-card .box-tab input:checked ~ .txt{ border-color: #4e81ff; background-color: rgba(78, 129, 255, 0.07); color: #4e81ff; }
.contents .pay-card .all-check{ -ms-word-break: keep-all; word-break: keep-all; }

.box-dt .txt-info{margin-top: 8px; font-size: 13px; line-height: 19px; font-weight: 400; color: #757f92;}
.added{position: relative;}
.added .box-dt .txt-info{color: #557ffe;}
.added .box-dt{padding-bottom: 8px;}
.btn-file-group label{display: inline-flex; align-items: center; justify-content: center; font-size: 15px; font-weight: 500; line-height: 48px; border: 1px solid #4E81FF; background-color: #fff; color: #4E81FF; width: 100%; box-sizing: border-box; cursor: pointer; border-radius: 26px; -webkit-appearance: button;}
.btn-file-group .btn-file{position: absolute; width: 0; height: 0; padding: 0; overflow: hidden; border: 0;}
.btn-edit-document{position:absolute; top:20px; right:16px; border: 1px solid #3f4e73; border-radius: 2px; display: inline-flex; align-items: center; justify-content: center; width: 62px; height: 22px; box-sizing: border-box; font-size: 12px; line-height: 17px; font-weight: 500; color: #3f4e73;}
.file-list{overflow-y: auto; padding-top: 8px; border-top: 1px solid #ebeef3; max-height: 207px; margin-bottom: 22px;}
.file-list li{padding: 10px 0; border-bottom: 1px solid #ebeef3;}
.file-list .file-name{font-size: 13px; line-height: 19px; font-weight: 400; color: #222; letter-spacing: -0.28px;}
.box-info.req-list .btns-cmm + .list{margin-top: 24px;}
.layer-pages.edit-document .document-list-outer{margin-left: -16px; margin-right:-16px; padding:15px 16px; border-bottom: 1px solid #ebebeb; margin-bottom: 15px; overflow-y: auto; max-height: 250px;}
.layer-pages.edit-document .document-list .form-chkbox{display: block;}
.layer-pages.edit-document .document-list .form-chkbox span{display: block; padding: 16px 42px 16px 8px; border-radius: 5px; font-size: 13px; font-weight: 400; color:#757f92; background-color: #fff;}
.layer-pages.edit-document .document-list .form-chkbox span:after{right:8px; left: auto; top: 50%; margin-top: -9px;}
.layer-pages.edit-document .document-list .form-chkbox input[type="checkbox"]:checked + span{background-color: #F2F5FF;}
.form-doc-number li .doc-number{display:flex; align-items: center; width: 100%; height: 40px; padding: 0; margin: 0; border: 0 none; border-bottom: 1px solid #ebeef3; font-size: 15px; }


.search-ticket-cn[data-ticket-type="type-oneway"] .arr{ text-align: right }
.search-ticket-cn[data-ticket-type="type-oneway"] .sec-city .arrival-before{ margin-left: -13px;left: 50%;position: absolute;margin-top: 7px;border: 33px;width: 26px;height: 23px; }
.search-ticket-cn[data-ticket-type="type-round"] .arr{ text-align: right }
.search-ticket-cn[data-ticket-type="type-round"] .sec-city .arrival-before{ margin-left: -13px;left: 50%;position: absolute;margin-top: 7px;border: 33px;width: 26px;height: 23px; }
.search-ticket-cn[data-ticket-type="type-multi"] .arr{ text-align: center }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-city .arrival-before{ margin-left: -13px;left: 30%;position: absolute;margin-top: 7px;border: 33px;width: 26px;height: 23px; }