/* 미주 내 체류지 정보 */
.layer-pages.traveler-modify {
}
.layer-pages.traveler-modify .layer-head {
  text-align: center;
}
.layer-pages.traveler-modify .layer-cotn {
  padding-left: 16px;
  padding-right: 16px;
}
.layer-pages.traveler-modify .form-tit:first-child {
  padding-top: 29px;
}
.layer-pages.traveler-modify .search-form {
  position: relative;
  z-index: 10;
}
.layer-pages.traveler-modify .search-form .box-city-list {
  display: none;
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  height: calc(100vh - 200px);
  background-color: #fff;
  overflow-y: auto;
}
.layer-pages.traveler-modify .search-form .box-city-list li {
}
.layer-pages.traveler-modify .search-form .box-city-list li button {
  outline: none;
  display: block;
  width: 100%;
  line-height: 40px;
  font-size: 15px;
  text-align: left;
}
.layer-pages.traveler-modify .search-form .box-city-list .point {
  color: #4e81ff;
}
.layer-pages.traveler-modify .box-btn {
  text-align: center;
}

@media (max-width: 480px) {
  .mileage .content {
    display: block;
  }
}

@media (min-width: 480px) {
  .mileage .content {
    display: flex;
  }
}

.mileage .content span {
  width: 50px;
  margin-right: 5px;
  margin-left: 5px;
  font-size: 13px;
}

.mileage .content input[type="text"] {
  height: 34px;
  border: 0 none;
  border-bottom: 1px solid #e2e4e8;
  outline: none;
  padding-left: 5px;
  margin-right: 5px;
}

.layer-pages.traveler-modify .desc-passport {
  position: relative;
  padding: 12px;
  margin-bottom: 15px;
  border-radius: 5px;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
  background-color: #f2f4f9;
}
.layer-pages.traveler-modify .desc-passport strong {
  color: #4e81ff;
}
