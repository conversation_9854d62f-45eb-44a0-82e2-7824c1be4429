@charset "utf-8";

.date-picker {
    width: 170px;
    height: 25px;
    padding: 0;
    border: 0;
    line-height: 25px;
    padding-left: 10px;
    font-size: 12px;
    font-family: Arial,sans-serif;
    font-weight: 700;
    cursor: pointer;
    color: #303030;
    position: relative;
    z-index: 2}
.date-picker-wrapper { padding-bottom: 50px;
    position: relative;
    width: 100%;
    z-index: 9;
    /*border: 1px solid #bfbfbf;*/
    font-size: 12px;
    line-height: 20px;
    color: #aaa;
    font-family: Arial,sans-serif;
    -webkit-box-sizing: initial;
    box-sizing: initial;
}
.dp-clearfix {
    clear: both;
    height: 0;
    font-size: 0}
.date-picker-wrapper.inline-wrapper {
    position: relative;
    -webkit-box-shadow: none;
    box-shadow: none;
    display: inline-block}
.date-picker-wrapper.single-date {
    width: 100%}
.date-picker-wrapper.no-shortcuts {
    /*padding-bottom: 12px*/
}
.date-picker-wrapper.no-topbar {
    padding-top: 12px}
.date-picker-wrapper .footer {
    display: none;
    font-size: 11px;
    padding-top: 3px}
.date-picker-wrapper b {
    color: #666;
    font-weight: 700}
.date-picker-wrapper a {
    color: #6bb4d6;
    text-decoration: underline}
.date-picker-wrapper .month-name {
    text-transform: uppercase;
    text-align: left !important;
}
.date-picker-wrapper .select-wrapper {
    position: relative;
    /*overflow: hidden;*/
    display: inline-block;
    vertical-align: middle}
.date-picker-wrapper .select-wrapper:hover {
    text-decoration: underline}
.date-picker-wrapper .month-element {
    display: none;
    font-size: 18px;
    color: #333333;
    font-weight: 400;
    display: inline-block;
    vertical-align: middle
}
.date-picker-wrapper .month-element:first-child{
     display: none;
}
.date-picker-wrapper .month-text {
    display: none;
    font-size: 20px;
    color: #333333;
    font-weight: 400;
    vertical-align: bottom
}
.date-picker-wrapper .select-wrapper select {
    position: absolute;
    margin: 0;
    padding: 0;
    left: 0;
    top: -1px;
    font-size: inherit;
    font-style: inherit;
    font-weight: inherit;
    text-transform: inherit;
    color: inherit;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: 0 0;
    border: 0;
    outline: 0;
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=1)";
    filter:alpha(opacity=1);
    opacity: .01}
.date-picker-wrapper .month-wrapper {
    width: 100% !important;
    /*height: 320px;*/
    border: none;
    /*border-radius: 3px;*/
    /*background-color: #fff;*/
    /*padding: 5px;*/
    cursor: default;
    position: relative;
    /*_overflow: hidden*/
}
.date-picker-wrapper .month-wrapper table {
    width: 100%;
    /*float: left;*/
    /*padding: 20px;*/
    /*margin-left: 20px;*/
    margin-top: 25px;
    box-sizing: border-box;
}
.date-picker-wrapper .month-wrapper table.month2 {
    width: 100%;
    /*float: right;*/
    /*padding: 20px;*/
    /*margin-right: 20px;*/
    margin-top: 25px;
    box-sizing: border-box;
}

.date-picker-wrapper .month-wrapper table td,
.date-picker-wrapper .month-wrapper table th {
    vertical-align: top;
    text-align: center;
    line-height: 14px;
    width: 30px;
    margin: 0;
    padding: 8px 0 ;
}

.date-picker-wrapper .month-wrapper table th{
    line-height: 30px;
    font-size: 12px;
}
.date-picker-wrapper .month-wrapper table td{
    line-height: 26px;
    width: 38px;
}
.date-picker-wrapper .month-wrapper table .day {
    position: relative;
    font-size: 15px;
    line-height: 16px;
    padding: 12px 0 12px;
    /*margin: 2px 0;*/
    color: #e1e4e8;
    font-weight: normal;
    cursor: default;
}
.date-picker-wrapper .month-wrapper table .day.vaild.tmp {
    color: #999999 !important;
}
.date-picker-wrapper .month-wrapper table div.day.lastMonth,
.date-picker-wrapper .month-wrapper table div.day.nextMonth {
    color: #999;
    cursor: default}
.date-picker-wrapper .month-wrapper table .day.checked {
    background-color: #f2f6ff;
}
.date-picker-wrapper .month-wrapper table td.checked{
    background: url('@/assets/mobile/images/cmm/bg_cal_sel.png') 0 50% no-repeat;
}
.date-picker-wrapper .month-wrapper table .week-name {
    display: none;
    height: 30px;
    line-height: 30px;
    font-weight: 100;
    text-transform: uppercase;
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid #e3e3e3;
}
.date-picker-wrapper .month-wrapper table .day.has-tooltip {
    cursor: help!important}
.date-picker-wrapper .month-wrapper table .day.has-tooltip .tooltip {
    white-space: nowrap}
.date-picker-wrapper .time label {
    white-space: nowrap}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid {
    position: relative;
    margin: 0 auto;
    color: #333;
    font-size: 15px;
    line-height: 16px;
    cursor: pointer;
    width: 40px;
    margin: 0 auto;
    border-radius: 50%;
}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid.checked:after {content: ''; position: absolute; background-color: #d9edf7; left: -7px; top: 0; width: 7px; height: 100%; display: none;}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid.checked.last-date-selected:after {content: ''; position: absolute; background-color: #d9edf7; left: -12px; top: 0; width: 12px; height: 100%; display: none;}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid.checked.real-today:after {content: ''; position: absolute; background-color: #d9edf7; left: -12px; top: 0; width: 12px; height: 100%;}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid.checked.first-date-selected:after {display: none;}
.date-picker-wrapper .month-wrapper table .day.toMonth.valid.real-today:before {
     content: 'TODAY'; display: block; position: absolute; width: 100%; text-align: center; color: #4e81ff; font-size: 10px; top: 40px; left: 0; width: 100%; text-align: center; background: none;
}
.date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected:before {
    content: '가는날'; display: block; position: absolute; width: 100%; text-align: center; color: #4e81ff; font-size: 11px; top: 40px; left: 0; width: 100%; text-align: center; background: none;
}
.date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected:before {
    content: '오는날'; display: block; position: absolute; width: 100%; text-align: center; color: #4e81ff; font-size: 11px; top: 40px; left: 0; width: 100%; text-align: center; background: none;
}
.date-picker-wrapper .month-wrapper table .day.toMonth.same-date-selected:before {
    content: '출/도착'; display: block; position: absolute; width: 100%; text-align: center; color: #4e81ff; font-size: 11px; top: 40px; left: 0; width: 100%; text-align: center; background: none;
}
.date-picker-wrapper .month-wrapper table .day.toMonth.hovering {
    background-color: #cdecfa;
    border-color: #cdecfa;
}
.date-picker-wrapper .month-wrapper table .day.lastMonth,
.date-picker-wrapper .month-wrapper table .day.nextMonth {
    display: none}
.date-picker-wrapper .month-wrapper table .day.real-today {
    /*background-color: #333333;*/
    color: #4e81ff !important;
    /*border-left: 6px solid #f2f2f2;*/
    /*border-right: 6px solid #f2f2f2;*/
}
.date-picker-wrapper .month-wrapper table .day.real-today.checked,
.date-picker-wrapper .month-wrapper table .day.real-today.hovering {
    background-color: #333333;
    border-color: #d9edf7;
}
.date-picker-wrapper .month-wrapper table .day.real-today.first-date-selected.bg-line.checked:after,
.date-picker-wrapper .month-wrapper table .day.real-today.first-date-selected.bg-line.hovering:after {content: ''; position: absolute; background-color: #d9edf7; left: auto; right: -7px; top: 0; width: 7px; height: 100%;}
.date-picker-wrapper .month-wrapper table .day.real-today.last-date-selected.checked:after,
.date-picker-wrapper .month-wrapper table .day.real-today.last-date-selected.hovering:after {content: ''; position: absolute; background-color: #d9edf7; left: -7px; top: 0; width: 7px; height: 100%;}

.date-picker-wrapper .month-wrapper table .day.real-today.first-date-selected {border-color: #f2f2f2;}
.date-picker-wrapper .month-wrapper table .day.real-today.last-date-selected {border-right: 6px solid #f2f2f2;}

.date-picker-wrapper table .caption {
    height: 45px}
.date-picker-wrapper table .caption > th:first-of-type,
.date-picker-wrapper table .caption > th:last-of-type {
    width: 27px}
.date-picker-wrapper table .caption .next,
.date-picker-wrapper table .caption .prev {
    display: none !important;
    padding: 0 5px;
    display: block;
    position: absolute;
    width: 21px; height: 21px;
    cursor: pointer;
}
.date-picker-wrapper table .caption .next:hover,
.date-picker-wrapper table .caption .prev:hover {
    background-color: #ccc;
    color: #fff}
.date-picker-wrapper .month-wrapper table td:first-child .day.toMonth.valid {color: #f23030;}
.date-picker-wrapper .month-wrapper table td:last-child .day.toMonth.valid {/*color: #4a90e2;*/}

.date-picker-wrapper .gap { display: none;
    position: relative;
    z-index: 1;
    width: 15px;
    height: 100%;
    background-color: red;
    font-size: 0;
    line-height: 0;
    float: left;
    top: -5px;
    margin: 0 10px -10px;
    visibility: hidden;
    height: 0}
.date-picker-wrapper .gap .gap-lines {
    height: 100%;
    /*overflow: hidden*/
}
.date-picker-wrapper .gap .gap-line {
    height: 15px;
    width: 15px;
    position: relative}
.date-picker-wrapper .gap .gap-line .gap-1 {
    z-index: 1;
    height: 0;
    border-left: 8px solid #fff;
    border-top: 8px solid #eee;
    border-bottom: 8px solid #eee}
.date-picker-wrapper .gap .gap-line .gap-2 {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 2;
    height: 0;
    border-left: 8px solid transparent;
    border-top: 8px solid #fff}
.date-picker-wrapper .gap .gap-line .gap-3 {
    position: absolute;
    right: 0;
    top: 8px;
    z-index: 2;
    height: 0;
    border-left: 8px solid transparent;
    border-bottom: 8px solid #fff}
.date-picker-wrapper .gap .gap-top-mask {
    width: 6px;
    height: 1px;
    position: absolute;
    top: -1px;
    left: 1px;
    background-color: #eee;
    z-index: 3}
.date-picker-wrapper .gap .gap-bottom-mask {
    width: 6px;
    height: 1px;
    position: absolute;
    bottom: -1px;
    left: 7px;
    background-color: #eee;
    z-index: 3}
.date-picker-wrapper .selected-days {
    display: none}
.date-picker-wrapper .drp_top-bar { display: none;
    line-height: 1.4;
    position: relative;
    padding: 10px 40px 10px 0}
.date-picker-wrapper .drp_top-bar .error-top,
.date-picker-wrapper .drp_top-bar .normal-top {
    display: none}
.date-picker-wrapper .drp_top-bar .default-top {
    display: block}
.date-picker-wrapper .drp_top-bar.error .default-top {
    display: none}
.date-picker-wrapper .drp_top-bar.error .error-top {
    display: block;
    color: red}
.date-picker-wrapper .drp_top-bar.normal .default-top {
    display: none}
.date-picker-wrapper .drp_top-bar.normal .normal-top {
    display: block}
.date-picker-wrapper .drp_top-bar.normal .normal-top .selection-top {
    color: #333}
.date-picker-wrapper .drp_top-bar .apply-btn {
    position: absolute;
    right: 0;
    top: 6px;
    padding: 3px 5px;
    margin: 0;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    color: #d9eef7;
    border: solid 1px #0076a3;
    background: #0095cd;
    background: -moz-linear-gradient(top,#00adee,#0078a5);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#00adee', endColorstr='#0078a5');
    color: #fff;
    line-height: initial}
.date-picker-wrapper .drp_top-bar .apply-btn.disabled {
    cursor: pointer;
    color: #606060;
    border: solid 1px #b7b7b7;
    background: #fff;
    background: -moz-linear-gradient(top,#fff,#ededed);
    filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#ededed')}
.date-picker-wrapper .time {
    position: relative}
.date-picker-wrapper.single-month .time {
    display: block}
.date-picker-wrapper .time input[type=range] {
    vertical-align: middle;
    width: 129px;
    padding: 0;
    margin: 0;
    height: 20px}
.date-picker-wrapper .time1 {
    width: 180px;
    padding: 0 5px;
    text-align: center}
.time2 {
    width: 180px;
    padding: 0 5px;
    text-align: center}
.date-picker-wrapper .time1 {
    float: left}
.date-picker-wrapper .time2 {
    float: right}
.date-picker-wrapper .hour {
    text-align: right}
.minute {
    text-align: right}
.date-picker-wrapper .hide {
    display: none}
.date-picker-wrapper div.first-date-selected,
.date-picker-wrapper div.last-date-selected,
.date-picker-wrapper div.middle-date-selected {
    background-color: #4e81ff!important;
    color: #fff!important;
}
.date-picker-wrapper td.first-date-selected{ background-position: -50% 50% !important; }
.date-picker-wrapper td.last-date-selected{ background-position: 150% 50% !important; }
.date-picker-wrapper .first-date-selected{
    /*border-left: 7px solid #f2f2f2;*/
    /*border-right: 7px solid #f2f2f2;*/
}

.date-picker-wrapper .second-date {}
.date-picker-wrapper .prev-date {}

.date-picker-wrapper .second-date.hovering.bg-line:after,
.date-picker-wrapper .second-date.checked.bg-line:after {content: ''; position: absolute; background-color: #d9edf7; left: -7px; top: 0; width: 7px; height: 100%;}
.date-picker-wrapper .prev-date.hovering.bg-line:after,
.date-picker-wrapper .prev-date.checked.bg-line:after {content: ''; position: absolute; background-color: #d9edf7; right: -7px; top: 0; width: 7px; height: 100%; z-index: 9;}
.date-picker-wrapper .bg-line:after {content: ''; position: absolute; background-color: #d9edf7; right: -7px; top: 0; width: 7px; height: 100%;}

.date-picker-wrapper .first-date-selected.checked{

}
.date-picker-wrapper .last-date-selected {
    /*border-left: 6px solid #d9edf7;*/
    /*border-right: 6px solid #f2f2f2;*/
}
.date-picker-wrapper .middle-date-selected {
    /*border-left: 6px solid #d9edf7;*/
    /*border-right: 6px solid #f2f2f2;*/
}
.date-picker-wrapper .date-range-length-tip {
    position: absolute;
    margin-top: -4px;
    margin-left: -8px;
    -webkit-box-shadow: 0 0 3px rgba(0,0,0,.3);
    box-shadow: 0 0 3px rgba(0,0,0,.3);
    display: none;
    background-color: #ff0;
    padding: 0 6px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 16px;
    -webkit-filter:drop-shadow(0 0 3px rgba(0, 0, 0, .3));
    -moz-filter:drop-shadow(0 0 3px rgba(0, 0, 0, .3));
    -ms-filter:drop-shadow(0 0 3px rgba(0, 0, 0, .3));
    -o-filter:drop-shadow(0 0 3px rgba(0, 0, 0, .3));
    filter:drop-shadow(0 0 3px rgba(0, 0, 0, .3))}
.date-picker-wrapper .date-range-length-tip:after {
    content: '';
    position: absolute;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid #ff0;
    left: 50%;
    margin-left: -4px;
    bottom: -4px}
.date-picker-wrapper.two-months.no-gap .month1 .next,
.date-picker-wrapper.two-months.no-gap .month2 .prev {
    display: none}
.date-picker-wrapper .week-number {
    padding: 5px 0;
    line-height: 1;
    font-size: 12px;
    margin-bottom: 1px;
    color: #999;
    cursor: pointer}
.date-picker-wrapper .week-number.week-number-selected {
    color: #49e;
    font-weight: 700}

.datepick {position: absolute; bottom: 0; left: 0; width: 100%; height: 38px; line-height: 38px; padding: 0 20px; box-sizing: border-box;}
.datepick:after {content: ''; display: block; clear: both;}
.datepick input {display: block; float: left; width: 50%; height: 100%; line-height: 38px; color: #ffffff; font-size: 14px; font-weight: 500; padding: 0; outline: none;}
.datepick input::-webkit-input-placeholder {color: #ffffff;}
.datepick input + input {color: #333333; padding-left: 20px;}
.datepick input + input::-webkit-input-placeholder{color: #333333;}


#one-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.hovering {border-left: 6px solid; border-right: 6px solid; border-color: #f2f2f2; }
#multi-inputs.datepick {padding: 0;}
#multi-inputs.datepick.multi-three input {width: 33.33%; padding-left: 12px;}
#multi-inputs.datepick.multi-four input {width: 25%; padding-left: 9px; letter-spacing: -1px;}
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.hovering {border-left: 6px solid; border-right: 6px solid; border-color: #f2f2f2; }


#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected.multi0:before {content: '여정1'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected.multi1:before {content: '여정2'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected.multi2:before {content: '여정3'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.first-date-selected.multi3:before {content: '여정4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }

#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.middle-date-selected.multi0:before {content: '여정1'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.middle-date-selected.multi1:before {content: '여정2'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.middle-date-selected.multi2:before {content: '여정3'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.middle-date-selected.multi3:before {content: '여정4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }

#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected.multi0:before {content: '여정1'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected.multi1:before {content: '여정2'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected.multi2:before {content: '여정3'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.last-date-selected.multi3:before {content: '여정4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }

#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiA.multiB:before {content: '여정1,2'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiA.multiB.multiC:before {content: '여정1,2,3'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiA.multiB.multiC.multiD:before {content: '여정1,2,3,4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiB.multiC:before {content: '여정2,3'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiB.multiC.multiD:before {content: '여정2,3,4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }
#multi-inputs-container .date-picker-wrapper .month-wrapper table .day.toMonth.multiC.multiD:before {content: '여정3,4'; display: block; position: absolute; width: 52px; text-align: center; color: #ffffff; font-size: 11px; top: 23px; left: 50%; margin-left: -26px; background: none; }









