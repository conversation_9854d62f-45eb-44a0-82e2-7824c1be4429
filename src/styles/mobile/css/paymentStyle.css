@charset "utf-8"; 

/* =====================================
                RESET
===================================== */
* { outline:0 !important; } 
html,body,h1,h2,h3,h4,h5,h6,div,p,blockquote,pre,code,address,ul,ol,li,menu,nav,section,article,aside,
dl,dt,dd,table,thead,tbody,tfoot,label,caption,th,td,form,fieldset,legend,hr,input,button,textarea,object,figure,figcaption,select { margin:0; padding:0; font-family:'pretendard','pretendard-subset',sans-serif; line-height: 120%; font-weight: 300; color: var(--secondary); word-break: keep-all; } 
html, body { width:100%; } 
html { -webkit-touch-callout:none; -webkit-user-select:none; -webkit-tap-highlight-color:rgba(0, 0, 0, 0); } 
body { width:100%; background:#fff; min-width:280px; -webkit-text-size-adjust:none; word-wrap:break-word; word-break:break-all; } 
body,input,select,textarea,button { border:none; -webkit-border-radius: 0; font-weight: 300; 
-webkit-appearance: none; 
-moz-appearance: none; 
appearance: none; } 
ul,ol,li { list-style:none; } 
table { width:100%; border-spacing:0; border-collapse:collapse; -webkit-user-select:text;  } 
img,fieldset { border:0; } 
address,cite,code,em { font-style:normal; font-weight:normal; } 
label,img,input,select,textarea,button { vertical-align:middle; } 
main,header,section,nav,footer,aside,article,figure { display:block; } 
a { text-decoration:none; color: #fff; cursor: pointer; } 
b {font-weight: 700;} 
button { cursor: pointer; background:none; } 
/* Scroll bar hidden */
body { -ms-overflow-style: none; /* IE and Edge */ scrollbar-width: none; /* Firefox */ } 
body::-webkit-scrollbar { display: none; /* Chrome, Safari, Opera*/ } 
::selection, ::-webkit-selection {background-color: #fceac1;}


label span{padding-left: 10px;font-size:14px;font-weight:500;}
label.param{border:none; font-weight:600;}



/* =====================================
                  Color   
===================================== */
:root { 
 --primary : #ffba1a; 
 --secondary : #141523; 

 --white :#fff; 
 --gray001 : #f6f6f6;
 --gray01 : #f1f1f1;
 --gray02 : #a8a8a8; 
 --gray03 : #5B5B5B; 

 --wt50 : #ffffff80; 
 --bk50 : #00000080; 

 --red01 : #FFDBDB; 
 --red02 : #FF3434; 

 --orange01 : #FFE6D1; 
 --orange02: #E44600; 

 --yellow01 : #fff1d1; 
 --yellow02 : #ff9900; 

 --ygreen01 : #EEFCE7; 
 --ygreen02 : #2C680F; 

 --lgreen02 : #6ebb1a; 
 
 --green01 : #ebf5f5; 
 --green02 : #0f6866; 

 --skyblue01 : #ECF7FC; 
 --skyblue02 : #007AB9; 

 --blue01 : #e9f1ff; 
 --blue02 : #06296e; 

 --navy01 :#E8EAFF; 
 --navy02 :#0A116B; 
 
 --purple01 : #EBDFFF; 
 --purple02 : #44227A; 

 --violet01 :#F1E0EE; 
 --violet02 : #742F5A; 

 } 
 
 :root { 
 --max-width :1240px; 
 --lnb-width :240px; 
 } 

.wrap { width: 100%; background-color: var(--gray01); } 
.inner { width: 100%; max-width: var(--max-width); margin: auto; }

.text_r { color: var(--red02); } 
.text_y { color: var(--yellow02); } 
.text_b { color: var(--skyblue02); } 
.text_g { color: var(--lgreen02); } 

.btn_lg {height: 60px; font-size: 18px; font-weight: 700;}


/* =====================================
                Components
===================================== */
/* 버튼 */
.btn_line_01 { border: 1px solid var(--white); color: var(--white); padding: 10px 50px; border-radius: 4px; font-size: 16px; display: inline-block; text-align: center;} 
.btn_line_02 { border: 1px solid var(--secondary); color: var(--secondary); padding: 10px 50px; border-radius: 4px; font-size: 16px; display: inline-block;text-align: center;} 
.btn_solid_pri { border: 1px solid var(--primary); color: var(--secondary); padding: 10px; border-radius: 4px; font-size: 16px; background-color: var(--primary); font-weight: 500; display: inline-block; text-align: center;} 
.btn_solid_sec { border: 1px solid var(--secondary); color: var(--white); padding: 10px; border-radius: 4px; font-size: 16px; background-color: var(--secondary); font-weight: 500; display: inline-block; text-align: center;} 
.btn_solid_red { border: 1px solid var(--red02); color: var(--red02); padding: 10px; border-radius: 4px; font-size: 16px; background-color: var(--red01); font-weight: 500; display: inline-block; text-align: center;} 
.btn_solid_yel { border: 1px solid var(--yellow02); color: var(--yellow02); padding: 10px; border-radius: 4px; font-size: 16px; background-color: var(--yellow01); font-weight: 500; display: inline-block; text-align: center;} 

.reinput {position: relative; padding: 20px; border-radius: 4px; height: 60px; display: flex; min-width: 200px; ;}

.input {position: relative; border:1px solid var(--gray02); padding: 20px; border-radius: 4px; height: 60px; display: flex; min-width: 200px; ;}
.input img {margin-right: 20px;}
.input input {width: 100%;}
.input select {width: 100%; margin-top: -20px; height: 60px; background: url(../img/acc_bot.png) center right no-repeat; background-size: 20px; }
.input textarea {width: 100%;}
.input.col {width: 49%;}


/* =====================================
                    Main
===================================== */

.main .section:first-child {max-width:100vw}
.main .section {margin: auto; display: flex; align-items: center; position: relative; min-width: 280px; left: 0;}
.main .section .sec_cont {align-items: center; align-content: center; margin-top: -120px; padding-left: 100px;}
.main .section .sec_cont h2 {font-size: 60px;font-weight: 700;}
.main .section .sec_cont span {font-size: 20px; line-height: 1.5em; display: block; margin: 30px 0;}
.main .section .sec_cont a { border: 1px solid var(--secondary); font-size: 16px; width: 200px; height: 40px; border-radius:4px ; -webkit-border-radius:4px ; -moz-border-radius:4px ; -ms-border-radius:4px ; -o-border-radius:4px ; }
.main .section .sec_img {position: absolute; top: 50%; right: 0; transform:translate(0%, -50%); width: 80vw; text-align: center; z-index: -1;} 
.main .section .sec_img img {max-width: 1000px; min-width: 280px; width: 100%;}

.main .page_scroll {z-index: 2;}


#fullpage {height: 100%; position: relative; touch-action: none; transition: all 700ms ease 0s;}
.main .section {display: table; table-layout: fixed; width: 100%; position: relative; box-sizing: border-box; height: 100vh;}


/* =====================================
                Content
===================================== */
.tit { margin-bottom: 40px; padding-top: 0px; } 
.tit h2 { font-size: 36px; font-weight: 700; padding-bottom: 10px;} 
.tit p { font-size: 20px; font-weight: 500; } 

main .card { width: 100%; background: var(--white); border-radius: 20px; box-shadow: 0 2px 6px rgba(0,0,0,0.05); padding: 80px; padding-top: 0; box-sizing: border-box; -webkit-user-select:text;} 
main .card .card_tit h3 { font-size: 28px; font-weight: 700; padding-top: 60px; -webkit-user-select:none;} 
main .card .card_tit p { font-size: 16px; font-weight: 300; padding: 10px 0; line-height: 1.5em; -webkit-user-select:none;} 
main .card .card_tit .step-tag { background-color: var(--primary); padding:0 10px; color: var(--secondary); font-weight: 700; font-size: 14px; line-height: 30px; display: inline-block; margin-right: 10px; margin-bottom: 4px; border-radius: 4px; vertical-align: middle; } 

main .card .card_sub_tit {padding: 40px 0 20px 0; margin-top: 30px; border-top: 2px solid var(--gray01);} 
main .card .card_sub_tit h3 { font-size: 20px; font-weight: 700;  } 
main .card .card_sub_tit p { font-size: 16px; font-weight: 300; padding: 10px 0; line-height: 1.5em; } 

.popup .card_sub_tit h3 { font-size: 20px; font-weight: 700;  } 
.popup .card_sub_tit p { font-size: 16px; font-weight: 300; padding: 10px 0; line-height: 1.5em; } 


main.cont { margin: auto;padding: 30px; padding-top:100px} 

main.cont section .card_desc { border: 6px solid var(--gray01); border-radius: 10px; padding: 40px 10px 20px 40px; box-sizing: border-box; margin: 30px 0; } 
main.cont section .card_desc h4 { font-weight: 700; margin-bottom: 4px; -webkit-user-select:none; } 
main.cont section .card_desc ul { margin-top: 20px; margin-bottom: 20px; } 
main.cont section .card_desc ul li { list-style-type: disc; margin-left: 20px; line-height:180%; font-size:14px;} 
main.cont section .card_desc ol { margin-top: 20px; margin-bottom: 20px; } 
main.cont section .card_desc ol li { list-style-type: decimal; margin-left: 40px; } 
main.cont section .card_desc ol li { list-style-type: decimal; margin-left: 40px; } 

/* TAB */
.card_tab li { flex: 1; text-align: center; border-bottom:4px solid var(--gray01); padding: 10px; box-sizing: border-box; font-weight: 700; color: var(--gray02); cursor: pointer; -webkit-user-select:none; } 
.card_tab li.sel { border-bottom:4px solid var(--primary); font-weight: 700; color: var(--secondary); } 
.code_table [class*=table_cont] { width: 100%; max-height: 500px;} 
.code_table [class*=table_cont] tbody { display:inline-table; width: 100%; } 
.code_table [class*=table_cont] tr { height: 92px; vertical-align: middle; border-bottom: 1px solid var(--gray02); } 
.code_table [class*=table_cont] th { font-weight: 700; padding: 10px; padding-left: 20px; position: relative; } 
.code_table [class*=table_cont] td { padding: 10px; padding-left: 20px;} 
.code_table [class*=table_cont] td .table_desc { font-size: 14px; font-weight: 700; } 
.code_table [class*=table_cont] th em { padding-left: 4px; } 

/* 테이블 디자인 */
.code_table .table_01 table {height: 60px}
.code_table .table_01 table tr {height: 60px;}
.code_table .table_01 table tr th {background-color: var(--gray01);border-bottom: 1px solid var(--gray01);}
.code_table .table_01 table td {word-break: break-all; border-left: 1px solid var(--gray01);border-bottom: 1px solid var(--gray01);}
.code_table .table_01 table th:first-child {border-left: 0; text-align: center;}
.code_table .table_01 table td:first-child {border-left: 0; text-align: center;}