div#progressBar {
  display: none;
  position: fixed;
  z-index: 1100;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}
div.poplayer01 {
  z-index: 31;
  width: 460px;
  height: 255px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  right: 50%;
  margin: auto;
  transform: translateX(-50%);
}
div.poplayer01 div.contbg {
  position: absolute;
  left: 0;
  top: 0;
  width: 460px;
  height: 275px;
  /*border: 2px solid #c0c0c0;*/
  background: #fff;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  border-radius: 0px;
  z-index: 40;
}
div#progressBar iframe.dimfrm {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #000;
  filter: alpha(opacity = 55);
  -moz-opacity: 0.55;
  -khtml-opacity: 0.55;
  opacity: 0.55;
  z-index: 30;
}
div.cont {
  position: relative;
  z-index: 60;
}
div.cont_text {
  position: relative;
  padding: 26px;
  z-index: 60;
}
#progressbox {
  position: absolute;
  left: 0;
  top: 0;
  width: 460px;
  height: 205px;
  margin-top: 0px;
  background: url('../../../assets/mobile/images/loading/loading_ani.gif') no-repeat center;
}
p.progress_txt {
  text-align: center;
  font-size: 17px;
  /*font-weight: bold;*/
  margin-top: 155px;
}
