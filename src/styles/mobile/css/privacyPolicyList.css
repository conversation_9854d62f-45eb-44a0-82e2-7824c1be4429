.layer-pages.privacy-policy-list .layer-head {
  text-align: center;
}
.layer-pages.privacy-policy-list .layer-cotn,
table {
  font-size: 12px;
}
.privacy-con {
  margin-top: 25px;
  padding: 0 18px 25px;
}
.privacy-con p {
  padding-bottom: 12px;
}
.privacy-con .box-dd {
  font-size: 14px;
  padding-bottom: 12px;
}
.privacy-con li {
  padding-bottom: 12px;
}
.privacy-con li span.con {
  padding-left: 14px;
}
.privacy-con li span.num {
  display: block;
  left: 18px;
  position: absolute;
}
.privacy-con ol li:before {
  content: "•";
  display: block;
  font-size: 12px;
  left: 18px;
  position: absolute;
}
.privacy-con ol.ico-style li:before {
  content: "◑";
  display: block;
  font-size: 12px;
  left: 18px;
  position: absolute;
}
.privacy-con ol.ico-style li {
  padding-left: 14px;
}
.privacy-con .table-wrap {
  padding-bottom: 25px;
  padding-top: 5px;
}
.privacy-con .line-table {
  width: 100%;
}
.privacy-con .line-table thead th,
.line-table tbody th,
.line-table tbody td {
  border: 1px solid #dddddd;
  padding: 4px 8px;
  text-align: left;
}
.privacy-con .line-table th {
  font-weight: 500;
}
a {
  color: inherit;
  text-decoration: none;
  background-color: transparent;
  font-size: 12px;
}
