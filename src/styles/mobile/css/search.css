﻿@charset "utf-8";
.pg-search{ position: relative; }

.pg-search .bg-white{ position: relative; z-index: 1; border-radius: 35px 35px 0 0; }

.pg-search .resent-search-sld {padding-bottom: 30px;}
.pg-search .resent-search-sld .tit-size01{ margin-bottom: 0; font-weight: 500; }

.pg-search .top-visual{ padding:0 16px; text-align: right;}
.pg-search .top-visual .tit{ position: absolute; top: 5px; left: 16px; font-size: 24px; line-height: 36px; font-weight: 500; color: #fff; }
.pg-search .top-visual img{ box-sizing: border-box;}

.pg-search .sec-hotel .top-visual{ position: relative;}
.pg-search .sec-hotel .top-visual::before{ content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: -30px;  background-image: linear-gradient(to bottom, #6f98fe, #cde4ff);}
.pg-search .sec-hotel .top-visual .tit{ z-index: 2; position: absolute; top: 0; left: 16px; padding-left: 16px; font-size: 24px; line-height: 36px; font-weight: 500; color: #fff;}
.pg-search .sec-hotel .top-visual img{ position: relative;z-index: 1; width: 100%; }

/* 0819 ~ 26  전체 스케줄 모드 RVYN-1036*/
.f_schedule_md{height: 59px; padding:0 12px; padding-top:20px;box-sizing: border-box;background-color: #f2f4f9; border-top:1px solid #ebeef3; position: relative;;}
.f_schedule_md ul{display: flex; align-items: center; justify-content: flex-end;}
.f_schedule_md ul li{padding-right: 6px;}
.f_schedule_md .toggle_btn{width: 34px; height: 18px; background: #D9DDE4; border-radius: 30px; }
.f_schedule_md ul .f_schedule_info{position: relative;}
.f_schedule_md ul .f_schedule_info span{display: block; height: 16px; }
.f_schedule_md ul .f_schedule_info img{width: 16px; height: 16px;}
.f_schedule_md ul .f_schedule_info span:hover +.speech_bubble{display: block;}
.f_schedule_md ul .f_schedule_info .speech_bubble{ font-size:13px;color:#4E81FF;display:none;position: absolute;left: 50%; top:30px;width: 296px; margin-left:-149px; height: 89.24px; background: #F0F4FF;border: 1px solid #4E81FF;box-shadow: 4px 2px 12px rgba(157, 169, 190, 0.2); padding:12px; box-sizing: border-box; border-radius: 5px; z-index: 10;}
.f_schedule_md ul .f_schedule_info .speech_bubble::after{	content: '';position: absolute;top: 0;left: 50%;width: 0;height: 0;border: 10px solid transparent;border-bottom-color: #4E81FF;border-top: 0;margin-left: -10px;margin-top: -10px;}
.f_schedule_md ul .f_schedule_info .speech_bubble::before{	content: '';position: absolute;top: 0;left: 50%;width: 0;height: 0;border: 9px solid transparent;border-bottom-color: #F0F4FF;;border-top: 0;margin-left: -9px;margin-top: -9px;z-index: 11;}
.f_schedule_md ul .toggle_btn {position: relative;}
.f_schedule_md ul .toggle_btn div.left{background: #FFFFFF;border: 1px solid #FFFFFF;box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.1);width: 14px;height: 14px; border-radius: 100%;margin: 1px; left: 2px;position: absolute;}
.f_schedule_md ul .toggle_btn div.right{left: 100%; right: 2px; left: auto; }
.f_schedule_md .f_schedule_chs{position: absolute;left:16px; color:#757F92;font-size: 12px;bottom:20px;display: none;}
.f_schedule_md .f_schedule_chs em{font-weight: 700;color:#34446E;}
.to_flight_box{background-color: white;height: 120px;display: none;}
.to_flight_box .sec-top{padding-top:12px;}
.to_flight_box .sec-top dt{display: flex; justify-content: space-between;}
.to_flight_box .sec-top dt>div{display: flex;align-items: center;}
.to_flight_box .sec-top dd .to_info{margin:10px 0;}
.to_flight_box .sec-top dd .flight_info{font-weight: 400;font-size: 12px;line-height: 18px;letter-spacing: -0.3px;color: #757F92;vertical-align: middle;}
.to_flight_box .sec-top dd .flight_info img{width: 13px;height: 13px;vertical-align: middle;}
.to_flight_box .sec-top dd .to_info em{font-weight: 700;font-size: 15px;color:#000;}
.to_flight_box .sec-top dd .to_info span{font-weight: 400;font-size: 12px;color:#000;margin-left: 10px;}
.to_flight_box .label{width: 44px;margin-right: 8px;border-radius: 16px;background-color: #34446e;color: #fff;font-size: 11px;text-align: center;}
.to_flight_box .to_change_button{border: 1px solid #34446E;border-radius: 3px;width: 52px;height: 27px;text-align: center;padding:3px 0;box-sizing: border-box;font-weight: 350;font-size: 13px;}
.nw_search_re.saerch-result-wrap  .air-plan .aircraft{width: 18px;height: 18px;}
.page-header.nw_full_shedule_header{border-bottom: 0;}

.saerch-result-wrap.nw_schedule_re {display: block; max-height: 60vh;  overflow-y: scroll;}
.saerch-result-wrap.nw_schedule_re .air-plan .share{margin-left: 32px;margin-top:6px;}
.nw_schedule_re .air-plan .left-col.aircraft{width: 22px; height: 22px;}
.nw_schedule_re .right-col {display: flex;align-items: center;}
.nw_schedule_re .right-col .total-time::before{content: '·'; padding:0 5px;}
/* modal */
/* 팝업 */
.schedule_modal.modal-wrap{text-align: center;}
.schedule_modal.modal-wrap img{width: 54px;}
.schedule_modal.modal-wrap .btns-cmm{width: 120px;}
.schedule_modal.modal-wrap p{font-size: 15px;max-width: 233px;display: inline-block;letter-spacing: -0.3px;padding-top:20px;padding-bottom: 30px;}
/* --팝업 */
.modal_bottom.search{height: calc(100% - 105px);background-color: white; position: fixed;bottom: -100%;left:0; right:0;width: 100%; z-index: 101;  border-radius: 15px 15px 0 0; box-sizing: border-box;}
.modal_bottom.search .modal-close{overflow: hidden;text-indent: -9999em;width: 12px;height: 12px;background: url(../../../assets/mobile/images/cmm/btn_close_page_b.png) 50% 50% no-repeat;-webkit-background-size: 14px;background-size: 14px;border: 0 none;right: 20px;top:25px;position: absolute;}
.modal_bottom.search.active{bottom: 0;transition: all linear 0.3s;}
.modal_bottom.search .plan{border-bottom: 0;}
.modal_bottom.search .sec-mid{display: block;}
.modal_bottom .modal_header{border-radius: 15px 0 15px 0; display: flex;padding:21px 21px 19px 21px;border-bottom: 1px solid #EBEEF3;}
.modal_bottom .modal_header p{padding-right: 10px;}
.modal_bottom .modal_header img{width: 11px; height: 3px;vertical-align: middle;margin-right: 6px;}
.modal_bottom .modal_header .modal_header_tit{font-weight: 500;font-size: 15px;color: #4E81FF; }
.modal_bottom.search .aircraft img{width: 32px;height: 20px;}
.modal_bottom.search .modal_content{overflow-y: scroll;max-height: calc(60vh - 100px);}
.modal_bottom.search .modal_btn{height: 68px; position: absolute;bottom:40px;width: 100%;padding-top:20px}
.modal_bottom.search .modal_btn button{background: #4E81FF;border-radius: 24px; width: 230px; height: 48px;color:white; left: 50%;; margin-left: -115px;position: absolute;}
.modal_dim{display:none;position: fixed;top:0;left: 0;width: 100%;height: 100%; background: rgba(0, 0, 0, 0.7); z-index: 100;}
.modal_bottom.search.active+.modal_dim{display: block;}
/* ==modal */
/* 요청 내역 */
.request_rule .box-item{border-top:1px solid #EBEEF3;padding:20px;}
.request_rule  .no-request{text-align: center; width: 100%; padding-top:182px}
.request_rule  .no-request p{font-weight: 350;font-size: 14px;color: #9DA9BE;margin-top:17px}
.request_rule .no-request img{text-align: center;vertical-align: middle;width: 69px;}
.request_rule .box-item:last-child{border-top:1px solid #EBEEF3;padding:20px; border-bottom:1px solid #EBEEF3;}
.request_rule .fare-condition{padding: 0!important;}
.request_rule .form-box{margin-bottom: 20px; margin-top:7px;padding:0 20px;}
.request_rule .form-box span{margin-right: 18px;}
.request_rule .box-item dt{display: flex; justify-content: space-between;}
.request_rule .box-item dt p span{font-size: 13px;}
.request_rule .box-item dt p span:last-child{font-weight: 500;}
.request_rule .box-item dd{display: flex; justify-content: space-between;margin-top: 16px;}
.request_rule .box-item dd .tit{font-weight: 500;font-size: 14px;margin-bottom: 5px;}
.request_rule .box-item dd .request_status{font-weight: 500;font-size: 14px;color:#4E81FF;}
.request_rule .box-item dd .request_status.req{color:#000;}
.request_rule .box-item dd .start-date{font-weight: 400;font-size: 13px;color: #757F92;}
/*== 요청 내역 */
/* 전체 스케줄모드  */
.request_opinion_box{padding-bottom: 40px;background-color: white;}
.request_opinion_box dd{position: relative;}
.request_opinion_tex textarea{border: 1px solid #E2E4E8;width: 100%;height: 209px;border-radius: 5px;}
.request_opinion_tex+.bottom-btn {text-align:center;}
.request_opinion_tex+.bottom-btn button{text-align:center;margin:30px 0;background: #4E81FF;border-radius: 24px;color:white;color:white;padding:13px;width: 230px;font-size: 15px;font-weight: 500;}
.clear_bt{clear: both;}
.clear_bt dl{padding-left: 28px;position: relative;padding-top: 19px;}
/* 항공사별 이미지 사이즈 수정 0921*/
.clear_bt dt{display: flex;align-items: center;padding: 0!important;border-bottom: 0!important;padding-left:8px!important}
.clear_bt dt img{margin-right:6px;flex-shrink:0;width: 30px;height: 20px;padding-right: 8px;position: absolute;left: 0;}
/*-- 항공사별 이미지 사이즈 수정 0921*/
.clear_bt dt p{font-size: 14px;font-weight: 400;padding-right: 8px;}
.clear_bt dt span{font-size: 12px;font-weight: 400;color:#757F92;padding-right: 8px;}
.reserv-request .box-item.active dd  .clear_bt dd{display: flex;margin-top:6px}
.reserv-request .box-item.active dd  .clear_bt dd p{font-size: 15px;font-weight: 700;}
.reserv-request .box-item.active dd  .clear_bt dd p span img{width: 12px;height: 3px;vertical-align: middle;padding:0 8px;}

/*== 0819 RVYN-1036*/

.sec-hotel .top-visual{ position: relative;}
.sec-domestic .top-visual,
.sec-foregin .top-visual{ position: relative; text-align: right;}
.sec-hotel .top-visual::before,
.sec-domestic .top-visual::before,
.sec-foregin .top-visual::before{ content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: -30px;  background-image: linear-gradient(to bottom, #6f98fe, #cde4ff);}
.sec-hotel .top-visual .tit,
.sec-domestic .top-visual .tit,
.sec-foregin .top-visual .tit{ z-index: 2; position: absolute; top: 0; left: 16px; padding-left: 16px; font-size: 24px; line-height: 36px; font-weight: 500; color: #fff;}
.sec-hotel .top-visual img{ position: relative;z-index: 1; width: 100%; }
.sec-domestic .top-visual img,
.sec-foregin .top-visual img{ position: relative; z-index: 1; width: 64%; margin-right: 15px;}

/* 항공권 검색 */
.search-ticket-cn{ position: relative; padding: 20px 20px 45px;}
.search-ticket-cn .box-tab{ margin:0 52px 25px; border: 1px solid #4e81ff; border-radius: 17.5px; overflow: hidden;}
.search-ticket-cn .box-tab ul{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
}
.search-ticket-cn.type-foregin li{width: 33.3%;}
.search-ticket-cn.type-domestic li{width: 50%;}
.search-ticket-cn.type-domestic .box-tab{ margin-left: 92px; margin-right: 92px; }
.search-ticket-cn .box-tab li{  border-radius: 17.5px; overflow: hidden; }
.search-ticket-cn .box-tab button{ width: 100%; text-align: center;  font-size: 14px; font-weight: 500; line-height: 28px; color: #4e81ff;}
.search-ticket-cn .box-tab li.active{ background-color: #4e81ff; }
.search-ticket-cn .box-tab li.active button{ background-color: #4e81ff;  color: #fff;}
.search-ticket-cn .box-cotn{ position: relative;}
.search-ticket-cn *[class^="sec-"]{overflow: hidden; padding-bottom: 10px; margin-bottom: 17px; border-bottom: 1px solid #ebeef3; }
.search-ticket-cn *[class^="sec-"] .txt{font-size: 13px; line-height: 19px; color: #4e81ff;}
.search-ticket-cn *[class^="sec-"] .val{font-size: 16px; line-height: 24px; color: #c9cfd8;}
.search-ticket-cn *[class^="sec-"] .in-value .val{ color: #000; }
.search-ticket-cn .sec-class{ margin-bottom: 29px; }
.search-ticket-cn .box-multi{display: none; clear: both; margin-bottom: 17px; border-bottom: 1px solid #ebeef3;}
.search-ticket-cn .box-multi .plan-item{ position: relative; }
.search-ticket-cn .box-multi .plan-item::after{ content: ''; clear: both; display: block; }
.search-ticket-cn .box-multi .box-btn{ padding-bottom: 19px; }
.search-ticket-cn .box-multi .btn-add-item{ width: 18px; height: 18px; padding: 10px; background: url(../../../assets/mobile/images/cmm/btn_add_36.png) 50% 50% no-repeat; -webkit-background-size: 18px; background-size: 18px; outline: none; }
.search-ticket-cn .box-multi .btn-remove-item{ position: absolute; top: 50%; right: 0; width: 24px; height: 24px;  background: url(../../../assets/mobile/images/cmm/btn_remove_36.png) 50% 50% no-repeat; -webkit-background-size: 18px; background-size: 18px; outline: none; text-indent: 999em; color: transparent; margin-top: -25px; overflow: hidden;}
.search-ticket-cn .sec-city{ background: url(../../../assets/mobile/images/cmm/icn_search_round.png) 50% 12px no-repeat; -webkit-background-size: 17px 13px; background-size: 17px 13px;}
.search-ticket-cn .sec-city button{ outline: none; width: 50%; float: left;}
.search-ticket-cn .sec-city button:nth-child(1){ text-align: left; }
.search-ticket-cn .sec-city button:nth-child(2){ text-align: right; }
.search-ticket-cn .sec-city .en{ font-size: 24px; line-height: 36px; font-weight: 700; color: #c9cfd8; }
.search-ticket-cn .sec-city .kr{ font-size: 13px; line-height: 19px; color: #c9cfd8;  text-overflow: ellipsis; white-space: nowrap; overflow: hidden }
.search-ticket-cn .sec-city .in-value *{ color: #000; }
.search-ticket-cn .sec-day button{ outline: none; width: 50%; float: left;}
.search-ticket-cn .sec-day button:nth-child(1){ text-align: left; }
.search-ticket-cn .sec-day button:nth-child(2){ text-align: right; }
.search-ticket-cn .sec-class button{ width: 100%; outline: none; }
.search-ticket-cn .sec-class *{ display: block; }
.search-ticket-cn .sec-class .passenger,
.search-ticket-cn .sec-class .seat{ float: left; width: 50%; }
.search-ticket-cn .sec-class .val{ color: #000; }
.search-ticket-cn .sec-class .passenger{ text-align: left; }
.search-ticket-cn .sec-class .seat{ text-align: right; }
.search-ticket-cn .box-btn{ text-align: center; }
.search-ticket-cn[data-ticket-type="type-oneway"] .sec-day .arr{ display: none; }
.search-ticket-cn[data-ticket-type="type-oneway"] .sec-city{ background-image: url(../../../assets/mobile/images/cmm/icn_search_oneway.png); -webkit-background-size: 17px 5px; background-size: 17px 5px; -ms-background-position-y: 15px; background-position-y: 15px;}
.search-ticket-cn[data-ticket-type="type-multi"] .sec-city{ background-image: url(../../../assets/mobile/images/cmm/icn_search_oneway.png); -webkit-background-size: 17px 5px; background-size: 17px 5px; -ms-background-position-y: 15px; background-position-y: 15px;}
.search-ticket-cn[data-ticket-type="type-multi"] .sec-city button{ box-sizing: border-box;  }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-city button:nth-child(2){ text-align: left; padding-left: 25px; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-city{ float: left; width: 60%; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day{ float: right; width: calc(40% - 15px); padding-top: 6px; padding-bottom: 9px; padding-left: 15px;	}
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day .txt{ margin-bottom: 7px; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day .txt::before{ content: '여정1'; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day .txt span{display: none;}
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day button{ width: 100%; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-class{ clear: both; }
.search-ticket-cn[data-ticket-type="type-multi"] .sec-day .arr{ display: none; }
.search-ticket-cn[data-ticket-type="type-multi"] .box-multi{display: block;}
.search-ticket-cn[data-ticket-type="type-multi"] .box-multi .sec-day .txt span{display: block;}
.search-ticket-cn[data-ticket-type="type-multi"] .box-multi .sec-day .txt::before{display: none;}

/* 호텔 검색 */
.search-ticket-cn.type-hotel .sec-destination{ text-align: left; }
.search-ticket-cn.type-hotel .sec-destination .location{ width: 100%; text-align: left;}




.btn-tickt-reserv{ margin-bottom: 45px;}
.btn-tickt-reserv .btns-cmm{ position: relative; width: 100%; border-color: #ebeef3; font-weight: 300; color: #000; text-align: left; text-indent: 37px;}
.btn-tickt-reserv .icn-list{ position: absolute; top: 18px; left: 16px; width: 14px; height: 15px; background: url(../../../assets/mobile/images/search/icn_list_reserv.png) 0 0 no-repeat; -webkit-background-size: 14px 15px; background-size: 14px 15px; }
.btn-tickt-reserv .icn-arrow{ position: absolute; top: 17px; right: 20px; width: 8px; height: 12px; background: url(../../../assets/mobile/images/search/icn_arr_list.png) 0 0 no-repeat; -webkit-background-size: 8px 12px; background-size: 8px 12px; }

.btn-open-compare{ position: absolute; top: 11px; right: 22px;}
.btn-open-compare .ico{ display: block; margin: 0 auto; width: 22px; height: 21px; background: url(../../../assets/mobile/images/cmm/icn_comparison.png) 0 0 no-repeat; -webkit-background-size: 22px 21px; background-size: 22px 21px;}
.btn-open-compare .txt{display: block; font-size: 8px; line-height: 12px; color: #4e81ff; letter-spacing: -0.5px; }
.btn-open-compare .num{ position: absolute; top: -3px; right: -5px; width: 17px; border-radius: 100%; line-height: 17px; background-color: #4e81ff; font-size: 10px;  font-weight: 500; letter-spacing: -0.5px; color: #fff; text-align: center;}

.search-result-top{ position: relative; padding: 0 0 0; background-color: #fff; }
.search-result-top dl{ margin-bottom: 12px; padding: 0 16px;}
.search-result-top dt{ padding-top: 8px; margin-bottom: 1px;font-size: 14px; line-height: 20px; }
.search-result-top dd{ font-size: 12px; line-height: 18px; color: #9da9be; }
.search-result-top .item-align{padding-bottom: 11px; white-space: nowrap;transform: translate3d(0,0,0); overflow-x: auto;}
.search-result-top .item-align::-webkit-scrollbar {  display: none; }
.search-result-top .item-align ul{ display: inline-block; padding: 0 16px;}
.search-result-top .item-align li{ display: inline-block; }
.search-result-top .item-align label{display: inline-block; line-height: 24px; border: 1px solid #c9cfd8; border-radius: 16px; background-color: #fff; overflow: hidden; }
.search-result-top .item-align input[type="radio"]{ visibility: hidden;width: 0; height: 0; outline: none; border: 0 none; opacity: 0; margin: 0; padding: 0; }
.search-result-top .item-align .txt{ display: inline-block; padding: 0 12px;  font-size: 12px; color: #34446e; vertical-align: top;}
.search-result-top .item-align input[type="radio"]:checked ~ .txt{ background-color: #34446e; color: #fff; font-weight: 500; border-color: #34446e;}
.search-result-top .policy-box{ position: relative;	padding:0  16px; border-bottom: 1px solid #ebeef3; }
.search-result-top .policy-box:last-child{ border-bottom: 0 none; }
.search-result-top .policy-box .left-col{ float: left; padding: 12px 0;}
.search-result-top .policy-box .right-col{ float: right; }
.search-result-top .policy-box .label-type{ position: relative;	 }
.search-result-top .policy-box .label-type *{ float: left; padding-left: 26px; margin-right: 20px; font-size: 12px;line-height: 18px; background-position: 0 0; background-repeat: no-repeat; -webkit-background-size: 18px; background-size: 18px;}
.search-result-top .policy-box .label-type .in{ background-image: url(../../../assets/mobile/images/cmm/img_inpolicy.png); }
.search-result-top .policy-box .label-type .out{ background-image: url(../../../assets/mobile/images/cmm/img_outofpolicy.png);  }
.search-result-top .policy-box .my-trip-rule{display: block; padding: 12px 18px 12px 0; font-size: 13px; line-height: 18px;padding-right: 12px; background: url(../../../assets/mobile/images/cmm/btn_more_s_black.png) 100% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px;}

.saerch-result-wrap{ position: relative;	padding: 12px 12px 30px; background-color: #f2f4f9;  }
.saerch-result-wrap .loading.box-item{ min-height: 196px; padding: 0; background: #fff url(../../../assets/mobile/images/loading/loading_booking.gif) 50% 50% no-repeat;}
.saerch-result-wrap .box-item{ position: relative; padding:0 0 15px;  margin-bottom: 12px;  border-radius: 8px;  box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15); background-color: #fff;}
.saerch-result-wrap .box-top{ position: relative; margin:0 15px; padding-top: 0 !important; border-bottom: 1px solid #ebeef3; }
.saerch-result-wrap .box-top::after{ content: ''; clear: both; display: block; }
.saerch-result-wrap .box-top .tit{ display: inline-block; font-size: 15px; font-weight: 500; line-height: 45px; vertical-align: top;}
.saerch-result-wrap .box-top .ico-select + .tit{  margin-left: 7px;margin-top: 13px; }
.saerch-result-wrap .box-top .fl-r{ float: right; }
.saerch-result-wrap .box-top .form-radio{ margin-top: 12px; }
.saerch-result-wrap .box-top .form-radio span{ padding-left: 20px; }
.saerch-result-wrap .box-top .ico-select{display: inline-block; margin-top: 12px; width: 16px; height: 16px;  background: url(../../../assets/mobile/images/cmm/icn_chk_arrow.png) no-repeat 50% 50%; -webkit-background-size: 10px 7px; background-size: 10px 7px; border-radius: 100%;}
.saerch-result-wrap .box-top .ico-select.off{ background-color: #c9cfd8; }
.saerch-result-wrap .box-top .ico-select.on{ background-color: #4e81ff; }
.saerch-result-wrap .air-plan{ position: relative; margin:0 14px 15px; }
.saerch-result-wrap .air-plan::after{ content: ''; clear: both; display: block;  }
.saerch-result-wrap .box-item > *:nth-child(1){ padding-top: 15px; }
.saerch-result-wrap .box-item > *:nth-child(1)::before{ content: ''; position: absolute; top: 0; left: 0; width: 23px; height: 23px; background-position: 0 0; background-repeat: no-repeat; -webkit-background-size: 23px; background-size: 23px;}
.saerch-result-wrap .btn-detail{ display: block; position: relative; width: 100%; outline: none;}
.saerch-result-wrap .btn-detail::before{left: 0 !important;}
.saerch-result-wrap .box-item.pl-in  > *:nth-child(1)::before{ background-image: url(../../../assets/mobile/images/cmm/ico_inpolicy.png); }
.saerch-result-wrap .box-item.pl-out  > *:nth-child(1)::before{ background-image: url(../../../assets/mobile/images/cmm/ico_outofpolicy.png); }
.saerch-result-wrap .box-item.fare-sp  > *:nth-child(1)::before{height: 18px; content: '특가'; width: 30px; background-color: #61c7cb; font-size: 10px; line-height: 18px;  z-index: 10; color: #fff; border-radius: 8px 0 8px 0;}
.saerch-result-wrap .box-item.fare-dc  > *:nth-child(1)::before{height: 18px; content: '할인'; width: 30px; background-color: #ff9268; font-size: 10px; line-height: 18px;  z-index: 10; color: #fff; border-radius: 8px 0 8px 0;}
.saerch-result-wrap .air-plan .left-col{ float: left; }
.saerch-result-wrap .air-plan .right-col{ float: right; text-align: right;}
.saerch-result-wrap .air-plan .aircraft{ width: 30px; height: 20px; margin-top: 4px; margin-right: 9px; }
.saerch-result-wrap .air-plan .plan{ margin-bottom: 2px; }
.saerch-result-wrap .air-plan .time{position: relative; float: left; margin-right: 5px; font-size: 17px; font-weight: 500; line-height: 25px; }
.saerch-result-wrap .air-plan .time[data-more-time]::after{ content: attr(data-more-time); display: inline-block; position: relative; top: -3px; margin-left: 3px; font-size: 10px; font-weight: 300; font-family: Roboto; vertical-align: top;}
.saerch-result-wrap .air-plan .city{ float: left; margin-top: 1px; letter-spacing: -0.2px; font-size: 11px; line-height: 18px; color: #9da9be; font-weight: 500; color: #757f92; }
.saerch-result-wrap .air-plan .share{ clear: both; text-align: left; width: 87%; margin-left: 38px; font-size: 13px; font-weight: 300; line-height: 19px; letter-spacing: -0.5px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden; color: #757f92; }
.saerch-result-wrap .air-plan .way-to{ width: 50px; margin-top: 10px; height: 7px; background-image: url(../../../assets/mobile/images/cmm/spr_ico_via.png); background-repeat: no-repeat; -webkit-background-size: 33px 35px; background-size: 33px 35px; }
.saerch-result-wrap .air-plan .way-to.n0{ background-position: 50% 0px; }
.saerch-result-wrap .air-plan .way-to.n1{ background-position: 50% -7px; }
.saerch-result-wrap .air-plan .way-to.n2{ background-position: 50% -14px; }
.saerch-result-wrap .air-plan .way-to.n3{ background-position: 50% -21px; }
.saerch-result-wrap .air-plan .way-to.n4{ background-position: 50% -28px; }
.saerch-result-wrap .air-plan .via{ font-size: 13px; line-height: 25px; }
.saerch-result-wrap .air-plan .via.on{ color: #ff9268; }
.saerch-result-wrap .air-plan .total-time{ font-size: 12px; line-height: 18px; color: #000; letter-spacing: -0.5px; }

.saerch-result-wrap.none-result{ position: relative; height: calc(100vh - 100px) }
.saerch-result-wrap.none-result .box-inner{ margin-top: 10vh; }
.saerch-result-wrap.none-result dl{ padding-top: 176px; background: url(../../../assets/mobile/images/cmm/bg_error.png) 50% 0 no-repeat; -webkit-background-size: 70px 150px;
    background-size: 70px 150px; text-align: center;}
.saerch-result-wrap.none-result dt{ margin-bottom: 11px; font-size: 16px; line-height: 24px; font-weight: 500; color: #757f92; }
.saerch-result-wrap.none-result dd{ margin-bottom: 30px; font-size: 14px; font-weight: 300; color: #757f92; }
.saerch-result-wrap.none-result .box-btn{ text-align: center; }

.saerch-result-wrap.hotel .swiper-container .swiper-wrapper{ display: block; }
.saerch-result-wrap.hotel .swiper-container.loading{}
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item{

}
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .thum{ height: 42.1vw;     background-image: linear-gradient(90deg, #e1e4e8 0px, rgba(229, 229, 229, 0.8) 40px, #e1e4e8 80px); /*background-color: #e1e4e8 ;*/
    animation: shine-avatar 2s infinite ease-out;
}
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .name .kr{ position: relative; height: 21px; margin-bottom: 4px; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .name .kr::before{ content: ''; position: absolute; top: 0; left: 0; bottom: 0; width: 50%; background-color: #e1e4e8; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .name .en{ position: relative; height: 18px; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .name .en::before{ content: ''; position: absolute; top: 0; left: 0; bottom: 0; width: 40%; background-color: #e1e4e8; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .grade{ position: relative; height: 13px; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .grade::before{ content: ''; position: absolute; top: 0; left: 0; bottom: 0; width: 30%; background-color: #e1e4e8; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .price{ position: relative; height: 20px; }
.saerch-result-wrap.hotel .swiper-container.loading .box-hotel-item .price::before{ content: ''; position: absolute; top: 0; right: 0; bottom: 0; width: 40%; background-color: #e1e4e8; }

@keyframes shine-lines {
    0% {
        background-position: -100px;
    }
    40%, 100% {
        background-position: 140px;
    }
}

@keyframes skeleton {
    0% {
        transform: translateX(0);
        opacity: 0;
    }

    20% {
        opacity: 0.25;
    }

    50% {
        opacity: 1;
    }

    80% {
        opacity: 0.5;
    }

    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

.saerch-result-wrap.domestic .btn-detail{ padding-top: 0; }
.saerch-result-wrap.domestic .air-plan{ padding-top: 12px; margin: 0 12px 0; }
.saerch-result-wrap.domestic .air-plan .plan{ margin-bottom: 6px; }
.saerch-result-wrap.domestic .air-plan .time{line-height: 26px; margin-right: 0;}
.saerch-result-wrap.domestic .air-plan .share{ margin-left: 33px; text-align: left; font-size: 12px; font-weight: 300;color: #757f92; letter-spacing: -1px;}
.saerch-result-wrap.domestic .air-plan .way-to{ width: 20px; margin-left: 8px; margin-right: 8px; }
.saerch-result-wrap.domestic .air-plan .via{ padding-top: 2px; margin-bottom: 6px; font-size: 12px; line-height: 18px; letter-spacing: -0.5px; }
.saerch-result-wrap.domestic .air-plan .aircraft{ margin-top: 2px; width: 30px; height: 20px; }
.saerch-result-wrap.domestic .air-plan .fare{ font-size: 16px; line-height: 24px; font-weight: 500; color: #4e81ff; }
.saerch-result-wrap.domestic .air-plan .air-share{ position: absolute; bottom: 1px; left: -3px; font-size: 8px;line-height: 10px; font-weight: 300; color: #757f92; text-align: center; }
.saerch-result-wrap .sec-bottom{position: relative; padding-top: 12px; margin:0 15px; border-top: 1px solid #ebeef3;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    justify-content: flex-end;
}
.saerch-result-wrap .sec-bottom .total-price{ min-width: 70%; }
.saerch-result-wrap .sec-bottom .box-btn{ min-width: 30%; text-align: right; }
.saerch-result-wrap .sec-bottom .box-btn .btns-remove{ display: block; margin-top: 5px; text-decoration: underline; color: #757f92; line-height: 20px; }
.saerch-result-wrap .sec-bottom .btns-cmm{ width: 100px; line-height: 32px;  border-radius: 16px; border: 1px solid #4e81ff; font-size: 14px; font-weight: 500; color: #4e81ff; background-color: #fff;}
.saerch-result-wrap .sec-bottom .btns-cmm.active{ background-color: #4e81ff; color: #fff;}
.saerch-result-wrap .sec-bottom .btns-cmm.active .txt{ display: none; }
.saerch-result-wrap .sec-bottom .btns-cmm.active::after{ content: attr(data-value); display: inline-block; position: relative; top: -2px; margin-left: 5px; line-height: 16px; width: 16px; text-align: center; border: 1px solid #fff; border-radius: 50%; font-size: 10px; vertical-align: middle; background-color: #fff; color: #4e81ff; right: -10px;}
.saerch-result-wrap .sec-bottom .btns-cmm.btn-reserv{ border-color: #4e81ff; background-color: #4e81ff; color: #fff;}
.saerch-result-wrap .sec-bottom .total-price{  line-height: 32px; font-size: 12px; font-weight: 300;}
.saerch-result-wrap .sec-bottom .total-price strong{ display: inline-block; margin-right: 5px; vertical-align: bottom; font-weight: 700; font-size: 17px;}
.saerch-result-wrap.hotel .box-item{ padding-left: 14px; padding-right: 14px; }
.saerch-result-wrap.hotel .box-item > *:nth-child(1){ padding-top: 0; }
.saerch-result-wrap.hotel .box-item > *:nth-child(1)::before{
    display: none;
}
.saerch-result-wrap.hotel .box-item .thum{ overflow: hidden; margin-bottom: 14px; border-radius: 8px 8px 0 0; margin-left: -14px; margin-right: -14px;}
.saerch-result-wrap.hotel .box-item .thum img{ width: 100%; }

.none-compare-cotn{ height: 100%; background-color: #f2f4f9; text-align: center;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    -ms-align-items: center;
    align-items: center;
}
.none-compare-cotn .innner{ width: 100%; padding-top: 176px; margin-top: -50px; background: url(../../../assets/mobile/images/cmm/bg_error.png) 50% 0 no-repeat; -webkit-background-size: 70px 150px; background-size: 70px 150px;}
.none-compare-cotn .innner dt{ margin-bottom: 11px; font-size: 16px; font-weight: 500; line-height: 24px; color: #757f92; }
.none-compare-cotn .innner dd{ margin-bottom: 30px; font-weight: 300; line-height: 20px; color: #757f92;}

.compare-ticket{ padding-bottom: 60px; }
.compare-ticket .layer-cotn{ padding-top: 100px; }
.compare-ticket.view{ padding-bottom: 0; }
.compare-ticket .layer-cotn{ background-color: #f2f4f9; }
/*.compare-ticket .saerch-result-wrap{ padding-bottom: 90px; }
.compare-ticket .saerch-result-wrap .box-item{ padding: 0; margin: 0; border-radius: 0; box-shadow: none; background-color: transparent; }
.compare-ticket .saerch-result-wrap .box-item > *:nth-child(1)::before{ top: 46px; left: 0; z-index: 1;}
.compare-ticket .saerch-result-wrap .box-item .box-inner{ position: relative; padding: 15px 0 15px 0; margin-bottom: 12px;
    border-radius: 8px; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15); background-color: #fff; }*/
.compare-ticket .form-radio span::before{ width: 16px; height: 16px; border: 0 none; background-color: #c9cfd8; }
.compare-ticket .layer-head { border-bottom-width: 1px; }
.compare-ticket .layer-head .tit{ text-align: center;}
.compare-ticket .layer-head .search-result-top{ border-top:2px solid #ebeef3 ; }

/* RVYN-980 */
/* 항공 스케줄 비교 */
.compare-ticket .box-top{width:100%;position: relative; margin-left: 0; }
.compare-ticket .box-top .box-top-delete_btn{right:10px; bottom:20px; position: absolute; color:#757F92; font-size: 13px; }
.compare-ticket .box-top .box-top-delete_btn a{font-size: inherit; color: inherit}
.compare-ticket .box-top .tit{line-height: 1; margin-bottom: 8px;}
.compare-ticket .btns-detail{display: block; margin-top: -25px; text-decoration: underline; color: #757f92; line-height: 20px;}
.box-item-wrap .aircraft{width: 18px; height: 18px;}
.compare-ticket .box-item-wrap .sec-bottom{align-items: flex-end;}
.compare-ticket .box-item-wrap .sec-bottom .box-btn{margin-bottom: 5px;}
.compare-ticket .box-item-wrap .box-item{margin-bottom: 24px;}
.saerch-result-wrap.lowest-price-wrap{border-top:1px dashed #DADDE1;padding-top:24px;padding-bottom: 90px;}
.saerch-result-wrap.lowest-price-wrap .tit{font-weight: 500;font-size: 15px;margin-bottom: 8px;}
.saerch-result-wrap .box-item-wrap:nth-last-of-type(1) .box-item{margin-bottom: 10px;}
.compare-ticket .box-item-wrap .sec-bottom {display: block;}
.compare-ticket .box-item-wrap .sec-bottom .total-price strong {position: relative;padding-right: 18px;}
.compare-ticket .box-item-wrap .sec-bottom .total-price strong.active::after {transform: rotate(180deg);}
.compare-ticket .box-item-wrap .sec-bottom .total-price strong::after {content: '';width: 10px;height: 5px;position: absolute;top:6px; right: 0; background-image: url(../../../assets/mobile/images/cmm/icn_list_dropdown.png);background-size: contain;background-repeat:no-repeat;top: 50%;margin-top: -2.5px;}
.compare-ticket .box-item-wrap .sec-bottom .box-btn{position: absolute;right: 0;top: 44px;}
.compare-ticket .box-item-wrap .sec-bottom ul.total-price_info{width: auto;line-height: 17.76px;display: none;}
.compare-ticket .box-item-wrap .sec-bottom ul.total-price_info.active{width: auto;line-height: 17.76px;display: block;}
.compare-ticket .box-item-wrap .sec-bottom ul.total-price_info li span{color:#747F92;display: inline-block;font-weight: 350;font-size: 12px;}


/* 최저가 요금 스케줄 상세보기*/
.lowest-price-detail-wrap .sec-schedule-info .sec-mid  .aircraft img{width: 18px;height: 18px;}
#popCompareBox.md-compare-excess .desc span{color:#000;}
/* --RVYN-980 */


/* 예약 요청 & 항공권 결제*/
.reserv-request{ position: relative; }
.reserv-request.in-pay{ padding-bottom: 80px; }
.reserv-request .box-gruoop{ position: relative; }
.reserv-request .box-gruoop .form-cn:last-child{ margin-bottom: 0; }
.reserv-request .box-item{ margin-bottom: 10px; position: relative; }
.reserv-request .box-item dt{padding: 16px; font-size: 18px; line-height: 26px; font-weight: 500; background-color: #fff;}
.reserv-request .box-item dd{ padding:0 16px; background-color: #fff;}
.reserv-request .box-item .box-btn-arrow{ position: absolute; top: 18px; right: 20px;  z-index: 20;}
.reserv-request .box-item .box-btn-arrow .btns-cmm{ width: 20px; height: 20px; border: 0 none; background: url(../../../assets/mobile/images/cmm/btn_dropdown_l.png) 50% 50% no-repeat; -webkit-background-size: 13px 7px; background-size: 13px 7px;}
.reserv-request .box-item dd{ display: none; }
.reserv-request .box-item.active dd{ display: block; }
.reserv-request .box-item.active .box-btn-arrow .btns-cmm{ right: 0;
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotateZ(180deg);
    -o-transform: rotateZ(180deg);
    transform: rotateZ(180deg);
}
.reserv-request .sel-air dt{ border-bottom: 1px solid #ebeef3;}
.reserv-request .sel-air dd{ padding-left: 0; padding-right: 0; }
.reserv-request .sel-air .plan{ padding-left: 0; padding-right: 0; }
.reserv-request .sel-air .plan:first-child{ margin-top: 0; }
.reserv-request .sel-air .plan:last-child{ border-bottom: 0 none; margin-bottom: 0; }
.reserv-request .sel-air .sec-schedule-info { padding-left: 16px; padding-right: 16px; }
.reserv-request .sel-air .sec-schedule-info .box-btn{ right: 0; }
.reserv-request .sel-air .bottom-btn{ padding: 16px; border-top: 1px solid #ebeef3; background-color: #fff;}
.reserv-request .fare-info dt{ padding-left: 0; padding-right: 0; }
.reserv-request .fare-info dd{ padding-left: 0; padding-right: 0; padding-bottom: 5px; }
.reserv-request .fare-info{ position: relative; padding: 0 16px; background-color: #fff;}
.reserv-request .fare-info .clearfix{ padding-bottom: 7px; }
.reserv-request .fare-info .tit{ margin-bottom: 10px; font-size: 15px; font-weight: bold; line-height: 22px;  }
.reserv-request .fare-info .tit span{ font-family: Roboto; }
.reserv-request .fare-info .val,
.reserv-request .fare-info .txt { float: left; width: 50%; font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-request .fare-info .val{ text-align: right; }
.reserv-request .fare-info .total{ overflow: hidden; padding:16px 0; border-top: 1px solid #ebeef3;}
.reserv-request .fare-info .total .txt{ font-size: 14px; line-height: 20px; color: #000; }
.reserv-request .fare-info .total .val{ font-size: 16px; line-height: 20px; font-weight: 700; color: #4e81ff; }
.reserv-request .pay-opinion{ position: relative; background-color: #fff;}
.reserv-request .pay-opinion dd{ padding:0 16px 16px; }
.reserv-request .pay-opinion .desc{ position: relative; padding-left: 14px; margin-bottom: 13px; line-height: 20px;}
.reserv-request .pay-opinion .desc::before{ content: ''; position: absolute; top: 50%; left: 0; width: 8px; height: 8px; margin-top: -4px; background-color: #ff4e50; border-radius: 50%;}
.reserv-request .pay-opinion .desc strong{ font-weight: 700; text-decoration: underline; }
.reserv-request .pay-opinion textarea{display: block; width: 100%; height: 150px; box-sizing: border-box; padding: 12px; border: 1px solid #ebeef3; }
.reserv-request .user-info dd{ padding-top: 15px; padding-bottom: 20px; }
.reserv-request .passenger-info{ position: relative; }
.reserv-request .passenger-info .desc-top{ padding-top: 10px; margin-bottom: 25px; font-size: 13px; line-height: 19px; color: #ff4e50; }
.reserv-request .passenger-info .person{ margin-bottom: 22px; font-size: 16px; font-weight: 700; line-height: 32px; }
.reserv-request .passenger-info .person span{font-family: Roboto;}
.reserv-request .passenger-info .desc-passport{ position: relative; padding: 12px; margin-bottom: 15px; border-radius: 5px; font-size: 13px; line-height: 19px; color: #757f92; background-color: #f2f4f9;}
.reserv-request .passenger-info .desc-passport strong{ color: #4e81ff; }
.reserv-request .passenger-info dd{ padding-bottom: 20px; }
.reserv-request .passenger-info .btn-search-passenger{ position: absolute; top: 0; right: 0; width: 98px; line-height: 30px}
.reserv-request .policy-agree{ background-color: #fff; margin-bottom: 0; padding-bottom: 150px;}
.reserv-request .policy-agree .form-chkbox span{ padding-left: 30px; }
.reserv-request .policy-agree .box-cell{ position: relative; border-bottom: 1px solid #ebeef3;  }
.reserv-request .policy-agree .box-cell:first-child{ margin-top: 5px; border-top: 1px solid #ebeef3;  }
.reserv-request .policy-agree .box-tit{ padding-top: 16px; padding-bottom: 16px; }
.reserv-request .policy-agree .box-desc{ display: none; padding: 0 0 15px 30px; font-size: 13px; font-weight: 300; line-height: 19px;}
.reserv-request .policy-agree .btns-acoodi{ position: absolute; top: 15px; right: 0; width: 20px; height: 20px; background: url(../../../assets/mobile/images/cmm/ico_select.png) 50% 50% no-repeat; -webkit-background-size: 8px 4px; background-size: 8px 4px;}
.reserv-request .policy-agree .all-check{ padding: 19px 0 0;  font-weight: 500;}
.reserv-request .policy-agree .box-cell.active .box-desc{ display: block; }
.reserv-request .policy-agree .box-cell.active .btns-acoodi{
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotateZ(180deg);
    -o-transform: rotateZ(180deg);
    transform: rotateZ(180deg);
}
.reserv-request .box-item .sec-bills-info{ padding: 0; }
.reserv-request .box-item .sec-bills-info .box-item{ padding-bottom: 0; margin-bottom: 0; }
.reserv-request .ticket-complete .desc-rule{ padding-left: 0; padding-right: 0; margin: 0; }
.reserv-request .desc-boarding{ font-size: 12px; line-height: 18px; color: #4e81ff; }
.reserv-request .pay-guide{ padding: 20px 16px;  background-color: #fff; }
.reserv-request .pay-guide dl{ padding: 16px; background-color: #f2f4f9; color: #3f4e73; border-radius: 5px;}
.reserv-request .pay-guide dt{ margin-bottom: 12px; font-weight: 500; line-height: 20px; }
.reserv-request .pay-guide dd{ margin-left: 9px; text-indent: -9px; font-size: 13px; line-height: 19px;   }
.reserv-request .pay-card{ padding:0 16px 20px; background-color: #fff; }
.reserv-request .pay-card .tit{ margin-bottom: 24px; font-size: 18px; font-weight: 500; line-height: 26px; }
.reserv-request .pay-card .form-tit{ position: relative; }
.reserv-request .pay-card .form-cn{}
.reserv-request .pay-card .box-tab{ overflow: hidden; padding-top: 12px; margin-bottom: 24px;}
.reserv-request .pay-card .box-tab label{   }
.reserv-request .pay-card .box-tab input{ width: 0; height: 0; margin: 0; padding: 0; opacity: 0;visibility: hidden; }
.reserv-request .pay-card .box-tab .txt{ display: inline-block; width: 100%; font-size: 13px; font-weight: 500; letter-spacing: -0.5px; color: #babdc3; line-height: 40px; border: 1px solid #c9cfd8; text-align: center; box-sizing: border-box;}
.reserv-request .pay-card .box-tab .personal{ border-radius: 5px; }
.reserv-request .pay-card .box-tab .personal .txt{ border-radius: 5px; }
.reserv-request .pay-card .box-tab .law{  }
.reserv-request .pay-card .box-tab .law.pr .txt{border-radius: 5px 0 0 5px; }
.reserv-request .pay-card .box-tab .law.no .txt{ border-radius: 0 5px 5px 0; }
.reserv-request .pay-card .box-left{ float: left; width: 33%;}
.reserv-request .pay-card .box-right{ float: right; width: 65%;}
.reserv-request .pay-card .box-right label{ float: left; width: 50%; display: block;}
.reserv-request .pay-card .box-tab input:checked ~ .txt{ border-color: #4e81ff; background-color: rgba(78, 129, 255, 0.07); color: #4e81ff; }
.reserv-request .pay-card .all-check{ -ms-word-break: keep-all; word-break: keep-all; }




/* 예약 완료 */
.reserv-complete{  }
.reserv-complete .tit{ padding: 19px 0; font-size: 18px; font-weight: 700; line-height: 30px;  }
.reserv-complete .box-top{ position: relative; height: 258px; padding:0 16px; border-bottom: 10px solid #f2f4f9; background: url(../../../assets/mobile/images/cmm/bg_reserv_complete.png) 0 0 no-repeat; -webkit-background-size: cover; background-size: cover; color: #fff;}
.reserv-complete .box-top.in-hotel{ height: auto; }
.reserv-complete .box-top dl{ padding-top: 60px; }
.reserv-complete .box-top dt{ margin-bottom: 15px; font-size: 24px; letter-spacing: -1px; line-height: 32px;  }
.reserv-complete .box-top dd{ margin-bottom: 25px; font-weight: 300; line-height: 20px; }
.reserv-complete .box-top .code{ font-size: 16px; line-height: 24px; font-weight: 500; }
.reserv-complete .box-top .code strong{ font-weight: 700; }
.reserv-complete .box-top .reserv-date{ overflow: hidden; padding: 14px 0; margin-top: 16px; border-top: 1px solid rgba(250,250,250,0.3); font-size: 13px; color: #fff;}
.reserv-complete .box-top .reserv-date .txt{ float: left; font-weight: 300;}
.reserv-complete .box-top .reserv-date .val{ float: right; font-weight: 500;}
.reserv-complete .box-middle { position: relative; border-bottom: 10px solid #f2f4f9;}
.reserv-complete .fare-info{ position: relative; padding: 0 16px; background-color: #fff;}
.reserv-complete .fare-info .inner{ padding-bottom: 5px; }
.reserv-complete .fare-info .person{margin-bottom: 10px; font-size: 15px; font-weight: 700; line-height: 22px; }
.reserv-complete .fare-info .person span{ font-family: Roboto; }
.reserv-complete .fare-info .room-date{ overflow: hidden; margin-bottom: 14px; }
.reserv-complete .fare-info .room-date span{ display: inline-block; line-height: 22px; font-size: 15px; font-weight: 500; }
.reserv-complete .fare-info .room-date strong{ font-weight: 700; }
.reserv-complete .fare-info .room-date .de{ color: #9da9be; }
.reserv-complete .fare-info .room-date .multi{ margin:0 4px; color: #9da9be; font-weight: 300; }
.reserv-complete .fare-info .clearfix{ padding-bottom: 7px; }
.reserv-complete .fare-info .tit{  font-size: 18px; font-weight: bold; line-height: 30px;  }
.reserv-complete .fare-info .tit span{ font-family: Roboto; }
.reserv-complete .fare-info .val,
.reserv-complete .fare-info .txt { float: left; width: 50%; font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-complete .fare-info .val{ text-align: right; }
.reserv-complete .fare-info .total{ overflow: hidden; padding:16px 0; border-top: 1px solid #ebeef3;}
.reserv-complete .fare-info .total .txt{ font-size: 16px; font-weight: 500; line-height: 20px; color: #000; }
.reserv-complete .fare-info .total .val{ font-size: 18px; font-weight: 700; line-height: 20px; font-weight: 700; color: #4e81ff; }
.reserv-complete.domestic .box-middle{ border-color: #fff; }
.reserv-complete.domestic .fare-info .txt{ position: relative; width: 40%; }
.reserv-complete.domestic .fare-info .txt .person{ position: absolute; top: 0; right: 0; font-weight: 400; font-size: 13px; line-height: 19px; color: #757f92;}
.reserv-complete.domestic .fare-info .total .txt{ font-size: 14px; }
.reserv-complete.domestic .fare-info .total .val{ font-size: 16px; }
.reserv-complete.domestic .box-btn{ position: fixed; bottom: 0; left: 0; right: 0;  }

.reserv-complete .box-bottom { position: relative; padding:0 16px; margin-bottom: 40px;}
.reserv-complete .pay-opinion{ position: relative; background-color: #fff;}
.reserv-complete .pay-opinion dd{ padding:0 16px 16px; }
.reserv-complete .pay-opinion .desc{ position: relative; padding-left: 14px; margin-bottom: 13px; line-height: 20px;}
.reserv-complete .pay-opinion .desc::before{ content: ''; position: absolute; top: 50%; left: 0; width: 8px; height: 8px; margin-top: -4px; background-color: #ff4e50; border-radius: 50%;}
.reserv-complete .pay-opinion .desc strong{ font-weight: 700; text-decoration: underline; }
.reserv-complete .box-btn{ overflow: hidden; padding:0 16px 20px; }
.reserv-complete .box-btn .btns-cmm{ width: 48%; }
.reserv-complete .box-btn .btns-cmm:first-child{ float: left; }
.reserv-complete .box-btn .btns-cmm:last-child{ float: right; }
.reserv-complete .btn-close{ position: absolute; top: 22px; right: 16px; }
.reserv-complete .btn-close .btns-cmm{ border:0 none;width: 14px; height: 14px; background: url(../../../assets/mobile/images/cmm/btn_close_side.png) 0 0 no-repeat; -webkit-background-size: 14px; background-size: 14px;}

.ticket-complete .sec-result-sel{ margin-bottom: 10px; }
.ticket-complete .sec-bills-info{ margin-bottom: 10px; }
.ticket-complete .desc-rule{ padding: 20px 16px; margin-bottom: 10px; text-align: center; background-color: #fff; }
.ticket-complete .desc-rule p{ margin-bottom: 18px; text-align: left; font-size: 13px; line-height: 19px; color: #757f92; }
.ticket-complete .reserv-btn{ padding-top: 24px; padding-bottom: 24px; background-color: #fff; text-align: center; }

.date-slider-domestic{ position: relative; }
.date-slider-domestic li{ min-width: 96px; width: auto !important;}
.date-slider-domestic li a{display: block; padding: 11px 0; margin: 0 11px; font-size: 12px; line-height: 18px; color: #9da9be; border-bottom: 2px solid #fff;}
.date-slider-domestic li.active a{ font-weight: 500; border-bottom-color: #34446e; color: #34446e;}

.btn-filter.type-hotel{ z-index: 10; }
body.open-map{ overflow: hidden; }
body.open-map .btn-filter.type-hotel{ z-index: 40; bottom: initial; top: 22px;}
body.open-map .btn-filter.type-hotel .box-wrap .btns-cmm.map{ display: none; }
body.open-map .btn-filter.type-hotel .box-wrap .btns-cmm.list{ display: inline-block; }
body.open-map .swiper-container{}
body.open-map .swiper-container .thum{ position: absolute; top: 0; left: 0;  border-radius: 0 !important; margin: 0 !important;}
body.open-map .swiper-container .thum img{ width: 24.5vw !important; height: 32vw; }
body.open-map .swiper-container .box-item { display: block; }
body.open-map .saerch-result-wrap.hotel { padding: 0; background-color: transparent;  height: auto !important;	position: fixed; bottom: 24px; left: 0; width: 100%; z-index: 40; padding: 0;}
body.open-map .swiper-container .swiper-wrapper { display: flex !important; padding-bottom: 8px; }
body.open-map .swiper-container .swiper-slide {  }
body.open-map .swiper-container .box-hotel-item{  padding: 0; height: 32vw; box-sizing: border-box; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15);border-radius: 8px; overflow: hidden; margin-bottom: 0;}
body.open-map .swiper-container .box-hotel-item .name{ width: 100%; padding-left: calc(24.5vw + 10px); box-sizing: border-box;}
body.open-map .swiper-container .box-hotel-item .name .kr{ padding-top: 11px; font-size: 15px; line-height: 22px; display: inline-block; width: 95%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
body.open-map .swiper-container .box-hotel-item .name .en{ font-size: 11px; line-height: 13px; margin-bottom: 6px; color: #000;  display: inline-block; width: 95%; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
body.open-map .swiper-container .box-hotel-item .grade{ width: 100%; padding-left: calc(24.5vw + 10px); margin-bottom: 0; box-sizing: border-box;}
body.open-map .swiper-container .box-hotel-item .price{ padding-right: 14px; }
body.open-map .swiper-container .box-hotel-item .price .day{display: block; line-height: 16px;}
.layer-mapview{ display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background-color: #fff; z-index: 35; }
.layer-mapview .map-box .box-inner{ height: 100vh; }
.layer-mapview .btn-close{position: absolute; top: 20px; right: 16px; width: 44px; height: 44px; background:#fff url(../../../assets/mobile/images/search/btn_close_b.png) 50% 50% no-repeat; -webkit-background-size: 14px; background-size: 14px; border-radius: 50%; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15);}
.layer-mapview .price{ position: absolute; z-index: 10;}
.layer-mapview .price button{
    position: relative;
    padding:0 10px;
    line-height: 28px;
    font-size: 14px;
    font-weight: 700;
    color: #4e81ff;
    border-radius: 14px;
    background-color: #fff;
    box-shadow: -2px 6px 15px 0 rgba(157, 169, 190, 0.3);
    cursor: pointer;
    outline: none;
}
.layer-mapview .price button:after {
    top: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
    border-color: rgba(136, 183, 213, 0);
    border-top-color: #FFFFFF;
    border-width: 5px;
    margin-left: -5px;
}
.layer-mapview .price.active button{
    background-color: #4e81ff;
    color: #fff;
}
.layer-mapview .price.active button::after {
    border-top-color: #4e81ff;
}
.gm-control-active.gm-fullscreen-control{ display: none; }

/* 호텔 상세 페이지 */
.detail-hotel-item{position: relative; padding:0 16px; background-color: #fff;}

.detail-hotel-item .pg-tit{ font-size: 15px; font-weight: 700; line-height: 22px; }
.detail-hotel-item .btn-more-arrow{ position: absolute; top: 0; right: 16px; padding-right: 14px; background: url(../../../assets/mobile/images/search/btn-open-s.png) 100% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px; font-size: 13px; line-height: 19px;}

.detail-hotel-item .room-photo{ margin-bottom: 20px; margin-left: -16px; margin-right: -16px; }
.detail-hotel-item .room-photo img{ width: 100%; }
.detail-hotel-item .room-photo .swiper-button{ width: 30px; height: 30px; background: rgba(0, 0, 0, 0.7) url(../../../assets/mobile/images/search/slide-arrow-right.png) 50% 50% no-repeat; border-radius: 50%; -webkit-background-size: 4px 8px;
    background-size: 4px 8px;}
.detail-hotel-item .room-photo .swiper-button::after{display: none;}
.detail-hotel-item .room-photo .swiper-button-next{
    -webkit-transform: rotateZ(180deg);
    -ms-transform: rotateZ(180deg);
    -o-transform: rotateZ(180deg);
    transform: rotateZ(180deg);
}
.detail-hotel-item .box-hotel-item{ margin-bottom: 20px; border-bottom: 1px solid #ebeef3; }
.detail-hotel-item .box-hotel-item .name .kr{ margin-bottom: 2px; font-size: 18px; line-height: 27px; font-weight: 700; }
.detail-hotel-item .box-hotel-item .name .en{ margin-bottom: 7px; font-size: 12px; line-height: 18px; color: #000; }
.detail-hotel-item .box-hotel-item .grade{ margin-bottom: 0; padding-bottom: 20px; }
.detail-hotel-item .rooms-info{ position: relative; padding-bottom: 20px; margin-bottom: 20px; border-bottom: 1px solid #ebeef3;}
.detail-hotel-item .rooms-info .pg-tit{ margin-bottom: 16px; }
.detail-hotel-item .rooms-info .address{ margin-bottom: 16px; padding-left: 14px; background: url(../../../assets/mobile/images/search/icn-marker-gray-solid.png) 0 50% no-repeat; -webkit-background-size: 10px 13px; background-size: 10px 13px; font-size: 12px; line-height: 18px; color: #9da9be; }
.detail-hotel-item .rooms-info .address .btn-view-map{ font-size: 12px; font-weight: 500; color: #4e81ff; margin-left: 10px; }
.detail-hotel-item .rooms-info .desc p{line-height: 20px;  max-height: 60px; overflow: hidden;}
.detail-hotel-item .rooms-info .desc .btn-more{ display: block; margin-top: 6px; font-size: 12px; font-weight: 500; color: #4e81ff;}
.detail-hotel-item .rooms-info .map-wrap{ height: 120px; margin-bottom: 6px;}
.detail-hotel-item .rooms-info .map-wrap .pin{ position: absolute; }
.detail-hotel-item .rooms-info .map-wrap .pin img{ width: 44px; height: 49px; }
.detail-hotel-item .rooms-info .map-wrap .inner{ height: 100%; position: relative; }
.detail-hotel-item .rooms-info .map-wrap .map-box{ height: 100%; border-radius: 10px; overflow: hidden; }
.detail-hotel-item .rooms-info .map-wrap .btn-close{ display: none; position: fixed; top: 0; right: 0; width: 36px; height: 36px; box-shadow: 0 2px 8px 0 rgba(132, 146, 170, 0.15); background-color: #fff; border-radius: 50%;}
.detail-hotel-item .rooms-info .map-wrap .bottom-area{ display: none;  padding: 14px 0 24px;}
.detail-hotel-item .rooms-info .map-wrap .bottom-area dl{ padding-left: 20px; }
.detail-hotel-item .rooms-info .map-wrap .bottom-area dt{  margin-bottom: 5px; font-weight: 700; line-height: 19px; color: #757f92; }
.detail-hotel-item .rooms-info .map-wrap .bottom-area dd{padding-left: 14px; margin-bottom: 20px; font-size: 12px; color: #9da9be; background: url(../../../assets/mobile/images/search/icn-marker-gray-solid.png) 0 50% no-repeat; -webkit-background-size: 10px 13px; background-size: 10px 13px;}
.detail-hotel-item .rooms-info .map-wrap .bottom-area .btn-search-road{display: block; width: 230px; margin: 0 auto; line-height: 48px; background-color: #4e81ff; border-radius: 24px; color: #fff; font-size: 15px; font-weight: 500;}
.detail-hotel-item .rooms-info.open .map-wrap .inner{ position: fixed; top: 0; left: 0; bottom: 0; right: 0; width: 100%; z-index: 35;}
.detail-hotel-item .rooms-info.open .map-wrap .map-box{border-radius: 0;}
.detail-hotel-item .rooms-info.open .map-wrap .btn-close{ display: block; top: 16px; right: 16px; text-indent: -9999em; background:#fff url(../../../assets/mobile/images/search/btn-close-b.png) 50% 50% no-repeat; -webkit-background-size: 10px; background-size: 10px;}
.detail-hotel-item .rooms-info.open .map-wrap .bottom-area{  display: block; position: fixed; bottom: 0; left: 0; width: 100%; background-color: #fff; }


.detail-hotel-item .comport-service{ position: relative; padding-bottom: 25px; margin-left: -16px; margin-right: -16px; margin-bottom: 20px; border-bottom: 12px solid #f2f4f9;}
.detail-hotel-item .comport-service .pg-tit{ margin-bottom: 24px; margin-left: 16px; }
.detail-hotel-item .bussiness-role{ position: relative; margin-bottom: 30px;}
.detail-hotel-item .bussiness-role .pg-tit{ margin-bottom: 24px; }
.detail-hotel-item .bussiness-role dt{ margin-bottom: 8px; font-size: 14px; font-weight: 500; line-height: 20px; }
.detail-hotel-item .bussiness-role dd{ border-radius: 6px; line-height: 38px; text-align: center; background-color: #3f4e73; font-size: 13px; }
.detail-hotel-item .bussiness-role dd span{ padding-left: 19px; display: inline-block; color: #e2e4e8; background: url(../../../assets/mobile/images/search/icn-qualification.png) 0 50% no-repeat; -webkit-background-size: 14px 14px; background-size: 14px 14px;}
.detail-hotel-item .bussiness-role dd strong{ display: inline-block; margin-left: 12px; font-weight: 700; }
.detail-hotel-item .bussiness-role .label-type{ position: absolute; top: 2px; right: 0; }
.detail-hotel-item .bussiness-role .label-type *{ float: left; padding-left: 26px; margin-right: 20px; font-size: 12px;line-height: 18px; background-position: 0 0; background-repeat: no-repeat; -webkit-background-size: 18px; background-size: 18px;}
.detail-hotel-item .bussiness-role .label-type .in{ background-image: url(../../../assets/mobile/images/cmm/img_inpolicy.png); }
.detail-hotel-item .bussiness-role .label-type .out{ background-image: url(../../../assets/mobile/images/cmm/img_outofpolicy.png);  }
.detail-hotel-item .rooms-recomand{ position: relative; }
.detail-hotel-item hr.line{margin-bottom: 20px; margin-left: -16px; margin-right: -16px; border: 0 none; height: 12px; background-color: #f2f4f9; }
.detail-hotel-item .rooms-recomand .pg-tit{ margin-bottom: 4px; }
.detail-hotel-item .rooms-recomand .sub-tit{ margin-bottom: 18px; font-size: 12px; line-height: 18px; color: #9da9be; }
.detail-hotel-item .rooms-recomand .box-none-room{ position: relative; text-align: center; padding: 24px 0; margin-bottom: 25px; border-radius: 8px; box-shadow: 0 2px 10px 0 rgba(132, 146, 170, 0.2); background-color: #fff;}
.detail-hotel-item .rooms-recomand .box-none-room::before{
    content: '';
    display: block;
    margin: 0 auto;
    width: 40px;
    height: 49px;
    margin-bottom: 16px;
    background: url(../../../assets/mobile/images/search/icn-noresult.png) no-repeat 0 0;
    -webkit-background-size: 100%;
    background-size: 100%;

}
.detail-hotel-item .rooms-recomand .box-none-room dt{ margin-bottom: 9px; font-weight: 500; line-height: 20px; color: #757f92; }
.detail-hotel-item .rooms-recomand .box-none-room dd{ font-size: 13px; line-height: 19px; color: #9da9be; }
.detail-hotel-item .rooms-recomand .box-item{
    position: relative;
    padding:17px 18px;
    margin-bottom: 14px;
    border-radius: 8px;
    box-shadow: 0 2px 10px 0 rgba(132, 146, 170, 0.2);
    background-color: #ffffff;
}
.detail-hotel-item .rooms-recomand .box-item:last-child{
    margin-bottom: 0; }
.detail-hotel-item .rooms-recomand .box-item::after{ content: ''; position: absolute; top: 0; left: 0; width: 20px; height: 20px; -webkit-background-size: 100%;background-size: 100%; background-repeat: no-repeat; background-position: 0 0;}
.detail-hotel-item .rooms-recomand .box-item.in::after{ background-image: url(../../../assets/mobile/images/cmm/ico_inpolicy.png); }
.detail-hotel-item .rooms-recomand .box-item.out::after{ background-image: url(../../../assets/mobile/images/cmm/ico_outofpolicy.png); }
.detail-hotel-item .rooms-recomand .box-item .name{ margin-bottom: 13px; font-size: 16px; font-weight: 500; line-height: 24px; margin-right: 50px;}
/*.detail-hotel-item .rooms-recomand .box-item.arlim::before{ content: '알림'; position: absolute; top: 16px; right: 16px; width: 31px; line-height: 16px; border: 1px solid #ff9268; border-radius: 5px; text-align: center; color: #ff9268; font-size: 12px; font-weight: 500;}*/
.detail-hotel-item .rooms-recomand .box-item .note{ content: '알림'; position: absolute; top: 16px; right: 16px; width: 31px; line-height: 16px; border: 1px solid #ff9268; border-radius: 5px; text-align: center; color: #ff9268; font-size: 12px; font-weight: 500;}
.detail-hotel-item .rooms-recomand .box-item .note * { color:#ff9268  }
.detail-hotel-item .rooms-recomand .box-item .meal{ margin-bottom: 2px; padding-left: 16px; background: url(/static/user/images/search/icn-breakfast.png) 0 50% no-repeat; -webkit-background-size: 11px 13px; background-size: 11px 13px; font-size: 12px; line-height: 18px; color: #757f92; }
.detail-hotel-item .rooms-recomand .box-item .meal.not{ background-image: url(/static/user/images/search/icn-breakfast-x.png); }
.detail-hotel-item .rooms-recomand .box-item .cancel{ margin-bottom: 8px; font-size: 12px; line-height: 18px; }
.detail-hotel-item .rooms-recomand .box-item .cancel .qustion{ display: inline-block; margin-left: 4px; width: 13px; height: 13px; background: url(/static/user/images/search/icn-qm-g-solid.png) 0 0 no-repeat; vertical-align: middle; -webkit-background-size: 100%; background-size: 100%;}
.detail-hotel-item .rooms-recomand .box-item .cancel .qustion:hover{ cursor: pointer; }
.detail-hotel-item .rooms-recomand .box-item .cancel.not{ color: #ff4e50 }
.detail-hotel-item .rooms-recomand .box-item .price{ font-size: 17px; line-height: 25px; font-weight: 700;}
.detail-hotel-item .rooms-recomand .box-item .btn-reserv{ position: absolute; bottom: 16px; right: 15px; width: 76px; text-align: center; font-weight: 500; color: #fff; background-color: #4e81ff; line-height: 30px; border-radius: 16px;}
.detail-hotel-item .rooms-recomand .box-item .btn-reserv-cant{ position: absolute; bottom: 16px; right: 15px; width: 76px; text-align: center; font-weight: 500; color: #fff; background-color: #757F92FF; line-height: 30px; border-radius: 16px;}
.detail-hotel-item .rooms-recomand .btn-view-all-room{ width: calc(100% - 150px); margin:30px 75px ; line-height: 40px; border: 1px solid #4e81ff; border-radius: 21px; font-size: 14px; font-weight: 500; color: #4e81ff; }
.detail-hotel-item .rooms-recomand .desc-list{ position: relative; padding: 13px 0 20px; }
.detail-hotel-item .rooms-recomand .desc-list .txt{ margin-bottom: 16px; font-size: 12px; line-height: 18px; color: #34446e; }
.detail-hotel-item .rooms-recomand .desc-list .txt span{ position: relative; top: -1px;display: inline-block; margin-right: 2px; margin-left: 4px; width: 13px; height: 13px; background: url(../../../assets/mobile/images/search/icn-qm-g-solid.png) 0 0 no-repeat; vertical-align: middle; -webkit-background-size: 100%; background-size: 100%;}
.detail-hotel-item .rooms-recomand .box-sort{ position: relative; }
.detail-hotel-item .rooms-recomand .box-sort label{ position: relative; display: inline-block; }
.detail-hotel-item .rooms-recomand .box-sort label input{ position: absolute; top: 0; left: 0; visibility: hidden; opacity: 0; width: 0; height: 0; padding: 0; margin: 0; border: 0 none; }
.detail-hotel-item .rooms-recomand .box-sort label span{ display: block;  width: 67px; line-height: 26px; box-sizing: border-box; border: 1px solid #c9cfd8; border-radius: 16px; font-size: 12px; text-align: center; color: #34446e;}
.detail-hotel-item .rooms-recomand .box-sort label input:checked ~ span{ background-color: #34446e; border-color: #34446e; color: #fff; }
.detail-hotel-item .rooms-recomand .none-opt-roomos{ position: relative;}
.detail-hotel-item .rooms-recomand .none-opt-roomos .box-inner{ padding: 103px 0 65px; text-align: center;}
.detail-hotel-item .rooms-recomand .none-opt-roomos::before{ content: ''; display: block; width: 70px; height: 150px; margin: 0 auto 26px; background: url(../../../assets/mobile/images/search/img_error.png) 0 0 no-repeat; -webkit-background-size: 100%; background-size: 100%;}
.detail-hotel-item .rooms-recomand .none-opt-roomos .txt{ margin-bottom: 30px; font-size: 16px; line-height: 24px; font-weight: 500; color: #757f92; }
.detail-hotel-item .rooms-recomand .none-opt-roomos .btn-remove-all{ display: block; width: 70%; line-height: 48px; margin: 0 auto; border-radius: 24px; background-color: #4e81ff;font-size: 15px; font-weight: 500; color: #fff;  }
.detail-hotel-item .rooms-recomand .spc-cotn{ position: relative; display: block; margin-top: 16px; margin-left: -18px; margin-right: -18px; padding: 0 23px 0 47px;  border: 1px solid rgba(78, 129, 255, 0.5); border-radius: 0 0 8px 8px; background:#f2f6ff url(../../../assets/mobile/images/search/arrow_blue.png) 97.5% 50% no-repeat; -webkit-background-size: 6px 8px; background-size: 6px 8px;}
.detail-hotel-item .rooms-recomand .spc-cotn span{ display: block; font-size: 12px; line-height: 28px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: #4e81ff; }
.detail-hotel-item .rooms-recomand .spc-cotn span::before{content: '특전'; position: absolute; top: 5px; left: 6px; width: 31px; text-align: center; line-height: 16px; border: 1px solid #4e81ff; vertical-align: middle; border-radius: 5px; }
.detail-hotel-item .rooms-recomand .box-item.special{ padding-bottom: 0; }
.detail-hotel-item .rooms-recomand .box-item.special .btn-reserv{ bottom: 44px; }

.detail-hotel-item .relative-products{ position: relative; padding-bottom: 20px; }
.detail-hotel-item .relative-products .swiper-container { margin-left: -16px; padding-left: 16px; margin-right: -16px; }
.detail-hotel-item .relative-products .pg-tit{ margin-bottom: 24px; }
.detail-hotel-item .relative-products .thum{ margin-bottom: 18px; }
.detail-hotel-item .relative-products .thum img{ width: 100%; border-radius: 10px; }
.detail-hotel-item .relative-products .box-hotel-item{ border-bottom: 0 none; }
.detail-hotel-item .relative-products .box-hotel-item .name .kr{ display: block; font-size: 15px; line-height: 18px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.detail-hotel-item .relative-products .box-hotel-item .grade{ padding-bottom: 7px; }
.detail-hotel-item .relative-products .val{ position: relative; }
.detail-hotel-item .relative-products .val .days{ font-size: 11px; line-height: 18px; }
.detail-hotel-item .relative-products .val .price{ text-align: left; font-size: 16px; font-weight: 500; }

.comport-list{
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    display: flex;
    flex-flow: wrap;
    justify-content: space-between;
}
.comport-list li{ min-width: 25%; text-align: center;}
.comport-list li span{text-align: center; display: inline-block; font-size: 12px; line-height: 18px;}
.comport-list li i{ display: block; position: relative; height: 34px; }
.comport-list li i::after{ content: ''; position: absolute; top: 50%; left: 50%; background: url(../../../assets/mobile/images/search/spr-ico-comport-detail.png) 0 0 no-repeat; -webkit-background-size: 107px 81px; background-size: 107px 81px;}
.comport-list li.wifi i::after{ width: 29px; height: 22px; margin-top:-11px; margin-left: -14.5px; background-position: 0 -59px;}
.comport-list li.pool i::after{ width: 27px; height: 26px; margin-top:-13px; margin-left: -13.5px; background-position: -60px -28px;}
.comport-list li.coast i::after{ width: 28px; height: 28px; margin-top:-14px; margin-left: -14px; background-position: -60px 0;}
.comport-list li.parking i::after{ width: 28px; height: 28px; margin-top:-14px; margin-left: -14px; background-position: -30px -31px;}
.comport-list li.language i::after{ width: 30px; height: 31px; margin-top:-15.5px; margin-left: -15px; background-position: 0 0;}
.comport-list li.baby i::after{ width: 30px; height: 28px; margin-top:-14px; margin-left: -15px; background-position: 0 -31px;}
.comport-list li.hour24 i::after{ width: 30px; height: 30px; margin-top:-15px; margin-left: -15px; background-position: -30px 0;}
.comport-list li.restaurant i::after{ width: 19px; height: 28px; margin-top:-14px; margin-left: -9.5px; background-position: -88px 0;}

.reserv-request .passenger-info .box-agree + .box-gruoop{ margin-top: 20px; }

.banner-section{ margin-bottom: 45px; }
.banner-section a{ display: block; }
.banner-section a img{ display: block; width: 100%; height: auto; }

.btn-tickt-reserv .btns-cmm + .btns-cmm { margin-top: 10px; }
.btn-tickt-reserv .icn-list.ntc { background-image: url(../../../assets/mobile/images/search/icn_list_reserv.svg); }

.hotel .search-result-top{ background-color: transparent; padding: 2px 0 14px; font-size: 14px; font-weight: 400; }
.hotel .search-result-top .left-col{ float: left; }
.hotel .search-result-top .total span{ font-weight: 700; }
.hotel .search-result-top .right-col{ float: right; }
.hotel .search-result-top .select-cmm select{ padding-right: 18px; height: auto; border: none; text-align: right; }
.hotel .search-result-top .select-cmm::before{ margin-top: -5px; right: 2px; width: 12px; height: 12px; background: url(../../../assets/mobile/images/cmm/btn_dropdown_l.png) no-repeat center / contain; }

.hotel .box-hotel-item a { display: block; }
.hotel .box-hotel-item .info { margin-top: 20px; text-align: right; }
.detail-hotel-item .rooms-recomand .box-item .info { margin-bottom: 5px; }
.hotel .box-hotel-item .info,
.detail-hotel-item .rooms-recomand .box-item .info { font-size: 0; line-height: 0; letter-spacing: -.2px; color: #757F92; }
.hotel .box-hotel-item .info .day,
.detail-hotel-item .rooms-recomand .box-item .info .day { display: inline-block; padding-right: 8px; padding-left: 0; font-size: 12px; line-height: 15px; }
.hotel .box-hotel-item .info .mileage,
.detail-hotel-item .rooms-recomand .box-item .info .mileage { position: relative; display: inline-block; padding-left: 8px; font-size: 12px; line-height: 15px; }
.hotel .box-hotel-item .info .mileage img,
.detail-hotel-item .rooms-recomand .box-item .info .mileage img { margin-right: 5px; width: auto; height: 12px; vertical-align: -2px; }
.hotel .box-hotel-item .info .day + .mileage::after,
.detail-hotel-item .rooms-recomand .box-item .info .day + .mileage::after { content: ''; position: absolute; top: 50%; left: 0; height: 10px; margin-top: -5px; border-left: 1px solid #e2e4e8; }
.hotel .box-hotel-item .price .val{ margin-left: 3px; vertical-align: middle; }
.detail-hotel-item .rooms-recomand .box-item .btn-arrow-rate { vertical-align: top; }
.detail-hotel-item .rooms-recomand .box-item .btn-arrow-rate .cn { right: unset; left: 0; }

.btn-arrow-rate { display: inline-flex; gap: 5px; align-items: center; font-size: 12px; font-weight: 400; vertical-align: middle; }
.btn-arrow-rate .btn-default { display: block; width: 10px; height: 10px; background: url(../../../assets/mobile/images/cmm/btn_dropdown_l.png) no-repeat center / contain; }
.btn-arrow-rate .cn { display: none; position: absolute; top: calc(100% + 5px); right: 0; z-index: 1; padding: 11px 13px 13px; width: 164px; height: auto; background-color: #34446e; border-radius: 5px; text-align: left; font-size: 12px; line-height: 26px; font-weight: 400; }
.btn-arrow-rate.active .btn-default { transform: rotate(180deg); }
.btn-arrow-rate.active .cn { display: block; }

.ul-table { display: flex; justify-content: space-between; padding-top: 12px; width: 100%; }
.ul-table > li { letter-spacing: -0.22px; font-size: 12px; font-weight: 400; line-height: 1.4; color: #ffffff; }
.info-date { font-size: 12px; font-weight: 400; line-height: 1.4; color: #E2E4E8; }

/* DT-24190 마스터카드 유입 고객 할인 표기 */
.search-reserv-cont{padding: 0 20px 42px 20px;}
.search-reserv-cont .search-reserv_title{margin-top: 56px;}
.search-reserv-cont .search-reserv_title dt{font-size: 22px; font-weight: 500; margin-bottom: 10px; line-height: 32px;}
.search-reserv-cont .search-reserv_title dd p{font-size: 13px; line-height: 19px; font-weight: 400; color: #757f92;}
.search-reserv-cont .box-btn{text-align: center; margin-top: 40px;}
.search-reserv-cont .box-btn button{width: 230px;}
.search-reserv_form{margin-top: 20px;}
.search-reserv_form .validate{padding-top: 20px; font-size: 12px;}
.search-reserv_form .name, .search-reserv_form .number{position: relative;}
.search-reserv_form .name input, .search-reserv_form .number input{height: 40px;}
.search-reserv_form .name.in02{margin-top: 15px;}
.search-reserv-cont input::placeholder{color: #c9cfd8; font-weight: 300;}

.saerch-result-wrap .sec-bottom .total-price .fare-detail{display: flex; justify-content: flex-start; gap: 4px;}
.saerch-result-wrap .sec-bottom .total-price.sales .sale{color: #ff4e50;}
.saerch-result-wrap .sec-bottom .total-price.sales .val.group{display: block;}
.saerch-result-wrap .sec-bottom .total-price.sales .fare-detail .val.group .result{color: #ff4e50;}
.saerch-result-wrap .sec-bottom .total-price.sales .total{font-size: 17px; font-weight: 700;}
.saerch-result-wrap .sec-bottom .total-price.sales .total .prev{color: #757f92; font-size: 13px;}
.saerch-result-wrap .sec-bottom .total-price.sales .del{text-decoration: line-through;}
.saerch-result-wrap .sec-bottom .total-price.sales .total .result{color: #000; font-size: 17px;}

/* DT-25030 객실리스트 */
.detail-hotel-item .rooms-recomand.strad .box-item {
    padding: 0 18px 16px 18px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum {
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    margin-left: -18px;
    margin-right: -18px;
    /*height: 120px;*/
    position: relative;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
    object-fit: cover;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum .btn-indicate {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 6px 8px;
    gap: 4px;
    position: absolute;
    right: 12px;
    bottom: 10px;
    background-color: rgba(12,12,12,0.6);
    border-radius: 15px;
    color: #fff;
    font-size: 10px;
    line-height: 10px;
    font-weight: 400;
    letter-spacing: 0.2rem;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum .btn-indicate .count {
    font: inherit;
    line-height: 10px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum .btn-indicate:after {
    content: '';
    display: block;
    width: 10px;
    height: 10px;
    background: url(/static/user/images/cmm/ico_indicate.png) no-repeat 0 0/10px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .box-item-cont {
    padding-top: 22px;
    position: relative;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum + .box-item-cont {
    padding-top: 16px;
}

.detail-hotel-item .rooms-recomand.strad .box-item:after {
    width: 30px;
    height: 31px
}

.detail-hotel-item .rooms-recomand.strad .box-item.in:after {
    background-image: url(../../../assets/mobile/images/cmm/ico_inpolicy_big.png);
}

.detail-hotel-item .rooms-recomand.strad .box-item.out:after {
    background-image: url(../../../assets/mobile/images/cmm/ico_outpolicy_big.png);
}

.detail-hotel-item .rooms-recomand.strad .box-item .name {
    line-height: 25px;
    margin-bottom: 8px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .note {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    border: 1px solid #ff9268;
    color: #ff9268;
    font-size: 12px;
    line-height: 17px;
    font-weight: 500;
    letter-spacing: -0.2px;
    width: auto;
    content: none;
    top: 22px;
    right: 0;
    box-sizing: border-box;
    height: 25px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .thum + .box-item-cont .note {
    top: 16px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .note a {
    font: inherit;
    display: inline-flex;
    padding: 3px 5px;
    align-items: center;
    justify-content: center;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status {
    background-color: #f5f7fb;
    border-radius: 2px;
    padding: 8px 16px;
    display: flex;
    gap: 6px;
    flex-direction: column;
    margin-bottom: 8px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status p {
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: -0.2px;
    display: flex;
    align-items: center;
    gap: 4px;
    margin: 0;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .question {
    width: 13px;
    height: 13px;
    display: block;
    background: url(../../../assets/mobile/images/search/icn-qm-g-solid.png) no-repeat 0 0/13px;
    margin: 0;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .notice {
    color: #3a83ff;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .notice .question {
    background-image: url(../../../assets/mobile/images/search/icn-qm-b-solid.png);
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .check {
    color: #757f92;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .fail {
    color: #ff4e50;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .mileage {
    color: #757f92;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-status .mileage img {
    width: 14px;
    height: 14px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price {
    position: relative;
    display: flex;
    flex-direction: column;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price-day {
    font-size: 10px;
    line-height: 14px;
    font-weight: 500;
    color: #9da9be;
    margin-bottom: 3px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price-prev {
    display: flex;
    gap: 5px;
    align-items: center;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price-prev .sale {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 3px;
    border-radius: 2px;
    font-size: 10px;
    font-weight: 500;
    color: #fff;
    line-height: 14px;
    background-color: #4E81FF;
    letter-spacing: 0;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price-prev .del {
    font-size: 12px;
    font-weight: 400;
    line-height: 17px;
    letter-spacing: -0.2px;
    text-decoration: line-through;
    color: #757f92;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price {
    display: flex;
    gap: 12px;
    position: relative;
    font-size: 16px;
    line-height: 23px;
    font-weight: 700;
}

.detail-hotel-item .rooms-recomand.strad .box-item .group-price .price .val {
    font-size: 16px;
    font-weight: 700;
    line-height: 23px;
    color: #333;
}

.detail-hotel-item .rooms-recomand.strad .box-item .btn-reserv {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 76px;
    text-align: center;
    font-weight: 500;
    color: #fff;
    background-color: #4e81ff;
    line-height: 30px;
    border-radius: 16px;
}

.detail-hotel-item .rooms-recomand.strad .box-item .btn-arrow-rate .cn {
    left: -18px;
}

.detail-hotel-item .rooms-recomand.strad .box-item.special {
    padding-bottom: 0;
}

.detail-hotel-item .rooms-recomand.strad .box-item.special .btn-reserv {
    bottom: 45px
}

.layer-pages.room-gallery .img-group {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-top: 14px;
}

.layer-pages.room-gallery .img-group .img-box {
    padding-bottom: 66.6666%;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.layer-pages.room-gallery .img-group .img-box img {
    position: absolute;
    width: 100%;
    height: 100%;
}

.reserv-complete .fare-info .total.sales {
    overflow: visible;
}

.reserv-complete .fare-info .total.sales:after {
    content: '';
    display: block;
    clear: both;
}