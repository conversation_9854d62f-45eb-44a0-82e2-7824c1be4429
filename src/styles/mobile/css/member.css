﻿@charset "utf-8";

/* 예약 요청 */
.pg-member { position: relative;  padding-top: 30px; margin-bottom: 30px;}
.pg-member .left-side{ float: left; position: relative; width: 210px;  box-shadow: 0 2px 16px 0 rgba(157, 169, 190, 0.15); border-radius: 10px;  background-color: #ffffff; }
.pg-member .left-side .user-info{ padding: 26px 0 0 24px; margin-bottom: 44px; }
.pg-member .left-side .user-info .name{margin-bottom: 13px; line-height: 29px; font-size: 20px; font-weight: 500; }
.pg-member .left-side .user-info .team{ position: relative; }
.pg-member .left-side .user-info .team span{position: relative; display: inline-block; }
.pg-member .left-side .user-info .team span:first-child{ padding-right: 8px; margin-right: 8px; font-size: 13px; }
.pg-member .left-side .user-info .team span:first-child::after{ content: ''; position: absolute; top: 50%; right: 0; height: 10px; margin-top: -5px; border-left: 1px solid #c9cfd8;}
.pg-member .left-side .page-menu{ position: relative; padding-left: 24px; padding-bottom: 30px;}
.pg-member .left-side .page-menu dl{ margin-top: 30px; }
.pg-member .left-side .page-menu dl:first-child{ margin-top: 0; }
.pg-member .left-side .page-menu dt{ margin-bottom: 16px; line-height: 24px; font-size: 16px; font-weight: 500; }
.pg-member .left-side .page-menu dd{ position: relative; margin-bottom: 14px; line-height: 22px; }
.pg-member .left-side .page-menu dd:last-child{ margin-bottom: 0; }
.pg-member .left-side .page-menu .active::after{ content: ''; position: absolute; top: -1px; left: -24px; width: 3px; height: 24px; background-color: #4e81ff; }
.pg-member .left-side .page-menu .active a{ color: #3b7ff3; font-weight: 500; }
.pg-member .left-side .page-menu dd a{ font-size: 15px; font-weight: 300;  }
.pg-member .left-side .btn-member-info{ position: absolute; top: 28px; right: 18px; width: 74px; line-height: 28px; border-radius: 14px; text-align: center; color: #293036; background-color: #f2f4f9; }
.pg-member .caution-america{ font-size: 13px; line-height: 19px; color: #4e81ff; }
.pg-member .right-side .box-tool-tip{ position: relative; }
.pg-member .right-side .box-tool-tip a{ line-height: 19px; font-size: 13px; color: #ff4e50; }
.pg-member .right-side .box-tool-tip a:hover + .box-desc{ display: block; }
.pg-member .right-side .box-tool-tip .box-desc{ display: none; position: absolute; top: 100%;  left: 0; width: 248px; padding: 16px ; margin-top: 15px; border: 1px solid #ff4e50; border-radius: 5px; box-shadow: 0 2px 15px 0 rgba(157, 169, 190, 0.2); font-size: 13px; font-weight: 300; color: #333; background-color: #fff; }
.pg-member .right-side .box-tool-tip .box-desc::after{ content: ''; position: absolute; top: -12px; left: 25px; width: 16px; height: 12px; background: url(../images/member/bu_tool_tip.jpg) 0 0 no-repeat;}

.pg-member .tab-btn-mypage{ overflow: hidden; margin-bottom: 20px;}
.pg-member .tab-btn-mypage li{ float: left; width: 137px; border: 1px solid #e2e4e8;}
.pg-member .tab-btn-mypage li{ margin-left: -1px; }
.pg-member .tab-btn-mypage li:first-child{width: 136px; margin-left: 0; border-radius: 5px 0 0 5px;}
.pg-member .tab-btn-mypage li:last-child{ width: 135px; border-radius:0 5px 5px 0;}
.pg-member .tab-btn-mypage li a{ display: block; font-size: 17px; font-weight: 500; color: #3f4e73; line-height: 54px; text-align: center; }
.pg-member .tab-btn-mypage li.active{ position: relative; background-color: #3f4e73; border-color: #3f4e73; }
.pg-member .tab-btn-mypage li.active a{ color: #fff; }
.pg-member .tab-item-mypage{ position: relative; }
.pg-member .tab-item-mypage li{ position: relative; padding: 20px; margin-bottom: 4px; border: 1px solid #e2e4e8; border-radius: 5px; background-color: #fff; }
.pg-member .tab-item-mypage li.active{ border-color: #4e81ff; }
.pg-member .tab-item-mypage li.active dd{ display: block; }
.pg-member .tab-item-mypage dt { line-height: 28px; }
.pg-member .tab-item-mypage dt::after{ content: ''; clear: both; display: block; }
.pg-member .tab-item-mypage dt .txt{ float: left; width: 579px; font-weight: 500; line-height: 28px; text-overflow: ellipsis; white-space: nowrap; overflow: hidden }
.pg-member .tab-item-mypage dt .date-write{ float: right; margin-right: 32px;}
.pg-member .tab-item-mypage dd{display: none;}
.pg-member .tab-item-mypage dd .txt{ padding-top: 18px; margin-top: 18px; border-top: 1px solid #e2e4e8; line-height: 20px; }
.pg-member .tab-item-mypage dd .img{ padding-top: 15px; }
.pg-member .tab-item-mypage .btn-accodi{ position: absolute; top: 24px; right: 17px; width: 23px; height: 23px; background: url(../images/cmm/btn_arrow_acoodi_s.png) 50% 50% no-repeat; }

.pg-member .right-side{ float: right; position: relative; width: 826px; padding-top: 10px; }
.pg-member h2.title{ margin-bottom: 34px; }

.pg-member .tab-cate{ position: relative; margin-bottom: 20px;}
.pg-member .tab-cate::after{ content: ''; clear: both; display: block; }
.pg-member .tab-cate li{ float: left; margin-right: 40px; border-bottom: 2px solid #fff; }
.pg-member .tab-cate li a{line-height: 25px; font-size: 17px; font-weight: 500; color: #9da9be;}
.pg-member .tab-cate li.active{ padding-bottom: 6px; border-bottom-color:#3f4e73; }
.pg-member .tab-cate li.active a{ color: #3f4e73; font-weight: 700; }

.pg-member .table-cmm.type-fare{ border-top: 0 none; border-bottom: 0 none;}
.pg-member .table-cmm.type-fare th{ border-bottom: solid 2px #34446e; }
.pg-member .table-cmm.type-fare td{ font-size: 14px;  border-bottom: 1px solid #e2e4e8;}
.pg-member .table-cmm.type-fare td.status{font-weight: 500;}
.pg-member .table-cmm.type-fare td.status.consult{color: #f59c79;}
.pg-member .table-cmm.type-fare td.status.reserv{color: #49b999;}
.pg-member .table-cmm.type-fare td.status.issue{color: #8487d1;	}
.pg-member .table-cmm.type-fare td a:hover{ text-decoration: underline; }
.pg-member .table-cmm.type-fare .cancel *{ color: #babdc3;  }
.pg-member .table-cmm.type-fare .none td{ border-bottom: 0 none;  }

.pg-member .box-tbl-bottom .paginate{ float: left; padding-top: 31px; }
.pg-member .box-tbl-bottom .btn-area{ float: right; padding-top: 15px;}
.pg-member .box-tbl-bottom .btn-area .desc{ float: left; padding-top: 18px; margin-right: 10px; font-size: 13px; color: #757f92;}
.pg-member .box-tbl-bottom .btn-area .btn-default{ float: left; }

.pg-member .btn-bottom { padding-top: 40px; text-align: center; }

/* 회원 정보 */
.member-info-cotn{ position: relative; padding: 10px 0 0 0 ;}
.member-info-cotn .box-division{ position: relative; padding: 18px 0 40px; border-bottom: solid 2px #ebeef3;}
.member-info-cotn .box-division:first-child{ border-top: solid 2px #ebeef3;}
.member-info-cotn .box-division .tit{margin-bottom: 32px; line-height: 25px; font-size: 17px; font-weight: 500; }
.member-info-cotn .box-division .form-select.has-scroll .ui-selectmenu-button.ui-button{ border-bottom: 1px solid #e2e4e8; }
.member-info-cotn .box-division .form-select.has-scroll .ui-selectmenu-button-open{ border-bottom-color: #4e81ff !important; }
.member-info-cotn .box-division dl{ position: relative; }
.member-info-cotn .box-division dl::after{ content: ''; display: block; clear: both; }
.member-info-cotn .box-division dt .essential{ display: inline-block; color: #4e81ff; }
.member-info-cotn .box-division dt{ clear: both; float: left; width: 130px; margin-top: 13px; color: #757f92; line-height: 32px;}
.member-info-cotn .box-division dd{ float: left; width: 696px; margin-top: 13px; font-size: 15px; line-height: 32px;}
.member-info-cotn .box-division dt:first-child,
.member-info-cotn .box-division dt:first-child + dd{ margin-top: 0; }
.member-info-cotn .box-division dd *{ font-size: 15px; line-height: 32px;}
.member-info-cotn .box-division dd.gender{  }
.member-info-cotn .box-division dd.gender .form-radio{ float: left; margin-right: 40px;}
.member-info-cotn .box-division dd.gender .form-radio span{}
.member-info-cotn .box-col{ float: left; width: 210px; margin-left: 33px; }
.member-info-cotn .box-col:first-child{ margin-left: 0; }
.member-info-cotn .box-col.box-ymd{ border-bottom: 1px solid #e2e4e8; }
.member-info-cotn .box-col.box-ymd .form-select{ border-bottom: 0 none; margin-right: 15px;}
.member-info-cotn .box-col input{}
.member-info-cotn .box-col .form-select{}
.member-info-cotn .box-col .form-chkbox span::after{ top: 50%; mt-9 }
.member-info-cotn .box-col.tel{border-bottom: 1px solid #e2e4e8; }
.member-info-cotn .box-col.tel .form-select{ border-bottom: 0 none; }
.member-info-cotn .box-col.tel input{ width: 80px; padding-left: 15px; border-bottom: 0 none;}
.member-info-cotn .box-col .agree-market{}
.member-info-cotn .box-col .agree-market .form-chkbox{}
.member-info-cotn .box-col .agree-market .desc{}
.member-info-cotn .desc-essential{ position: absolute; top: 20px; right: 0; line-height: 20px; color: #757f92;}
.member-info-cotn .desc-essential span{ color: #4e81ff; }
.member-info-cotn .form-chkbox span::after{ top: 50%; margin-top: -9px; }
.member-info-cotn .agree-market{}
.member-info-cotn .agree-market .form-chkbox{ margin-right: 36px; }
.member-info-cotn .agree-market .desc{padding-top: 10px; font-size: 13px; font-weight: 300; line-height: 19px; }
.member-info-cotn .box-bottom { text-align: center; padding-top: 40px; }

/* 예약 내역 */
.reserv-list-cotn .btn-default{}
.reserv-list-cotn .box-select-tbl{ position: relative;  z-index: 10;}
.reserv-list-cotn .box-select-tbl .btn-default{ position: relative; display: inline-block; padding: 0 15px; text-indent: -10px;}
.reserv-list-cotn .box-select-tbl .btn-default::after{ content: ''; position: absolute; top: 7px; right: 0; width: 9px; height: 5px; background: url(../images/cmm/btn_board_dropdown.png) 80% 50% no-repeat;  }
.reserv-list-cotn .box-select-tbl.active .btn-default::after{ background-image: url(../images/cmm/btn_board_dropdownup.png); }
.reserv-list-cotn .box-select-tbl .box{display: none; position: absolute; top: 100%; left: 0; right: 0; width: 81px; margin-top: 10px; padding: 17px 0 17px 17px;    border-radius: 5px; box-shadow: 0 2px 15px 0 rgba(157, 169, 190, 0.2); background-color: #ffffff; border: 1px solid #e2e4e8; text-align: left;}
.reserv-list-cotn .box-select-tbl li{ margin-top: 10px; }
.reserv-list-cotn .box-select-tbl li:first-child{ margin-top: 0; }
.reserv-list-cotn .box-select-tbl li span{ font-size: 14px; font-weight: normal; }
.reserv-list-cotn .box-select-tbl.active .box{ display: block; }

.pg-member .title.type-reserv{ font-size: 26px; line-height: 38px; font-weight: normal; }
.pg-member .path{ overflow: hidden; margin-bottom: 25px;}
.pg-member .path span{ float: left; font-size: 12px; font-weight: 500; line-height: 18px; padding-right: 13px; margin-right: 8px; background: url(../images/cmm/icn_snavi_arrow.png) 100% 50% no-repeat; }
.pg-member .path span:last-child{ background: none; }
.pg-member .path span.bold{ font-weight: 700; }
.reserv-detail-cotn{ position: relative; }
.reserv-detail-cotn .box-divison .box-line{ margin-bottom: 20px; }
.reserv-detail-cotn .box-divison .tit{ margin-bottom: 20px; font-size: 18px; font-weight: 500; line-height: 27px; }
.reserv-detail-cotn .box-divison .passanger-info .tit{ margin-bottom: 0; }

.reserv-detail-cotn .box-reserv-num{ position: relative; margin-bottom: 32px; }
.reserv-detail-cotn .box-reserv-num p{ margin-bottom: 4px; font-weight: 300; line-height: 20px; }
.reserv-detail-cotn .box-reserv-num .btns-status-reserv{ position: absolute; top: 0; right: 0; }
.reserv-detail-cotn .box-reserv-num .btns-status-reserv .btn-default{ width: 85px; line-height: 32px; border-radius: 17px; border-width: 1px; border-style: solid; font-size: 13px; font-weight: 500; text-align: center;}
.reserv-detail-cotn .box-reserv-num .btns-status-reserv .btn-white{ border-color: #4e81ff;  color: #4e81ff;}
.reserv-detail-cotn .box-reserv-num .btns-status-reserv .btn-blue{ border-color: #4e81ff; background-color: #4e81ff; color: #fff; }
.reserv-detail-cotn .box-reserv-num .btns-status-reserv .disabled{ background-color: #fff; border-color: #e2e4e8; color: #e2e4e8; cursor: default;}
.reserv-detail-cotn .pay-status{ overflow: hidden; padding-top: 18px; margin-top: 14px; border-top: 1px solid #e2e4e8; line-height: 18px; border-top: 1px solid #e2e4e8; font-weight: 700;}
.reserv-detail-cotn .pay-status dt{ float: left; font-size: 17px; line-height: 25px;}
.reserv-detail-cotn .pay-status dd{ float: left; margin-left: 14px; font-size: 17px; line-height: 25px;}
.reserv-detail-cotn .pay-status.ready dd{ color: #f59c79; }
.reserv-detail-cotn .pay-status.complete dd{color: #49b999;}
.reserv-detail-cotn .pay-status.reject dd{color: #ff4e50;}
.reserv-detail-cotn .paid-date{ padding-top: 5px; font-size: 13px; line-height: 19px; color: #293036; }
.reserv-detail-cotn .info-booking .plan{ display: block; padding-left: 0; padding-right: 0; }
.reserv-detail-cotn.contents .left-side{ float: left; width: 780px; background: #fff none ; box-shadow: none;}
.reserv-detail-cotn.contents .right-side{ float: right; width: 280px; padding-top: 0;}
.reserv-detail-cotn .box-tit{position: relative; overflow: hidden; padding:18px 0 18px 21px; margin-bottom: 20px; border-radius: 5px; background-color: #f1f5ff; }
.reserv-detail-cotn .box-tit { font-size: 13px; line-height: 19px; color: #596a92; }
.reserv-detail-cotn .box-tit .tit{margin-bottom: 6px; font-size: 20px; font-weight: 700; line-height: 29px;  }
.reserv-detail-cotn .box-tit .date{ position: absolute; top: 24px; right: 32px; }
.reserv-detail-cotn .box-tit::before{ content: ''; position: absolute; top: 0; left: 0; bottom: 0; width: 5px;  }
.reserv-detail-cotn .box-tit.complete .tit{ color: #4e81ff; }
.reserv-detail-cotn .box-tit.complete::before{background-color: #4e81ff; }
.reserv-detail-cotn .box-tit.cancel .tit{ color: #596a92; }
.reserv-detail-cotn .box-tit.cancel::before{background-color: #596a92; }
.reserv-detail-cotn .box-accodion{ position: relative; padding-bottom: 70px;}
.reserv-detail-cotn .box-accodion .box-item{ position: relative;  padding: 20px; margin-top: 20px; border: 1px solid #e2e4e8; border-radius: 5px;}
.reserv-detail-cotn .box-accodion .tit{  font-size: 18px; font-weight: 500; line-height: 27px; }
.reserv-detail-cotn .box-accodion .box-cotn{ position: relative; display: none; margin-top: 24px; }
.reserv-detail-cotn .box-accodion div.btn-arrow{ position: absolute; top: 13px; right: 10px;  }
.reserv-detail-cotn .box-accodion div.btn-arrow .btn-default{ width: 40px; height: 40px; padding: 10px; background: url(../images/cmm/btn_arrow_acoodi_b.png) no-repeat 50% 50%; }
.reserv-detail-cotn .box-accodion div.btn-arrow .btn-default ,
.reserv-detail-cotn .box-accodion .box-item.active .box-cotn{ display: block; }
.reserv-detail-cotn .box-accodion .box-item.active div.btn-arrow{
    -webkit-transform: rotate(180deg);
-ms-transform: rotate(180deg);
-o-transform: rotate(180deg);
transform: rotate(180deg);
}
.reserv-detail-cotn .box-line{ padding: 22px; margin-bottom: 15px; border: 1px solid #e2e4e8; border-radius: 5px; }
.reserv-detail-cotn .box-line .txt-big{ margin-bottom: 15px; font-size: 18px; font-weight: 700; line-height: 27px; letter-spacing: -1px; }
.reserv-detail-cotn .box-line .txt-big .fl-l{ float: left; }
.reserv-detail-cotn .box-line .txt-big .fl-r{ float: right; }
.reserv-detail-cotn .box-line .txt-big .c-price{ font-size: 22px; }
.reserv-detail-cotn .box-line .txt-big .c-price strong{ color: #4e81ff; }
.reserv-detail-cotn .fare-detail { position: relative; padding: 17px 0 5px;}
.reserv-detail-cotn .fare-detail dt{ padding-bottom: 16px; margin-bottom: 5px; font-weight: 700; line-height: 20px; border-bottom: 1px solid #e2e4e8; }
.reserv-detail-cotn .fare-detail dd{ overflow: hidden; padding-top: 7px; font-size: 13px; line-height: 19px; color: #3f4e73;  }
.reserv-detail-cotn .fare-detail .tit{ float: left; }
.reserv-detail-cotn .fare-detail .sum{ float: right; }
.reserv-detail-cotn .fare-detail .date{ padding-top: 20px; color: #293036; }
.reserv-detail-cotn .brakedown{ position: relative; margin-bottom: 12px;}
.reserv-detail-cotn .brakedown li{ position: relative; margin-top: 7px; padding: 0 0 0 14px; }
.reserv-detail-cotn .brakedown li:first-child{ margin-top: 0; }
.reserv-detail-cotn .brakedown li::before{ content: ''; position: absolute; top: 50%; left: 0; width: 8px; height: 8px; margin-top: -4px; background-color: #ff4e50; border-radius: 8px; font-size: 13px; line-height: 19px;}
.reserv-detail-cotn .brakedown li span{ font-weight: 700; text-decoration: underline; }
.reserv-detail-cotn .opinion-insert{ border: 1px solid #e2e4e8; border-radius: 3px; }
.reserv-detail-cotn .opinion-insert textarea{ border: 0 none; background-color: #fff ; padding: 12px; width: 100%; height: 157px; box-sizing: border-box; font-size: 13px;}
.reserv-detail-cotn .opinion-insert textarea::placeholder { color: #babdc3 }
.reserv-detail-cotn .box-applys{ padding: 5px 0 42px; }
.reserv-detail-cotn .box-applys .btn-default{ width: 100%; margin-bottom: 12px; line-height: 48px;  border-radius: 5px; border: 1px solid #4e81ff; text-align: center; font-size: 16px; font-weight: 500;}
.reserv-detail-cotn .box-applys .btn-compare{ background-color: #fff; color:#4e81ff;  }
.reserv-detail-cotn .personal-info{ position: relative; overflow: hidden; }
.reserv-detail-cotn .personal-info .box-col{ float: left;}
.reserv-detail-cotn .personal-info .box-col.name{ width: 190px;}
.reserv-detail-cotn .personal-info .box-col.num{ width: 290px;}
.reserv-detail-cotn .personal-info dt{ margin-bottom: 10px; font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-detail-cotn .personal-info dd{ font-size: 15px; line-height: 22px;  }
.reserv-detail-cotn .box-applys .btn-request{ background-color: #4e81ff; color: #fff; }
.reserv-detail-cotn .box-applys .btn-compare{ background-color: #fff; color:#4e81ff;  }

.reserv-detail-cotn .passanger-info{ position: relative; }
.reserv-detail-cotn .passanger-info .caution-america{ margin-bottom: 0; }
.reserv-detail-cotn .passanger-info .edit{ overflow: hidden; padding-bottom: 6px;  margin-top: 25px;}
.reserv-detail-cotn .passanger-info .edit p{ float: left; font-size: 16px; font-weight: 500; line-height: 34px;}
.reserv-detail-cotn .passanger-info .edit .btn-default{ float: right; width: 107px; font-size: 13px; font-weight: 500; text-align: center; line-height: 34px; color: #fff;background-color: #34446e; border-radius: 5px;}
.reserv-detail-cotn .passanger-info .clearfix{ border-top: 2px solid #34446e; }
.reserv-detail-cotn .passanger-info .box-col{float: left; padding: 16px 0 16px 20px; -webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;width: 33.3%;border-bottom: 1px solid #e2e4e8;	border-left: 1px solid #e2e4e8;}
.reserv-detail-cotn .passanger-info .box-col.col-half{ width: 50%; }
.reserv-detail-cotn .passanger-info .box-col:nth-child(1),
.reserv-detail-cotn .passanger-info .box-col:nth-child(4){ border-left: 0 none; }
.reserv-detail-cotn .passanger-info dt{ margin-bottom: 10px; font-size: 13px; line-height: 19px; color: #757f92; }
.reserv-detail-cotn .passanger-info dd{ font-size: 15px; line-height: 22px;  }
.reserv-detail-cotn .stay-adress{ position: relative; }
.reserv-detail-cotn .stay-adress .caution-america{ margin-bottom: 20px; }
.reserv-detail-cotn .stay-adress .clearfix { margin-bottom: 15px; }
.reserv-detail-cotn .stay-adress .clearfix dl{ float: left; width: 360px; }
.reserv-detail-cotn .stay-adress .clearfix dl:first-child{ margin-right: 18px; }
.reserv-detail-cotn .stay-adress dt{ font-size: 13px; line-height: 19px; color: #757f92;}
.reserv-detail-cotn .stay-adress .essential{ color: #4e81ff; }
.reserv-detail-cotn .stay-adress .center{ text-align: center; margin-top: 20px; }
.reserv-detail-cotn .stay-adress .btn-regist{ width: 98px; line-height: 38px; border-radius: 5px; border: 1px solid #4e81ff; text-align: center; color: #4e81ff; font-weight: 500;}
.reserv-detail-cotn .reserv-change{ position: relative; }
.reserv-detail-cotn .reserv-change .desc{ font-size: 13px; margin-bottom: 10px; color: #757f92; }
.reserv-detail-cotn .reserv-change .desc .etc{ font-weight: 500; color: #4e81ff; text-decoration: underline; }
.reserv-detail-cotn .reserv-change thead th{ font-size: 14px; font-weight: 500; }
.reserv-detail-cotn .reserv-change tbody td{ font-size: 14px; font-weight: 300; }
.reserv-detail-cotn .reserv-change .question.active{  }
.reserv-detail-cotn .reserv-change .answer{ display: none; background-color: #e2e4e8; }
.reserv-detail-cotn .reserv-change .answer.active{display: table-row;}
.reserv-detail-cotn .reserv-change .box-answer{  position: relative;  padding:15px 0 15px 53px; text-align: left;}
.reserv-detail-cotn .reserv-change .box-answer .tag{ position: absolute; top: 12px; left: 0; width: 43px; line-height: 22px; border: 1px solid #4e81ff; border-radius: 17px; text-align: center; font-size: 12px; font-weight: 500; color: #4e81ff;}
.reserv-detail-cotn .reserv-change .none{ padding: 15px 0; text-align: center; color: #7f848d; border-bottom: 0 none;}
.reserv-detail-cotn .reserv-change .left{ text-align: left; }
.reserv-detail-cotn .reserv-change .txt-short{ width: 395px; }
.reserv-detail-cotn .reserv-status{ position: relative; }
.reserv-detail-cotn .reserv-status{ position: relative; }
.reserv-detail-cotn .reserv-status thead th{ font-size: 14px; font-weight: 500; }
.reserv-detail-cotn .reserv-status tbody td{ font-size: 14px; font-weight: 300; }
.reserv-detail-cotn .reserv-status .none{ padding: 15px 0; text-align: center; color: #7f848d; border-bottom: 0 none;}
.reserv-detail-cotn .reserv-status .left{ text-align: left; }
.reserv-detail-cotn .reserv-status .txt-short{ width: 395px; }

/* 법인/단체 견적 문의 - 목록*/
.estimate-list-cotn{ position: relative; }
.estimate-list-cotn .consult-time{ position: relative; margin-bottom: 30px;	 }
.estimate-list-cotn .consult-time dl{ overflow: hidden; line-height: 20px;}
.estimate-list-cotn .consult-time dt{ float: left; width: 72px; font-size: 15px; font-weight: 700; }
.estimate-list-cotn .consult-time dd{ float: left; }

/* 법인/단체 견적 문의 - 상세*/
.estimate-detail-cotn{ position: relative;}
.estimate-detail-cotn .box-divison{ padding: 16px 0 40px; border-top: solid 2px #ebeef3; }
.estimate-detail-cotn .box-divison .tit{ margin-bottom: 30px; line-height: 27px; font-size: 18px; font-weight: 500; }
.estimate-detail-cotn .box-divison dl{ overflow: hidden; line-height: 22px; }
.estimate-detail-cotn .box-divison dt{ float: left; margin-bottom: 10px; width: 109px; color: #757f92;}
.estimate-detail-cotn .box-divison dd{ float: left; margin-bottom: 10px; width: 304px; font-size: 15px;}
.estimate-detail-cotn .box-divison .etc-info .form-chkbox{ margin-left: 12px; }
.estimate-detail-cotn .box-divison .etc-info .form-chkbox:first-child{ margin-left: 0; }
.estimate-detail-cotn .cotn-inquiry{ padding: 22px 20px; margin-bottom: 30px; border: 1px solid #e2e4e8; border-radius: 5px; }
.estimate-detail-cotn .cotn-inquiry dt{ padding-bottom: 19px; border-bottom: 1px solid #e2e4e8; line-height: 22px; font-size: 15px; font-weight: 500; }
.estimate-detail-cotn .cotn-inquiry dd{ padding-top: 20px; min-height: 97px; line-height: 19px; }
.estimate-detail-cotn .desc-inquiry{  padding: 20px; position: relative; border-radius: 5px; background-color: #f2f4f9;}
.estimate-detail-cotn .desc-inquiry li{ font-size: 13px; line-height: 19px; }

/* 1:1 문의 :: 내역*/
.onetoone-inquiry-cotn{ position: relative; }
.onetoone-inquiry-cotn .consult-time{ overflow: hidden; margin-bottom: 30px; }
.onetoone-inquiry-cotn .consult-time dt{ float: left; width: 72px; font-size: 15px; font-weight: 500; line-height: 20px; }
.onetoone-inquiry-cotn .consult-time dd{ float: left; line-height: 20px; }
.onetoone-inquiry-cotn .list-item{ position: relative; }
.onetoone-inquiry-cotn .list-item .tit{ margin-bottom: 19px; line-height: 27px; font-size: 18px; font-weight: 500; }
.onetoone-inquiry-cotn .list-item li{ position: relative; padding: 20px; margin-bottom: 4px; border: 1px solid #e2e4e8; border-radius: 5px; background-color: #fff; }
.onetoone-inquiry-cotn .list-item li.active{ border-color: #4e81ff; }
.onetoone-inquiry-cotn .list-item li.active dd{ display: block; }
.onetoone-inquiry-cotn .list-item dl{}
.onetoone-inquiry-cotn .list-item dt{ line-height: 28px;}
.onetoone-inquiry-cotn .list-item dt::after{ content: ''; clear: both; display: block; }
.onetoone-inquiry-cotn .list-item dt .txt{ float: left; width: 579px; font-weight: 500; text-overflow: ellipsis; white-space: nowrap; overflow: hidden }
.onetoone-inquiry-cotn .list-item .tag-status{ float: left; margin-right: 15px; width: 75px;  border-radius: 17px; text-align: center; font-size: 12px; font-weight: 500; color: #fff;}
.onetoone-inquiry-cotn .list-item .tag-status.ready{background-color: #c9cfd8;}
.onetoone-inquiry-cotn .list-item .tag-status.complete{background-color: #4e81ff;}
.onetoone-inquiry-cotn .list-item .date-write{ float: right; margin-right: 32px;}
.onetoone-inquiry-cotn .list-item dd{display: none; }
.onetoone-inquiry-cotn .list-item dd .txt{ padding-top: 15px; }
.onetoone-inquiry-cotn .list-item .question{padding-top: 18px; margin-top: 18px; border-top: 1px solid #e2e4e8; line-height: 20px;}
.onetoone-inquiry-cotn .list-item .answer{ margin-top: 30px; padding: 20px; border-radius: 2px; background-color: #f2f4f9; }
.onetoone-inquiry-cotn .list-item .answer .txt{ line-height: 20px; }
.onetoone-inquiry-cotn .list-item .answer .tag{ float: left; width: 53px; margin-right: 15px; border-radius: 17px; line-height: 26px; border: 1px solid #4e81ff; font-size: 12px; font-weight: 500; text-align: center; color: #4e81ff;}
.onetoone-inquiry-cotn .list-item .answer .date{ float: left; margin-right: 15px; line-height: 28px; color: #4e81ff;}
.onetoone-inquiry-cotn .list-item .btn-accodi{ position: absolute; top: 24px; right: 12px; width: 23px; height: 23px; background: url(../images/cmm/btn_arrow_acoodi_s.png) 50% 50% no-repeat; }
.onetoone-inquiry-cotn .list-item.none{ font-size: 16px; line-height: 200px; text-align: center;  border-radius: 5px; background-color: rgba(242, 244, 249, 0.5); color: #7f848d;}
.onetoone-inquiry-cotn .desc-inquiry{  padding: 20px; position: relative; border-radius: 5px; background-color: #f2f4f9;}
.onetoone-inquiry-cotn .desc-inquiry li{ font-size: 13px; line-height: 19px; }
.onetoone-inquiry-cotn .write-form{ position: relative; }
.onetoone-inquiry-cotn .write-form dl{ margin-top: 30px; }
.onetoone-inquiry-cotn .write-form dl:first-child{ margin-top: 0; }
.onetoone-inquiry-cotn .write-form dt{ margin-bottom: 8px; font-size: 15px; font-weight: 500; line-height: 22px; }
.onetoone-inquiry-cotn .write-form dd{  }
.onetoone-inquiry-cotn .write-form .essential{color: #3b7ff3; display: inline-block; margin-left: 3px;}
.onetoone-inquiry-cotn .write-form .form-select select{ width: 120px; height: 44px; padding:0 10px; border: 1px solid #e2e4e8; border-radius: 5px;}
.onetoone-inquiry-cotn .write-form input[type="text"]{ width: 100%; padding:0 15px; height: 44px; border: 1px solid #e2e4e8; border-radius: 5px;  }
.onetoone-inquiry-cotn .write-form textarea{ width: 100%; height: 190px; padding: 15px;border: 1px solid #e2e4e8; border-radius: 5px; }
.onetoone-inquiry-cotn .write-form .add-file{ padding: 15px 0; }
.onetoone-inquiry-cotn .write-form .add-file::after{ content: ''; clear: both; display: block;}
.onetoone-inquiry-cotn .write-form .add-file .btn-file{ float: left; position: relative; width: 78px; line-height: 32px; border: 1px solid #4e81ff; border-radius: 5px; text-align: center; font-size: 13px; font-weight: 500; color: #4e81ff; cursor: pointer;}
.onetoone-inquiry-cotn .write-form .add-file input{position: absolute; top: 0; left: 0; width: 100%; height: 100%;opacity: 0; padding: 0; margin: 0; cursor: pointer;}
.onetoone-inquiry-cotn .write-form .add-file .desc{ float: left; margin-left: 16px; line-height: 32px; font-size: 13px; color: #7f848d; }
.onetoone-inquiry-cotn .write-form .alarm{ margin-bottom: 50px; overflow: hidden; }
.onetoone-inquiry-cotn .write-form .alarm dd{ overflow: hidden; margin-bottom: 14px; line-height: 20px; }
.onetoone-inquiry-cotn .write-form .alarm .form-chkbox{ float: left; width: 105px;}
.onetoone-inquiry-cotn .write-form .alarm .txt{ float: left; line-height: 18px; }

/* 공지사항 */
.board-notice-cotn{ position: relative; padding-bottom: 50px;}
.board-notice-cotn .box-saerch-cmm{ margin-bottom: 15px; }
.board-notice-cotn .desc-search{margin-bottom: 50px; font-size: 13px; line-height: 19px; color: #7f848d; }
.board-notice-cotn .paginate { padding-top: 40px; text-align: center;}

/* 자주 묻는 질문 */
.board-qna-cotn{ position: relative; padding-bottom: 50px;}
.board-qna-cotn .box-saerch-cmm{ margin-bottom: 15px; }
.board-qna-cotn .desc-search{margin-bottom: 50px; font-size: 13px; line-height: 19px; color: #7f848d; }
.board-qna-cotn .paginate { padding-top: 40px; text-align: left;}
.board-qna-cotn .btns-type{ overflow: hidden; margin-bottom: 20px; }
.board-qna-cotn .btns-type li{ float: left; margin-right: 6px;}
.board-qna-cotn .btns-type li button{ padding:0 17px; line-height: 34px; border-radius: 17px; font-size: 13px; background-color: #f2f4f9; color: #3f4e73; cursor: pointer; outline:0 none;}
.board-qna-cotn .btns-type li.active button{ font-weight: 500; color: #fff; background-color: #4e81ff; }

