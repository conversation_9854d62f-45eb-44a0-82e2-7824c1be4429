@font-face {
   font-family: 'Noto Sans KR';
   font-style: normal;
   font-weight: 300;
   src: url(font/NotoSansCJKkr-Light.woff2) format('woff2') , url(font/NotoSansCJKkr-Light.otf) format('opentype') ;
 }
@font-face {
   font-family: 'Noto Sans KR';
   font-style: normal;
   font-weight: 400;
   src: url(font/NotoSansCJKkr-Regular.woff2) format('woff2') , url(font/NotoSansCJKkr-Regular.otf) format('opentype') ;
 }
 @font-face {
   font-family: 'Noto Sans KR';
   font-style: normal;
   font-weight: 500;
   src: url(font/NotoSansCJKkr-Medium.woff2) format('woff2') , url(font/NotoSansCJKkr-Medium.otf) format('opentype') ;
 }
 @font-face {
   font-family: 'Noto Sans KR';
   font-style: normal;
   font-weight: 700;
   src: url(font/NotoSansCJKkr-Bold.woff2) format('woff2') , url(font/NotoSansCJKkr-Bold.otf) format('opentype') ;
 }

input[type=text]::-ms-clear,
input[type=password]::-ms-clear,
input[type=date]::-ms-clear,
input[type=number]::-ms-clear {
    display: none
}

input[type=text]::-ms-clear {
    display: none
}

html,body {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    font-family: "Noto Sans KR" !important;
    font-size: 14px;
    line-height: 18px;
    color: #000;
}

html,
body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
li,
dl,
dt,
dd,
span,
form,
fieldset,
input,
select,
label,
textarea,
table,
caption,
thead,
tfoot,
tbody,
tr,
th,
td,
button,
pre,
a,
button,
label {
    margin: 0;
    padding: 0;

}

body,
input,
textarea,
select,
button,
table ,
a{
    padding: 0;
    margin: 0;
     font-family: "Noto Sans KR" !important;
    font-size: 14px;
    line-height: 18px;
    color: #000;
}

optgroup {
    font-weight: 700
}

body {
    -webkit-text-size-adjust: none;
    word-wrap: break-word
}

html,
body {
    background: transparent;
    height: 100%;
    box-sizing: border-box;
}

img,
fieldset {
    border: 0 none
}

ul,
ol {
    list-style: none
}

em,
address {
    font-style: normal
}

a {
    background-color: transparent;
    -webkit-text-decoration-skip: objects;
    text-decoration: none;
    -webkit-user-drag: none
}

a:active,
a:hover {
    outline-width: 0
}

table {
    border-collapse: collapse
}

caption {
    font-size: 0;
    width: 0;
    height: 0;
    visibility: hidden;
    outline: 0;
    padding: 0 !important
}

textarea {
    resize: none
}

hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible
}

button {
    margin: 0;
    padding: 0;
    border: 0;
    background-color: transparent;
    -webkit-appearance: none;
    text-transform: none
}

button::-moz-focus-inner {
    padding: 0;
    border: 0
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
    border-style: none;
    padding: 0
}

button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
    outline: 1px dotted ButtonText
}

select {
    text-transform: none;
    -moz-appearance:none; /* Firefox */
    -webkit-appearance:none; /* Safari and Chrome */
    appearance:none;
}
select::-ms-expand {display:none}
article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
    display: block
}

audio,
canvas,
progress,
video {
    display: inline-block
}

audio:not([controls]) {
    display: none;
    height: 0
}

progress {
    vertical-align: baseline
}

template,
[hidden] {
    display: none
}

abbr[title] {
    border-bottom: none;
    text-decoration: underline;
    text-decoration: underline dotted
}

b,
strong {
    font-weight: 500;
}

dfn {
    font-style: italic
}

h1 {
    font-size: 2em
}

mark {
    background-color: #ff0;
    color: #000
}

small {
    font-size: 80%
}

sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline
}

sub {
    bottom: -0.25em
}

sup {
    top: -0.5em
}

img {
    border-style: none;
    vertical-align: top
}

svg:not(:root) {
    overflow: hidden
}

code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em
}

figure {
    margin: 1em 40px
}

legend {
    box-sizing: border-box;
    color: inherit;
    display: table;
    max-width: 100%;
    padding: 0;
    white-space: normal
}

textarea {
    overflow: auto
}

i,
cite,
em,
var,
address,
dfn {
    font-style: normal
}

[type="checkbox"],
[type="radio"] {
    box-sizing: border-box;
    padding: 0
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    height: auto
}

[type="search"] {
    -webkit-appearance: textfield;
    outline-offset: -2px
}

::-webkit-input-placeholder {
    color: inherit;
    opacity: 0.54
}

::-webkit-file-upload-button {
    -webkit-appearance: button;
    font: inherit
}

.g_invisible {
    overflow: hidden;
    position: absolute;
    clip: rect(0 0 0 0);
    width: 1px;
    height: 1px;
    margin: -1px
}

.g_skip {
    overflow: hidden;
    position: absolute;
    clip: rect(0 0 0 0);
    width: 1px;
    height: 1px;
    margin: -1px
}

.g_btn-skip {
    position: absolute;
    left: 0;
    top: -30px;
    width: 128px;
    background-color: #191919;
    border: 1px solid #231f20;
    text-align: center;
    outline: none
}

.g_btn-skip:active,
.g_btn-skip:focus {
    top: 2px;
    text-decoration: none;
    cursor: pointer;
    zoom: 1
}

.g_btn-skip__inner {
    display: inline-block;
    padding-top: 2px;
    font-size: 12px;
    letter-spacing: -1px;
    color: #fff;
    line-height: 26px
}

.font_b {
    font-weight: bold
}

.font_n {
    font-weight: nomal
}

.float_l {
    float: left
}

.float_r {
    float: right
}

.col_devide>* {
    float: left
}

.col_devide.set_two>* {
    width: 50%
}

.col_devide.set_three>* {
    width: 33.33333333%
}

.col_devide.set_four>* {
    width: 25%
}

.col_devide.set_five>* {
    width: 20%
}

.col_devide:after {
    content: '';
    display: block;
    clear: both
}

.col-md-1 {
    width: 8.33333333%
}

.col-md-2 {
    width: 16.66666667%
}

.col-md-3 {
    width: 25%
}

.col-md-4 {
    width: 33.33333333%
}

.col-md-5 {
    width: 41.66666667%
}

.col-md-6 {
    width: 50%
}

.col-md-7 {
    width: 58.333333%
}

.col-md-8 {
    width: 66.66666667%
}

.col-md-9 {
    width: 75%
}

.col-md-10 {
    width: 83.33333333%
}

.col-md-11 {
    width: 91.66666667%
}

.col-md-12 {
    width: 100%
}

.mt5 {
    margin-top: 5px !important
}

.mt10 {
    margin-top: 10px !important
}

.mt15 {
    margin-top: 15px !important
}

.mt20 {
    margin-top: 20px !important
}

.mt30 {
    margin-top: 30px !important
}

.mt50 {
    margin-top: 50px !important
}

.ml10 {
    margin-left: 10px !important
}

.ml20 {
    margin-left: 20px !important
}

.ml30 {
    margin-left: 30px !important
}

.ml70 {
    margin-left: 70px !important
}

.al_c {
    text-align: center !important
}

.al_l {
    text-align: left !important
}

.al_r {
    text-align: right !important
}

.scroll_y {
    overflow: hidden;
    overflow-y: scroll
}

.scroll_x {
    overflow: hidden;
    overflow-x: scroll
}

.arrow_box {
    position: absolute;
    background: #fff;
    border: 1px solid #d6d6d6;
    border-radius: 3px;
    padding: 5px 15px;
    white-space: nowrap
}

.arrow_box:after,
.arrow_box:before {
    bottom: 100%;
    left: 50%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none
}

.arrow_box:after {
    border-color: rgba(255, 255, 255, 0);
    border-bottom-color: #fff;
    border-width: 6px;
    margin-left: -6px
}

.arrow_box:before {
    border-color: rgba(214, 214, 214, 0);
    border-bottom-color: #d6d6d6;
    border-width: 7px;
    margin-left: -7px
}

@-webkit-keyframes loading {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

@keyframes loading {
    0% {
        -webkit-transform: rotate(0);
        transform: rotate(0)
    }

    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg)
    }
}

/*# sourceMappingURL=reset.css.map */