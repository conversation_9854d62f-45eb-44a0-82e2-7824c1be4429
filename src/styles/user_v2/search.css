@charset "utf-8";
.pg-search {
  position: relative;
}
.pg-search .left-side {
  float: left;
  width: 214px;
}
.pg-search .right-side {
  float: right;
  width: 826px;
}
.pg-search .contents {
  width: 1080px;
  margin: 0 auto;
  padding-top: 37px;
  padding-bottom: 99px;
}
.pg-search .desc-price {
  padding-top: 7px;
  margin-bottom: 24px;
  font-size: 13px;
  line-height: 20px;
}
.pg-search .desc-price strong {
  font-weight: 700;
}
.pg-search .area-top {
  position: relative;
  margin-bottom: 14px;
}
.pg-search .area-top .result {
  float: left;
  padding-top: 6px;
  font-size: 17px;
  line-height: 25px;
}
.pg-search .area-top .result strong {
  font-weight: 700;
}
.pg-search .area-top .result-b {
  display: none;
}
.pg-search .area-top .box-mark {
  line-height: 40px;
  margin-left: 30px;
}
.pg-search .area-top .box-mark p {
  float: left;
  padding-top: 5px;
  margin-left: 30px;
}
.pg-search .area-top .box-mark img {
  margin-right: 5px;
}
.pg-search .area-top .select-align {
  margin-left: 23px;
}
.pg-search .select-align .ui-selectmenu-button {
  width: 280px;
  height: 38px;
  padding: 0 0 0 20px;
  margin: 0;
  border: 1px solid #e2e4e8;
  border-radius: 20px;
  background-color: #fff;
  line-height: 38px;
  outline: none;
}
.pg-search .select-align .ui-selectmenu-button::before {
  position: absolute;
  top: 0;
  left: 20px;
  line-height: 38px;
}
.pg-search .select-align .ui-selectmenu-button strong {
  display: none;
}
.pg-search .select-align .ui-selectmenu-button .ui-icon {
  width: 12px;
  margin-right: 17px;
  height: 38px;
  background: url(../../assets/images/cmm/ico_arrow_84.png) no-repeat 0 50%;
}
.pg-search .select-align .ui-selectmenu-button.ui-selectmenu-button-open .ui-icon,
.pg-search .select-align[is-open="true"] .ui-selectmenu-button .ui-icon {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.pg-search .area-top .select-align .inner {
  display: none;
  width: 255px;
  height: 38px;
  border: 1px solid #e2e4e8;
  border-radius: 20px;
  background-color: #fff;
}
.pg-search .area-cotn {
  position: relative;
}

/* 상단 재검색 */
.research-top {
  background-color: #fff;
  box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.06);
}
.research-top * {
  font-size: 15px;
  line-height: 22px;
}
.research-top .ico {
  position: absolute;
  top: 50%;
  right: 14px;
  width: 10px;
  height: 4px;
  margin-top: -2px;
  cursor: pointer;
  background: url(../../assets/images/search/ico_arrow_research.png) no-repeat 0 0;
}
.research-top .default-wd {
  position: relative;
}
.research-top .inner .plan {
  display: none;
  position: relative;
  float: left;
  width: 92px;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .plan .ico {
  right: 14px;
}
.research-top .inner .type {
  position: relative;
  float: left;
  width: 89px;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .type .list {
  top: 89px;
  width: 88px;
  border: 1px solid #e2e4e8;
  border-radius: 0 0 5px 5px;
  box-shadow: 0 6px 8px 0 rgba(90, 95, 104, 0.3);
}
.research-top .inner .type li {
  padding-top: 0;
  padding-bottom: 0;
  line-height: 36px;
}
.research-top .inner .type li .btn-default {
  display: block;
  width: 100%;
  height: 36px;
  font-size: 14px;
  line-height: 36px;
  padding-left: 16px;
  text-align: left;
}
.research-top .inner .type li .btn-default:hover {
  color: #4e81ff;
  background-color: rgba(59, 127, 243, 0.07);
}
.research-top .inner .depature {
  float: left;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .arrival {
  float: left;
  position: relative;
}
.research-top .inner .arrival::before {
  content: "";
  position: absolute;
  top: 53px;
  left: -20px;
  width: 17px;
  height: 12px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}

.research-top .inner .arrival-before + .arrival::before {
  background: none;
}

.research-top .inner .arrival dl.tit {
  padding-left: 32px;
}
.research-top .inner .date-picker {
  position: relative;
  float: left;
}
.research-top .btn-datepicker .val.empty {
  color: #c9cfd8;
}
.research-top .inner .date-depa {
  float: left;
}
.research-top .inner .date-arri {
  display: none;
  position: relative;
  float: left;
  width: 139px;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .date-arri dd {
  position: relative;
}
.research-top .inner .date-arri dd::before {
  content: "";
  position: absolute;
  bottom: 9px;
  left: -25px;
  width: 10px;
  height: 1px;
  background-color: #3b7ff3;
}
.research-top .inner .passenger {
  position: relative;
  float: left;
  width: 202px;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .nonstop {
  float: left;
  width: 103px;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .btn-research {
  float: left;
  padding: 10px 0 11px 19px;
  text-align: center;
  border-left: 1px solid #e2e4e8;
}
.research-top .inner .btn-research .btn-default {
  width: 68px;
  height: 68px;
  border-radius: 50px;
  color: #fff;
  font-weight: 500;
}
.research-top .inner .btn-research .btn-default:hover {
  background-color: #2e5ffb;
}
.research-top .inner .nonstop,
.research-top .inner .plan .btn-default,
.research-top .inner .type .sel .btn-default {
  position: relative;
  display: block;
  width: 100%;
  padding: 33px 14px 34px;
  text-align: left;
}
.research-top .inner .type.active .sel .btn-default {
  padding-bottom: 32px;
  border-bottom: 2px solid #4e81ff;
}
.research-top .inner dl.tit {
  padding-left: 14px;
}
.research-top .inner dl.tit {
  padding-left: 15px;
}
.research-top .inner dl.tit dt {
  padding-top: 22px;
  margin-bottom: 7px;
  font-size: 12px;
  line-height: 18px;
  font-weight: 500;
  color: #3b7ff3;
}
.research-top .inner dl.tit dd {
  position: relative;
  margin-bottom: 18px;
  letter-spacing: -1px;
}
.research-top .inner dl.tit dd input {
  width: 135px;
  height: 22px;
  border: 0 none;
  padding: 0;
  margin: 0;
  outline: none;
}
.research-top .inner dl.tit dd input:focus + .line {
  position: absolute;
  left: -15px;
  right: 0;
  bottom: -20px;
  height: 2px;
  background-color: #4e81ff;
}
.research-top .inner .txt-short {
  position: relative;
}
.research-top .inner .txt-short .active .ico {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.research-top .inner .nonstop {
  width: 77px;
  padding-left: 14px;
}
.research-top .inner .depature {
  padding-bottom: 20px;
}
.research-top .inner .depature dl.tit dd {
  margin-bottom: 0;
}
.research-top .inner .arrival {
  padding-bottom: 20px;
  border-right: 1px solid #e2e4e8;
}
.research-top .inner .arrival dl.tit dd {
  margin-bottom: 0;
}
.research-top .inner .passenger {
  padding-bottom: 20px;
}
.research-top .inner .passenger dl.tit dd {
  margin-bottom: 0;
}
.research-top .inner .passenger dl.tit dd a {
  display: block;
  margin-right: 14px;
}
.research-top .inner[data-ticket] .arrival dl.tit {
  margin-left: 24px;
  padding-left: 8px !important;
  padding-left: 0;
}
.research-top .inner[data-ticket] .sec-dates {
  border-bottom: 2px solid #fff;
}
.research-top .inner[data-ticket] .sec-dates.on {
  border-bottom-color: #4e81ff;
}
.research-top .inner[data-ticket] .date-arri {
  width: 131px;
  border-left: 0 none;
  padding-left: 10px;
}
.research-top .inner[data-ticket="oneway"] .date-depa {
  width: 208px;
}
.research-top .inner[data-ticket="round"] .date-depa {
  width: 129px;
}
.research-top .inner[data-ticket="round"] .date-arri {
  display: block;
}
.research-top .inner[data-ticket="multi"] .plan {
  display: block;
}
.research-top .inner[data-ticket="multi"] .plan .active .ico {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.research-top .inner[data-ticket="multi"] .depature {
  width: 189px;
}

.research-top .inner[data-ticket="multi"] .date-depa {
  width: 208px;
}
.research-top .inner[data-ticket="multi"] .nonstop {
  display: none;
}
.research-top.domestic .inner .depature {
  width: 183px;
}
.research-top.domestic .inner .arrival {
  width: 208px;
}
.research-top.domestic .inner .date-depa {
  width: 150px;
}
.research-top.domestic .inner .date-arri {
  width: 144px;
}
.research-top.domestic .inner[data-ticket="oneway"] .date-arri {
  display: block;
  opacity: 0;
  visibility: hidden;
}
.research-top.domestic .inner .passenger {
  width: 204px;
}
.research-top.domestic .inner[data-ticket] .sec-city {
  padding-bottom: 0;
}
.research-top.domestic .inner[data-ticket] .sec-city dl.tit {
  border-bottom: 2px solid #fff;
  padding-bottom: 18px;
}
.research-top.domestic .inner[data-ticket] .sec-city.on dl.tit {
  border-bottom-color: #4e81ff;
}

.research-top .plan-list {
  display: none;
  padding-bottom: 20px;
  padding-left: 90px;
  margin-right: 87px;
  line-height: 60px;
  border-top: 1px solid #e2e4e8;
}
.research-top .plan-list.active {
  display: block;
}
.research-top .plan-list * {
  line-height: 60px;
}
.research-top .plan-list li {
  position: relative;
  margin-top: 6px;
  list-style: none;
}
.research-top .plan-list li:after {
  content: "";
  display: block;
  clear: both;
}
.research-top .plan-list .plan {
  float: left;
  width: 64px;
  padding: 0 14px 0;
  border: 1px solid #e2e4e8;
}
.research-top .plan-list .depature {
  float: left;
  width: 174px;
  padding-left: 15px;
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
}
.research-top .plan-list .depature input {
  height: 60px;
  width: 135px;
  border: 0 none;
  outline: 0 none;
}
.research-top .plan-list .arrival {
  float: left;
  width: 175px;
  position: relative;
  /* padding-left: 32px; */
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
}
.research-top .plan-list .arrival::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 17px;
  height: 12px;
  margin-top: -6px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}

.research-top .plan-list .arrival-before + .arrival::before {
  background: none;
}

.research-top .plan-list .arrival input {
  height: 60px;
  width: 135px;
  border: 0 none;
  outline: 0 none;
}
.research-top .plan-list .date-arri {
  float: left;
  width: 193px;
  padding-left: 15px;
  border: 1px solid #e2e4e8;
}
.research-top .plan-list .btn-multi-ctrl.remove {
  float: left;
  padding: 21px 0 0 12px;
  line-height: 20px;
}
.research-top .plan-list .btn-multi-ctrl.remove .btn-default {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/search/btn_multi_ctrl.png) 0 0 no-repeat;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.research-top .plan-list .btn-multi-ctrl.add {
  margin: 10px 0 0;
}
.research-top .plan-list .btn-multi-ctrl.add .btn-default {
  width: 698px;
  height: 66px;
  line-height: 66px;
  text-align: center;
  background-color: #ffffff;
  border: 1px dashed #557ffe;
  border-radius: 8px;
}
.research-top .plan-list .btn-multi-ctrl.add .btn-default .btn-plus {
  padding-left: 23px;
  background: url(../../assets/images/main/btn_multi_ctrl.png) no-repeat left center / 14px 14px;
  font-size: 16px;
  font-weight: 500;
  color: #557ffe;
}
.research-top.hotel {
  height: 89px;
}
.research-top.hotel .inner[data-ticket="round"] .depature {
  width: 470px;
}
.research-top.hotel .inner[data-ticket="round"] .date-depa {
  border-left: 1px solid #e2e4e8;
}
.research-top.hotel .inner[data-ticket="round"] .date-arri .tit dt {
  height: 18px;
  text-indent: -99999em;
  color: transparent;
}
.research-top.hotel .depature dd {
  padding-right: 15px;
}
.research-top.hotel .depature .cityInput {
  width: 100%;
}

.pg-search .sec-map {
  position: relative;
}
.pg-search .sec-map .btn {
  display: block;
  width: 100%;
  cursor: pointer;
  margin-bottom: 30px;
  line-height: 54px;
  text-align: center;
  background: url(../../assets/images/search/img-map.png) 0 0 no-repeat;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
}
.pg-search .sec-map .btn::before {
  content: "";
  position: relative;
  top: -2px;
  margin-right: 8px;
  vertical-align: middle;
  display: inline-block;
  width: 13px;
  height: 16px;
  background: url(../../assets/images/search/icn-marker-w.png) 0 0 no-repeat;
}

/* 나의 출장 규정 */
.pg-search .sec-regulation {
  position: relative;
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 5px;
  background-color: #3f4e73;
  color: #fff;
}
.pg-search .sec-regulation h2 {
  margin-bottom: 16px;
  font-size: 17px;
  line-height: 25px;
  letter-spacing: -1px;
  font-weight: 700;
}
.pg-search .sec-regulation dl {
  padding-left: 22px;
  border-bottom: 1px solid rgba(226, 228, 232, 0.2);
}
.pg-search .sec-regulation dt {
  position: relative;
  padding: 8px 0 2px;
  font-size: 13px;
  line-height: 19px;
  color: #e2e4e8;
}
.pg-search .sec-regulation dt::before {
  content: "";
  position: absolute;
  top: 10px;
  left: -22px;
  width: 14px;
  height: 16px;
  background-image: url(../../assets/images/cmm/spr_ico_rule.png);
  background-repeat: no-repeat;
}
.pg-search .sec-regulation dl.time dt::before {
  background-position: 0 0;
}
.pg-search .sec-regulation dl.caleandar dt::before {
  background-position: 0 -16px;
}
.pg-search .sec-regulation dl.seat-df dt::before {
  background-position: 0 -32px;
}
.pg-search .sec-regulation dl.seat-mx dt::before {
  background-position: 0 -48px;
}
.pg-search .sec-regulation dl.condition dt::before {
  background-position: 0 -64px;
}
.pg-search .sec-regulation dl.etc dt::before {
  background-position: 0 -80px;
}
.pg-search .sec-regulation dd {
  padding-bottom: 9px;
  font-size: 13px;
  line-height: 19px;
  font-weight: 300;
}
.pg-search .sec-regulation dd strong {
  font-weight: 700;
}

/* 검색결과 내 검색 */
.pg-search .sec-in-search {
  position: relative;
}
.pg-search .sec-in-search h2 {
  padding-bottom: 18px;
  font-size: 17px;
  font-weight: 500;
  line-height: 25px;
  border-bottom: 1px solid #d2d6df;
}
.pg-search .sec-in-search dl {
  position: relative;
  padding-bottom: 16px;
  border-bottom: 1px solid #d2d6df;
}
.pg-search .sec-in-search dt {
  padding-top: 16px;
  margin-bottom: 17px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
}
.pg-search .sec-in-search dd {
  padding-bottom: 10px;
  line-height: 20px;
}
.pg-search div.sec-in-search dd.btn-updown {
  position: absolute;
  top: 16px;
  right: 0;
  display: block;
}
.pg-search .sec-in-search dd.btn-updown .btn {
  width: 22px;
  height: 22px;
  background: url(../../assets/images/cmm/btn-dropdownup-m.png) 50% 50% no-repeat;
  z-index: 1;
  cursor: pointer;
}
.pg-search .sec-in-search .form-chkbox span {
  line-height: 20px;
}
.pg-search .sec-in-search .airline {
  position: relative;
}
.pg-search .sec-in-search .airline dl {
  border-bottom: 0 none;
}
.pg-search .sec-in-search .airline dd:last-child {
  padding-bottom: 0;
}
.pg-search .sec-in-search .airline .btn-regi-all {
  position: absolute;
  top: 18px;
  right: 0;
}
.pg-search .sec-in-search .airline .btn-regi-all span {
  display: inline-block;
  color: #9da9be;
}
.pg-search .sec-in-search .airline .btn-regi-all .btn-default {
  font-size: 12px;
  line-height: 19px;
  color: #757f92;
}
.pg-search .sec-in-search .airline .btn-regi-all .btn-default.active {
  color: #4e81ff;
  font-weight: 500;
}
.pg-search .sec-in-search .airline .btn-view-all {
  padding: 6px 0 15px;
}
.pg-search .sec-in-search .airline .btn-view-all .btn-default {
  font-size: 13px;
  color: #4e81ff;
}
.pg-search .sec-in-search .airline .btn-view-all .btn-default.active {
  color: #4e81ff;
  font-weight: 500;
}
.pg-search .sec-in-search .slider-range {
  margin: 0 7px;
}
.pg-search .sec-in-search .btn-time {
  border-bottom: 0 none;
}
.pg-search .sec-in-search .btn-time label {
  display: inline-block;
  cursor: pointer;
}
.pg-search .sec-in-search .btn-time label input {
  visibility: hidden;
  position: absolute;
  top: -9999px;
  left: -9999em;
  opacity: 0;
  width: 0;
  height: 0;
}
.pg-search .sec-in-search .btn-time label span {
  display: inline-block;
  width: 102px;
  text-align: center;
  font-size: 13px;
  line-height: 30px;
  background-color: #fff;
  color: #9da9be;
  border-color: #e2e4e8;
  border: 1px solid #e2e4e8;
  border-radius: 18px;
}
.pg-search .sec-in-search .btn-time input:checked + span {
  background-color: #4e81ff;
  border-color: #4e81ff;
  color: #fff;
}
.pg-search .sec-in-search .range-time {
  border-top: 1px solid #d2d6df;
  border-bottom: 0 none;
}
.pg-search .sec-in-search .range-time .tit {
  margin-bottom: 3px;
  line-height: 20px;
}
.pg-search .sec-in-search .range-time .time {
  margin-bottom: 10px;
  font-size: 13px;
  line-height: 19px;
}
.pg-search .sec-in-search .btn-init {
  position: absolute;
  top: 0;
  right: 0;
  width: 61px;
  border: 1px solid #4e81ff;
  border-radius: 13px;
  font-size: 12px;
  line-height: 26px;
  line-height: 26px;
  font-weight: 500;
  color: #4e81ff;
  background-color: #fff;
}
.pg-search .sec-in-search .btn-init:hover {
  background-color: #4e81ff;
  color: #fff;
}
.pg-search .sec-in-search .sec-map {
  position: relative;
}
.pg-search .sec-in-search .hotel-name dt {
  position: relative;
  margin-bottom: 13px;
}
.pg-search .sec-in-search .hotel-name .box-search {
  position: relative;
  border: 1px solid #c9cfd8;
  border-radius: 5px;
  overflow: hidden;
}
.pg-search .sec-in-search .hotel-name .box-search input {
  box-sizing: border-box;
  width: 100%;
  height: 44px;
  border: 0 none;
  padding-left: 15px;
  padding-right: 46px;
}
.pg-search .sec-in-search .hotel-name .box-search .btn {
  position: absolute;
  top: 0;
  right: 0;
  width: 46px;
  height: 100%;
  background: url(../../assets/images/search/btn-search.png) 50% 50% no-repeat;
  cursor: pointer;
}
.pg-search .sec-in-search .range-fare {
  position: relative;
}
.pg-search .sec-in-search .range-fare .slider-range {
  margin-top: 20px;
  margin-bottom: 10px;
}
.pg-search .sec-in-search .range-fare .val {
  overflow: hidden;
  line-height: 20px;
}
.pg-search .sec-in-search .range-fare .left {
  float: left;
}
.pg-search .sec-in-search .range-fare .right {
  float: right;
}
.pg-search .sec-in-search .option-service {
  position: relative;
  border-bottom: 0 none;
}
.pg-search .sec-in-search .option-service dd {
  display: none;
}
.pg-search .sec-in-search .option-service dd.active {
  display: block;
}
.pg-search .sec-in-search .option-service dd.btn-more {
  position: relative;
  display: block;
  padding-bottom: 0;
}
.pg-search .sec-in-search .option-service dd.btn-more::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 10px;
  border-bottom: 2px solid #757f92;
  height: 0;
  margin-top: 0px;
}
.pg-search .sec-in-search .option-service dd.btn-more button {
  position: relative;
}
.pg-search .sec-in-search .option-service dd.btn-more span {
  display: none;
  position: relative;
  display: inline-block;
  padding-left: 14px;
  font-size: 13px;
  font-weight: 500;
  line-height: 19px;
  color: #757f92;
}
.pg-search .sec-in-search .option-service dd.btn-more .detail::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 4px;
  width: 0;
  height: 10px;
  margin-top: -5px;
  border-left: 2px solid #757f92;
}
.pg-search .sec-in-search .option-service dd.btn-more .simple {
  display: none;
}
.pg-search .sec-in-search .option-service dd.btn-more .active span.detail {
  display: none;
}
.pg-search .sec-in-search .option-service dd.btn-more .active .simple {
  display: block;
}
.pg-search .sec-in-search .option-service.box-arrow .active,
.pg-search .sec-in-search .option-service.box-arrow .btn-more {
  display: none !important;
}
.pg-search .sec-in-search .option-service.box-arrow .btn-updown {
  display: block !important;
}
.pg-search .sec-in-search .box-arrow dd {
  display: none;
}
.pg-search .sec-in-search .box-arrow dd.btn-updown {
  display: block;
}
.pg-search .sec-in-search .box-arrow dd .btn {
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
.pg-search .sec-in-search .box-arrow dt {
  margin-bottom: 0;
}

/* 검색결과 리스트 */
.result-item {
  overflow: hidden;
  position: relative;
  margin-top: 17px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  background-color: #fff;
}
.result-item:first-child {
  margin-top: 0;
}
.result-item::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-position: 0 0;
}
/* .result-item.in{ border: 1px solid #3b7ff3; } */
.result-item.in::after {
  background-image: url(../../assets/images/cmm/img_inpolicy.png);
}
.result-item.out::after {
  background-image: url(../../assets/images/cmm/img_outofpolicy.png);
}
.result-item.active {
  border: 1px solid #3b7ff3;
}
.nw-popup .result-item.active {
  border: 1px solid #e2e4e8;
}
.result-item .col-left {
  position: relative;
  width: 565px;
  padding: 26px 0 6px 30px;
}
.result-item .col-left .clearfix {
  margin-bottom: 25px;
}
.result-item .col-right {
  position: relative;
  width: 206px;
  padding: 23px 22px 20px 0;
  text-align: right;
  border-left: 1px solid #e2e4e8;
}
.nw-popup .result-item .col-left {
  border-right: 1px solid #e2e4e8;
}
.nw-popup .result-item .col-right {
  padding: 40px 22px 0 0;
  border-left: none;
}

/* info-default */
.result-item .info-default {
  position: relative;
}
.result-item .info-default .logo {
  float: left;
  width: 140px;
  padding-top: 13px;
  font-weight: 500;
  line-height: 18px;
  color: #333;
}
.result-item .info-default .logo img {
  margin-right: 7px;
}
.result-item .info-default .date {
  float: left;
  width: 50px;
  padding-top: 14px;
  font-size: 12px;
  line-height: 18px;
}
.result-item .info-default .info {
  float: left;
  position: relative;
  width: 60px;
  padding-top: 5px;
  margin-right: 20px;
  text-align: center;
}
.result-item .info-default .info .time {
  margin-bottom: 1px;
  font-size: 22px;
  font-weight: 700;
  line-height: 33px;
  color: #293036;
}
.result-item .info-default .info .airport {
  font-size: 12px;
  line-height: 18px;
  font-weight: 500;
  color: #9da9be;
}
.result-item .info-default .info .over-time {
  position: absolute;
  top: 14px;
  right: -16px;
  font-size: 12px;
  line-height: 18px;
  color: #ff4e50;
}
.result-item .info-default .overstop {
  float: left;
  width: 71px;
  margin-right: 20px;
  text-align: center;
  background: url(../../assets/images/cmm/icn_via_01.png) no-repeat 50% 50%;
}
.result-item .info-default .overstop p {
  font-size: 10px;
  line-height: 15px;
  color: #9da9be;
}
.result-item .info-default .overstop p:first-child {
  margin-bottom: 16px;
}
.result-item .info-default .etc-info {
  float: left;
  padding-top: 9px;
  margin-left: 23px;
  text-align: right;
  font-size: 12px;
  line-height: 18px;
  color: #757f92;
}
.result-item .info-default .etc-info .time {
  padding-left: 14px;
  background: url(../../assets/images/cmm/icn_clock_s.png) no-repeat 0 50%;
}
.result-item .info-default .etc-info strong {
  font-weight: bold;
  color: #4e81ff;
}
.result-item .info-default .btn-add-compare .btn-default {
  margin-bottom: 60px;
  width: 98px;
  height: 34px;
  line-height: 20px;
  border-radius: 20px;
  border: 1px solid #4e81ff;
  background-color: rgba(78, 129, 255, 0.07);
  font-weight: 500;
  color: #4e81ff;
}
.result-item .info-default .btn-add-compare .label-set {
  display: inline-block;
  text-indent: 5px;
  margin-bottom: 60px;
  width: 98px;
  border-radius: 20px;
  border: 1px solid #34446e;
  background-color: rgba(78, 129, 255, 0.07);
  font-size: 14px;
  line-height: 34px;
  font-weight: 500;
  color: #fff;
  background-color: #34446e;
  text-align: right;
}
.result-item .info-default .btn-add-compare .label-set i {
  display: inline-block;
  margin: 6px 7px 0 13px;
  width: 20px;
  height: 20px;
  border: 1px solid #fff;
  border-radius: 20px;
  vertical-align: top;
  text-align: center;
  text-indent: 0;
  color: #fff;
  line-height: 20px;
}
.nw-popup .result-item .info-default .price-detail {
  padding-left: 75px;
}
.nw-popup .result-item .info-default .price-detail .flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 7px;
}
.nw-popup .result-item .info-default .price-detail .flex:last-child {
  margin-bottom: 11px;
}
.nw-popup .result-item .info-default .price-detail .flex span {
  font-size: 13px;
  color: #3f4e73;
}
.result-item .info-default .price {
  overflow: hidden;
  margin-bottom: 6px;
}
.result-item .info-default .price .type {
  position: relative;
  top: 8px;
  display: inline-block;
  margin-right: 9px;
  font-size: 12px;
  line-height: 500;
  vertical-align: top;
  line-height: 18px;
  color: #ff4e50;
}
.result-item .info-default .price .val {
  display: inline-block;
  font-size: 22px;
  line-height: 33px;
  font-weight: 700;
  color: #333;
}
.result-item .info-default .btn-more {
  position: relative;
}
.result-item .info-default .btn-more .btn-default {
  padding-right: 16px;
  font-size: 13px;
  line-height: 19px;
  color: #3f4e73;
}
.result-item .info-default .btn-more .btn-default::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  width: 12px;
  height: 8px;
  margin-top: -4px;
  background: url(../../assets/images/cmm/ico_arrow_84.png) no-repeat 0 50%;
}
.result-item .info-default .btn-more .close {
  display: none;
}
.result-item.active .info-default .btn-more .view {
  display: none;
}
.result-item.active .info-default .btn-more .close {
  display: block;
}
.result-item.active .info-default .btn-more .btn-default::after {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* 상세보기 > 여행정보 */
.result-item .info-booking {
  display: none;
  position: relative;
}
.result-item.active .info-booking {
  display: block;
  border: 0 none;
  border-radius: 0;
  border-top: 1px solid #e2e4e8;
  padding: 0;
}
.result-item .info-booking .btn-tabs {
  overflow: hidden;
  padding: 0 22px 0 5px;
  border-radius: 0;
  font-weight: normal;
  background-color: #fff;
  border: 0 none;
}
.result-item .info-booking .btn-tabs li {
  position: relative;
  float: left;
  padding: 15px 0 20px;
  margin: 0 0 0 16px;
  border: 0 none;
  background-color: #fff;
  outline: none;
}
.result-item .info-booking .btn-tabs li a {
  padding: 0 4px;
  background-color: #fff;
  color: #282d5c;
  font-size: 14px;
  line-height: 20px;
}
.result-item .info-booking .btn-tabs li.active {
  font-weight: 700;
}
.result-item .info-booking .btn-tabs li.active::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  border-radius: 1px;
  background-color: #000;
}
.result-item .info-booking .btn-tabs li.ui-state-active {
  font-weight: 700;
}
.result-item .info-booking .btn-tabs li.ui-state-active::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  border-radius: 1px;
  background-color: #000;
}
.result-item .info-booking .plan.active,
.result-item .info-booking .fare.active,
.result-item .info-booking .business.active {
  display: block;
}

/* 상세보기 > 운임규정 */
.result-item .info-booking .fare {
  display: none;
  position: relative;
  padding: 0 20px 20px;
}
.result-item .info-booking .fare .scrollbar-inner {
  max-height: 604px;
}
.result-item .info-booking .fare .slimScrollBar {
  cursor: pointer;
}
.result-item .info-booking .fare .slimScrollBar,
.result-item .info-booking .fare .slimScrollRail {
  right: 0px !important;
  width: 6px !important;
}
.result-item .info-booking .fare .slimScrollRail {
  opacity: 0.4 !important;
}
.result-item .info-booking .fare .tab-btm {
  overflow: hidden;
  margin-bottom: 14px;
}
.result-item .info-booking .fare .tab-btm li {
  float: left;
  margin-right: 6px;
}
.result-item .info-booking .fare .tab-btm li.ui-tabs-active a {
  background-color: #3e4d91;
  color: #fff;
}
.result-item .info-booking .fare .tab-btm li.active a {
  background-color: #3e4d91;
  color: #fff;
}
.result-item .info-booking .fare .tab-btm a {
  display: block;
  width: 84px;
  height: 30px;
  border: 1px solid #3e4d91;
  border-radius: 16px;
  text-align: center;
  font-size: 13px;
  line-height: 30px;
  font-weight: 500;
  color: #3e4d91;
}
.result-item .info-booking .fare .desc {
  position: absolute;
  top: 10px;
  right: 20px;
  font-size: 13px;
  line-height: 19px;
  color: #293036;
}
.result-item .info-booking .fare .tbl-cmm {
  display: none;
}
.result-item .info-booking .fare .tbl-cmm.active {
  display: block;
}

/* 상세보기 > 출장규정 */
.result-item .info-booking .business {
  position: relative;
  display: none;
}
.result-item .info-booking .business .access {
  position: relative;
  margin: 0 20px 24px;
  padding-left: 40px;
  border-radius: 20px;
  font-size: 14px;
  line-height: 40px;
  font-weight: 500;
  color: #49b999;
}
.result-item .info-booking .business .access::before {
  content: "";
  position: absolute;
  top: 10px;
  left: 10px;
  width: 20px;
  height: 20px;
  border-radius: 100%;
}
.result-item .info-booking .business .access.pass {
  background-color: rgba(0, 190, 136, 0.1);
  color: #49b999;
}
.result-item .info-booking .business .access.pass::before {
  background-color: #49b999;
}
.result-item .info-booking .business .access.fail {
  background-color: rgba(255, 78, 80, 0.1);
  color: #ff4e50;
}
.result-item .info-booking .business .access.fail::before {
  background-color: #ff4e50;
}
.result-item .info-booking .business .tit {
  margin-bottom: 20px;
  font-size: 17px;
  line-height: 20px;
  font-weight: 300;
  text-align: center;
  color: #000;
}
.result-item .info-booking .business .tit strong {
  font-weight: 700;
}
.result-item .info-booking .business .inner {
  padding: 30px 34px;
  margin: 0 63px 20px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.result-item .info-booking .business .inner .desc {
  margin-bottom: 15px;
  font-size: 15px;
  line-height: 22px;
  color: #293036;
}
.result-item .info-booking .business .inner .desc strong {
  font-weight: 700;
  color: #4e81ff;
}
.result-item .info-booking .business .inner dl {
  overflow: hidden;
  padding: 4px 0;
  font-size: 13px;
  line-height: 19px;
}
.result-item .info-booking .business .inner dt {
  float: left;
  position: relative;
  width: 139px;
  padding-left: 22px;
}
.result-item .info-booking .business .inner dt::after {
  content: "";
  position: absolute;
  top: 5px;
  left: 0;
  width: 10px;
  height: 10px;
  border-radius: 100%;
}
.result-item .info-booking .business .inner dd {
  float: left;
  width: 455px;
}
.result-item .info-booking .business .inner strong {
  font-weight: 700;
}
.result-item .info-booking .business .pass dt::after {
  background-color: #49b999;
}
.result-item .info-booking .business .fail dt::after {
  background-color: #ff4e50;
}
.result-item .info-booking .business .normal dt::after {
  width: 8px;
  height: 8px;
  border: 1px solid #babdc3;
}
.result-item .info-booking .business .etc dt::after {
  background-color: #e2e4e8;
}

#listAlign-menu {
  padding: 7px 0;
  margin-top: 3px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  background-color: #fff;
}
#listAlign-menu li {
  padding: 0 20px;
}
#listAlign-menu li div {
  line-height: 36px;
  padding: 0;
  margin: 0;
}
#listAlign-menu li:hover {
  background-color: #f1f6fe;
}
#listAlign-menu li:hover div {
  color: #4e81ff;
}
#listAlign-menu li strong {
  display: inline-block;
  margin-left: 5px;
  font-size: 13px;
  color: #9da9be;
  font-weight: normal;
}

/* 비교하가 레이어 */
.layer-compare-box {
  position: absolute;
  top: 0;
  left: 50%;
  width: 110px;
  margin-left: 558px;
  padding-top: 168px;
} /* RVYN-1332 비교함 스크롤따라 움직임 추가로 수정 - 20230103 hej! */
.layer-compare-box.fixed {
  position: fixed;
}
.layer-compare-box .inner {
  overflow: hidden;
  margin-bottom: 10px;
  padding-bottom: 24px;
  border-radius: 5px;
  box-shadow: 0 6px 8px 0 rgba(157, 169, 190, 0.15);
  background-color: #ffffff;
}
.layer-compare-box .tit {
  padding: 21px 0 23px;
  border-top: 5px solid #4e81ff;
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  text-align: center;
  color: #4e81ff;
}
.layer-compare-box li {
  width: 70px;
  margin: 11px auto 0;
  border-radius: 17.5px;
  line-height: 35px;
  font-size: 18px;
  color: #babdc3;
  background-color: #f7f7f7;
  text-align: center;
}
.layer-compare-box li:first-child {
  margin-top: 0;
}
.layer-compare-box li.active {
  background-color: #34446e;
  color: #fff;
}
.layer-compare-box .btn-compare .btn-default {
  width: 100%;
  border-radius: 5px;
  background-color: #4e81ff;
  font-size: 15px;
  line-height: 48px;
  font-weight: 500;
  text-align: center;
  color: #fff;
}
.layer-compare-box .btn-compare .btn-default:hover {
  background-color: #2e5ffb;
}

/* 국내선 검색 결과 */
.result-domestic {
  position: relative;
  width: auto !important;
  padding-bottom: 0 !important;
}
.result-domestic .bg-inner {
  background-color: #fff;
}
.result-domestic .desc-ticket {
  padding-top: 5px;
  margin-bottom: 43px;
  font-size: 13px;
  line-height: 19px;
  font-weight: 300;
}
.result-domestic .desc-ticket strong {
  font-weight: 500;
}

.result-domestic .list-ticket {
  position: relative;
  overflow: hidden;
  margin-bottom: 40px;
}

.result-domestic .list-ticket .slimScrollDiv {
  padding-right: 9px;
}
.result-domestic .list-ticket .slimScrollDiv .slimScrollBar {
  background-color: #9da9be !important;
  opacity: 1 !important;
  border-radius: 3px !important;
}
.result-domestic .list-ticket .slimScrollDiv .slimScrollRail {
  background-color: #e0e1e4 !important;
  border-radius: 3px !important;
}
.result-domestic .list-ticket .slimScrollDiv .slimScrollBar,
.result-domestic .list-ticket .slimScrollDiv .slimScrollRail {
  width: 3px !important;
  right: 0 !important;
}
.result-domestic .list-ticket .col-left {
  float: left;
  width: 529px;
}
.result-domestic .list-ticket .col-right {
  float: right;
  width: 529px;
}
.result-domestic .list-ticket .desc-oneway {
  text-align: center;
  padding-top: 334px;
  background: url(../../assets/images/search/bg_none_oneway_deme.png) 50% 127px no-repeat;
  font-size: 13px;
  color: #7f848d;
}
.result-domestic .list-ticket .title {
  float: left;
  margin-right: 20px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
  color: #4e81ff;
}
.result-domestic .list-ticket .city {
  float: left;
}
.result-domestic .list-ticket .city span {
  float: left;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
.result-domestic .list-ticket .city span:first-child {
  padding-right: 27px;
  margin-right: 8px;
  background: url(../../assets/images/search/ico_arrow_city_dome.png) 100% 50% no-repeat;
}
.result-domestic .list-ticket .date {
  float: left;
  padding-top: 11px;
}
.result-domestic .list-ticket .date .btn-default {
  float: left;
  width: 9px;
  height: 16px;
  margin-top: 6px;
  background: url(../../assets/images/search/btn_arrow_date_dome.png) 100% 50% no-repeat;
  text-indent: -999em;
  color: transparent;
}

.result-domestic .list-ticket .date .btn-default.prev {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.result-domestic .list-ticket .date .sel {
  float: left;
  margin: 0 24px;
  font-size: 17px;
  line-height: 25px;
}
.result-domestic .list-ticket .select-align {
  float: right;
  margin: 0 9px 14px 0;
}
.result-domestic .list-ticket .select-align .ui-selectmenu-button {
  width: 126px;
  padding-left: 20px;
}
.result-domestic .list-ticket .select-align .ui-selectmenu-button::before {
  display: none;
}

.result-domestic .list-ticket .box-select-tbl {
  position: relative;
  z-index: 10;
}
.result-domestic .list-ticket .box-select-tbl ul {
  display: none;
}
.result-domestic .list-ticket .box-select-tbl.active ul {
  display: block;
}
.result-domestic .list-ticket .box-select-tbl.air ul {
  width: 109px;
  left: 25px;
}
.result-domestic .list-ticket .box-select-tbl.time ul {
  width: 109px;
  left: 0px;
}
.result-domestic .list-ticket .box-select-tbl.seat ul {
  width: 109px;
  left: 0px;
}
.result-domestic .list-ticket .box-select-tbl .tit {
  color: #fff;
}
.result-domestic .list-ticket .box-select-tbl .tit::after {
  content: "";
  display: inline-block;
  margin-left: 5px;
  width: 10px;
  height: 4px;
  background: url(../../assets/images/search/ico_arrow_thead.png) 0 0 no-repeat;
  vertical-align: middle;
}
.result-domestic .list-ticket .box-select-tbl ul {
  position: absolute;
  top: 100%;
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  box-shadow: 0 2px 15px 0 rgba(157, 169, 190, 0.2);
}
.result-domestic .list-ticket .box-select-tbl li {
  margin-top: 8px;
  line-height: 20px;
  text-align: left;
}
.result-domestic .list-ticket .box-select-tbl li span {
  color: #000;
}
.result-domestic .list-ticket .box-select-tbl li:first-child {
  margin-top: 0;
}
.result-domestic .list-ticket thead .box-in {
  font-size: 13px;
}
.result-domestic .list-ticket thead .box-in * {
  font-size: 13px;
}
.result-domestic .list-ticket tbody .box-in {
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
  font-size: 13px;
  background-color: transparent !important;
}
.result-domestic .list-ticket tbody td {
  height: 60px;
  background: #000;
  background: url(../../assets/images/cmm/bg_tbl_fare_center.png) 0 0 repeat-x;
  border-bottom: 0 none;
}
.result-domestic .list-ticket tbody td:first-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_left.png) 0 0 no-repeat;
}
.result-domestic .list-ticket tbody td:last-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_right.png) right 0 no-repeat;
}
.result-domestic .list-ticket tbody tr:hover td {
  background: url(../../assets/images/cmm/bg_tbl_fare_center_h.png) 0 0 repeat-x;
}
.result-domestic .list-ticket tbody tr:hover td:first-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_left_h.png) 0 0 no-repeat;
}
.result-domestic .list-ticket tbody tr:hover td:last-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_right_h.png) right 0 no-repeat;
}
.result-domestic .list-ticket tbody tr:hover .box-in {
  background-color: transparent !important;
}
.result-domestic .list-ticket tbody tr:hover .active .box-in {
  background-color: transparent !important;
}
.result-domestic .list-ticket tbody .active td {
  background: #000;
  background: url(../../assets/images/cmm/bg_tbl_fare_center_a.png) 0 0 repeat-x !important;
}
.result-domestic .list-ticket tbody .active td:first-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_left_a.png) 0 0 no-repeat !important;
}
.result-domestic .list-ticket tbody .active td:last-child {
  background: url(../../assets/images/cmm/bg_tbl_fare_right_a.png) right 0 no-repeat !important;
}
.result-domestic .list-ticket .table-cmm.type-schedule tbody td .box-in {
  line-height: 18px;
  border: 0 none;
}
.result-domestic .list-ticket tbody .aircraft {
  position: relative;
  padding: 0 0 0 24px;
  text-align: left;
}
.result-domestic .list-ticket tbody .aircraft img {
  position: absolute;
  top: 50%;
  left: 7px;
  width: 13px;
  height: 13px;
  margin-top: -7px;
}
.result-domestic .list-ticket tbody .aircraft .txt {
  display: block;
  width: 90px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.result-domestic .list-ticket tbody .aircraft .share {
  display: block;
  color: #4b75bf;
}
.result-domestic .list-ticket tbody .line-two.aircraft {
  padding-top: 8px;
  padding-bottom: 9px;
}
.result-domestic .list-ticket tbody .line-two.aircraft img {
  top: 18px;
}
.result-domestic .list-ticket tbody .line-two.aircraft .txt {
  line-height: 18px;
}
.result-domestic .list-ticket tbody .line-two.aircraft .share {
  line-height: 18px;
}
.result-domestic .list-ticket tbody .right .box-in {
  padding-right: 10px;
}
.result-domestic .list-ticket tbody tr {
  cursor: pointer;
}
.result-domestic .list-ticket tbody tr:hover .box-in {
  background-color: rgba(78, 129, 255, 0.07);
  border-color: #4e81ff;
}
.result-domestic .list-ticket tbody tr.active .box-in {
  border-color: #4e81ff;
  background-color: #4e81ff;
}
.result-domestic .list-ticket tbody tr.active * {
  color: #fff !important;
}

.result-domestic .selected-ticket {
  padding-bottom: 40px;
}
.result-domestic .selected-ticket .tit {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 700;
  color: #293036;
}
.result-domestic .selected-ticket .table-cmm .txt-short {
  width: 188px;
}
.result-domestic .selected-ticket .table-cmm .txt-short img {
  display: inline-block;
  margin-top: 17px;
  margin-right: 4px;
}
.result-domestic .selected-ticket .table-cmm strong {
  font-weight: 700;
  color: #293036;
}
.result-domestic .selected-ticket .table-cmm td.none {
  border-radius: 8px;
  height: 56px;
  background-color: #fff;
  font-size: 15px;
  color: #7f848d;
}

.result-domestic .fare-detail {
  position: relative;
  padding-top: 40px;
  margin-bottom: 44px;
}
.result-domestic .fare-detail .title {
  margin-bottom: 14px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
  color: #293036;
}
.result-domestic .fare-detail .table-cmm strong {
  font-weight: 700;
  color: #282d5c;
}
.result-domestic .fare-detail .none td {
  font-size: 15px;
  color: #7f848d;
}
.result-domestic .total-price {
  position: relative;
  margin-bottom: 87px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  text-align: center;
}
.result-domestic .total-price p {
  font-size: 18px;
  font-weight: 700;
  color: #293036;
  line-height: 76px;
}
.result-domestic .total-price p span {
  color: #4e81ff;
}
.result-domestic .total-price .btn-reserv {
  position: absolute;
  top: 13px;
  right: 13px;
  width: 140px;
  line-height: 50px;
  border-radius: 5px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  background-color: #4e81ff;
  color: #fff;
}

.pg-search .list-hotel {
  position: relative;
}
.pg-search .list-hotel li {
  position: relative;
  height: 196px;
  padding: 0 220px;
  margin-top: 15px;
  background-color: #ffffff;
  border-radius: 5px;
  border: 1px solid #e2e4e8;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
}
.pg-search .list-hotel li:fist-child {
  margin-top: 0;
}
.pg-search .list-hotel .thum {
  position: absolute;
  top: -1px;
  left: -1px;
  width: 198px;
  height: 198px;
  border-radius: 5px 0 0 5px;
  overflow: hidden;
}
.pg-search .list-hotel .thum:hover *[class*="swiper-button"] {
  display: block;
}
.pg-search .list-hotel .thum img {
  width: 198px;
  height: 198px;
}
.pg-search .list-hotel .thum *[class*="swiper-button"] {
  width: 30px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.6);
  overflow: hidden;
  border-radius: 100%;
  display: none;
}
.pg-search .list-hotel .thum *[class*="swiper-button"]::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(../../assets/images/search/slide-arrow-left-s.png) 50% 50% no-repeat;
}
.pg-search .list-hotel .thum *[class*="swiper-button"]::after {
  display: none;
}
.pg-search .list-hotel .thum *[class="swiper-button-prev"] {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.pg-search .list-hotel .name-kr {
  padding-top: 24px;
  margin-bottom: 3px;
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
  color: #293036;
}
.pg-search .list-hotel .name-en {
  margin-bottom: 14px;
  font-size: 12px;
  font-weight: 300;
}
.pg-search .list-hotel .grade {
  float: left;
  margin-right: 3px;
  font-size: 12px;
  color: #757f92;
}
.pg-search .list-hotel .star {
  float: left;
  margin-right: 12px;
}
.pg-search .list-hotel .star img {
  position: relative;
  top: -2px;
  margin-right: 2px;
  vertical-align: middle;
}
.pg-search .list-hotel .info {
  overflow: hidden;
  margin-bottom: 52px;
  line-height: 18px;
}
.pg-search .list-hotel .info .address {
  float: left;
  width: 260px;
  padding-left: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: url(../../assets/images/cmm/icn-marker-s-grey.png) 0 50% no-repeat;
  color: #757f92;
}
.pg-search .list-hotel .service {
  position: absolute;
  bottom: 24px;
  left: 220px;
  width: 450px;
  font-size: 13px;
  height: 19px;
  color: #4e81ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pg-search .list-hotel .box-right {
  z-index: 50;
  position: absolute;
  bottom: 24px;
  right: 22px;
}
.pg-search .list-hotel .box-right .price {
  margin-bottom: 20px;
  text-align: right;
}
.pg-search .list-hotel .box-right .price dt {
  font-size: 12px;
  font-weight: 500;
  color: #9da9be;
}
.pg-search .list-hotel .box-right .price dd {
  font-size: 22px;
  line-height: 33px;
  color: #333;
  font-weight: 700;
}
.pg-search .list-hotel .box-right .btn-sel-room {
  display: block;
  width: 120px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #4e81ff;
  color: #fff;
  text-align: center;
}

.pg-search .select-align.type-hotel .ui-selectmenu-button {
  width: 95px;
}

.none-result-hotel {
  position: relative;
  padding-top: 274px;
  text-align: center;
  background: url(../../assets/images/search/bg_none_oneway_deme.png) no-repeat 50% 90px;
}
.none-result-hotel dt {
  padding: 20px 0 6px;
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
  color: #757f92;
}
.none-result-hotel dd {
  margin-bottom: 20px;
  font-size: 13px;
  color: #757f92;
}
.none-result-hotel .btns-remove-filter {
  width: 120px;
  line-height: 36px;
  text-align: center;
  color: #000000;
  border-radius: 20px;
  background-color: #4e81ff;
  color: #fff;
}

.layer-saerch-city.type-hotel {
  position: absolute;
  top: 0;
  border: 0 none;
  box-shadow: none;
}
.layer-saerch-city.type-hotel .city-foregin {
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
  overflow: hidden;
  border-radius: 5px;
}

.result-map-box {
  display: none;
}
.result-map-box #map {
  height: 100%;
}
.result-map-box .item-point {
  position: absolute;
}
.result-map-box .item-point .price {
  position: relative;
}
.result-map-box .item-point .price button:after {
  top: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-top-color: #ffffff;
  border-width: 5px;
  margin-left: -5px;
}
.result-map-box .item-point .price button {
  position: relative;
  padding: 0 10px;
  line-height: 28px;
  font-size: 14px;
  font-weight: 700;
  color: #4e81ff;
  border-radius: 14px;
  background-color: #fff;
  box-shadow: -2px 6px 15px 0 rgba(157, 169, 190, 0.3);
  cursor: pointer;
  outline: none;
}
.result-map-box .item-point .info {
  display: none;
  position: absolute;
  top: 39px;
  left: 50%;
  margin-left: -150px;
  width: 186px;
  height: 100px;
  padding-left: 114px;
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.2);
}
.result-map-box .item-point .info .thum {
  position: absolute;
  top: 0;
  left: 0;
}
.result-map-box .item-point .info .thum img {
  width: 100px;
  height: 100px;
}
.result-map-box .item-point .info .name {
  padding: 15px 14px 0 0;
  margin-bottom: 4px;
  font-size: 13px;
  font-weight: 50;
  line-height: 19px;
}
.result-map-box .item-point .info .grade {
  font-size: 12px;
  color: #757f92;
}
.result-map-box .item-point .info .grade .star {
  display: inline-block;
  vertical-align: middle;
  margin-left: 3px;
}
.result-map-box .item-point .info .grade img {
  width: 13px;
  height: 12px;
  position: relative;
  top: 3px;
  vertical-align: top;
  margin-left: 2px;
}
.result-map-box .item-point.active .price button {
  background-color: #4e81ff;
  color: #fff;
}
.result-map-box .item-point.active .price button::after {
  border-top-color: #4e81ff;
}
.result-map-box .item-point.active .info {
  display: block;
}

.open-map {
  overflow: hidden;
}
.open-map #searchTicketBox .default-wd {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 30;
  width: auto;
  box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.06);
  background-color: #fff;
}
.open-map #searchTicketBox .default-wd .inner {
  width: 1080px;
  margin: 0 auto;
}
.open-map .contents .left-side {
  position: fixed;
  top: 89px;
  left: -236px;
  bottom: 0;
  z-index: 20;
  background-color: #fff;
  width: 214px;
  padding: 22px 0 22px 22px;
  -webkit-transition: left 0.5s ease;
  -moz-transition: left 0.5s ease;
  -o-transition: left 0.5s ease;
  transition: left 0.5s ease;
  box-shadow: -2px 6px 15px 0 rgba(157, 169, 190, 0.3);
}
.open-map .contents .left-side.open {
  left: 0;
}
.open-map .contents .left-side .slimScrollBar,
.open-map .contents .left-side .slimScrollRail {
  right: 8px !important;
}
.open-map .contents .left-side .box-inner {
  max-height: 550px;
  padding-right: 22px;
}
.open-map .contents .left-side .sec-map {
  display: none;
}
.open-map .contents .right-side {
  position: fixed;
  top: 89px;
  right: 0;
  z-index: 20;
  width: 366px;
  padding: 0 0 0 20px;
  background-color: #fff;
  box-shadow: -2px 6px 15px 0 rgba(157, 169, 190, 0.3);
}
.open-map .contents .right-side .slimScrollBar,
.open-map .contents .right-side .slimScrollRail {
  right: 8px !important;
}
.open-map .pg-search .area-top {
  padding-top: 25px;
}
.open-map .pg-search .area-top .result {
  display: none;
}
.open-map .pg-search .area-top .result-b {
  display: block;
  float: left;
}
.open-map .pg-search .area-top .result-b dt {
  font-size: 16px;
  line-height: 24px;
  font-weight: bold;
}
.open-map .pg-search .area-top .result-b dd {
  font-size: 13px;
  line-height: 19px;
  color: #34446e;
}
.open-map .pg-search .select-align .ui-selectmenu-button {
  padding-left: 20px;
}
.open-map .pg-search .select-align .ui-selectmenu-button::before {
  display: none;
}
.open-map .pg-search .list-hotel .thum *[class*="swiper-button"] {
  display: none;
}
.open-map .pg-search .list-hotel ul {
  max-height: 550px;
}
.open-map .pg-search .list-hotel li {
  height: 120px;
  margin-right: 20px;
  padding: 0 16px 0 136px;
  border-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.2);
}
.open-map .pg-search .list-hotel .thum {
  top: 0px;
}
.open-map .pg-search .list-hotel .thum img {
  width: 120px;
  height: 120px;
}
.open-map .pg-search .list-hotel .name-kr {
  padding-top: 15px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.open-map .pg-search .list-hotel .name-en {
  display: none;
}
.open-map .pg-search .list-hotel .info {
  margin-bottom: 6px;
}
.open-map .pg-search .list-hotel .info .address {
  display: none;
}
.open-map .pg-search .list-hotel .box-right {
  position: static;
}
.open-map .pg-search .list-hotel .service {
  display: none;
}
.open-map .pg-search .list-hotel .box-right .price dd {
  font-size: 18px;
  line-height: 27px;
  color: #4e81ff;
}
.open-map .pg-search .list-hotel .box-right .price dd .won {
  color: #000;
}
.open-map .pg-search .list-hotel .box-right .btn-sel-room {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.open-map .left-side .btns-sld-option {
  position: absolute;
  top: 26px;
  left: 100%;
  cursor: pointer;
  border-radius: 0 5px 5px 0;
  background-color: #4e81ff;
}
.open-map .left-side .btns-sld-option::after {
  content: "";
  display: block;
  width: 40px;
  height: 40px;
  background: url(../../assets/images/search/btn-back-wh.png) no-repeat 48% 53%;
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
.open-map .left-side.open .btns-sld-option::after {
  -webkit-transform: rotateZ(0deg);
  -ms-transform: rotateZ(0deg);
  -o-transform: rotateZ(0deg);
  transform: rotateZ(0deg);
}
.open-map .result-map-box {
  z-index: 15;
  display: block;
  position: absolute !important;
  top: 89px;
  left: 0;
  bottom: 0;
  right: 386px;
  background-color: #fff;
}
.open-map .btn-close-map {
  display: block;
  position: fixed;
  top: 36px;
  right: 36px;
  z-index: 100;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/search/btn-close-l.png) 0 0 no-repeat;
  text-indent: -999em;
  cursor: pointer;
}
.btn-close-map {
  display: none;
}

/*호텔 상세*/
#wrap .saerch-detail {
  padding-top: 0;
  width: auto;
  padding-bottom: 0;
}
.saerch-detail .list-hotel {
  background-color: #fff;
}
.saerch-detail .list-hotel ul {
  padding: 40px 0 59px;
}
.saerch-detail .list-hotel li {
  height: auto;
  margin-top: 0;
  padding: 0;
  background-color: #fff;
  border: 0 none;
  box-shadow: none;
}
.saerch-detail .list-hotel .service {
  position: static;
}
.saerch-detail .list-hotel .name-kr {
  margin-bottom: 5px;
  font-size: 20px;
  line-height: 29px;
}
.saerch-detail .list-hotel .name-en .grade {
  display: inline-block;
  float: none;
  margin-left: 13px;
  font-size: 13px;
  font-weight: 300;
}
.saerch-detail .list-hotel .name-en .star {
  display: inline-block;
  float: none;
}
.saerch-detail .list-hotel .service span {
  display: inline-block;
  margin-right: 16px;
  font-weight: 13;
  font-weight: 500;
  color: #34446e;
}
.saerch-detail .list-hotel .info {
  margin-bottom: 29px;
}

.saerch-detail .box-tab-hotel {
  position: relative;
}
.saerch-detail .box-tab-hotel .tab-btns {
  position: relative;
  margin-bottom: 46px;
  background-color: #fff;
}
.saerch-detail .box-tab-hotel .tab-btns li {
  float: left;
  width: 25%;
  border-bottom: 2px solid #fff;
}
.saerch-detail .box-tab-hotel .tab-btns li.active {
  border-color: #34446e;
}
.saerch-detail .box-tab-hotel .tab-btns li.active a {
  font-weight: 700;
}
.saerch-detail .box-tab-hotel .tab-btns a {
  display: block;
  padding: 9px 0;
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  color: #34446e;
}
.saerch-detail .trip-rule-chk {
  position: relative;
  margin-bottom: 29px;
}
.saerch-detail .trip-rule-chk .form-chkbox span {
  width: 63px;
  padding-left: 35px;
  line-height: 32px;
  background-color: #fff;
  border: 1px solid #9da9be;
  border-radius: 20px;
  color: #9da9be;
}
.saerch-detail .trip-rule-chk .form-chkbox input[type="checkbox"] + span::after {
  top: 6px;
  left: 7px;
  width: 20px;
  height: 20px;
  border: 0 none;
  background: url(../../assets/images/search/check_type02_dis.png) 0 0 no-repeat;
}
.saerch-detail .trip-rule-chk .form-chkbox input[type="checkbox"]:checked + span {
  color: #4e81ff;
  border: 1px solid #4e81ff;
  color: #4e81ff;
}
.saerch-detail .trip-rule-chk .form-chkbox input[type="checkbox"]:checked + span::after {
  background: url(../../assets/images/search/check_type02_sel.png) 0 0 no-repeat;
}

.saerch-detail .room-sel {
  position: relative;
  padding-bottom: 70px;
}
.saerch-detail .room-sel .tbl-wrap {
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head {
  position: relative;
  border-top: 2px solid #3f4e73;
  border-bottom: 2px solid #3f4e73;
  margin-bottom: 8px;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head::after {
  content: "";
  clear: both;
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head .qustion {
  position: relative;
  display: inline-block;
  z-index: 5;
  width: 14px;
  height: 14px;
  background: url(../../assets/images/search/icn_qm.png) 0 0 no-repeat;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head .qustion .cn {
  display: none;
  position: absolute;
  top: 100%;
  left: 50%;
  margin-top: 10px;
  margin-left: -145px;
  background-color: #34446e;
  line-height: 17px;
  padding: 13px 14px;
  border-radius: 5px;
  font-size: 12px;
  white-space: nowrap;
  color: #fff;
  border: 1px solid #34446e;
  text-align: left;
  font-weight: 400;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head .qustion:hover .cn {
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head .qustion .cn:after {
  bottom: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #34446e;
  border-width: 7px;
  margin-left: -7px;
}
.saerch-detail .room-sel .tbl-wrap .tbl-head .tbl-cell {
  float: left;
  padding: 15px 0;
  line-height: 22px;
  text-align: center;
  font-size: 15px;
  font-weight: 500;
  color: #3f4e73;
}
.saerch-detail .room-sel .tbl-wrap .tbl-cell {
  float: left;
  text-align: center;
}
.saerch-detail .room-sel .tbl-wrap .tbl-cell:nth-child(3) {
  width: 44%;
}
.saerch-detail .room-sel .tbl-wrap .tbl-cell:nth-child(4) {
  width: 16%;
}
.saerch-detail .room-sel .tbl-wrap .tbl-cell:nth-child(5) {
  width: 15%;
}
.saerch-detail .room-sel .tbl-wrap .tbl-cell:nth-child(6) {
  width: 25%;
}
.saerch-detail .room-sel .tbl-wrap .tbl-body {
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr {
  position: relative;
  margin-bottom: 4px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  background-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  width: 100%;
  align-items: center;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  flex-flow: wrap;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  background-repeat: no-repeat;
  background-position: 0 0;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.in::after {
  background-image: url(../../assets/images/cmm/img_inpolicy.png);
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.out::after {
  background-image: url(../../assets/images/cmm/img_outofpolicy.png);
}
/*.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn{display: none; position:absolute; top: 21%; margin-top: -22px; background-color: #34446e; line-height: 17px; padding: 13px 14px; border-radius: 5px; font-size: 12px; white-space: nowrap; color: #fff; border: 1px solid #34446e; text-align: left; font-weight: 400;}*/
/*.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn-upper:hover .cn{ display: block; }*/
/*.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn-upper{ width: 30px; height: 30px; position: absolute; top:0;z-index: 9999}*/
/*.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn:after {*/
/*	border: solid transparent;*/
/*	content: " ";*/
/*	height: 0;*/
/*	width: 0;*/
/*	position: absolute;*/
/*	border-color: rgba(136, 183, 213, 0);*/
/*	border-left-color: #34446e;*/
/*	border-width: 7px;*/
/*}*/
.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn-upper {
  position: absolute;
  display: inline-block;
  width: 30px;
  height: 30px;
  top: 0;
  z-index: 9999;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn {
  display: none;
  position: absolute;
  top: 73%;
  left: 101%;
  margin-top: 10px;
  margin-left: -145px;
  background-color: #34446e;
  line-height: 17px;
  padding: 13px 14px;
  border-radius: 5px;
  font-size: 12px;
  white-space: nowrap;
  color: #fff;
  border: 1px solid #34446e;
  text-align: left;
  font-weight: 400;
  z-index: 99999;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn-upper:hover .saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn {
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.out .cn:after {
  bottom: 100%;
  left: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #34446e;
  border-width: 7px;
  margin-left: -7px;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr:last-child {
  margin-bottom: 0;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr::after {
  content: "";
  clear: both;
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .name {
  padding: 15px 0 40px 40px;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  letter-spacing: -0.5px;
  text-align: left;
}
.saerch-detail .room-sel .tbl-wrap .able {
  position: relative;
  height: 100%;
}
.saerch-detail .room-sel .tbl-wrap .total {
  overflow: hidden;
  text-align: right;
}
.saerch-detail .room-sel .tbl-wrap .total .price {
  display: inline-block;
  margin-right: 20px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
  vertical-align: middle;
  color: #333;
}
.saerch-detail .room-sel .tbl-wrap .total .btn-reserv {
  display: inline-block;
  width: 100px;
  line-height: 36px;
  margin-right: 20px;
  border-radius: 17px;
  background-color: #4e81ff;
  color: #fff;
  text-align: center;
}
.saerch-detail .room-sel .tbl-wrap .total .btn-reserv-cant {
  display: inline-block;
  width: 100px;
  line-height: 36px;
  margin-right: 20px;
  border-radius: 17px;
  background-color: #757f92ff;
  color: #fff;
  text-align: center;
}
.saerch-detail .room-sel .tbl-wrap .type-status {
  width: 100%;
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .type-status .txt {
  position: absolute;
  top: -32px;
  left: 40px;
  padding-right: 12px;
  line-height: 18px;
}
.saerch-detail .room-sel .tbl-wrap .type-status .txt i {
  position: absolute;
  top: 50%;
  right: 0;
  width: 14px;
  height: 14px;
  margin-top: -7px;
  background: url(../../assets/images/search/icn_r_arrow.png) 0 0 no-repeat;
}
.saerch-detail .room-sel .tbl-wrap .type-status .txt button {
  cursor: pointer;
  display: inline-block;
  width: 286px;
  line-height: 18px;
  font-size: 12px;
  color: #757f92;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.saerch-detail .room-sel .tbl-wrap .type-status .txt::before {
  display: inline-block;
  padding: 0 7px;
  margin-right: 13px;
  border: 1px solid #000;
  font-size: 10px;
  font-weight: 500;
  line-height: 18px;
  border-radius: 5px;
}
.saerch-detail .room-sel .tbl-wrap .type-status.fail .txt::before {
  content: "환불불가";
  border-color: #ff4e50;
  color: #ff4e50;
}
.saerch-detail .room-sel .tbl-wrap .type-status.check .txt::before {
  content: "취소규정확인";
  border-color: #757f92;
  color: #757f92;
}
.saerch-detail .room-sel .tbl-wrap .type-status.special .txt::before {
  content: "특정";
  border-color: #4e81ff;
  color: #4e81ff;
}

.saerch-detail .room-sel .tbl-wrap .type-status .list-spc {
  padding: 20px 0 20px 40px;
  overflow: hidden;
  display: none;
}
.saerch-detail .room-sel .tbl-wrap .type-status .list-spc .tit {
  float: left;
  width: 65px;
  font-size: 13px;
  font-weight: bold;
  color: #4e81ff;
}
.saerch-detail .room-sel .tbl-wrap .type-status .list-spc ul {
  float: left;
}
.saerch-detail .room-sel .tbl-wrap .type-status .list-spc li {
  position: relative;
  padding-left: 21px;
  margin-bottom: 5px;
  font-size: 13px;
  line-height: 1.38em;
}
.saerch-detail .room-sel .tbl-wrap .type-status .list-spc li:last-child {
  margin-bottom: 0;
}
.saerch-detail .room-sel .tbl-wrap .type-status .list-spc li::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 4px;
  background-color: #4e81ff;
}
.saerch-detail .room-sel .tbl-wrap .type-status.active .list-spc {
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .type-status.active .txt i {
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
.saerch-detail .room-sel .tbl-wrap .contain {
  position: relative;
  z-index: 5;
}
.saerch-detail .room-sel .tbl-wrap .contain.notice::before {
  content: "알림";
  display: inline-block;
  width: 33px;
  margin-right: 4px;
  line-height: 18px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #ff9268;
  color: #ff9268;
  font-size: 10px;
  font-weight: 500;
  border-radius: 5px;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion {
  position: relative;
  top: -2px;
  display: inline-block;
  margin-left: 2px;
  line-height: 20px;
  width: 15px;
  height: 15px;
  background: url(../../assets/images/search/icn_qm_line.png) 0 0 no-repeat;
  vertical-align: middle;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion span {
  display: none;
  position: absolute;
  top: -8px;
  left: 100%;
  margin-left: 10px;
  background-color: #34446e;
  line-height: 17px;
  padding: 8px 11px;
  border-radius: 5px;
  font-size: 11px;
  white-space: nowrap;
  color: #fff;
  border: 1px solid #34446e;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion:hover span {
  display: block;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion span:after,
.saerch-detail .room-sel .tbl-wrap .contain .qustion span:before {
  right: 100%;
  top: 50%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion span:after {
  border-color: rgba(136, 183, 213, 0);
  border-right-color: #34446e;
  border-width: 7px;
  margin-top: -7px;
}
.saerch-detail .room-sel .tbl-wrap .contain .qustion span:before {
  border-color: rgba(194, 225, 245, 0);
  border-right-color: #34446e;
  border-width: 9px;
  margin-top: -9px;
}
.saerch-detail .room-sel .btns-room-all {
  text-align: center;
  margin-top: 40px;
}
.saerch-detail .room-sel .btns-room-all .btns {
  display: inline-block;
  width: 280px;
  line-height: 50px;
  border-radius: 5px;
  background-color: #4e81ff;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}
.saerch-detail .room-sel .result-none {
  position: relative;
  padding-top: 252px;
  background: url(../../assets/images/search/bg_none_oneway_deme.png) 50% 68px no-repeat;
}
.saerch-detail .room-sel .result-none .box-in {
  padding-bottom: 100px;
}
.saerch-detail .room-sel .result-none dt {
  text-align: center;
  padding-top: 18px;
  font-size: 18px;
  font-weight: 500;
  color: #757f92;
  line-height: 27px;
}
.saerch-detail .room-sel .result-none dd {
  text-align: center;
}
.saerch-detail .room-sel .result-none .type-btn {
  padding-top: 20px;
}
.saerch-detail .room-sel .result-none .type-btn .btns {
  display: inline-block;
  width: 150px;
  line-height: 36px;
  border-radius: 18px;
  font-size: 14px;
  font-weight: 500;
  background-color: #4e81ff;
  color: #fff;
  cursor: pointer;
}
.saerch-detail .room-sel .result-none .type-desc {
  padding-top: 5px;
  font-size: 13px;
  color: #757f92;
  line-height: 19px;
}
.saerch-detail .room-sel .result-none .relation-product {
  position: relative;
  overflow: hidden;
  background-color: transparent;
}
.saerch-detail .room-sel .result-none .relation-product li {
  position: relative;
  float: left;
  width: calc(33.3% - 20px);
  height: 120px;
  padding: 12px 16px 0 136px;
  margin-left: 20px;
  border: solid 1px #e2e4e8;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  background-color: #fff;
  border-radius: 5px;
  box-sizing: border-box;
}
.saerch-detail .room-sel .result-none .relation-product li:first-child {
  margin-left: 0;
}
.saerch-detail .room-sel .result-none .relation-product .thum {
  width: 120px;
  height: 120px;
  border-radius: 5px 0 0 5px;
}
.saerch-detail .room-sel .result-none .relation-product .name {
  padding: 0;
  margin-bottom: 4px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.saerch-detail .room-sel .result-none .relation-product .grade {
  position: static;
  margin-bottom: 6px;
}
.saerch-detail .room-sel .result-none .relation-product .price {
  text-align: right;
}
.saerch-detail .room-sel .result-none .relation-product .price em {
  display: block;
  font-size: 12px;
  line-height: 18px;
  color: #757f92;
}

.saerch-detail .my-bussines-rule {
  position: absolute;
  top: 663px;
  left: 50%;
  width: 174px;
  padding: 20px;
  margin-left: -786px;
  background-color: #3f4e73;
  border-radius: 5px;
  color: #fff;
}
.saerch-detail .my-bussines-rule dl {
  padding-left: 22px;
  border-bottom: 1px solid rgba(226, 228, 232, 0.2);
}
.saerch-detail .my-bussines-rule dt {
  margin-bottom: 16px;
  font-size: 17px;
  line-height: 25px;
  padding: 8px 0 2px;
}
.saerch-detail .my-bussines-rule dd {
  position: relative;
  padding-left: 19px;
  padding-bottom: 9px;
}
.saerch-detail .my-bussines-rule dd::before {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: 14px;
  height: 14px;
  background: url(../../assets/images/search/icn-qualification.png) 0 0 no-repeat;
}
.saerch-detail .my-bussines-rule dd span {
  display: block;
  margin-bottom: 5px;
  font-size: 13px;
  line-height: 19px;
}
.saerch-detail .my-bussines-rule dd strong {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #fff;
}
.saerch-detail .my-bussines-rule .etcRuleLine {
  line-height: 20px;
}
.saerch-detail .box-map-location {
  padding: 70px 0 80px;
  background-color: #fff;
}
.saerch-detail .box-map-location .default-wd {
  height: 300px;
}
.saerch-detail .trip-rule {
  position: relative;
  background-color: #fff;
}
.saerch-detail .trip-rule .title {
  margin-bottom: 29px;
  font-size: 20px;
  font-weight: 500;
  line-height: 29px;
}
.saerch-detail .trip-rule .box-item {
  position: relative;
  padding: 20px 0 29px;
  border-top: 1px solid #f2f4f9;
}
.saerch-detail .trip-rule .tit {
  position: absolute;
  top: 20px;
  left: 0;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.saerch-detail .trip-rule .box-ctn {
  padding-left: 200px;
}
.saerch-detail .trip-rule .info {
  position: relative;
}
.saerch-detail .trip-rule .info .name-kr {
  margin-bottom: 7px;
  font-size: 20px;
  font-weight: 500;
  line-height: 29px;
  color: #293036;
}
.saerch-detail .trip-rule .info .name-en {
  margin-bottom: 10px;
  font-size: 12px;
  line-height: 18px;
}
.saerch-detail .trip-rule .info .grade {
  display: inline-block;
}
.saerch-detail .trip-rule .info .star {
  display: inline-block;
}
.saerch-detail .trip-rule .info .star img {
  position: relative;
  top: 2px;
  margin-left: 2px;
}
.saerch-detail .trip-rule .info .address {
  padding-left: 14px;
  margin-bottom: 22px;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: url(../../assets/images/cmm/icn-marker-s-grey.png) 0 50% no-repeat;
  color: #757f92;
  font-size: 12px;
  line-height: 18px;
}
.saerch-detail .trip-rule .info .address .btn-view-map {
  display: inline-block;
  padding-right: 12px;
  margin-left: 20px;
  font-size: 12px;
  line-height: 18px;
  color: #4e81ff;
  background: url(../../assets/images/search/btn_board_arrow_more.png) 100% 50% no-repeat;
}
.saerch-detail .trip-rule .info dl {
  overflow: hidden;
}
.saerch-detail .trip-rule .info dt {
  margin-bottom: 5px;
  float: left;
  width: 9%;
  font-weight: 500;
}
.saerch-detail .trip-rule .info dd {
  margin-bottom: 5px;
  float: right;
  width: 91%;
}
.saerch-detail .trip-rule .etc {
  position: relative;
  line-height: 20px;
}
.saerch-detail .trip-rule .etc dl {
  margin-bottom: 20px;
}
.saerch-detail .trip-rule .etc dt {
  font-weight: 500;
}
.saerch-detail .trip-rule .etc .box-inner {
  height: 150px;
  overflow: hidden;
  margin-bottom: 15px;
}
.saerch-detail .trip-rule .etc .btn-more button {
  cursor: pointer;
  padding-left: 16px;
  background: url(../../assets/images/search/icn-plus-s-grey.png) 0 50% no-repeat;
  font-size: 13px;
  font-weight: 500;
  color: #757f92;
}
.saerch-detail .trip-rule .etc.active .box-inner {
  height: auto;
}
.saerch-detail .trip-rule .notice {
  line-height: 20px;
}
.saerch-detail .trip-rule .policy {
  position: relative;
}
.saerch-detail .trip-rule .policy .check {
  overflow: hidden;
}
.saerch-detail .trip-rule .policy .check .in,
.saerch-detail .trip-rule .policy .check .out {
  float: left;
  width: 190px;
  margin-right: 5px;
  line-height: 60px;
  border: 1px solid #c9cfd8;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  color: #282d5c;
}
.saerch-detail .trip-rule .policy strong {
  display: inline-block;
  margin-left: 7px;
  color: #4e81ff;
}
.saerch-detail .trip-rule .policy .desc {
  padding-top: 20px;
}
.saerch-detail .trip-rule .comport {
  position: relative;
  padding-bottom: 43px;
}
.saerch-detail .trip-rule .comport ul {
  overflow: hidden;
}
.saerch-detail .trip-rule .comport .has-ico li {
  float: left;
  width: auto;
  padding-top: 5px;
  margin-left: 32px;
  margin-bottom: 40px;
}
.saerch-detail .trip-rule .comport .has-ico li:first-child {
  margin-left: 0;
}
.saerch-detail .trip-rule .comport .has-ico li::before {
  content: "";
  display: block;
  width: 40px;
  height: 40px;
  margin: 0 auto 10px;
  background-image: url(../../assets/images/search/spr-comport.jpg);
  background-repeat: no-repeat;
}
.saerch-detail .trip-rule .comport li.pool::before {
  background-position: 50% 0px;
}
.saerch-detail .trip-rule .comport li.wifi::before {
  background-position: 50% -40px;
}
.saerch-detail .trip-rule .comport li.descs::before {
  background-position: 50% -80px;
}
.saerch-detail .trip-rule .comport li.language::before {
  background-position: 50% -120px;
}
.saerch-detail .trip-rule .comport li.cost::before {
  background-position: 50% -160px;
}
.saerch-detail .trip-rule .comport li.park::before {
  background-position: 50% -200px;
}
.saerch-detail .trip-rule .comport li.baby::before {
  background-position: 50% -240px;
}
.saerch-detail .trip-rule .comport li.restaurant::before {
  background-position: 50% -280px;
}
.saerch-detail .trip-rule .comport .none-ico li {
  float: left;
  width: 33.3%;
  line-height: 20px;
  margin-bottom: 16px;
}
.saerch-detail .trip-rule .comport .desc {
  font-size: 13px;
  color: #757f92;
}
.saerch-detail .relation-product {
  position: relative;
  padding-bottom: 90px;
  background-color: #fff;
}
.saerch-detail .relation-product .default-wd {
  position: relative;
}
.saerch-detail .relation-product .tit {
  margin-bottom: 30px;
  font-size: 20px;
  line-height: 29px;
  font-weight: 500;
}
.saerch-detail .relation-product .swiper-slide {
  position: relative;
  overflow: hidden;
  padding: 0 16px 21px;
  border-radius: 5px;
  overflow: hidden;
  border: 1px solid #e2e4e8;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  box-sizing: border-box;
}
.saerch-detail .relation-product .thum {
  position: absolute;
  top: -1px;
  left: -1px;
  border-radius: 5px 5px 0 0;
  width: 255px;
  height: 184px;
}
.saerch-detail .relation-product .city {
  margin-bottom: 12px;
  padding-top: 202px;
  font-size: 12px;
  line-height: 18px;
  color: #4e81ff;
}
.saerch-detail .relation-product .name {
  margin-bottom: 14px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
}
.saerch-detail .relation-product .price {
  font-size: 13px;
  line-height: 29px;
}
.saerch-detail .relation-product .price strong {
  display: inline-block;
  margin-left: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #4e81ff;
  vertical-align: middle;
}
.saerch-detail .relation-product .price span {
  display: inline-block;
  font-size: 18px;
  font-weight: 500;
  color: #333;
  vertical-align: middle;
}
.saerch-detail .relation-product .grade {
  position: absolute;
  top: 202px;
  right: 16px;
  font-size: 12px;
  line-height: 18px;
  color: #757f92;
}
.saerch-detail .relation-product .grade img {
  position: relative;
  top: -1px;
  vertical-align: middle;
}
.saerch-detail .relation-product .swiper-button {
  top: 54%;
  width: 66px;
  height: 66px;
  background: url(../../assets/images/search/banner_arrow_left_hover.png) 0 0 no-repeat;
}
.saerch-detail .relation-product .swiper-button::after {
  display: none;
}
.saerch-detail .relation-product .swiper-button-prev {
  left: -33px;
}
.saerch-detail .relation-product .swiper-button-next {
  right: -33px;
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
.box-photo .swiper-container .swiper-slide {
  height: 320px;
  background-repeat: no-repeat;
  -webkit-background-size: cover;
  background-size: cover;
}
.box-photo .swiper-container .swiper-slide img {
  display: none;
}
.box-photo .swiper-button {
  width: 60px;
  height: 60px;
  background: rgba(0, 0, 0, 0.5) url(../../assets/images/search/slide_arrow_left.png) 50% 50% no-repeat;
  border-radius: 50%;
}
.box-photo .swiper-button::after {
  display: none;
}
.box-photo .swiper-button-prev {
  left: 25px;
}
.box-photo .swiper-button-next {
  right: 25px;
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}

/* DT-11325 notice 호텔메인추가 - 20230531*/
.main-notice {
  padding-top: 8px;
}
.main-notice::before {
  content: "알림";
  display: inline-block;
  width: 33px;
  margin-right: 4px;
  line-height: 18px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #ff9268;
  color: #ff9268;
  font-size: 10px;
  font-weight: 500;
  border-radius: 5px;
  vertical-align: middle;
}
.main-notice a {
  padding-right: 12px;
  background: url(../../assets/images/svg/btn_dropdown_r.svg) no-repeat top 5px right/6px 10px;
  font-weight: 400;
  font-size: 12px;
  line-height: 1;
  vertical-align: middle;
}
.main-notice a span.c-blue {
  font-weight: 700;
  color: #557ffe !important;
}

/* DT-11325 대한항공 마일리지 표기 추가 - 20230531 */
.badge-mileage {
  margin-top: 5px;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: -0.28px;
  line-height: 1;
  color: #4e81ff;
}
.badge-mileage img {
  margin-right: 4px;
  width: 14px;
  height: 14px;
  vertical-align: top;
}
.badge-mileage .sky-mile {
  vertical-align: top;
}

/* DT-24190 마스터카드 유입 고객 할인 표기 */
.pg-search-reserv {
  position: relative;
  min-height: 600px;
}
.pg-search-reserv h2.title {
  margin-bottom: 40px;
  font-size: 26px;
  font-weight: 500;
  line-height: 38px;
}
.pg-search-reserv .contents {
  padding-top: 120px;
}
.search-reserv-cotn {
  position: relative;
  width: 400px;
  margin: 0 auto;
}
.search-reserv-cotn .info {
  font-size: 13px;
  line-height: 19px;
  font-weight: 400;
  color: #757f92;
  margin-bottom: 50px;
}
.search-reserv-cotn .number {
  position: relative;
}
.search-reserv-cotn .number input {
  height: 40px;
}
.search-reserv-cotn input::placeholder {
  color: #babdc3;
  font-weight: 400;
}
.search-reserv-cotn .name.in02 {
  margin-top: 20px;
}
.search-reserv-cotn .validate {
  padding-top: 20px;
  font-size: 12px;
}
.search-reserv-cotn .btns {
  margin-top: 40px;
}
.search-reserv-cotn .btn-search {
  width: 100%;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 50px;
  background-color: #4e81ff;
  color: #fff;
}

.pg-search .list-hotel .box-right .price .prev {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 4px;
  margin-top: 2px;
}
.pg-search .list-hotel .box-right .price .prev .sale {
  display: inline-block;
  padding: 2px 3px;
  background-color: #4e81ff;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  border-radius: 2px;
}
.pg-search .list-hotel .box-right .price .prev .del {
  color: #757f92;
  font-size: 12px;
  font-weight: 500;
  line-height: 17px;
  text-decoration: line-through;
}

/* DT-25030 스트라드비전 */
.saerch-detail .mark-desc {
  display: flex;
  align-items: center;
  gap: 20px;
}
.saerch-detail .mark-desc .mark {
  display: inline-flex;
  align-items: center;
  gap: 5px;
}
.saerch-detail .mark-desc .label {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}
.saerch-detail .room-sel .strad .thum {
  width: 168px;
  height: 168px;
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .tbl-tr.strad {
  border-radius: 10px;
  margin-bottom: 16px;
}
.saerch-detail .room-sel .strad .btn-indicate {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 8px;
  gap: 4px;
  position: absolute;
  right: 12px;
  bottom: 10px;
  background-color: rgba(12, 12, 12, 0.6);
  border-radius: 15px;
  color: #fff;
  font-size: 10px;
  line-height: 10px;
  font-weight: 400;
  letter-spacing: 0.2rem;
}
.saerch-detail .room-sel .strad .btn-indicate .count {
  font: inherit;
  line-height: 10px;
}
.saerch-detail .room-sel .strad .btn-indicate:after {
  content: "";
  display: block;
  width: 10px;
  height: 10px;
  background: url(../../assets/images/cmm/ico_indicate.png) no-repeat 0 0/10px;
}
.saerch-detail .room-sel .strad .thum img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.saerch-detail .room-sel .strad .tbl-cell-group {
  display: flex;
  height: 168px;
  width: 100%;
  padding: 20px 24px;
  box-sizing: border-box;
  position: relative;
  justify-content: space-between;
}
.saerch-detail .room-sel .strad.with-thum .tbl-cell-group {
  width: calc(100% - 168px);
}
.saerch-detail .room-sel .strad .name {
  padding: 0;
  font-size: 18px;
  line-height: 25px;
  font-weight: 700;
  color: #0c0c0c;
  overflow: hidden;
  white-space: normal;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: keep-all;
}
.saerch-detail .room-sel .strad .btn-group {
  margin-top: 8px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
}
.saerch-detail .room-sel .strad .btn-group .btn-roominfo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 3px 5px;
  border-width: 1px;
  border-style: solid;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 17px;
  border-radius: 4px;
  box-sizing: border-box;
  outline: 0;
  cursor: pointer;
}
.saerch-detail .room-sel .strad .btn-group span.btn-roominfo {
  cursor: default;
}
.saerch-detail .room-sel .strad .btn-group .check {
  border-color: #55575a;
  color: #55575a;
}
.saerch-detail .room-sel .strad .btn-group .alarm {
  border-color: #ff9268;
  color: #ff9268;
}
.saerch-detail .room-sel .strad .btn-group .fail {
  border-color: #ff4e50;
  color: #ff4e50;
}
.saerch-detail .room-sel .strad .btn-group .notice {
  gap: 4px;
  border-color: #4e81ff;
}
.saerch-detail .room-sel .strad .btn-group .notice .n-tit {
  color: #4e81ff;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: -0.02em;
  line-height: 17px;
}
.saerch-detail .room-sel .strad .btn-group .notice .n-bar {
  display: inline-block;
  width: 1px;
  height: 12px;
  background-color: #557ffe;
}
.saerch-detail .room-sel .strad .btn-group .notice .n-time {
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  color: #557ffe;
}
.saerch-detail .room-sel .strad .tbl-cell-group .tbl-cell {
  float: none;
  width: auto;
  max-width: 560px;
}
.saerch-detail .room-sel .strad .status {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 4px;
}
.saerch-detail .room-sel .strad .room-status {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}
.saerch-detail .room-sel .strad .room-status:before {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: 20px 20px;
  background-position: 0px 0px;
}
.saerch-detail .room-sel .strad .room-status.direct:before {
  background-image: url("/static/img/icon/ico_direct.png");
}
.saerch-detail .room-sel .strad .room-status.breakfast:before {
  background-image: url("/static/img/icon/ico_breakfast.png");
}
.saerch-detail .room-sel .strad .room-status.standby:before {
  background-image: url("/static/img/icon/ico_standby.png");
}
.saerch-detail .room-sel .strad .bar-status {
  width: 14px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.saerch-detail .room-sel .strad .bar-status:before {
  content: "";
  display: block;
  width: 2px;
  height: 2px;
  background-color: #0c0c0c;
  border-radius: 50%;
}
.saerch-detail .room-sel .tbl-wrap .strad .total {
  overflow: visible;
  width: 304px;
  height: 128px;
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price {
  display: block;
  margin-bottom: 4px;
  margin-right: 0;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price dt {
  font-size: 12px;
  font-weight: 500;
  color: #9da9be;
  line-height: 17px;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price dd {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 20px;
  line-height: 29px;
  color: #333;
  font-weight: 700;
  position: relative;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .prev {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 4px;
  margin-top: 2px;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .prev .sale {
  display: inline-block;
  padding: 2px 3px;
  background-color: #4e81ff;
  color: #fff;
  font-size: 10px;
  font-weight: 500;
  line-height: 14px;
  border-radius: 2px;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .prev .del {
  color: #757f92;
  font-size: 12px;
  font-weight: 500;
  line-height: 17px;
  text-decoration: line-through;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .mileage {
  height: 19px;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .btn-arrow-rate {
  margin-top: 0;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .btn-arrow-rate .cn {
  right: 0;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .badge-mileage {
  margin-top: 0px;
  display: inline-flex;
  gap: 4px;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .price .badge-mileage img {
  margin: 0;
}
.saerch-detail .room-sel .tbl-wrap .strad .total .btn-reserv {
  position: absolute;
  bottom: 0;
  right: 0;
  margin-right: 0;
}

/* 객실사진팝업 */
#popRoomPictures .modal-head h2 {
  padding: 24px 30px;
  font-size: 18px;
  line-height: 26px;
  font-weight: 700;
  color: #000;
}
#popRoomPictures .modal-cotn {
  padding: 0 35px 24px;
}
#popRoomPictures a.close-modal {
  top: 27px;
  right: 26px;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/svg/btn_close_popup_s.svg) no-repeat center center/12px 12px;
}
#popRoomPictures .swiper {
  box-sizing: border-box;
  line-height: normal;
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}
#popRoomPictures .swiper-wrapper {
  position: relative;
  z-index: 1;
  display: flex;
  box-sizing: content-box;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  list-style: none;
  transition-property: transform;
}
#popRoomPictures .swiper-wrapper,
#popRoomPictures .swiper-slide {
  height: auto;
}
#popRoomPictures .swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
#popRoomPictures .images-list {
  padding: 0 114px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}
#popRoomPictures .images-list .swiper-slide {
  width: 900px;
  height: 595px;
}
#popRoomPictures .images-list .swiper-pagination-fraction {
  position: absolute;
  display: flex;
  padding: 0 8px;
  border-radius: 15px;
  z-index: 1;
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  right: 50%;
  transform: translateX(50%);
  bottom: 12px;
  left: unset;
  width: auto;
  height: 26px;
  color: #000;
  background: rgba(255, 255, 2555, 0.8);
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  align-items: center;
  justify-content: center;
}
#popRoomPictures .images-list .swiper-pagination-fraction .swiper-pagination-current {
  margin-right: 5px;
}
#popRoomPictures .images-list .swiper-pagination-fraction .swiper-pagination-total {
  margin-left: 3px;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button {
  position: absolute;
  z-index: 1;
  top: 50%;
  display: flex;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: opacity 0.5s;
  transform: translateY(-50%);
  opacity: 0.9;
  border-radius: 100%;
  background-color: #fff;
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.1);
  align-items: center;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button:before {
  width: 100%;
  height: 100%;
  content: "";
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button.swiper-button-custom-prev {
  left: 24px;
  justify-content: right;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button.swiper-button-custom-next {
  right: 24px;
  justify-content: right;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button.swiper-button-custom-prev:before {
  transform: translateX(1px);
  background: url(../../assets/images/svg/ico-arrow-left-big.svg) no-repeat center center / 23% auto;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button.swiper-button-custom-next:before {
  transform: translateX(1px);
  background: url(../../assets/images/svg/ico-arrow-right-big.svg) no-repeat center center / 23% auto;
}
#popRoomPictures .slide-area .swiper-button-custom .swiper-button.swiper-button-disabled {
  opacity: 0.6;
  cursor: default;
}
#popRoomPictures .thumbs-list {
  padding: 0;
  margin-top: 12px;
}
#popRoomPictures .swiper-free-mode > .swiper-wrapper {
  margin: 0 auto;
  transition-timing-function: ease-out;
}
#popRoomPictures .thumbs-list .swiper-slide {
  position: relative;
  overflow: hidden;
  width: 102px;
  height: auto;
  aspect-ratio: 3 / 2;
  box-sizing: border-box;
}
#popRoomPictures .thumbs-list .swiper-slide::after {
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  width: 100%;
  height: 100%;
  content: "";
  opacity: 0.4;
  background-color: #000;
}
#popRoomPictures .thumbs-list .swiper-slide-thumb-active {
  border: 4px solid #01c5fd;
}
