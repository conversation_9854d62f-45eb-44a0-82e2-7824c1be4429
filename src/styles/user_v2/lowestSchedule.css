/* 최저가 항공권 팝업 */
#popLowestSchedule .modal-head h2 {
  padding: 25px 40px 10px;
  line-height: 38px;
}
#popLowestSchedule .modal-head .flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 40px 22px;
}
#popLowestSchedule .modal-head .flex .desc {
  font-size: 14px;
  color: #293036;
}
#popLowestSchedule .modal-head .flex span {
  display: inline-block;
  position: relative;
  margin-right: 50px;
  line-height: 22px;
}
#popLowestSchedule .modal-head .flex span + span {
  margin-right: 0;
}
#popLowestSchedule .modal-head .flex span::before {
  content: "";
  position: absolute;
  top: 0;
  left: -30px;
  width: 22px;
  height: 22px;
}
#popLowestSchedule .modal-head .flex .in-ico::before {
  background: url(../../assets/images/cmm/img_inpolicy.png) 50% 50% no-repeat;
  background-size: 22px;
}
#popLowestSchedule .modal-head .flex .out-ico::before {
  background: url(../../assets/images/cmm/img_outofpolicy.png) 50% 50% no-repeat;
  background-size: 22px;
}
#popLowestSchedule .modal-cotn {
  padding: 0 40px 40px;
}
#popLowestSchedule .btn-request {
  padding-top: 40px;
  text-align: center;
}
#popLowestSchedule .btn-request .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}

#popLowestSchedule .city-date {
  overflow: hidden;
  margin-bottom: 11px;
}
#popLowestSchedule .city-date p {
  font-size: 17px;
  line-height: 25px;
}
#popLowestSchedule .city-date div {
  font-size: 15px;
  line-height: 25px;
  color: #757f92;
  letter-spacing: -1px;
}
#popLowestSchedule .city-date .dep {
  float: left;
  margin-right: 15px;
}
#popLowestSchedule .city-date .arr {
  float: left;
  padding-left: 32px;
  margin-right: 13px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 60%;
}
#popLowestSchedule .city-date .date {
  float: left;
  position: relative;
  top: 2px;
  padding-left: 13px;
}
/* rvyn-590 */
.deatil-view {
  display: inline-block;
  position: relative;
  margin-top: 6px;
  margin-right: 20px;
  font-size: 13px;
  color: #3f4e73;
}
.deatil-view::before {
  content: "";
  position: absolute;
  top: 7px;
  right: -10px;
  width: 6px;
  height: 1px;
  background-color: #000;
  transform: rotate(45deg);
}
.deatil-view::after {
  content: "";
  position: absolute;
  top: 11px;
  right: -10px;
  width: 6px;
  height: 1px;
  background-color: #000;
  transform: rotate(-225deg);
}
#popLowestSchedule .city-date .date::after {
  content: "";
  position: absolute;
  top: 50%;
  right: -13px;
  width: 1px;
  height: 20px;
  margin-top: -10px;
  background-color: #4e81ff;
}
#popLowestSchedule .city-date .date::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 1px;
  height: 20px;
  margin-top: -10px;
  background-color: #4e81ff;
}
#popLowestSchedule .city-date .low {
  float: left;
  margin-left: 23px;
  margin-right: 8px;
  padding-top: 2px;
  font-size: 15px;
  color: #ff4e50;
}
/* #popLowestSchedule .city-date .view {display: inline-block; position:relative; margin-top:6px; margin-right: 20px; font-size: 13px; color: #3f4e73; } */
/* #popLowestSchedule .city-date .view::before { content: ''; position:absolute; top:7px; right: -10px; width:6px; height:1px; background-color: #000; transform: rotate(45deg);} */
/* #popLowestSchedule .city-date .view::after { content: ''; position:absolute; top:11px; right: -10px; width:6px; height:1px; background-color: #000; transform: rotate(-225deg);} */
#popLowestSchedule .desc-info {
  margin-bottom: 26px;
  font-size: 13px;
  line-height: 19px;
  color: #fe7d5c;
}

#popLowestSchedule .inner-scoll {
  position: relative;
}
#popLowestSchedule .box-scroll {
  height: 480px;
  overflow-y: auto;
}
#popLowestSchedule .list-schdule {
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  align-items: stretch;
  align-content: stretch;
}
#popLowestSchedule .list-schdule::after {
  content: "";
  clear: both;
  display: block;
}
#popLowestSchedule .list-schdule .cell-col {
  position: relative;
  width: 25.3%;
  padding-top: 13px;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #757f92;
}
/* #popLowestSchedule .list-schdule .cell-col:nth-child(3) .btn-add-col{ left:61%;}
#popLowestSchedule .list-schdule .cell-col:nth-child(4) .btn-add-col{ left:86.5%} */
#popLowestSchedule .list-schdule .cell-row {
  border-top: 2px solid #757f92;
}
#popLowestSchedule .list-schdule .cell-row:first-child {
  border-top: 0 none;
}
#popLowestSchedule .list-schdule .cell-row::after {
  content: "";
  clear: both;
  display: block;
}
#popLowestSchedule .list-schdule .box-head {
  display: block;
  width: 24.1%;
}
#popLowestSchedule .list-schdule .inner {
  float: right;
  width: 181px;
  border-bottom: 1px solid #c9cfd8;
}
#popLowestSchedule .list-schdule .title {
  height: 29px;
  padding: 1px 0 0 5px;
  border-bottom: 2px solid #757f92;
  font-size: 12px;
}
#popLowestSchedule .list-schdule .tit-01 {
  float: left;
  width: 78px;
  padding: 10px 0 0 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: -1px;
}
#popLowestSchedule .list-schdule .dep {
  border-top: 0 none;
}
#popLowestSchedule .list-schdule .dep .inner {
  border-bottom: 0 none;
}
#popLowestSchedule .list-schdule .arrs {
  background-color: #fff;
}
#popLowestSchedule .list-schdule .arrs .inner {
  border-bottom: 0 none;
}
#popLowestSchedule .list-schdule .fee {
  background-color: #fff;
}
#popLowestSchedule .list-schdule .fee .tit-01 {
  padding-top: 7px;
}
#popLowestSchedule .list-schdule .fee .inner {
  border-bottom: 0 none;
}
#popLowestSchedule .list-schdule .fee dd {
  padding-top: 10px;
  padding-bottom: 12px;
  margin-left: 47px;
}
#popLowestSchedule .list-schdule .fare {
  border-bottom: 0 none;
  border-top: 1px solid #e2e4e8;
}
#popLowestSchedule .list-schdule .fare dd {
  padding-top: 13px;
  padding-bottom: 12px;
  margin-left: 75px;
}
#popLowestSchedule .list-schdule .fare dd p {
  padding-top: 10px;
}
#popLowestSchedule .list-schdule .fare dd p:first-child {
  padding-top: 0px;
}
#popLowestSchedule .list-schdule .fare .inner {
  border-bottom: 0 none;
}
#popLowestSchedule .list-schdule .plan {
  border-top: 1px solid #e2e4e8;
}
#popLowestSchedule .list-schdule .plan dd p {
  display: none;
  padding-top: 10px;
}
#popLowestSchedule .list-schdule .plan dd p:first-child {
  padding-top: 0;
}
#popLowestSchedule .list-schdule dl {
  overflow: hidden;
}
#popLowestSchedule .list-schdule dt {
  float: left;
  padding: 14px 23px 14px 0;
  font-weight: 500;
}
#popLowestSchedule .list-schdule dt.deatil-view {
  margin-top: 0;
  margin-right: 0;
  color: #000;
}
#popLowestSchedule .list-schdule dt.deatil-view::before {
  top: 21px;
  right: 0;
}
#popLowestSchedule .list-schdule dt.deatil-view::after {
  top: 25px;
  right: 0;
}
#popLowestSchedule .list-schdule dt.way-info {
  position: relative;
}
#popLowestSchedule .list-schdule dt.way-info::after {
  content: "";
  display: inline-block;
  position: absolute;
  right: 0;
  top: 20px;
  width: 10px;
  height: 6px;
  background: url(../assets/images/cmm/btn_dropdown_black.png) 50% 50% no-repeat;
}
#popLowestSchedule .list-schdule dd {
  float: right;
  padding: 13px 20px 9px 0;
}
#popLowestSchedule .list-schdule dd p {
  font-size: 13px;
  line-height: 20px;
  text-align: right;
}
#popLowestSchedule .list-schdule dd .air {
  padding-bottom: 15px;
}
#popLowestSchedule .list-schdule dd .seat {
  padding-bottom: 10px;
}
#popLowestSchedule .list-schdule dd .overstop {
  position: relative;
}

#popLowestSchedule .list-schdule .box-radio {
  padding: 0 0 12px 15px;
  border-bottom: 2px solid #757f92;
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
}
#popLowestSchedule .list-schdule .form-chkbox span::after {
  top: 0;
}
#popLowestSchedule .list-schdule .box-radio span {
  padding-left: 30px;
}
#popLowestSchedule .list-schdule .info-default {
  position: relative;
  padding: 13px 11px 11px 15px;
  line-height: 19px;
  border-bottom: 1px solid #e2e4e8;
}
#popLowestSchedule .list-schdule .info-default p {
  font-size: 13px;
  margin-top: 11px;
  color: #293036;
}
#popLowestSchedule .list-schdule .info-default p:first-child {
  margin-top: 0;
  padding-bottom: 4px;
}
#popLowestSchedule .list-schdule .info-default img {
  margin-right: 7px;
}
#popLowestSchedule .list-schdule .info-default .name {
  margin-right: 11px;
  font-weight: 500;
}
#popLowestSchedule .list-schdule .info-default .code {
  color: #757f92;
}
#popLowestSchedule .list-schdule .info-plan {
  position: relative;
  border-bottom: 2px solid #757f92;
}
#popLowestSchedule .list-schdule .info-plan .btn-default {
  position: relative;
  padding: 14px 24px 13px 15px;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
}
#popLowestSchedule .list-schdule .info-plan .btn-default::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 20px;
  right: 0;
  width: 10px;
  height: 6px;
  background: url(../assets/images/cmm/btn_dropdown_gray.png) 50% 50% no-repeat;
}
#popLowestSchedule .list-schdule .info-plan ul {
  display: none;
  padding: 11px 20px 9px 15px;
}
#popLowestSchedule .list-schdule .info-plan li {
  padding-top: 13px;
  font-size: 13px;
}
#popLowestSchedule .list-schdule .info-plan li:first-child {
  padding-top: 0;
}
#popLowestSchedule .list-schdule .info-fee {
  position: relative;
  padding: 10px 0 11px 15px;
  border-bottom: 1px solid #e2e4e8;
  line-height: 19px;
}
#popLowestSchedule .list-schdule .info-fee strong {
  font-weight: 700;
}
#popLowestSchedule .list-schdule .info-fee span {
  color: #4e81ff;
}
#popLowestSchedule .list-schdule .info-rule {
  position: relative;
}
#popLowestSchedule .list-schdule .info-rule .baggage {
  padding: 13px 0 11px 15px;
  font-size: 13px;
  line-height: 19px;
}
#popLowestSchedule .list-schdule .info-rule .refund {
  padding: 0 0 10px 15px;
  font-size: 13px;
  line-height: 19px;
}
#popLowestSchedule .list-schdule .info-rule .refund .btn-default {
  width: 60px;
  height: 20px;
  border: 1px solid #000;
  border-radius: 3px;
  border: solid 1px #000000;
  text-align: center;
}
#popLowestSchedule .list-schdule .btn-delete {
  position: absolute;
  top: 12px;
  right: 20px;
  font-size: 13px;
  line-height: 19px;
  text-decoration: underline;
}
#popLowestSchedule .list-schdule .btn-delete[disabled] {
  color: #e2e4e8;
}
#popLowestSchedule .list-schdule .btn-add-col {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -49px;
  margin-top: -9px;
}
#popLowestSchedule .list-schdule .btn-add-col .btn-default {
  width: 98px;
  line-height: 34px;
  border: 1px solid #4e81ff;
  border-radius: 20px;
  background-color: rgba(78, 129, 255, 0.07);
  font-weight: 500;
  color: #4e81ff;
}

#popLowestSchedule .list-schdule .info-default::after,
#popLowestSchedule .list-schdule .info-plan::after,
#popLowestSchedule .list-schdule .info-fee::after,
#popLowestSchedule .list-schdule .info-rule::after,
#popLowestSchedule .list-schdule .schdule-01 *[class*="info-"]::before {
  content: "";
  position: absolute;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background-color: #e2e4e8;
}
#popLowestSchedule .list-schdule .schdule-01 *[class*="info-"]::before {
  left: 0;
}
#popLowestSchedule .list-schdule .schdule-03 *[class*="info-"]::after {
  display: none;
}
#popLowestSchedule .list-schdule *[class*="info-"]::after {
  right: -1px;
}

#popLowestSchedule .list-schdule .cell-col.active {
  background-color: rgba(78, 129, 255, 0.05);
  border-color: #4e81ff;
  z-index: 1;
}
#popLowestSchedule .list-schdule .cell-col.active::before,
#popLowestSchedule .list-schdule .cell-col.active::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  border-left: 2px solid #4e81ff;
  z-index: 1;
}
#popLowestSchedule .list-schdule .cell-col.active::before {
  left: 0;
}
#popLowestSchedule .list-schdule .cell-col.active::after {
  right: 0;
}
#popLowestSchedule .list-schdule .cell-col.active *[class*="info-"]::after {
  right: 0;
}

#popLowestSchedule .list-schdule.view-all .plan dd p {
  display: block;
}
#popLowestSchedule .list-schdule.view-all .info-plan .btn-default {
  display: none;
}
#popLowestSchedule .list-schdule.view-all .info-plan ul {
  display: block;
}

#popLowestSchedule .btn-request-reserv {
  padding-top: 28px;
  text-align: center;
}
.nw-popup#popLowestSchedule .btn-request-reserv {
  padding-top: 0;
  padding-bottom: 40px;
}
#popLowestSchedule .btn-request-reserv .btn-default {
  width: 138px;
  height: 48px;
  border-radius: 5px;
  border: 1px solid #4e81ff;
  font-size: 16px;
  line-height: 48px;
  font-weight: 500;
  text-align: center;
}
#popLowestSchedule .btn-request-reserv .btn-default.off {
  background-color: #fff;
  color: #4e81ff;
}
#popLowestSchedule .btn-request-reserv .btn-default.on {
  background-color: #4e81ff;
  color: #fff;
}
