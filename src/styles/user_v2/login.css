﻿@charset "utf-8";
.pg-login {
  position: relative;
  min-height: 600px;
}
.pg-login h2.title {
  margin-bottom: 50px;
  font-size: 26px;
  font-weight: 500;
  line-height: 38px;
}
.pg-login .contents {
  padding-top: 120px;
}

.login-cotn {
  position: relative;
  width: 400px;
  margin: 0 auto;
}
.login-cotn .id {
  position: relative;
  margin-bottom: 30px;
}
.login-cotn .id .box-col {
}
.login-cotn .id input {
  height: 40px;
  padding-right: 30%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.login-cotn .id .mail {
  position: absolute;
  top: 10px;
  right: 0;
}
.login-cotn .pw {
  position: relative;
}
.login-cotn .pw input {
  height: 40px;
}
.login-cotn .etc-col {
  overflow: hidden;
}
.login-cotn .etc-col .btns {
  float: left;
  padding-top: 34px;
}
.login-cotn .etc-col .btns a {
  float: left;
  line-height: 20px;
  color: #9da9be;
}
.login-cotn .etc-col .btns a:first-child {
  position: relative;
  margin-right: 15px;
  padding-right: 15px;
  color: #9da9be;
}
.login-cotn .etc-col .btns a:first-child::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  width: 1px;
  height: 12px;
  margin-top: -6px;
  background-color: #9da9be;
}
.login-cotn .etc-col .btn-login {
  float: right;
  width: 86px;
  height: 86px;
  margin-top: 30px;
  border-radius: 50px;
  text-align: center;
  background-color: #4e81ff;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
}
.login-cotn .etc-col .btn-login:hover {
  background-color: #2e5ffb;
}
.login-cotn .validate {
  padding-top: 20px;
  font-size: 12px;
}

.pw-reset-cotn {
  position: relative;
  width: 400px;
  margin: 0 auto;
}
.pw-reset-cotn .id {
  position: relative;
}
.pw-reset-cotn .id .box-col {
}
.pw-reset-cotn .id input {
  height: 40px;
  padding-right: 30%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.pw-reset-cotn .id .mail {
  position: absolute;
  top: 10px;
  right: 0;
}
.pw-reset-cotn .new-pw input {
  height: 40px;
}
.pw-reset-cotn .new-pw.in02 {
  margin-top: 20px;
}
.pw-reset-cotn .validate {
  padding-top: 20px;
  font-size: 12px;
}
.pw-reset-cotn .btns {
  margin-top: 40px;
}
.pw-reset-cotn .btn-link-send {
  width: 100%;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 50px;
  background-color: #4e81ff;
  color: #fff;
}

.policy-agree-cotn {
  position: relative;
  width: 400px;
  margin: 0 auto;
}
.policy-agree-cotn h2.title {
  line-height: 38px;
}
.policy-agree-cotn .form-chkbox {
  line-height: 22px;
}
.policy-agree-cotn .form-chkbox span {
  font-size: 15px;
}
.policy-agree-cotn .form-chkbox span::after {
  border-radius: 100%;
}
.policy-agree-cotn .all .desc {
  padding: 0 0 21px 28px;
  margin-bottom: 20px;
  border-bottom: 1px solid #e2e4e8;
  padding-top: 20px;
  font-size: 13px;
  line-height: 19px;
  font-weight: 300;
  color: #7f848d;
}
.policy-agree-cotn .cell {
  margin-bottom: 20px;
}
.policy-agree-cotn .btns {
  padding-top: 10px;
}
.policy-agree-cotn .btn-confirm {
  width: 100%;
  border-radius: 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 50px;
  background-color: #4e81ff;
  color: #fff;
}

.join-cotn {
  position: relative;
  padding: 40px 0 0 0 !important ;
  width: 1080px;
  margin: 0 auto;
}
.join-cotn .box-division {
  position: relative;
  padding: 18px 0 40px;
  border-top: solid 2px #ebeef3;
}
.join-cotn .box-division:first-child {
  border-top: solid 2px #ebeef3;
}
.join-cotn .box-division .tit {
  margin-bottom: 32px;
  line-height: 25px;
  font-size: 17px;
  font-weight: 500;
}
.join-cotn .box-division dl {
  position: relative;
}
.join-cotn .box-division dl::after {
  content: "";
  display: block;
  clear: both;
}
.join-cotn .box-division dt .essential {
  display: inline-block;
  color: #4e81ff;
}
.join-cotn .box-division dt {
  clear: both;
  float: left;
  width: 130px;
  margin-top: 13px;
  color: #757f92;
  line-height: 32px;
}
.join-cotn .box-division dd {
  float: left;
  width: 950px;
  margin-top: 13px;
  font-size: 15px;
  line-height: 32px;
}
.join-cotn .box-division dt:first-child,
.join-cotn .box-division dt:first-child + dd {
  margin-top: 0;
}
.join-cotn .box-division dd * {
  font-size: 15px;
  line-height: 32px;
}
.join-cotn .box-division dd.gender .form-radio {
  float: left;
  margin-right: 40px;
}
.join-cotn .box-division .mail span {
  float: left;
}
.join-cotn .box-division .mail .btn-default {
  float: left;
  margin-left: 14px;
}
.join-cotn .box-division .mail .time {
  margin-left: 10px;
  font-size: 12px;
  line-height: 36px;
  color: #4e81ff;
}
.join-cotn .box-division .mail .validate {
  clear: both;
  font-size: 12px;
}
.join-cotn .box-division .code .btn-default {
  margin-left: 3px;
}
.join-cotn .box-division .btn-white {
  width: 98px;
  line-height: 34px;
  border: 1px solid #4e81ff;
  border-radius: 5px;
  text-align: center;
  color: #4e81ff;
  font-size: 13px;
  font-weight: 500;
}
.join-cotn .box-division .btn-gray {
  width: 98px;
  line-height: 34px;
  border: 1px solid #babdc3;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-size: 13px;
  font-weight: 500;
  background-color: #babdc3;
}
.join-cotn .box-col {
  float: left;
  width: 210px;
  margin-left: 33px;
}
.join-cotn .box-col:first-child {
  margin-left: 0;
}
.join-cotn .box-col.box-ymd {
  border-bottom: 1px solid #e2e4e8;
}
.join-cotn .box-col.box-ymd .form-select {
  border-bottom: 0 none;
  margin-right: 15px;
}
.join-cotn .box-col .form-chkbox span::after {
  top: 50%;
}
.join-cotn .box-col.tel {
  border-bottom: 1px solid #e2e4e8;
}
.join-cotn .box-col.tel .form-select {
  border-bottom: 0 none;
}
.join-cotn .box-col.tel input {
  width: 80px;
  padding-left: 15px;
  border-bottom: 0 none;
}
.join-cotn .desc-essential {
  position: absolute;
  top: 20px;
  right: 0;
  line-height: 20px;
  color: #757f92;
}
.join-cotn .desc-essential span {
  color: #4e81ff;
}
.join-cotn .form-chkbox span::after {
  top: 50%;
  margin-top: -9px;
}
.join-cotn .btns-bottom {
  text-align: center;
  padding-top: 40px;
  padding-bottom: 100px;
}
.join-cotn .btns-bottom .btn-blue {
  width: 140px;
  line-height: 50px;
  border-radius: 5px;
  background-color: #4e81ff;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

.join-complete {
  position: relative;
}
.join-complete dt {
  margin-bottom: 40px;
  font-size: 26px;
  line-height: 38px;
  font-weight: 500;
  color: #000;
}
.join-complete dd {
  margin-bottom: 40px;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
}
.join-complete dd span {
  color: #ff4e50;
}

/* RVYN-561 ----------*/
.login-cont {
  max-width: 400px;
  margin: 0 auto;
  padding: 120px 0 100px;
}
#container .login-cont h2 {
  line-height: normal;
  padding-left: 0;
  background: none;
}
.login-top {
  line-height: 19.24px;
  margin-top: 20px;
  font-size: 13px;
  border-bottom: 2px solid #ebeef3;
}
.dot-list > li {
  position: relative;
  font-size: 13px;
  letter-spacing: -0.3px;
}
.dot-list > li::before {
  content: "•";
  float: left;
  padding-right: 5px;
}
.dot-list > li p {
  overflow: hidden;
}
.login-top .text-top {
  font-weight: 350;
}
.login-top .dot-list {
  margin: 20px 0 40px 0;
}
.login-middle > .tit {
  line-height: 25.16px;
  padding: 17px 0;
  font-size: 17px;
  font-weight: 500;
  letter-spacing: -0.28px;
}
.login-form-list li {
  display: flex;
  flex: 1 0 0;
  width: 100%;
  margin-top: 10px;
}
.login-form-list li .tit {
  width: 130px;
  padding-top: 7px;
  letter-spacing: 0.3px;
  color: #757f92;
}
.login-form-list li .area {
  flex-basis: 210px;
}
.login-bottom {
  margin-top: 50px;
}
.login-bottom .btn-default {
  width: 196px;
  height: 60px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 5px;
  vertical-align: middle;
}
.btn-outline {
  border: 1px solid #4e81ff;
  color: #4e81ff;
  letter-spacing: -0.32px;
}
.login-bottom .btn-outline span {
  display: block;
  font-size: 14px;
}
.btn-pull {
  background-color: #4e81ff;
  color: #fff;
}
.login-bottom .btn-pull {
  float: right;
}
/* //RVYN-561 ----------*/
