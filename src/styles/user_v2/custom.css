#popUseCardHistory .modal-head h2 {
  padding: 24px 30px 30px;
}
#popUseCardHistory .modal-cotn {
  padding: 0 0 30px 30px;
}
#popUseCardHistory .box-saerch-cmm {
  margin-right: 30px;
}
#popUseCardHistory .list-result .total {
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 22px;
}
#popUseCardHistory .list-result .total span {
  font-weight: 700;
  color: #4e81ff;
}
#popUseCardHistory .table-cmm th {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
#popUseCardHistory .table-cmm td {
  height: 50px;
}
#popUseCardHistory .table-cmm .ready td {
  padding: 20px 0 40px;
  height: 20px;
  border: 0 none !important;
}
#popUseCardHistory .table-cmm .none td {
  padding: 20px 0 40px;
  height: 20px;
  color: #7f848d;
  border: 0 none !important;
}
#popUseCardHistory .table-cmm div {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 20px;
}
#popUseCardHistory .scrollbar-inner {
  max-height: 360px;
  padding-right: 30px;
}
#popUseCardHistory .slimScrollDiv .slimScrollBar,
#popUseCardHistory .slimScrollDiv .slimScrollRail {
  right: 12px !important;
}
#popUseCardHistory .table-cmm .code div {
  width: 80px;
}
#popUseCardHistory .table-cmm .name div {
  width: 95px;
}
#popUseCardHistory .table-cmm .company div {
  width: 110px;
}
#popUseCardHistory .table-cmm .team div {
  width: 135px;
}
#popUseCardHistory .table-cmm .email div {
  width: 207px;
}
#popUseCardHistory .btn-select {
  width: 60px;
  line-height: 32px;
  border-radius: 5px;
  background-color: #4e81ff;
  text-align: center;
  color: #fff;
}
#popUseCardHistory .table-cmm.type-fare {
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popUseCardHistory .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
}
#popUseCardHistory .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}

/* 상단 재검색 */
.research-top .inner .arrival-before {
  content: "";
  position: absolute;
  top: 53px;
  left: 243px;
  width: 17px;
  height: 12px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}
.research-top .inner .arrival::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0;
  width: 0px;
  height: 0px;
}

.research-top .inner[data-ticket="oneway"] .arrival {
  width: 179px;
}
.research-top .inner[data-ticket="oneway"] .arrival-before {
  content: "";
  position: absolute;
  top: 53px;
  left: 243px;
  width: 17px;
  height: 12px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}
.research-top .inner[data-ticket="round"] .arrival {
  width: 152px;
}
.research-top .inner[data-ticket="round"] .arrival-before {
  content: "";
  position: absolute;
  top: 53px;
  left: 243px;
  width: 17px;
  height: 12px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}
.research-top .inner[data-ticket="multi"] .arrival {
  width: 190px;
}
.research-top .inner[data-ticket="multi"] .arrival-before {
  content: "";
  position: absolute;
  top: 53px;
  left: 373px;
  width: 17px;
  height: 12px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 0;
}

.research-top .plan-list .arrival-before {
  content: "";
  position: absolute;
  top: 0%;
  left: 283px;
  width: 17px;
  height: 97%;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 center;
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
}
.research-top .plan-list .arrival::before {
  content: "";
  position: absolute;
  top: 0px;
  left: 0;
  width: 0px;
  height: 0px;
}
.research-top .plan-list .arrival {
  padding-left: 15px;
}

.search-trip-home .city .arrival {
  float: left;
  padding-left: 42px;
  background: url(../../assets/images/main/ico_round_trip.png) no-repeat 0 50%;
}
.search-trip-home .city .arrival-before {
  float: left;
  margin-top: 24px;
  width: 22px;
  height: 16px;
  background: url(../../assets/images/main/ico_round_trip.png) no-repeat 0 50%;
}
.bottom-area .city .arrival-before {
  float: left;
  margin-top: 24px;
  width: 22px;
  height: 16px;
  background: url(../../assets/images/main/ico_round_trip.png) no-repeat 0 50%;
}

.dot-flashing {
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  background-color: #4e81ff;
  color: #4e81ff;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;
}
.dot-flashing::before,
.dot-flashing::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}
.dot-flashing::before {
  left: -32px;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  background-color: #4e81ff;
  color: #4e81ff;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 0s;
}
.dot-flashing::after {
  left: 32px;
  width: 16px;
  height: 16px;
  border-radius: 100%;
  background-color: #4e81ff;
  color: #4e81ff;
  animation: dot-flashing 1s infinite alternate;
  animation-delay: 1s;
}

@keyframes dot-flashing {
  0% {
    background-color: #4e81ff;
  }
  50%,
  100% {
    background-color: #e9eaed;
  }
}
