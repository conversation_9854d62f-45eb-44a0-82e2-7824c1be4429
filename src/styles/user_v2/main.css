﻿@charset "utf-8";
.pg-main {
  position: relative;
}

/*.daterangepicker{ top: 50% !important; }*/

.search-trip-home {
  padding-top: 71px;
  margin-bottom: 52px;
}
.search-trip-home .copy {
  width: 184px;
  margin-bottom: 30px;
  border-radius: 22px 22px 22px 0;
  text-align: center;
  line-height: 44px;
  background-color: rgba(59, 127, 243, 0.14);
  font-size: 17px;
  font-weight: 700;
  color: #4e81ff;
}
.search-trip-home .box-line {
  float: left;
  padding: 0 20px;
  margin-top: 10px;
  margin-left: 7px;
  border: 1px solid #ebeef3;
  line-height: 64px;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  background-color: #fff;
}

.search-trip-home.type-domestic .top-area .btn-default[data-ticket-type="multi"],
.search-trip-home.type-domestic .top-area .form-chkbox,
.search-trip-home.type-domestic .layer-class-member ul {
  display: none;
}

.search-trip-home .tab-area {
  padding: 0 0 10px;
  margin-bottom: 35px;
  border-bottom: 3px solid #e2e4e8;
}
.search-trip-home .tab-area::after {
  content: "";
  clear: both;
  display: block;
}
.search-trip-home .tab-area .btn-default {
  position: relative;
  float: left;
  width: 50%;
  font-size: 26px;
  line-height: 38px;
  font-weight: 700;
  letter-spacing: -1px;
  color: #e2e4e8;
}
.search-trip-home .tab-area .btn-default.active {
  color: #4e81ff;
}
.search-trip-home .tab-area .btn-default.active span {
  background: url(../../assets/images/main/ico_search_check.png) no-repeat 100% 50%;
}
.search-trip-home .tab-area .btn-default.active::after {
  content: "";
  position: absolute;
  bottom: -13px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #4e81ff;
}
.search-trip-home .tab-area .btn-default span {
  display: inline-block;
  padding: 0 30px 0 10px;
}

.search-trip-home .top-area {
  position: relative;
  margin-bottom: 7px;
  overflow: hidden;
}
.search-trip-home .top-area .btn-default {
  float: left;
  width: 87px;
  margin-left: 4px;
  border: 1px solid #e1e4ff;
  border-radius: 18px;
  line-height: 30px;
  text-align: center;
  color: #4e81ff;
  background-color: #fff;
}
.search-trip-home .top-area .btn-default:first-child {
  margin-left: 0;
}
.search-trip-home .top-area .btn-default.active {
  background-color: #4e81ff;
  color: #fff;
}
.search-trip-home .top-area .form-chkbox span::after {
  top: 7px;
}
.search-trip-home .top-area .etc {
  float: left;
  line-height: 32px;
  margin-left: 24px;
}
.search-trip-home .top-area .more {
  display: none;
  font-size: 13px;
  color: #7f848d;
}
.search-trip-home .top-area .more a {
  margin-left: 10px;
  text-decoration: underline;
  font-size: 13px;
  color: #3b7ff3;
}
.search-trip-home .top-area .active {
  background-color: #4e81ff;
  color: #fff;
}

.search-trip-home .mid-area {
  position: relative;
}
.search-trip-home .mid-area::after {
  content: "";
  display: block;
  clear: both;
}

.search-trip-home .bottom-area {
  display: none;
  position: relative;
  margin-top: 10px;
}
.search-trip-home .bottom-area .sec-item::after,
.search-trip-home .bottom-area .multi-btn-wrap::after {
  content: "";
  display: block;
  clear: both;
}
.search-trip-home .bottom-area .sec-item:first-child .box-line {
  margin-top: 0;
}

.search-trip-home .city {
  margin-left: 0;
}
.search-trip-home .city .form-radio {
  display: none;
  float: left;
}
.search-trip-home .city .form-radio span::before {
  top: 50%;
  margin-top: -8px;
}
.search-trip-home .city .form-radio span::after {
  top: 50%;
  margin-top: -3px;
}
.search-trip-home .city .depature {
  float: left;
}
.search-trip-home .city .arrival {
  float: left;
  padding-left: 42px !important;
  background: url(../../assets/images/main/ico_round_trip.png) no-repeat 0 50%;
}

.search-trip-home .city .arrival-before + .arrival {
  background: none;
  padding-left: 20px !important;
}
.search-trip-home .city .default {
  color: #c9cfd8;
}
.search-trip-home .city .val {
  display: none;
}
.search-trip-home .city a {
  display: inline-block;
  line-height: 64px;
  font-size: 16px;
  font-weight: 300;
}
.search-trip-home .city strong {
  font-weight: 700;
}
.search-trip-home .city .in .default {
  display: none;
}
.search-trip-home .city .in .val {
  display: block;
}
.search-trip-home .city .in .val * {
  display: inline-block;
  vertical-align: top;
}
.search-trip-home .city .in .val strong {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 120px;
}

.search-trip-home .period {
  position: relative;
}
.search-trip-home .period::after {
  content: "";
  clear: both;
  display: block;
}
.search-trip-home .period a {
  float: left;
  width: 128px;
  font-size: 16px;
  line-height: 64px;
}
.search-trip-home .period .start {
  display: none;
  position: relative;
  padding-right: 29px;
}
.search-trip-home .period .start::after {
  content: "";
  position: absolute;
  top: 50%;
  right: 0;
  height: 32px;
  margin-top: -16px;
  border-left: 1px solid #e2e4e8;
}
.search-trip-home .period .end {
  overflow: hidden;
  padding-left: 19px;
}
.search-trip-home .period input[data-date-select] {
  visibility: hidden;
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 0 none;
}
.search-trip-home .period .default {
  color: #c9cfd8;
}
.search-trip-home .period .val {
  display: none;
}
.search-trip-home .passenger {
  position: relative;
  width: 233px;
}
.search-trip-home .passenger > .tit {
  background: url(../../assets/images/main/btn_dropdown_m.png) no-repeat 99% 50%;
}
.search-trip-home .passenger .btn-class-member {
  display: block;
  padding-right: 15px;
  line-height: 64px;
}
.search-trip-home .passenger .layer-class-member {
  width: 273px;
}
.search-trip-home .search {
  float: right;
}
.search-trip-home .search .btn-default {
  width: 86px;
  line-height: 86px;
  border-radius: 100%;
  font-size: 18px;
  font-weight: 500;
  background-color: #4e81ff;
  color: #fff;
  text-align: center;
}
.search-trip-home .search .btn-default:hover {
  background-color: #2e5ffb;
}

.search-trip-home .type-round .city {
  padding-right: 5px;
}
.search-trip-home .type-round .city .depature {
  width: 141px;
}
.search-trip-home .type-round .city .depature .val strong {
  max-width: 90px;
}
.search-trip-home .type-round .city .arrival {
  width: 120px;
}
.search-trip-home .type-round .city .arrival .val strong {
  max-width: 80px;
}
.search-trip-home .type-round .period .start {
  display: block;
}
.search-trip-home .type-oneway .city .depature {
  width: 205px;
}
.search-trip-home .type-oneway .city .arrival {
  width: 185px;
}
.search-trip-home .type-oneway .period {
  width: 178px;
}
.search-trip-home .type-oneway .period .start {
  display: block;
  width: 100%;
  padding-right: 0;
}
.search-trip-home .type-oneway .period .start::after {
  display: none;
}
.search-trip-home .type-oneway .period .end {
  display: none;
}
.search-trip-home .type-multi .multi-none {
  display: none;
}
.search-trip-home .type-multi .form-chkbox {
  display: none;
}
.search-trip-home .type-multi .city {
  padding-right: 10px;
}
.search-trip-home .type-multi .top-area .more {
  display: inline-block;
}
.search-trip-home .type-multi .period .start {
  display: block;
  width: 100%;
  padding-right: 0;
}
.search-trip-home .type-multi .period .start::after {
  display: none;
}
.search-trip-home .type-multi .period .end {
  display: none;
}
.search-trip-home .type-multi .city .form-radio {
  display: block;
  width: 75px;
  margin-right: 23px;
}
.search-trip-home .type-multi .city .depature {
  width: 160px;
}
.search-trip-home .type-multi .city .arrival {
  width: 138px;
}
.search-trip-home .type-multi .city .arrival .val strong {
  max-width: 85px;
}
.search-trip-home .type-multi .period {
  width: 178px;
}
.search-trip-home .type-multi .period .end {
  padding-left: 0;
}
.search-trip-home .type-multi .bottom-area {
  display: block;
}
.search-trip-home .type-multi .sec-item:first-child .btn-multi-ctrl {
  margin: 23px 11px 23px;
}
.search-trip-home .type-multi .btn-multi-ctrl {
  float: left;
  margin: 33px 11px 23px;
}
.search-trip-home .type-multi .btn-multi-ctrl.remove .btn-default {
  display: block;
}
.search-trip-home .type-multi .btn-multi-ctrl.remove .btn-default {
  width: 20px;
  height: 20px;
  background: url(../../assets/images/main/btn_multi_ctrl.png) 0 0 no-repeat;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.search-trip-home .type-multi .btn-multi-ctrl.add {
  margin: 10px 0 0;
}
.search-trip-home .type-multi .btn-multi-ctrl.add .btn-default {
  width: 698px;
  height: 66px;
  line-height: 66px;
  text-align: center;
  background-color: #ffffff;
  border: 1px dashed #557ffe;
  border-radius: 8px;
  /* background-image: linear-gradient(to right, #557FFE 33%, rgba(255,255,255,0) 0%), linear-gradient(to left, #557FFE 33%, rgba(255,255,255,0) 0%), linear-gradient(to bottom, #557FFE 33%, rgba(255,255,255,0) 0%), linear-gradient(to top, #557FFE 33%, rgba(255,255,255,0) 0%);
    background-position: bottom, top, left, right;
    background-size: 10px 1px, 10px 1px, 1px 10px, 1px 10px;
    background-repeat: repeat-x, repeat-x, repeat-y, repeat-y; */
}
.search-trip-home .type-multi .btn-multi-ctrl.add .btn-default .btn-plus {
  padding-left: 23px;
  background: url(../../assets/images/main/btn_multi_ctrl.png) no-repeat left center / 14px 14px;
  font-size: 16px;
  font-weight: 500;
  color: #557ffe;
}

.coming-trip-home {
  margin: 0 auto;
  padding-bottom: 122px;
  max-width: 1374px;
  background: url(../../assets/images/main/bg_coming.png) 50% 100% no-repeat;
}
.coming-trip-home h2.tit {
  margin-bottom: 11px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
}
.coming-trip-home .none-item {
  display: none;
  width: 979px;
  height: 196px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.coming-trip-home .none-item .desc {
  float: left;
  width: 569px;
  text-align: center;
}
.coming-trip-home .none-item .desc dt {
  padding-top: 70px;
  margin-bottom: 3px;
  font-size: 22px;
  line-height: 33px;
  font-weight: 500;
  color: #c9cfd8;
}
.coming-trip-home .none-item .desc dd {
  font-size: 15px;
  color: #c9cfd8;
  line-height: 22px;
}
.coming-trip-home .none-item .box-btns {
  float: right;
  padding-top: 48px;
  margin-right: 27px;
}
.coming-trip-home .none-item .box-btns::after {
  content: "";
  clear: both;
  display: block;
}
.coming-trip-home .none-item .box-btns li {
  float: left;
}
.coming-trip-home .none-item .box-btns a {
  display: block;
  overflow: hidden;
  width: 127px;
  height: 92px;
  padding-top: 8px;
  border-left: 1px solid #e2e4e8;
  text-align: center;
}
.coming-trip-home .none-item .box-btns a:first-child {
  border-left: 0 none;
}
.coming-trip-home .none-item .box-btns a::before {
  content: "";
  display: block;
  width: 71px;
  height: 55px;
  margin: 0 auto 17px;
  background-repeat: no-repeat;
  background-image: url(../../assets/images/main/spr_btn_trip_type.png);
}
.coming-trip-home .none-item .box-btns .air {
  color: #84ced1;
}
.coming-trip-home .none-item .box-btns .air::before {
  background-position: 0 0;
}
.coming-trip-home .none-item .box-btns .hotel {
  color: #8487d1;
}
.coming-trip-home .none-item .box-btns .hotel::before {
  background-position: 0 -55px;
}
.coming-trip-home .none-item .box-btns .car {
  color: #f59c79;
}
/*.coming-trip-home .none-item .box-btns .car:hover{ color: #f59c79; }*/
.coming-trip-home .none-item .box-btns .car::before {
  background-position: 0 -110px;
}
.coming-trip-home .have-item {
  position: relative;
}
.coming-trip-home .have-item::after {
  content: "";
  clear: both;
  display: block;
}
.coming-trip-home .have-item .box-trip {
  float: left;
  position: relative;
  width: 559px;
  height: 170px;
  padding: 14px 0 14px 265px;
  margin: 0 auto;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border-radius: 5px;
  border: 1px solid #e2e4e8;
}
.coming-trip-home .have-item .thum {
  position: absolute;
  top: 14px;
  left: 14px;
}
.coming-trip-home .have-item .thum img {
  border-radius: 5px;
}
.coming-trip-home .have-item .date {
  padding-top: 9px;
  margin-bottom: 8px;
  line-height: 24px;
  color: #7f848d;
}
.coming-trip-home .have-item .date .d-day {
  display: inline-block;
  width: 62px;
  height: 24px;
  line-height: 22px;
  border-radius: 17px;
  margin-right: 10px;
  text-align: center;
  color: #4e81ff;
  background-color: #ecf2fe;
}
.coming-trip-home .have-item .name {
  width: 500px;
  margin-bottom: 65px;
  overflow: hidden;
  -ms-text-overflow: ellipsis;
  text-overflow: ellipsis;
  font-size: 22px;
  line-height: 33px;
  font-weight: 500;
  color: #333;
}
.coming-trip-home .have-item .type {
  overflow: hidden;
  padding-left: 7px;
}
.coming-trip-home .have-item .type li {
  float: left;
  font-weight: 500;
  line-height: 16px;
}
.coming-trip-home .have-item .type li span {
  display: inline-block;
  width: 38px;
}
.coming-trip-home .have-item .type li:before {
  content: "";
  display: inline-block;
  margin-right: 10px;
  vertical-align: top;
  height: 16px;
  background-image: url(../../assets/images/main/spr_ico_trip_type.png);
  background-repeat: no-repeat;
}
.coming-trip-home .have-item .type .air:before {
  width: 15px;
  background-position: 0 50%;
}
.coming-trip-home .have-item .type .hotel:before {
  width: 19px;
  background-position: -15px 50%;
}
.coming-trip-home .have-item .type .car:before {
  width: 16px;
  background-position: -34px 50%;
}
.coming-trip-home .have-item .btn-more {
  position: absolute;
  top: 25px;
  right: 30px;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  background: url(../../assets/images/main/ico_btn_more.png) 0 50% no-repeat;
}
.coming-trip-home .have-item .btn-all {
  position: absolute;
  top: 50%;
  right: 0;
  width: 187px;
  padding: 0 4px 24px 0;
  line-height: 20px;
  margin: -22px 0 0 0;
  text-align: right;
  background: url(../../assets/images/main/ico_arrow_all.png) 0 100% no-repeat;
  color: #4e81ff;
}

.mayjor-trip-home {
  background-color: #282d5c;
  padding: 50px 0 70px;
  margin-bottom: 70px;
}
.mayjor-trip-home .tit {
  margin-bottom: 20px;
  font-size: 24px;
  letter-spacing: -1px;
  line-height: 36px;
  font-weight: 500;
  color: #fff;
}
.mayjor-trip-home .box-swiper {
  position: relative;
  height: 270px;
}
.mayjor-trip-home .swiper-slide {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
}
.mayjor-trip-home .swiper-slide .name {
  position: absolute;
  top: 19px;
  left: 20px;
}
.mayjor-trip-home .swiper-slide .name span {
  display: block;
  color: #fff;
}
.mayjor-trip-home .swiper-slide .name .kr {
  margin-bottom: 2px;
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
}
.mayjor-trip-home .swiper-slide .name .en {
  font-size: 12px;
  font-weight: 500;
  font-family: Roboto;
}
.mayjor-trip-home .swiper-container {
  border-radius: 12px;
}
.mayjor-trip-home .swiper-button {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  overflow: hidden;
}
.mayjor-trip-home .swiper-button a {
  display: block;
  width: 14px;
  height: 39px;
  background: url(../../assets/images/cmm/btn_swiper.png) 0 0 no-repeat;
  text-indent: -999em;
  color: transparent;
}
.mayjor-trip-home .swiper-button.prev {
  left: -45px;
}
.mayjor-trip-home .swiper-button.next {
  right: -45px;
}
.mayjor-trip-home .swiper-button.next a {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.recent-trip-home {
  position: relative;
  margin-bottom: 74px;
}
.recent-trip-home h2.tit {
  margin-bottom: 26px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
}
.recent-trip-home .box-col {
  position: relative;
}
.recent-trip-home .box-col::after {
  content: "";
  clear: both;
  display: block;
}
.recent-trip-home .box-col .inner {
  position: relative;
  top: -6px;
  right: 6px;
  width: 504px;
  height: 147px;
  padding-left: 22px;
  border: 1px solid #e2e4e8;
  background-color: #fff;
  border-radius: 5px;
}
.recent-trip-home .left-col {
  float: left;
  position: relative;
  border-radius: 5px;
  background-color: rgba(230, 234, 244, 0.5);
}
.recent-trip-home .right-col {
  float: right;
  border-radius: 5px;
  background-color: rgba(230, 234, 244, 0.5);
}
.recent-trip-home .type {
  padding-top: 20px;
  margin-bottom: 10px;
}
.recent-trip-home .type span {
  display: inline-block;
  width: 54px;
  border-radius: 16px;
  font-size: 12px;
  line-height: 26px;
  font-weight: 500;
  text-align: center;
  color: #fff;
}
.recent-trip-home .city {
  overflow: hidden;
  margin-bottom: 9px;
}
.recent-trip-home .city p {
  float: left;
  font-size: 16px;
  line-height: 24px;
  font-weight: 700;
}
.recent-trip-home .city p:first-child {
  padding-right: 33px;
  margin-right: 10px;
  background: url(../../assets/images/main/ico_city_section.png) no-repeat 100% 60%;
}
.recent-trip-home .period {
  margin-bottom: 2px;
  font-size: 13px;
  line-height: 19px;
}
.recent-trip-home .info {
  font-size: 13px;
}
.recent-trip-home .btn-delete {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/main/btn_delete_saerch.png) 50% 50% no-repeat;
}
.recent-trip-home .round .type span {
  background-color: #f59c79;
}
.recent-trip-home .round .period {
  color: #f59c79;
}
.recent-trip-home .oneway .type span {
  background-color: #8487d1;
}
.recent-trip-home .oneway .period {
  color: #8487d1;
}
.recent-trip-home .multi .type span {
  background-color: #84ced1;
}
.recent-trip-home .multi .period {
  color: #84ced1;
}
.recent-trip-home .none {
  border-radius: 5px;
  background-color: rgba(230, 234, 244, 0.5);
}
.recent-trip-home .none .inner {
  position: relative;
  top: -6px;
  right: 6px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  background-color: #fff;
  font-size: 16px;
  font-weight: 500;
  color: #babdc3;
  line-height: 149px;
  text-align: center;
}
.recent-trip-home.type-hotel {
}
.recent-trip-home.type-hotel .city p:first-child {
  padding-top: 20px;
  padding-right: 0;
  background: none;
  color: #4e81ff;
}
.recent-trip-home.type-hotel .box-col .inner {
  height: 115px;
}
.recent-trip-home.type-hotel .round .period {
  color: #282d5c;
}

.board-home {
  position: relative;
  overflow: hidden;
  background-color: #f2f4f9;
  margin-bottom: 70px;
}
.board-home .box-col {
  position: relative;
  float: left;
  width: 520px;
  padding: 70px 0;
}
.board-home .box-col:first-child {
  margin-right: 40px;
}
.board-home .tit {
  padding-bottom: 14px;
  border-bottom: 2px solid #34446e;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
  color: #34446e;
}
.board-home li {
  overflow: hidden;
  padding: 14px 0 15px;
  border-bottom: 1px solid #d8d8d8;
  line-height: 20px;
}
.board-home li a {
  float: left;
  max-width: 420px;
  color: #111;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.board-home li a:hover {
  text-decoration: underline;
}
.board-home li .date {
  float: right;
  font-size: 13px;
  color: #666;
}
.board-home .btn-more {
  position: absolute;
  top: 75px;
  right: 0;
  padding-right: 15px;
  font-size: 12px;
  line-height: 18px;
  color: #34446e;
  background: url(../../assets/images/main/btn_board_arrow_more.png) 100% 50% no-repeat;
}

.service-cs-home {
  position: relative;
  margin-bottom: 80px;
}
.service-cs-home h2.tit {
  margin-bottom: 54px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
  color: #34446e;
}
.service-cs-home .box-col {
  float: left;
  width: 25%;
  text-align: center;
}
.service-cs-home .box-col .ico {
  margin-bottom: 26px;
}
.service-cs-home .box-col .ico i {
  display: inline-block;
  width: 84px;
  height: 81px;
  background-image: url(../../assets/images/main/ico_cs.png);
  background-repeat: no-repeat;
}

.service-cs-home .box-col.visa .ico i {
  background-image: url(../../assets/images/main/icn-service-visa.png);
}

.service-cs-home .box-col.air .ico i {
  background-position: 0 0;
}
.service-cs-home .box-col.hotel .ico i {
  background-position: 0 -81px;
}
.service-cs-home .box-col.mice .ico i {
  background-position: 0 -162px;
}
.service-cs-home .box-col.partner .ico i {
  background-position: 0 -243px;
}
.service-cs-home .name {
  margin-bottom: 5px;
  font-size: 15px;
  line-height: 22px;
}
.service-cs-home .num {
  font-size: 15px;
  font-weight: 700;
}

/* 호텔 */
.pg-main.type-hotel .box-line.city .depature {
  width: 318px;
}
.pg-main.type-hotel .search-trip-home {
  max-width: 1374px;
  width: 100%;
  margin-bottom: 0;
  padding-bottom: 170px;
  background: url(../../assets/images/main/bg_coming.png) 50% 100% no-repeat;
}
.pg-main.type-hotel .search-trip-home .copy {
  position: relative;
  left: 50%;
  margin-left: -540px;
}
.pg-main.type-hotel .search-trip-home .period .start {
  width: 274px;
  position: relative;
  z-index: 1;
}
.pg-main.type-hotel .search-trip-home .period .start::after {
  display: none;
  height: 1px;
  width: 10px;
  border-color: #c9cfd8;
  margin-top: 0;
  right: initial;
  left: 94px;
  border-left: 0 none;
  border-bottom: 1px solid #c9cfd8;
}
.pg-main.type-hotel .search-trip-home .period .start.in::after {
  display: block;
}
.pg-main.type-hotel .search-trip-home .period .end {
  display: none;
  position: absolute;
  top: 0;
  left: 136px;
  padding-left: 0;
}
.pg-main.type-hotel .search-trip-home .period .end.in {
  display: block;
}
.pg-main.type-hotel .search-trip-home #ticketSearch {
  width: 1080px;
  margin: 0 auto;
}

/* DT-11325 notice 호텔메인추가 - 20230531*/
.main-notice {
  padding-top: 8px;
}
.main-notice::before {
  content: "알림";
  display: inline-block;
  width: 33px;
  margin-right: 4px;
  line-height: 18px;
  text-align: center;
  border-radius: 5px;
  border: 1px solid #ff9268;
  color: #ff9268;
  font-size: 10px;
  font-weight: 500;
  border-radius: 5px;
  vertical-align: middle;
}
.main-notice a {
  padding-right: 12px;
  background: url(../../assets/images/svg/btn_dropdown_r.svg) no-repeat top 5px right/6px 10px;
  font-weight: 400;
  font-size: 12px;
  line-height: 1;
  vertical-align: middle;
}
.main-notice a span.c-blue {
  font-weight: 700;
  color: #557ffe !important;
}

.tooltip-custom {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  height: 15px;
  width: 15px;
  margin-left: 3px;
}

.tooltip-custom-icon {
  position: absolute;
  top: 2px;
  height: 100%;
  width: 100%;
  display: inline-block;
  background-image: url(../../assets/images/main/question-icon.svg);
  background-position: center;
  background-size: cover;
}

.tooltip-custom .tooltip-custom-text {
  visibility: hidden;
  width: 400px;
  background-color: white;
  color: #000000;
  font-weight: 300;
  text-align: left;
  padding: 10px;
  border-radius: 8px;
  border: 0;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 130%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 10;
}

.tooltip-custom .tooltip-custom-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px;
  border-style: solid;
  border-color: white transparent transparent transparent;
}

.tooltip-custom:hover .tooltip-custom-text {
  visibility: visible;
  opacity: 1;
}

.autocomplete-custom .MuiInputBase-root::after {
  content: none !important;
}

.autocomplete-custom.outlined .MuiInputBase-root {
  padding: 0 10px !important;
}

.autocomplete-custom .MuiInputBase-root::before {
  border-bottom: 1px solid #e2e4e8 !important;
}
.autocomplete-custom input {
  border: none !important;
  padding: 0 !important;
}
.autocomplete-custom.base-input input {
  padding: 4px 4px 4px 0px !important;
}

.stay-autocomplete-paper {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.87);
  -webkit-transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  border-radius: 4px;
  box-shadow:
    0px 2px 1px -1px rgba(0, 0, 0, 0.2),
    0px 1px 1px 0px rgba(0, 0, 0, 0.14),
    0px 1px 3px 0px rgba(0, 0, 0, 0.12);
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  overflow: auto;
}
.stay-autocomplete-listbox {
  list-style: none;
  margin: 0;
  padding: 8px 0;
  max-height: 40vh;
  overflow: auto;
  position: relative;
}
.stay-autocomplete-option {
  padding: 5px 10px;
  cursor: pointer;
}
.stay-autocomplete-option:hover {
  background-color: #0000000a;
}
.clear-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
}
