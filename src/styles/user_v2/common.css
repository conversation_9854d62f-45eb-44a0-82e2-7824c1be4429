﻿@charset "utf-8";
.clearfix {
  position: relative;
}
.clearfix::after {
  content: "";
  display: block;
  clear: both;
}
.contents {
  position: relative;
  min-height: 500px;
}
#wrap {
  min-width: 1080px;
}
.sec-pay-to {
  z-index: 1;
  background-color: #fff;
}
/*=========================================*/
/* ################ Layout ############### */
/*=========================================*/

/* header */
#header {
  position: relative;
  border-bottom: 1px solid #e2e4e8;
  z-index: 10;
  background-color: #fff;
}
#header .left-col {
  float: left;
}
#header .right-col {
  float: right;
}
#header h1 {
  float: left;
  padding: 17px 0;
  margin-left: 50px;
}
#header h1 img {
  width: 220px;
  height: 40px;
}
#header #gnb {
  float: left;
  margin-left: 20px;
}
#header #gnb .depth01 > li {
  position: relative;
  float: left;
}
#header #gnb .depth01 > li > a {
  display: block;
  padding: 26px 20px;
  font-size: 15px;
  line-height: 22px;
}
#header #gnb .depth01 > li.active {
  font-weight: 700;
}
#header #gnb .depth01 > li.active::after {
  content: "";
  position: absolute;
  top: 71px;
  left: 20px;
  right: 20px;
  height: 4px;
  background-color: #000;
}
#header #gnb .depth02 {
  position: absolute;
  top: 74px;
  left: 0;
  width: 130px;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
  background-color: #ffffff;
}
#header #gnb .depth02 a {
  display: block;
  padding-left: 20px;
  font-size: 14px;
  line-height: 36px;
  color: #000;
}
#header #gnb .depth02 a:hover {
  background-color: rgba(78, 129, 255, 0.07);
  color: #4e81ff;
}
#header #gnb .more .depth02 {
  display: none;
}
#header #gnb .more > a {
  position: relative;
  padding-right: 20px;
}
#header #gnb .more i {
  position: absolute;
  top: 50%;
  right: 0;
  width: 11px;
  height: 7px;
  margin-top: -3px;
  background: url(../../assets/images/cmm/ico_arrow_down.png) no-repeat 0 0;
}
#header #gnb .more:hover .depth02 {
  display: block;
}
#header #gnb .more:hover::after {
  display: none;
}
#header .box-mypage {
  position: relative;
  float: left;
  margin-right: 40px;
}
#header .box-mypage a {
  display: block;
  font-size: 14px;
  color: #000;
  line-height: 20px;
  padding-top: 26px;
}
#header .box-mypage .layer {
  display: none;
  position: absolute;
  top: 74px;
  left: 50%;
  width: 262px;
  padding: 19px 0 12px;
  margin-left: -131px;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
  background-color: #fff;
}
#header .box-mypage .layer * {
  padding: 0 20px;
  font-size: 14px;
  line-height: 20px;
}
#header .box-mypage .layer .name {
  margin-bottom: 11px;
  font-size: 17px;
  font-weight: 500;
  line-height: 25px;
}
#header .box-mypage .layer .team {
  margin-bottom: 6px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ebeef3;
}
#header .box-mypage .layer .link {
  padding: 0;
}
#header .box-mypage .layer .link a {
  display: block;
  padding: 8px 20px;
}
#header .box-mypage .layer.active {
  display: block;
}
#header .right-col > .name {
  float: left;
  padding: 26px 0;
  margin-right: 40px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #000;
}

/* footer */
#footer {
  position: relative;
  padding: 20px 0 44px;
  margin: 0 auto;
  background-color: #fff;
}
#footer .inner {
  position: relative;
  border-bottom: solid 1px #d5d5d5;
}
#footer .logo {
  float: left;
  margin-right: 32px;
  padding-top: 14px;
}
#footer .lnb {
  float: left;
}
#footer .lnb li {
  float: left;
}
#footer .lnb li a {
  display: block;
  padding: 24px 16px;
  font-size: 14px;
  line-height: 20px;
}
#footer .family-site {
  float: right;
}
#footer .family-site .tit {
  float: left;
  padding-top: 24px;
  margin-right: 18px;
}
#footer .family-site .box-select {
  overflow: hidden;
  position: relative;
  float: left;
  width: 168px;
  height: 38px;
  margin-top: 14px;
  border: 1px solid #d5d5d5;
  border-radius: 4px;
}
#footer .family-site .box-select::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
}
#footer .family-site .box-select ul {
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  border-radius: 4px;
  border: solid 1px #d5d5d5;
  background-color: #fff;
}
#footer .family-site .box-select a {
  display: block;
  padding-left: 20px;
  font-size: 14px;
  line-height: 34px;
  color: #505050;
}
#footer .family-site .box-select a:hover {
  background-color: rgba(78, 129, 255, 0.07);
  color: #4e81ff;
}
#footer .family-site .ico {
  position: absolute;
  top: 15px;
  right: 17px;
  width: 12px;
  height: 6px;
  background: url(../../assets/images/cmm/ico_arrow_fstie.png) no-repeat 0 0;
}
#footer .family-site:hover .box-select {
  overflow: visible;
}
#footer .family-site:hover .box-select::after {
  display: none;
}
#footer .family-site:hover ul {
  display: block;
}

#footer .comp-name {
  border-top: 1px solid #d5d5d5;
  padding-top: 19px;
  margin-bottom: 11px;
  font-size: 13px;
  font-weight: 500;
  line-height: 19px;
  color: #7c7c7c;
}
#footer .address,
#footer .cs,
#footer .copy {
  padding-bottom: 6px;
  font-size: 13px;
  color: #7c7c7c;
  line-height: 19px;
}

#btn-top {
  text-align: right;
}

#container {
  position: relative;
}
#dimContents {
  position: absolute;
  top: 89px;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.2);
}
.contents {
  position: relative;
}
.contents.dim-contents::after {
  content: "";
  position: absolute;
  top: 0;
  left: -99999em;
  right: -999999em;
  bottom: 0;
  background-color: #fff;
}

/* col Commmon */
.col-left {
  float: left;
}
.col-right {
  float: right;
}

.col-right.border-custom {
  position: relative;
}
.col-right.border-custom::before {
  content: "";
  position: absolute;
  top: 0;
  left: -2px;
  display: block;
  width: 1px;
  height: 100%;
  background-color: #e2e4e8;
}

.col-left.border-custom {
  position: relative;
}
.col-left.border-custom::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: 1px;
  height: 100%;
  background-color: #e2e4e8;
}

/* cmm Css */
#header #gnb .more:hover i,
.select-ticket .sel .ico #footer .family-site:hover .ico {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

.bg-c01 {
  background-color: #f2f4f9;
}
.default-wd {
  width: 1080px;
  margin: 0 auto;
}

/* background color */
.bg-blue {
  background-color: #3b7ff3;
}

/* btutton-default */
.btn-default {
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  color: #000;
  outline: none;
  box-sizing: border-box;
}
.btn-default.bg-blue {
  width: 140px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
  line-height: 50px;
  border-radius: 5px;
}
.btn-datepicker {
  cursor: pointer;
  outline: none;
}
#ticketSearch .btn-datepicker.in .default {
  display: none;
}
#ticketSearch .btn-datepicker.in .val {
  display: block;
}
#ticketSearch .btn-datepicker.in .val[data-date-count]::after {
  content: attr(data-date-count);
  display: inline-block;
  margin-left: 10px;
  font-size: 16px;
  line-height: 24px;
  color: #9da9be;
}

/* 1줄 말즐일 표*/
.txt-short {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.paginate {
  position: relative;
  padding-top: 20px;
}
.paginate * {
  vertical-align: top;
}
.paginate .btns {
  display: inline-block;
  font-size: 0;
}
.paginate .btns .btn-default {
  width: 20px;
  height: 20px;
  background-position: 0 0;
  background-repeat: no-repeat;
}
.paginate .btns .step-all {
  background: url(../../assets/images/cmm/btn_navi_ll.png) 50% 7px no-repeat;
}
.paginate .btns .step-one {
  background: url(../../assets/images/cmm/btn_navi_l.png) 50% 7px no-repeat;
}
.paginate .btns.next .btn-default {
  background-position: 50% 6px;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.paginate ol {
  display: inline-block;
  margin: 0 10px;
}
.paginate ol::after {
  content: "";
  clear: both;
  display: block;
}
.paginate li {
  float: left;
}
.paginate li a {
  display: block;
  width: 20px;
  line-height: 20px;
  color: #babdc3;
  text-align: center;
}
.paginate li.active {
  font-weight: 700;
}
.paginate li.active a {
  color: #000;
}

.box-saerch-cmm {
  position: relative;
  margin-bottom: 40px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  overflow: hidden;
}
.box-saerch-cmm input {
  width: 748px;
  height: 44px;
  border: 0 none;
  padding: 0 56px 0 20px;
  outline: none;
}
.box-saerch-cmm .btn-search {
  position: absolute;
  top: 0;
  right: 5px;
  width: 44px;
  height: 44px;
  background: url(../../assets/images/cmm/btn_search.png) 50% 50% no-repeat;
  cursor: pointer;
  outline: none;
}

.plan-info .table-cmm.type-schedule {
  margin-bottom: 10px;
}
.plan-info .table-cmm.type-schedule th {
  border-bottom-color: #fff;
}
.plan-info .table-cmm.type-schedule th .box-in {
  font-size: 14px;
}
.plan-info .table-cmm.type-schedule td {
  border-bottom-color: #fff;
}
.plan-info .table-cmm.type-schedule td .box-in {
  line-height: 52px;
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
  font-size: 13px;
  letter-spacing: -1px;
}
.plan-info .table-cmm.type-schedule td strong {
  font-weight: 700;
}
.plan-info .table-cmm.type-schedule img {
  height: 15px;
  position: relative;
  top: -2px;
  margin-right: 2px;
  vertical-align: middle;
}
.plan-info .table-cmm.type-schedule td:first-child .box-in {
  border-left: 1px solid #e2e4e8;
}
.plan-info .table-cmm.type-schedule td:last-child .box-in {
  border-right: 1px solid #e2e4e8;
}
.plan-info .box-btns {
  overflow: hidden;
  margin-bottom: 43px;
}
.plan-info .box-btns .btn-default {
  float: left;
  width: 120px;
  margin-right: 8px;
  line-height: 40px;
  font-weight: 500;
  text-align: center;
  border: 1px solid #4e81ff;
  border-radius: 5px;
  color: #4e81ff;
}
.plan-info .box-btns .btn-default.change-air {
  text-indent: 5px;
  background: url(../../assets/images/cmm/ico_btn_blue.png) 12px 50% no-repeat;
}

/*=========================================*/
/* ################ form ############### */
/*=========================================*/
.form-chkbox {
  display: inline-block;
}
.form-chkbox span {
  position: relative;
  display: inline-block;
  padding-left: 26px;
  vertical-align: top;
}
.form-chkbox input[type="checkbox"] {
  visibility: hidden;
  width: 0;
  height: 0;
  opacity: 0;
  position: absolute;
  padding: 0;
  margin: 0;
}
.custom-hotel-map-search .form-chkbox input[type="checkbox"]:checked + span::after {
  background: url(../../assets/images/svg/ic-checkbox.svg) no-repeat 50% 50% !important;
}
.form-chkbox input[type="checkbox"]:checked + span::after {
  background: #4e81ff url(../../assets/images/cmm/ico_arrow_checkbox.png) no-repeat 50% 50%;
}
.custom-hotel-map-search .form-chkbox span::after {
  background: url(../../assets/images/svg/ic-uncheckbox.svg) no-repeat 50% 50% !important;
  border: none !important;
  width: 18px !important;
  height: 18px !important;
  transform: translateY(-1px);
}
.form-chkbox span::after {
  content: "";
  position: absolute;
  top: 2px;
  left: 0;
  width: 18px;
  height: 18px;
  border-radius: 2px;
  background-color: #4e81ff;
  border: 1px solid #e2e4e8;
  background-color: #fff;
}
.chk-radio .form-chkbox input[type="checkbox"]:checked + span::after {
  background: url(../../assets/images/search/check_type02_sel.png) no-repeat 50% 50%;
}
.chk-radio .form-chkbox span::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/search/check_type02_dis.png) no-repeat 50% 50%;
  border: none;
}
.form-radio {
  display: inline-block;
  position: relative;
}
.form-radio span {
  position: relative;
  display: inline-block;
  padding-left: 26px;
  vertical-align: top;
}
.form-radio input[type="radio"] {
  visibility: hidden;
  width: 0;
  height: 0;
  opacity: 0;
}
.form-radio span::before {
  content: "";
  position: absolute;
  top: 3px;
  left: 0;
  width: 14px;
  height: 14px;
  border-radius: 16px;
  background-color: #fff;
  border: 1px solid #e2e4e8;
}
.form-radio span::after {
  content: "";
  position: absolute;
  top: 8px;
  left: 5px;
  width: 6px;
  height: 6px;
  border-radius: 6px;
  background-color: #fff;
}
.form-radio input[type="radio"]:checked + span::before {
  background-color: #4e81ff;
  border-color: #4e81ff;
  box-sizing: content-box;
}
.form-select {
  position: relative;
  display: inline-block;
}
.form-select.w100 {
  width: 100%;
}
.form-select.w100 select {
  box-sizing: border-box;
  width: 100%;
  display: block;
}
.form-select select {
  text-align: left;
  height: 22px;
  font-size: 13px;
  border: 0 none;
  padding: 0 20px 0 0;
  box-sizing: content-box;
  background: url(../../assets/images/cmm/ico_arrow_select.png) no-repeat 96% 50%;
  outline: none;
}
.select-ticket {
  position: relative;
  z-index: 10;
}
.form-select.has-scroll {
  background-color: #fff;
  line-height: 34px;
  border-radius: 5px;
}
.form-select.has-scroll .ui-selectmenu-button.ui-button {
  padding: 0;
  line-height: 36px;
  outline: none;
}
.form-select.has-scroll .ui-icon-triangle-1-s {
  top: 13px;
  right: 13px;
  background: url(../../assets/images/cmm/ico_arrow_select.png) no-repeat 0% 0%;
}
.form-select select option {
  text-align: left;
}
input.text-focus {
  height: 34px;
  border: 0 none;
  border-bottom: 1px solid #e2e4e8;
  outline: none;
}
input.text-focus:focus {
  border-color: #4e81ff;
}
.box-col input[type="text"],
.box-col input[type="password"] {
  width: 100%;
  height: 34px;
  border: 0 none;
  border-bottom: 1px solid #e2e4e8;
  outline: none;
}
.box-col input[type="text"]:focus {
  border-color: #4e81ff !important;
}
.box-col input[type="password"]:focus {
  border-color: #4e81ff !important;
}
.box-col .desc.none-bor {
  padding-bottom: 0 !important;
}
.box-col .box-ymd {
  padding-bottom: 0 !important;
}
.box-col .box-ymd .form-select {
  border-bottom: 0 none;
}
.box-col .form-select {
  border-bottom: 1px solid #e2e4e8;
}
.box-col .form-select select {
  height: 34px;
}
.box-col .form-radio {
  line-height: 28px;
}
.box-col .form-radio span::before {
  top: 50%;
  margin-top: -8px;
}
.box-col .form-radio span::after {
  top: 50%;
  margin-top: -3px;
}

.none-bor {
  border: 0 none !important;
}

.select-ticket .list {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
}
.select-ticket.active .list {
  display: block;
  background-color: #fff;
}
.in-slide .ui-widget.ui-widget-content {
  border: 0 none;
  height: 3px;
  background-color: #c9cfd8;
  border-radius: 3px;
}
.in-slide .ui-slider-horizontal .ui-slider-range {
  background-color: #4e81ff;
}
.in-slide .ui-slider .ui-slider-handle {
  top: -5px;
  width: 14px;
  height: 14px;
  border-radius: 100%;
  border: 0 none;
  background-color: #4e81ff;
}

.box-col.box-ymd .form-select.yy {
  width: 54px;
}
.box-col.box-ymd .form-select.mm {
  width: 51px;
}
.box-col.box-ymd .form-select.dd {
  width: 51px;
}

/*=========================================*/
/* ################ datepicer ############### */
/*=========================================*/
#dateFrom {
  visibility: hidden;
  position: absolute;
  opacity: 0;
  left: 0;
  bottom: 0;
}
div.daterangepicker {
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
}
div.daterangepicker.opensright:before,
div.daterangepicker.opensright:after {
  display: none;
}
div.daterangepicker .calendar-table th.month {
  font-size: 16px;
  font-weight: normal;
}
div.daterangepicker .calendar-table th {
  width: 34px;
  height: 34px;
  font-size: 13px;
  font-weight: normal;
}
div.daterangepicker .calendar-table td {
  width: 34px;
  height: 34px;
  font-size: 14px;
  line-height: 34px;
  border: 0 none;
}

div.daterangepicker .ranges {
  display: none;
}

div.daterangepicker .left .next {
  display: none;
}
div.daterangepicker td span {
  position: relative;
}
div.daterangepicker td.off.ends {
  opacity: 0;
  overflow: hidden;
  text-indent: -999em;
  line-height: 0;
}
div.daterangepicker td.off {
  opacity: 1;
  text-indent: 0;
  line-height: 34px;
  cursor: default;
  text-decoration: none;
}
div.daterangepicker td.in-range {
  background-color: rgba(78, 129, 255, 0.07);
}
div.daterangepicker td.start-date,
div.daterangepicker td.end-datem,
div.daterangepicker td.active,
div.daterangepicker td.active:hover {
  background-color: rgba(78, 129, 255, 0.07);
}
div.daterangepicker td.start-date,
div.daterangepicker td.end-date {
  position: relative;
}
div.daterangepicker td.start-date,
div.daterangepicker td.end-date {
  background: rgba(78, 129, 255, 0.07) url(../../assets/images/cmm/bg_date.png) no-repeat 0 0;
}
div.daterangepicker td.start-date {
  border-radius: 7px 0 0 7px;
}
div.daterangepicker td.end-date {
  border-radius: 0 7px 7px 0;
}
div.daterangepicker thead span {
  margin-top: 9px;
  width: 10px;
  height: 18px;
  background: url(../../assets/images/cmm/spr_btn_datepicker.png) no-repeat 0 0;
  -webkit-transform: rotate(0) !important;
  -ms-transform: rotate(0) !important;
  -o-transform: rotate(0) !important;
  transform: rotate(0) !important;
  border: 0 none !important;
  padding: 0 !important;
}
div.daterangepicker thead th {
  border: 0 none !important;
  padding: 0 !important;
}
div.daterangepicker thead th:hover {
  background: #fff !important;
}
div.daterangepicker thead th:hover span {
  background-position: 0 -18px !important;
}
div.daterangepicker thead .prev span {
  -webkit-transform: rotate(180deg) !important;
  -ms-transform: rotate(180deg) !important;
  -o-transform: rotate(180deg) !important;
  transform: rotate(180deg) !important;
}
div.daterangepicker .drp-calendar.right {
  display: block !important;
}
/* .daterangepicker tbody td.today:before {
  display: none;
} */

/*=========================================*/
/* ################ scroll-wrapper ############### */
/*=========================================*/
.slimScrollDiv .slimScrollBar {
  width: 4px !important;
  background-color: #b3b3b3 !important;
  opacity: 1 !important;
}
.slimScrollDiv .slimScrollRail {
  width: 4px !important;
  opacity: 1 !important;
}
.scroll-wrapper {
  overflow: hidden !important;
  padding: 0 !important;
  position: relative;
}

.scroll-wrapper > .scroll-content {
  border: none !important;
  box-sizing: content-box !important;
  height: auto;
  left: 0;
  margin: 0;
  max-height: none;
  max-width: none !important;
  overflow-y: scroll !important;
  padding: 0;
  position: relative !important;
  top: 0;
  width: auto !important;
  -ms-overflow-style: none;
}

.scroll-wrapper > .scroll-content::-webkit-scrollbar {
  height: 0;
  width: 0;
}

.scroll-element {
  display: none;
}
.scroll-element,
.scroll-element div {
  box-sizing: content-box;
}

.scroll-element.scroll-x.scroll-scrollx_visible,
.scroll-element.scroll-y.scroll-scrolly_visible {
  display: block;
}

.scroll-element .scroll-bar,
.scroll-element .scroll-arrow {
  cursor: default;
}

.scroll-textarea {
  border: 1px solid #cccccc;
  border-top-color: #999999;
}
.scroll-textarea > .scroll-content {
  overflow: hidden !important;
}
.scroll-textarea > .scroll-content > textarea {
  border: none !important;
  box-sizing: border-box;
  height: 100% !important;
  margin: 0;
  max-height: none !important;
  max-width: none !important;
  overflow: scroll !important;
  outline: none;
  padding: 2px;
  position: relative !important;
  top: 0;
  width: 100% !important;
}
.scroll-textarea > .scroll-content > textarea::-webkit-scrollbar {
  height: 0;
  width: 0;
}

/*************** SIMPLE INNER SCROLLBAR ***************/

.scrollbar-inner > .scroll-element,
.scrollbar-inner > .scroll-element div {
  border: none;
  margin: 0;
  padding: 0;
  position: absolute;
  z-index: 10;
}

.scrollbar-inner > .scroll-element div {
  display: block;
  height: 100%;
  left: 0;
  top: 0;
  width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-x {
  bottom: 2px;
  height: 8px;
  left: 0;
  width: 100%;
}

.scrollbar-inner > .scroll-element.scroll-y {
  height: 100%;
  right: 2px;
  top: 0;
  width: 3px;
}
.layer-saerch-city .scrollbar-inner > .scroll-element.scroll-y {
  width: 4px;
  right: 8px;
}
.layer-saerch-city .scrollbar-inner > .scroll-element .scroll-element_track {
  background-color: transparent;
}
.layer-saerch-city .scrollbar-inner > .scroll-element .scroll-bar {
  background-color: #c9cfd8;
}
.scrollbar-inner > .scroll-element .scroll-element_outer {
  overflow: hidden;
}

.scrollbar-inner > .scroll-element .scroll-element_outer,
.scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
}

/* .scrollbar-inner > .scroll-element .scroll-element_track,
.scrollbar-inner > .scroll-element .scroll-bar {
    -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=40)";
    filter: alpha(opacity=40);
    opacity: 0.4;
} */
.scrollbar-inner > .scroll-element .scroll-element_track {
  background-color: #e0e1e4;
}
.scrollbar-inner > .scroll-element .scroll-bar {
  background-color: #9da9be;
}
/*.scrollbar-inner > .scroll-element:hover .scroll-bar { background-color: #919191; }*/
.scrollbar-inner > .scroll-element.scroll-draggable .scroll-bar {
  background-color: #919191;
}
/* update scrollbar offset if both scrolls are visible */
.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_track {
  left: -12px;
}
.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_track {
  top: -12px;
}
.scrollbar-inner > .scroll-element.scroll-x.scroll-scrolly_visible .scroll-element_size {
  left: -12px;
}
.scrollbar-inner > .scroll-element.scroll-y.scroll-scrollx_visible .scroll-element_size {
  top: -12px;
}

.ui-selectmenu-menu .slimScrollDiv {
  margin-top: 4px;
}
.ui-selectmenu-menu .slimScrollDiv .slimScrollRail {
  background-color: transparent !important;
}
.ui-selectmenu-menu .scrollbar-inner {
  background-color: #fff;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.ui-selectmenu-menu .scrollbar-inner .ui-menu-item-wrapper {
  padding: 0 14px;
  line-height: 34px;
  color: #000;
  box-sizing: border-box;
}
.ui-selectmenu-menu .scrollbar-inner .ui-menu-item-wrapper:hover {
  background-color: rgba(78, 129, 255, 0.07);
  color: #4e81ff;
}
.ui-selectmenu-menu .scrollbar-inner .ui-menu-divider {
  text-indent: -999em;
  height: 0;
  border-top: 1px solid #e2e4e8;
}
.ui-selectmenu-menu .scrollbar-inner .ui-menu-divider:first-child {
  border-top: 0 none;
}
.ui-selectmenu-menu .scrollbar-inner.passport-nation {
  height: 268px;
}
.ui-selectmenu-menu .scrollbar-inner.customer-nationality {
  height: 268px;
}
.ui-selectmenu-menu .scrollbar-inner > .scroll-element.scroll-y {
  right: 7px;
}
.ui-selectmenu-menu .scrollbar-inner .scroll-element_track {
  background-color: #fff;
}
.ui-selectmenu-menu .scrollbar-inner > .scroll-element .scroll-bar {
  background-color: #c9cfd8;
}
.ui-selectmenu-menu .scrollbar-inner .scroll-element_outer {
  top: 12px;
  bottom: 12px;
  height: auto;
}
.ui-selectmenu-menu .slimScrollDiv .slimScrollBar {
  right: 10px !important;
  width: 3px !important;
  background-color: #c9cfd8 !important;
}

.box-tit.type-cmm {
  position: relative;
  overflow: hidden;
  padding: 18px 0 18px 21px;
  margin-bottom: 20px;
  border-radius: 5px;
  background-color: #f1f5ff;
}
.box-tit.type-cmm::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 5px;
  background-color: #4e81ff;
}
.box-tit.type-cmm dt {
  margin-bottom: 6px;
  font-size: 20px;
  font-weight: 700;
  line-height: 29px;
  color: #4e81ff;
}
.box-tit.type-cmm dd {
  font-size: 13px;
  line-height: 19px;
  color: #596a92;
}

.validate {
  padding: 6px 0 3px;
  font-size: 11px;
  line-height: 17px;
  color: #ff4e50;
}

/*=========================================*/
/* ################ table ############### */
/*=========================================*/
.tbl-cmm {
  position: relative;
  border-top: 2px solid #e2e4e8;
  border-bottom: 2px solid #e2e4e8;
}
.tbl-cmm table {
  width: 100%;
}
.tbl-cmm tbody th {
  background-color: #f2f4f9;
  padding: 12px 0 12px 15px;
  font-size: 13px;
  font-weight: 500;
  line-height: 19px;
  text-align: left;
  color: #293036;
  border-top: 1px solid #e2e4e8;
}
.tbl-cmm tbody td {
  padding: 12px 0 12px 15px;
  font-size: 13px;
  line-height: 19px;
  border-top: 1px solid #e2e4e8;
}
.tbl-cmm tbody tr:first-child th,
.tbl-cmm tbody tr:first-child td {
  border-top: 0 none;
}

.table-cmm {
  position: relative;
}
.table-cmm table {
  width: 100%;
}
.table-cmm thead th {
  font-weight: 500;
}
.table-cmm tbody th {
}
.table-cmm tbody td {
}
.table-cmm .left {
  text-align: left !important;
}
.table-cmm .center {
  text-align: center !important;
}
.table-cmm .right {
  text-align: right !important;
}
.table-cmm.type-schedule .box-in {
  display: block;
}
.table-cmm.type-schedule th {
}
.table-cmm.type-schedule th:first-child .box-in {
  border-radius: 5px 0 0 5px;
}
.table-cmm.type-schedule th:last-child .box-in {
  border-radius: 0 5px 5px 0;
}
.table-cmm.type-schedule td:first-child .box-in {
  border-radius: 5px 0 0 5px;
}
.table-cmm.type-schedule td:last-child .box-in {
  border-radius: 0 5px 5px 0;
}
.table-cmm.type-schedule thead th {
  height: 46px;
  border-bottom: 8px solid #f2f4f9;
  font-size: 15px;
  font-weight: 400;
  color: #3f4e73;
}
.table-cmm.type-schedule thead th {
  line-height: 46px;
  background: url(../../assets/images/cmm/bg_tbl_schedule_center.png) repeat-x 0 0;
  color: #fff;
}
.table-cmm.type-schedule thead th:first-child {
  background: url(../../assets/images/cmm/bg_tbl_schedule_left.png) repeat-x 0 0;
}
.table-cmm.type-schedule thead th:last-child {
  background: url(../../assets/images/cmm/bg_tbl_schedule_right.png) repeat-x 100% 0;
}
.table-cmm.type-schedule tbody td {
  border-bottom: 4px solid #f2f4f9;
  text-align: center;
  font-size: 15px;
  color: #333;
}
.table-cmm.type-schedule tbody td .box-in {
  line-height: 54px;
  background-color: #fff;
}
.table-cmm.fixed .fix-head {
  margin-right: 9px;
}
.table-cmm.fixed .fix-body {
  max-height: 296px;
  overflow-y: auto;
}
.table-cmm.fixed .fix-body.scroll-wrapper {
  padding-right: 9px !important;
}
.table-cmm.fixed thead th {
}
.table-cmm.fixed tbody th {
}
.table-cmm.fixed tbody td {
}
.table-cmm.type-fare {
  border-top: 2px solid #9da9be;
  border-bottom: 2px solid #9da9be;
}
.table-cmm.type-fare thead th {
  height: 46px;
  font-size: 15px;
  font-weight: 500;
}
.table-cmm.type-fare tbody td {
  height: 57px;
  border-top: 1px solid rgba(157, 169, 190, 0.6);
  font-size: 15px;
  color: #333;
  text-align: center;
}

/*=========================================*/
/* ################ layer ############### */
/*=========================================*/
/* 인원 및 좌석 선택 레이어*/
.layer-class-member {
  /* display: none; */
  position: absolute;
  top: 100%;
  left: 0;
  width: 298px;
  margin-top: 4px;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
  background-color: #ffffff;
  z-index: 50;
}
.layer-class-member.active {
  display: block;
  z-index: 51;
}
.layer-class-member.active * {
  line-height: 22px;
}
.layer-class-member .tit {
  padding: 20px 20px 16px;
  font-weight: 700;
  line-height: 20px;
}
.layer-class-member dl {
  overflow: hidden;
  padding-bottom: 15px;
  margin-bottom: 17px;
  border-bottom: 1px solid #ebeef3;
}
.layer-class-member dl::after {
  content: "";
  display: block;
  clear: both;
}
.layer-class-member dt {
  float: left;
  margin-left: 20px;
  font-size: 15px;
  line-height: 24px;
}
.layer-class-member .count {
  float: right;
  margin-right: 20px;
}
.layer-class-member .count .btn-default {
  position: relative;
  width: 24px;
  height: 24px;
  text-indent: 999em;
  border-radius: 12px;
  background-color: rgba(78, 129, 255, 0.07);
}
.layer-class-member .count .btn-default:hover {
  background-color: #2e5ffb;
}
.layer-class-member .count .btn-default::before,
.layer-class-member .count .btn-default::after {
  content: "";
  position: absolute;
  width: 10px;
  height: 2px;
  background-color: #779fff;
}
.layer-class-member .count .btn-default::before {
  top: 11px;
  left: 50%;
  margin-left: -5px;
}
.layer-class-member .count .btn-default::after {
  width: 2px;
  height: 10px;
  top: 50%;
  margin-top: -5px;
  left: 11px;
}
.layer-class-member .count .btn-default.minus::after {
  display: none;
}
.layer-class-member .count .btn-default:hover::before,
.layer-class-member .count .btn-default:hover::after {
  background-color: #fff;
}
.layer-class-member .count .btn-default[disabled],
.layer-class-member .count .btn-default[disabled]:hover {
  background-color: #fbfbfc;
  cursor: default;
}
.layer-class-member .count .btn-default[disabled]::before,
.layer-class-member .count .btn-default[disabled]::after {
  background-color: #d6dae1;
}
.layer-class-member .count input {
  width: 39px;
  height: 24px;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  border: 0 none;
}
.layer-class-member li {
  margin: 13px 0 0 20px;
}
.layer-class-member li:first-child {
  margin-top: 0;
}
.layer-class-member .btn-apply {
  text-align: center;
  padding: 23px 0 20px;
}
.layer-class-member .btn-apply .btn-default {
  width: 98px;
  height: 38px;
  line-height: 38px;
  font-weight: 500;
  color: #4e81ff;
  border-radius: 5px;
  border: 1px solid #4e81ff;
}
.layer-class-member .btn-apply .btn-default:hover {
  background-color: #4e81ff;
  color: #fff;
}
.layer-class-member .btn-add-room .btn-default {
  width: 100%;
  line-height: 50px;
  text-align: center;
  color: #4e81ff;
}
.layer-class-member .btn-add-room .btn-default::before {
  content: "";
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 8px;
  background: url(../../assets/images/cmm/icn-plus-b-s.png) 0 0 no-repeat;
}
.layer-class-member.type-hotel dl {
  margin-bottom: 0;
  display: none;
  border-bottom: 0 none;
}
.layer-class-member.type-hotel .btn-item-arrow {
  position: absolute;
  top: 17px;
  right: 18px;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/main/btn_dropdown_m.png) no-repeat 50% 50%;
  cursor: pointer;
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.layer-class-member.type-hotel .box-item {
  position: relative;
  border-bottom: 1px solid #ebeef3;
}
.layer-class-member.type-hotel .box-item.active dl {
  display: block;
}
.layer-class-member.type-hotel .box-item.active .btn-item-arrow {
  -webkit-transform: rotate(0);
  -ms-transform: rotate(0);
  -o-transform: rotate(0);
  transform: rotate(0);
}
.layer-class-member.type-hotel .btn-remove-item {
  display: inline-block;
  width: 36px;
  border-radius: 10px;
  line-height: 20px;
  text-align: center;
  font-size: 11px;
  font-weight: 500;
  color: #9da9be;
  background-color: #f2f4f9;
  margin-left: 10px;
  cursor: pointer;
  outline: none;
}

/*  여행지 선택 레이어 */
.layer-saerch-city {
  position: absolute;
  top: 0;
  left: 50%;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.15);
  border: solid 1px #ebeef3;
  background-color: #ffffff;
  z-index: 50;
  overflow: hidden;
}
.layer-saerch-city .insert-word {
  border-bottom: solid 1px #ebeef3;
}
.layer-saerch-city .insert-word input {
  height: 65px;
  width: 976px;
  padding: 0 20px;
  border: 0 none;
  margin: 0;
  border-radius: 8px;
}
.layer-saerch-city .item-name {
  display: none;
  position: relative;
  overflow: hidden;
  padding-left: 8px;
}
.layer-saerch-city .item-name dl {
  float: left;
  width: 120px;
  padding-bottom: 16px;
  line-height: 22px;
}
.layer-saerch-city .item-name .domestic {
  width: 125px;
}
.layer-saerch-city .item-name .japan {
  width: 127px;
}
.layer-saerch-city .item-name .china {
  width: 126px;
}
.layer-saerch-city .item-name .asia {
  width: 125px;
}
.layer-saerch-city .item-name .usa {
  width: 129px;
}
.layer-saerch-city .item-name .eu {
  width: 124px;
}
.layer-saerch-city .item-name .soc {
  width: 126px;
}
.layer-saerch-city .item-name .etc {
  width: 125px;
}
.layer-saerch-city .item-name dt {
  padding: 20px 0 11px 8px;
  font-size: 15px;
  font-weight: 700;
  color: #34446e;
}
.layer-saerch-city .item-name a {
  display: block;
  padding-left: 8px;
  line-height: 36px;
}
.layer-saerch-city .item-name a:hover {
  background-color: rgba(78, 129, 255, 0.07);
  color: #4e81ff;
}
.layer-saerch-city .item-searhing {
  position: relative;
  display: none;
}
.layer-saerch-city .item-searhing .scrollbar-inner {
  max-height: 364px;
  overflow-y: auto;
}
.layer-saerch-city .item-searhing li {
  border-top: 1px solid #ebeef3;
}
.layer-saerch-city .item-searhing li:first-child {
  border-top: 0;
}
.layer-saerch-city .item-searhing a {
  display: block;
  padding: 15px 16px 14px;
}
.layer-saerch-city .item-searhing a:hover {
  background-color: rgba(78, 129, 255, 0.07);
}
.layer-saerch-city .item-searhing .name {
  display: block;
  margin-bottom: 2px;
  line-height: 22px;
  font-size: 15px;
}
.layer-saerch-city .item-searhing .name strong {
  font-weight: normal;
  color: #4e81ff;
}
.layer-saerch-city .item-searhing .nation {
  line-height: 19px;
  display: block;
  font-size: 13px;
  color: #757f92;
}
.layer-saerch-city[data-type-city="default"] .insert-word input {
  width: 976px;
}
.layer-saerch-city[data-type-city="default"] .item-name {
  display: block;
}
.layer-saerch-city[data-type-city="search"] .insert-word input {
  width: 308px;
}
.layer-saerch-city[data-type-city="search"] .item-searhing {
  display: block;
}
.layer-saerch-city .city-foregin {
  display: none;
}
.layer-saerch-city .city-domestic {
  display: none;
}
.layer-saerch-city .city-domestic .item-name {
  display: block;
  width: 162px;
  padding-left: 0;
}
.layer-saerch-city .city-domestic .item-name a {
  padding-left: 20px;
  padding-right: 20px;
}
.layer-saerch-city.domestic {
  border-radius: 0 0 8px 8px;
}
.layer-saerch-city.domestic .city-domestic .item-name {
  width: 184px;
}
.layer-saerch-city .slimScrollBar {
  right: 5px !important;
  background-color: #c9cfd5 !important;
}
.layer-saerch-city .slimScrollRail {
  right: 5px !important;
  background-color: #fff !important;
  opacity: 0.7 !important;
}
.layer-saerch-city.type-hotel {
}
.layer-saerch-city.type-hotel[data-type-city="default"] .insert-word input {
  width: 939px;
}
.layer-saerch-city.type-hotel .item-name dl {
  width: 117px;
}
.layer-saerch-city.type-hotel .item-name dl.etc {
  width: 121px;
}

.layer-saerch-city.type-hotel[data-type-city="search"] .insert-word input {
  width: 580px;
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing {
  display: block;
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing ul {
  max-height: 338px;
  overflow: auto;
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing a {
  overflow: hidden;
  padding-left: 40px;
  background-position: 20px 50%;
  background-repeat: no-repeat;
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing span {
  float: left;
  margin-bottom: 0;
  color: #000;
  line-height: 22px;
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing .city a,
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing .googlemap a {
  background-image: url(../../assets/images/cmm/icn-marker-s-grey.png);
}
.layer-saerch-city.type-hotel[data-type-city="search"] .item-searhing .hotel a {
  background-image: url(../../assets/images/cmm/icn-search-hotel.png);
}

.layer-select-date {
  position: absolute;
  top: 0;
  left: 0;
}

/*=========================================*/
/* ################ modal popup ############### */
/*=========================================*/
/* modal popup*/
.blocker {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  z-index: 25;
  padding: 20px;
  box-sizing: border-box;
  background-color: #000;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  z-index: 100;
}
div.modal a.close-modal {
  top: 34px;
  right: 40px;
  width: 20px;
  height: 20px;
  background: url(../../assets/images/cmm/btn_close_pop.png) no-repeat 0 0;
}
.modal-wrap {
  display: none;
  max-width: none;
  padding: 0;
  margin: 0;
  box-shadow: 0 2px 10px 4px rgba(41, 48, 54, 0.2);
}
.modal-wrap .modal-head h2 {
  font-size: 26px;
  line-height: 38px;
  font-weight: 500;
  letter-spacing: -1px;
}

.box-guide {
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 5px;
  background-color: #f2f4f9;
}
.box-guide dt {
  margin-bottom: 15px;
  font-weight: 700;
}
.box-guide dd {
  line-height: 19px;
  text-indent: -9px;
  margin-left: 9px;
}
.box-guide dd span {
  color: #4e81ff;
}

/* 운임규정 팝업 */
#popFareRule {
}
/*#popFareRule .close-modal{ display: none; }*/
#popFareRule .modal-head h2 {
  padding: 24px 30px 30px;
}
#popFareRule .modal-cotn {
  padding: 0 0 30px 30px;
}
#popFareRule .scrollbar-inner {
  max-height: 394px;
  overflow-y: auto;
  margin-right: 40px;
  overflow-x: hidden;
}
#popFareRule .tab-btm {
  overflow: hidden;
  margin-bottom: 14px;
}
#popFareRule .tab-btm li {
  float: left;
  margin-right: 6px;
}
#popFareRule .tab-btm li.ui-tabs-active a {
  background-color: #3e4d91;
  color: #fff;
}
#popFareRule .tab-btm li.active a {
  background-color: #3e4d91;
  color: #fff;
}
#popFareRule .tab-btm a {
  display: block;
  width: 84px;
  height: 30px;
  border: 1px solid #3e4d91;
  border-radius: 16px;
  text-align: center;
  font-size: 13px;
  line-height: 30px;
  font-weight: 500;
  color: #3e4d91;
}
#popFareRule .tab-btm .btn-default.fare {
  width: 97px;
}
#popFareRule .tbl-cmm {
  display: none;
}
#popFareRule .tbl-cmm.active {
  display: block;
}
#popFareRule .btn-confirm {
  padding-top: 40px;
  text-align: center;
}
#popFareRule .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}
#popFareRule .desc-tbl {
  position: absolute;
  top: 95px;
  right: 35px;
  font-size: 13px;
  color: #293036;
}
#popFareRule .slimScrollBar,
#popFareRule .slimScrollRail {
  right: 15px !important;
}
#popFareRule .close-modal {
  top: 22px;
  right: 22px;
}

#popCancelRule {
}
#popCancelRule .modal-head h2 {
  padding: 25px 40px 26px;
  line-height: 38px;
}
#popCancelRule .modal-cotn {
  padding: 0 30px 30px;
}
#popCancelRule .btn-confirm {
  padding-top: 40px;
  text-align: center;
}
#popCancelRule .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}
#popCancelRule .tit {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
#popCancelRule .table-cmm {
  border-top: 0 none;
  margin-bottom: 40px;
}
#popCancelRule .table-cmm th {
  padding-top: 11px;
  padding-bottom: 11px;
  height: 20px;
  text-align: center;
  border-bottom: 2px solid #34446e;
}
#popCancelRule .table-cmm td {
  height: 50px;
}
#popCancelRule .cancel-rule {
  margin-top: 10px;
}
#popCancelRule .cancel-rule p {
  letter-spacing: -1px;
  text-indent: -8px;
  margin-left: 8px;
}
#popCancelRule .cancel-desc {
  font-size: 13px;
  line-height: 19px;
}
#popCancelRule .cancel-desc .limit {
  padding-left: 22px;
  margin-bottom: 20px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  background: url(../../assets/images/cmm/icn-notice-red.png) 0 50% no-repeat;
}
#popCancelRule .cancel-desc .limit strong {
  color: #ff4e50;
}

/* 항공권 비교 팝업 */
#popPlaneCompare .modal-head h2 {
  padding: 25px 40px 10px;
  line-height: 38px;
}
#popPlaneCompare .modal-cotn {
  padding: 0 40px 40px;
}
#popPlaneCompare .btn-request {
  padding-top: 40px;
  text-align: center;
}
#popPlaneCompare .btn-request .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}

#popPlaneCompare .city-date {
  overflow: hidden;
  margin-bottom: 11px;
}
#popPlaneCompare .city-date p {
  font-size: 17px;
  line-height: 25px;
}
#popPlaneCompare .city-date div {
  font-size: 15px;
  line-height: 25px;
  color: #757f92;
  letter-spacing: -1px;
}
#popPlaneCompare .city-date .dep {
  float: left;
  margin-right: 15px;
}
#popPlaneCompare .city-date .arr {
  float: left;
  padding-left: 32px;
  margin-right: 13px;
  background: url(../../assets/images/cmm/ico_arrival.png) no-repeat 0 60%;
}
#popPlaneCompare .city-date .date {
  float: left;
  position: relative;
  top: 2px;
  padding-left: 13px;
}
#popPlaneCompare .city-date .date::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 1px;
  height: 20px;
  margin-top: -10px;
  background-color: #4e81ff;
}
#popPlaneCompare .desc-info {
  margin-bottom: 26px;
  font-size: 13px;
  line-height: 19px;
  color: #fe7d5c;
}

#popPlaneCompare .inner-scoll {
  position: relative;
}
#popPlaneCompare .box-scroll {
  height: 480px;
  overflow-y: auto;
}
#popPlaneCompare .list-schdule {
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  align-items: stretch;
  align-content: stretch;
}
#popPlaneCompare .list-schdule::after {
  content: "";
  clear: both;
  display: block;
}
#popPlaneCompare .list-schdule .cell-col {
  position: relative;
  width: 25.3%;
  padding-top: 13px;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #757f92;
}
/* #popPlaneCompare .list-schdule .cell-col:nth-child(3) .btn-add-col{ left:61%;}
#popPlaneCompare .list-schdule .cell-col:nth-child(4) .btn-add-col{ left:86.5%} */
#popPlaneCompare .list-schdule .cell-row {
  border-top: 2px solid #757f92;
}
#popPlaneCompare .list-schdule .cell-row:first-child {
  border-top: 0 none;
}
#popPlaneCompare .list-schdule .cell-row::after {
  content: "";
  clear: both;
  display: block;
}
#popPlaneCompare .list-schdule .box-head {
  display: block;
  width: 24.1%;
}
#popPlaneCompare .list-schdule .inner {
  float: right;
  width: 181px;
  border-bottom: 1px solid #c9cfd8;
}
#popPlaneCompare .list-schdule .title {
  height: 29px;
  padding: 1px 0 0 5px;
  border-bottom: 2px solid #757f92;
  font-size: 12px;
}
#popPlaneCompare .list-schdule .tit-01 {
  float: left;
  width: 78px;
  padding: 10px 0 0 5px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  letter-spacing: -1px;
}
#popPlaneCompare .list-schdule .dep {
  border-top: 0 none;
}
#popPlaneCompare .list-schdule .dep .inner {
  border-bottom: 0 none;
}
#popPlaneCompare .list-schdule .arrs {
  background-color: #fff;
}
#popPlaneCompare .list-schdule .arrs .inner {
  border-bottom: 0 none;
}
#popPlaneCompare .list-schdule .fee {
  background-color: #fff;
}
#popPlaneCompare .list-schdule .fee .tit-01 {
  padding-top: 7px;
}
#popPlaneCompare .list-schdule .fee .inner {
  border-bottom: 0 none;
}
#popPlaneCompare .list-schdule .fee dd {
  padding-top: 10px;
  padding-bottom: 12px;
  margin-left: 47px;
}
#popPlaneCompare .list-schdule .fare {
  border-bottom: 0 none;
  border-top: 1px solid #e2e4e8;
}
#popPlaneCompare .list-schdule .fare dd {
  padding-top: 13px;
  padding-bottom: 12px;
  margin-left: 75px;
}
#popPlaneCompare .list-schdule .fare dd p {
  padding-top: 10px;
}
#popPlaneCompare .list-schdule .fare dd p:first-child {
  padding-top: 0px;
}
#popPlaneCompare .list-schdule .fare .inner {
  border-bottom: 0 none;
}
#popPlaneCompare .list-schdule .plan {
  border-top: 1px solid #e2e4e8;
}
#popPlaneCompare .list-schdule .plan dd p {
  display: none;
  padding-top: 10px;
}
#popPlaneCompare .list-schdule .plan dd p:first-child {
  padding-top: 0;
}
#popPlaneCompare .list-schdule dl {
  overflow: hidden;
}
#popPlaneCompare .list-schdule dt {
  float: left;
  padding: 14px 23px 14px 0;
  font-weight: 500;
}
#popPlaneCompare .list-schdule dd {
  float: right;
  padding: 13px 20px 9px 0;
}
#popPlaneCompare .list-schdule dd p {
  font-size: 13px;
  line-height: 20px;
  text-align: right;
}
#popPlaneCompare .list-schdule dd .air {
  padding-bottom: 15px;
}
#popPlaneCompare .list-schdule dd .seat {
  padding-bottom: 10px;
}
#popPlaneCompare .list-schdule dd .overstop {
  position: relative;
}

#popPlaneCompare .list-schdule .box-list {
}
#popPlaneCompare .list-schdule .box-radio {
  padding: 0 0 12px 15px;
  border-bottom: 2px solid #757f92;
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
}
#popPlaneCompare .list-schdule .form-chkbox span::after {
  top: 0;
}
#popPlaneCompare .list-schdule .box-radio span {
  padding-left: 30px;
}
#popPlaneCompare .list-schdule .info-default {
  position: relative;
  padding: 13px 11px 11px 15px;
  line-height: 19px;
  border-bottom: 1px solid #e2e4e8;
}
#popPlaneCompare .list-schdule .info-default p {
  font-size: 13px;
  margin-top: 11px;
  color: #293036;
}
#popPlaneCompare .list-schdule .info-default p:first-child {
  margin-top: 0;
  padding-bottom: 4px;
}
#popPlaneCompare .list-schdule .info-default img {
  margin-right: 7px;
}
#popPlaneCompare .list-schdule .info-default .name {
  margin-right: 11px;
  font-weight: 500;
}
#popPlaneCompare .list-schdule .info-default .code {
  color: #757f92;
}
#popPlaneCompare .list-schdule .info-plan {
  position: relative;
  border-bottom: 2px solid #757f92;
}
#popPlaneCompare .list-schdule .info-plan .btn-default {
  padding: 14px 24px 13px 15px;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
}
#popPlaneCompare .list-schdule .info-plan ul {
  display: none;
  padding: 11px 20px 9px 15px;
}
#popPlaneCompare .list-schdule .info-plan li {
  padding-top: 13px;
  font-size: 13px;
}
#popPlaneCompare .list-schdule .info-plan li:first-child {
  padding-top: 0;
}
#popPlaneCompare .list-schdule .info-fee {
  position: relative;
  padding: 10px 0 11px 15px;
  border-bottom: 1px solid #e2e4e8;
  line-height: 19px;
}
#popPlaneCompare .list-schdule .info-fee strong {
  font-weight: 700;
}
#popPlaneCompare .list-schdule .info-fee span {
  color: #4e81ff;
}
#popPlaneCompare .list-schdule .info-rule {
  position: relative;
}
#popPlaneCompare .list-schdule .info-rule .baggage {
  padding: 13px 0 11px 15px;
  font-size: 13px;
  line-height: 19px;
}
#popPlaneCompare .list-schdule .info-rule .refund {
  padding: 0 0 10px 15px;
  font-size: 13px;
  line-height: 19px;
}
#popPlaneCompare .list-schdule .info-rule .refund .btn-default {
  width: 60px;
  height: 20px;
  border: 1px solid #000;
  border-radius: 3px;
  border: solid 1px #000000;
  text-align: center;
}
#popPlaneCompare .list-schdule .btn-delete {
  position: absolute;
  top: 12px;
  right: 20px;
  font-size: 13px;
  line-height: 19px;
  text-decoration: underline;
}
#popPlaneCompare .list-schdule .btn-delete[disabled] {
  color: #e2e4e8;
}
#popPlaneCompare .list-schdule .btn-add-col {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -49px;
  margin-top: -9px;
}
#popPlaneCompare .list-schdule .btn-add-col .btn-default {
  width: 98px;
  line-height: 34px;
  border: 1px solid #4e81ff;
  border-radius: 20px;
  background-color: rgba(78, 129, 255, 0.07);
  font-weight: 500;
  color: #4e81ff;
}

#popPlaneCompare .list-schdule .info-default::after,
#popPlaneCompare .list-schdule .info-plan::after,
#popPlaneCompare .list-schdule .info-fee::after,
#popPlaneCompare .list-schdule .info-rule::after,
#popPlaneCompare .list-schdule .schdule-01 *[class*="info-"]::before {
  content: "";
  position: absolute;
  top: 10px;
  bottom: 10px;
  width: 1px;
  background-color: #e2e4e8;
}
#popPlaneCompare .list-schdule .schdule-01 *[class*="info-"]::before {
  left: 0;
}
#popPlaneCompare .list-schdule .schdule-03 *[class*="info-"]::after {
  display: none;
}
#popPlaneCompare .list-schdule *[class*="info-"]::after {
  right: -1px;
}

#popPlaneCompare .list-schdule .cell-col.active {
  background-color: rgba(78, 129, 255, 0.05);
  border-color: #4e81ff;
  z-index: 1;
}
#popPlaneCompare .list-schdule .cell-col.active::before,
#popPlaneCompare .list-schdule .cell-col.active::after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  border-left: 2px solid #4e81ff;
  z-index: 1;
}
#popPlaneCompare .list-schdule .cell-col.active::before {
  left: 0;
}
#popPlaneCompare .list-schdule .cell-col.active::after {
  right: 0;
}
#popPlaneCompare .list-schdule .cell-col.active *[class*="info-"]::after {
  right: 0;
}

#popPlaneCompare .list-schdule.view-all .cell-col {
}
#popPlaneCompare .list-schdule.view-all .plan dd p {
  display: block;
}
#popPlaneCompare .list-schdule.view-all .info-plan .btn-default {
  display: none;
}
#popPlaneCompare .list-schdule.view-all .info-plan ul {
  display: block;
}

#popPlaneCompare .btn-request-reserv {
  padding-top: 28px;
  text-align: center;
}
#popPlaneCompare .btn-request-reserv .btn-default {
  width: 138px;
  height: 48px;
  border-radius: 5px;
  border: 1px solid #4e81ff;
  font-size: 16px;
  line-height: 48px;
  font-weight: 500;
  text-align: center;
}
#popPlaneCompare .btn-request-reserv .btn-default.off {
  background-color: #fff;
  color: #4e81ff;
}
#popPlaneCompare .btn-request-reserv .btn-default.on {
  background-color: #4e81ff;
  color: #fff;
}

/* 검색결과 새로고침 */
#popResultReset .modal-head {
  padding: 60px 0 16px;
  text-align: center;
}
#popResultReset .modal-head h2 {
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
#popResultReset .modal-cotn {
  text-align: center;
  padding-bottom: 30px;
}
#popResultReset .desc-info {
  margin-bottom: 40px;
  font-weight: 300;
  line-height: 20px;
}
#popResultReset .btn-reset .btn-default {
  width: 100px;
  line-height: 40px;
  border-radius: 5px;
  background-color: #4e81ff;
  color: #fff;
}
#popResultReset a.close-modal {
  top: 22px;
  right: 22px;
}

/* session time */
#popSessionTimeHotel .modal-cotn {
  text-align: center;
  padding-top: 125px;
  padding-bottom: 30px;
  background: url(../../assets/images/search/icn-clock.png) 50% 56px no-repeat;
}
#popSessionTimeHotel .desc-info {
  margin-bottom: 40px;
  font-weight: 300;
  line-height: 20px;
}
#popSessionTimeHotel .btn-confirm .btn-default {
  font-size: 15px;
  line-height: 22px;
  display: inline-block;
  width: 100px;
  line-height: 40px;
  background-color: #4e81ff;
  color: #fff;
  font-weight: 500;
  border-radius: 5px;
}
#popSessionTimeHotel a.close-modal {
  display: none;
  top: 22px;
  right: 22px;
}

/* 대기 좌석 안내 */
#popStandbyInfo .modal-head {
  padding: 60px 0 16px;
  text-align: center;
}
#popStandbyInfo .modal-head h2 {
  margin-bottom: 20px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
#popStandbyInfo .modal-cotn {
  text-align: center;
  padding-bottom: 30px;
}
#popStandbyInfo .desc {
  margin-bottom: 34px;
  font-weight: 300;
  line-height: 20px;
}
#popStandbyInfo .desc strong {
  font-weight: 500;
  color: #4e81ff;
}
#popStandbyInfo .cs-call {
  margin-bottom: 40px;
}
#popStandbyInfo .cs-call .tit {
  display: inline-block;
  padding-left: 16px;
  background: url(../../assets/images/search/ico_tell.png) 0 100% no-repeat;
  font-weight: 300;
}
#popStandbyInfo .cs-call strong {
  display: inline-block;
  font-weight: 500;
}
#popStandbyInfo .btu-confirm .btn-default {
  width: 100px;
  line-height: 40px;
  border-radius: 5px;
  background-color: #4e81ff;
  color: #fff;
}
#popStandbyInfo a.close-modal {
  top: 22px;
  right: 22px;
}

/* 결제시간 만료 */
#popPayTimeEnd .modal-head {
  padding: 60px 0 16px;
  text-align: center;
}
#popPayTimeEnd .modal-head h2 {
  padding-top: 71px;
  background: url(../../assets/images/reserv/icn_notice.png) 50% 0 no-repeat;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
}
#popPayTimeEnd .modal-cotn {
  padding-bottom: 30px;
  text-align: center;
}
#popPayTimeEnd .desc {
  margin-bottom: 40px;
  font-weight: 300;
}
#popPayTimeEnd .box-btns .btn-default {
  width: 98px;
  line-height: 38px;
  border: 1px solid #4e81ff;
  border-radius: 5px;
  font-weight: 500;
}
#popPayTimeEnd .box-btns .btn-default.b-reserch {
  margin-right: 3px;
  background-color: #3b7ff3;
  color: #fff;
}
#popPayTimeEnd .box-btns .btn-default.b-main {
  background-color: #fff;
  color: #4e81ff;
}
#popPayTimeEnd .close-modal {
  top: 22px;
  right: 22px;
}

/* 항공 스케줄 해외 공통*/
.info-booking .plan {
  display: none;
  position: relative;
  padding: 0 22px;
}
.info-booking .plan dt {
  position: relative;
  overflow: hidden;
  padding: 9px;
  margin-bottom: 20px;
  background-color: #f2f4f9;
}
.info-booking .plan .type {
  float: left;
  width: 55px;
  line-height: 28px;
  border-radius: 2px;
  background-color: #3f4e73;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  text-align: center;
}
.info-booking .plan div.city {
  float: left;
  margin: 4px 0 0 15px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  font-weight: 500;
}
.info-booking .plan div.city span {
  float: left;
}
.info-booking .plan div.city span.dep {
  margin-right: 15px;
}
.info-booking .plan div.city span.arr {
  padding-left: 34px;
  background: url(../../assets/images/cmm/icn_arrow_city_s.png) no-repeat 0 50%;
}
.info-booking .plan div.time {
  float: right;
  margin: 5px 0 0 15px;
  font-size: 13px;
  line-height: 19px;
  color: #000;
}
.info-booking .plan .boarding {
  position: relative;
  height: 10px;
}
.info-booking .plan .boarding span {
  position: absolute;
  top: -25px;
  left: 27px;
  font-size: 12px;
  line-height: 18px;
  color: #4b75bf;
}
.info-booking .plan .plane {
  overflow: hidden;
  margin-bottom: 28px;
  padding-left: 4px;
}
.info-booking .plan .plane span {
  float: left;
  position: relative;
  padding: 0 8px;
  font-size: 13px;
  line-height: 19px;
  color: #293036;
}
.info-booking .plan .plane span::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 1px;
  height: 14px;
  margin-top: -7px;
  background-color: #babdc3;
}
.info-booking .plan .plane span:first-child {
  padding-left: 0;
}
.info-booking .plan .plane span:first-child img {
  margin-right: 4px;
}
.info-booking .plan .plane span:first-child::before {
  display: none;
}
.info-booking .plan .schdule {
  position: relative;
  line-height: 22px;
}
.info-booking .plan .schdule:last-child {
  margin-bottom: 29px;
}
.info-booking .plan .schdule .date {
  float: left;
  width: 136px;
  text-align: right;
}
.info-booking .plan .schdule .time {
  float: left;
  width: 82px;
  text-align: right;
}
.info-booking .plan .schdule .sign {
  float: left;
  width: 55px;
  height: 22px;
}
.info-booking .plan .schdule .sign::before {
  content: "";
  display: block;
  width: 13px;
  height: 13px;
  border-radius: 100%;
  margin: 3px auto 0;
  border: 1px solid #9da9be;
}
.info-booking .plan .schdule .sign.point::before {
  background-color: #9da9be;
}
.info-booking .plan .schdule .sign.ing::before {
  background-color: #fff;
}
.info-booking .plan .schdule .city {
  float: left;
  width: 450px;
}
.info-booking .plan .schdule .city .en {
  float: left;
  margin-right: 15px;
  font-size: 15px;
  font-weight: bold;
  color: #282d5c;
}
.info-booking .plan .schdule .city .kr {
  float: left;
  color: #293036;
}
.info-booking .plan .schdule .time-plan {
  position: relative;
  margin-left: 245px;
  padding: 7px 0 7px 27px;
  font-size: 12px;
  line-height: 19px;
  color: #9da9be;
}
.info-booking .plan .schdule .time-plan::before {
  content: "";
  position: absolute;
  top: -4px;
  left: 0;
  bottom: -3px;
  width: 1px;
  background-color: #9da9be;
}
.info-booking .plan .schdule .overstop {
  position: relative;
  margin: 8px 0 20px 244px;
  padding: 9px 0 9px 27px;
  font-size: 12px;
  line-height: 18px;
  color: #49b999;
}
.info-booking .plan .schdule .overstop::before {
  content: "";
  position: absolute;
  top: -4px;
  left: 0;
  bottom: -4px;
  width: 4px;
  background: url(../../assets/images/cmm/dot_line_ready.gif) repeat-y 0 0;
}
.info-booking .plan .schdule .etc {
  position: absolute;
  top: -30px;
  right: 12px;
  font-size: 13px;
  line-height: 19px;
  text-align: right;
  color: #757f92;
}
.info-booking .plan .schdule .etc strong {
  font-weight: 700;
  color: #3b7ff3;
}

/* 직원 검색 */
#popEmployeeSearch .modal-head h2 {
  padding: 24px 30px 30px;
}
#popEmployeeSearch .modal-cotn {
  padding: 0 0 30px 30px;
}
#popEmployeeSearch .box-saerch-cmm {
  margin-right: 30px;
}
#popEmployeeSearch .list-result .total {
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 22px;
}
#popEmployeeSearch .list-result .total span {
  font-weight: 700;
  color: #4e81ff;
}
#popEmployeeSearch .table-cmm th {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
#popEmployeeSearch .table-cmm td {
  height: 50px;
}
#popEmployeeSearch .table-cmm .ready td {
  padding: 20px 0 40px;
  height: 20px;
  border: 0 none !important;
}
#popEmployeeSearch .table-cmm .none td {
  padding: 20px 0 40px;
  height: 20px;
  color: #7f848d;
  border: 0 none !important;
}
#popEmployeeSearch .table-cmm div {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 20px;
}
#popEmployeeSearch .scrollbar-inner {
  max-height: 360px;
  padding-right: 30px;
}
#popEmployeeSearch .slimScrollDiv .slimScrollBar,
#popEmployeeSearch .slimScrollDiv .slimScrollRail {
  right: 12px !important;
}
#popEmployeeSearch .table-cmm .code div {
  width: 80px;
}
#popEmployeeSearch .table-cmm .name div {
  width: 95px;
}
#popEmployeeSearch .table-cmm .company div {
  width: 110px;
}
#popEmployeeSearch .table-cmm .team div {
  width: 135px;
}
#popEmployeeSearch .table-cmm .email div {
  width: 207px;
}
#popEmployeeSearch .btn-select {
  width: 60px;
  line-height: 32px;
  border-radius: 5px;
  background-color: #4e81ff;
  text-align: center;
  color: #fff;
}
#popEmployeeSearch .table-cmm.type-fare {
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popEmployeeSearch .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
}
#popEmployeeSearch .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}

/* 탑승객 검색 */
#popPassengerSearch .modal-head h2 {
  padding: 24px 30px 30px;
}
#popPassengerSearch .modal-cotn {
  padding: 0 0 30px 30px;
}
#popPassengerSearch .box-saerch-cmm {
  margin-right: 30px;
}
#popPassengerSearch .list-result .total {
  margin-bottom: 10px;
  font-size: 15px;
  line-height: 22px;
}
#popPassengerSearch .list-result .total span {
  font-weight: 700;
  color: #4e81ff;
}
#popPassengerSearch .table-cmm th {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
#popPassengerSearch .table-cmm td {
  height: 50px;
}
#popPassengerSearch .table-cmm .ready td {
  padding: 20px 0 40px;
  height: 20px;
  border: 0 none !important;
}
#popPassengerSearch .table-cmm .none td {
  padding: 20px 0 40px;
  height: 20px;
  color: #7f848d;
  border: 0 none !important;
}
#popPassengerSearch .table-cmm div {
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  line-height: 20px;
}
#popPassengerSearch .scrollbar-inner {
  max-height: 360px;
  padding-right: 30px;
}
#popPassengerSearch .slimScrollDiv .slimScrollBar,
#popPassengerSearch .slimScrollDiv .slimScrollRail {
  right: 12px !important;
}
#popPassengerSearch .table-cmm .code div {
  width: 80px;
}
#popPassengerSearch .table-cmm .name div {
  width: 95px;
}
#popPassengerSearch .table-cmm .company div {
  width: 110px;
}
#popPassengerSearch .table-cmm .team div {
  width: 135px;
}
#popPassengerSearch .table-cmm .email div {
  width: 207px;
}
#popPassengerSearch .btn-select {
  width: 60px;
  line-height: 32px;
  border-radius: 5px;
  background-color: #4e81ff;
  text-align: center;
  color: #fff;
}
#popPassengerSearch .table-cmm.type-fare {
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popPassengerSearch .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
}
#popPassengerSearch .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}

/* 탑승객 정보 확인 */
#popPassengerInfo .modal-head h2 {
  padding: 24px 30px 30px;
}
#popPassengerInfo .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popPassengerInfo .desc {
  margin-bottom: 30px;
  font-size: 13px;
  line-height: 19px;
  color: #293036;
}
#popPassengerInfo .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popPassengerInfo .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
}
#popPassengerInfo .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}
#popPassengerInfo .bottom-btn {
  text-align: center;
}
#popPassengerInfo .btn-request {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}

/* popEticket */
#popEticket .modal-head h2 {
  padding: 24px 30px 30px;
}
#popEticket .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popEticket .desc {
  margin-bottom: 30px;
  font-size: 13px;
  line-height: 19px;
}
#popEticket .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popEticket .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
}
#popEticket .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}
#popEticket .btn-confirm {
  width: 58px;
  line-height: 30px;
  border-radius: 5px;
  border: 1px solid #4e81ff;
  text-align: center;
  color: #4e81ff;
}
#popEticket .bottom-btn {
  text-align: center;
}
#popEticket .bottom-btn .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}

/* 여권 정보 수정 */
#popPassportEdit .modal-head h2 {
  padding: 24px 30px 30px;
}
#popPassportEdit .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popPassportEdit .desc {
  margin-bottom: 30px;
  font-size: 13px;
  line-height: 19px;
}
#popPassportEdit .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popPassportEdit .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
}
#popPassportEdit .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
}
#popPassportEdit .table-cmm.type-fare .left {
  padding-left: 10px;
}
#popPassportEdit .form-select {
  border-bottom: 0 none;
}
#popPassportEdit .box-ymd .form-select {
  margin-left: 10px;
}
#popPassportEdit .btn-confirm {
  width: 58px;
  line-height: 30px;
  border-radius: 5px;
  border: 1px solid #4e81ff;
  text-align: center;
  color: #4e81ff;
}
#popPassportEdit .bottom-btn {
  text-align: center;
}
#popPassportEdit .bottom-btn .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}
#popPassportEdit td input {
  border: 0 none;
}

/* 예약 변경 요청 */
#popResevChange .modal-head h2 {
  padding: 24px 30px 30px;
}
#popResevChange .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popResevChange .desc {
  margin-bottom: 30px;
  font-size: 13px;
  line-height: 19px;
}
#popResevChange .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popResevChange .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
  text-align: left;
  padding-left: 10px;
}
#popResevChange .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
  text-align: left;
  padding-left: 10px;
}
#popResevChange .box-text {
  margin-bottom: 40px;
  border: 1px solid #e2e4e8;
  border-radius: 2px;
}
#popResevChange .box-text textarea {
  width: 100%;
  height: 130px;
  padding: 15px;
  outline: none;
  resize: none;
  border: 0 none;
  box-sizing: border-box;
}
#popResevChange .bottom-btn {
  text-align: center;
}
#popResevChange .bottom-btn .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}
#popResevChange .bottom-btn .btn-white {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #4e81ff;
  font-weight: 500;
  background-color: #fff;
  border-color: #4e81ff;
  border-width: 1px;
  border-style: solid;
}

/* 예약 취소 요청 */
#popResevCancel .modal-head h2 {
  padding: 24px 30px 30px;
}
#popResevCancel .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popResevCancel .desc {
  margin-bottom: 30px;
  font-size: 13px;
  line-height: 19px;
}
#popResevCancel .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popResevCancel .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
  text-align: left;
  padding-left: 10px;
}
#popResevCancel .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
  text-align: left;
  padding-left: 10px;
}
#popResevCancel .box-text {
  margin-bottom: 40px;
  border: 1px solid #e2e4e8;
  border-radius: 2px;
}
#popResevCancel .box-text textarea {
  width: 100%;
  height: 130px;
  padding: 15px;
  outline: none;
  resize: none;
  border: 0 none;
  box-sizing: border-box;
}
#popResevCancel .price {
  margin-bottom: 15px;
  text-align: center;
  font-size: 17px;
  line-height: 25px;
  font-weight: 500;
}
#popResevCancel .price span {
  display: inline-block;
  margin-left: 5px;
}
#popResevCancel .price strong {
  font-weight: 700;
  color: #4e81ff;
}
#popResevCancel .last-chk {
  text-align: center;
  margin-bottom: 40px;
  line-height: 20px;
}
#popResevCancel .last-chk p {
  font-size: 14px;
}
#popResevCancel .bottom-btn {
  text-align: center;
}
#popResevCancel .bottom-btn .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}

#loading-search {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10000;
}
#loading-search .inner {
  position: absolute;
  top: 50%;
  left: 0%;
  right: 0;
  margin-top: -200px;
  text-align: center;
  color: #3b7ff3;
}
#loading-search .img {
  margin-bottom: 30px;
}
#loading-search .desc {
  margin-bottom: 20px;
  font-size: 26px;
  line-height: 38px;
}
#loading-search .desc strong {
  font-weight: 700;
}
#loading-search .date {
  margin-bottom: 4px;
  font-size: 17px;
  line-height: 25px;
}
#loading-search-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.3);
}
#loading-search-bg .inner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 486px;
  padding: 15px 0 20px;
  margin-top: -231px;
  margin-left: -243px;
  text-align: center;
  color: #3b7ff3;
  background-color: #fff;
}
#loading-search-bg .img {
  margin-bottom: 30px;
}
#loading-search-bg .desc {
  margin-bottom: 20px;
  font-size: 26px;
  line-height: 38px;
}
#loading-search-bg .desc strong {
  font-weight: 700;
}
#loading-search-bg .date {
  margin-bottom: 4px;
  font-size: 17px;
  line-height: 25px;
}

#loading-payment {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10000;
}
#loading-payment .inner {
  position: absolute;
  top: 50%;
  left: 0%;
  right: 0;
  margin-top: -220px;
  text-align: center;
  color: #3b7ff3;
}
#loading-payment .img {
  margin-bottom: 30px;
}
#loading-payment dt {
  margin-bottom: 10px;
  font-size: 26px;
  line-height: 38px;
  color: #4e81ff;
  font-weight: 700;
}
#loading-payment dd {
  margin-bottom: 41px;
  font-size: 17px;
  line-height: 25px;
  color: #3b7ff3;
}
#loading-payment .desc {
  line-height: 20px;
  color: #596a92;
}
#loading-payment-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.3);
}
#loading-payment-bg .inner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 600px;
  padding: 10px 0 30px;
  margin-top: -212px;
  margin-left: -300px;
  text-align: center;
  color: #3b7ff3;
  background-color: #fff;
}
#loading-payment-bg .img {
  margin-bottom: 30px;
}
#loading-payment-bg dt {
  margin-bottom: 10px;
  font-size: 26px;
  line-height: 38px;
  color: #4e81ff;
  font-weight: 700;
}
#loading-payment-bg dd {
  margin-bottom: 41px;
  font-size: 17px;
  line-height: 25px;
  color: #3b7ff3;
}
#loading-payment-bg .desc {
  line-height: 20px;
  color: #596a92;
}

#loading-booking {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.3);
}
#loading-booking .inner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 370px;
  height: 243px;
  margin: -120px 0 0 -160px;
  border-radius: 10px;
  text-align: center;
  color: #3b7ff3;
  background-color: #fff;
}
#loading-booking .img {
  padding-top: 60px;
  margin-bottom: 30px;
}
#loading-booking dt {
  margin-bottom: 16px;
  font-size: 18px;
  line-height: 27px;
  color: #000;
  font-weight: 700;
}
#loading-booking dd {
  margin-bottom: 41px;
  font-weight: 300;
  line-height: 20px;
  color: #000;
}

#loading-booking-notice {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.3);
}
#loading-booking-notice .inner {
  position: relative;
  margin: 15% auto;
  width: 640px;
  height: 233px;
  text-align: center;
  color: #3b7ff3;
  background-color: #fff;
  display: flex;
  justify-content: center;
}
#loading-booking-notice p {
  position: absolute;
  margin-top: 50px;
  color: #000;
  font-size: 17px;
}
#lbn-confirm-btn {
  position: absolute;
  bottom: 0;
  margin-bottom: 25px;
  border: 1px solid #4e81ff;
  padding: 10px 50px;
  border-radius: 10px;
  color: #4e81ff;
}

#loading-hotel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  z-index: 10000;
  background-color: rgba(0, 0, 0, 0.3);
}
#loading-hotel .inner {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 370px;
  height: 370px;
  margin: -185px 0 0 -185px;
  border-radius: 10px;
  text-align: center;
  color: #3b7ff3;
  background-color: #fff;
  border-radius: 50%;
}
#loading-hotel .img {
  padding-top: 60px;
  margin-bottom: 24px;
}
#loading-hotel img {
  width: 239px;
  height: 136px;
}
#loading-hotel dl {
  color: #4e81ff;
}
#loading-hotel dt {
  margin-bottom: 8px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
#loading-hotel dd {
  margin-bottom: 41px;
  font-size: 13px;
  font-weight: 300;
  line-height: 19px;
}

/* 호텔 검색결과 리스트 */
#popResultMap {
  border-radius: 10px;
  overflow: hidden;
}
#popResultMap .box-top {
  position: relative;
  z-index: 1;
  height: 120px;
  padding-left: 30px;
  box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.06);
}
#popResultMap .box-top .title {
  padding: 18px 0 15px;
  font-weight: 500;
  line-height: 20px;
}
#popResultMap .box-top .name {
  margin-bottom: 4px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
#popResultMap .box-top .addr {
  padding-left: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: url(../../assets/images/cmm/icn-marker-s-grey.png) 0 50% no-repeat;
  color: #757f92;
}
#popResultMap .box-map {
  position: relative;
}
#popResultMap .box-map .map-rect {
  height: 570px;
}
#popResultMap .box-map .pin {
  position: absolute;
}
#popResultMap .box-filter {
  position: absolute;
  top: 120px;
  left: -22.5%;
  bottom: 0;
  width: 270px;
  background-color: #fff;
  box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.06);
}
#popResultMap .box-filter .tit {
  padding: 20px 0 12px;
  font-size: 14px;
  line-height: 20px;
  font-weight: 500;
  color: #4e81ff;
}
#popResultMap .box-filter .box-near {
  font-size: 13px;
  line-height: 1.69;
}
#popResultMap .box-filter .list-near {
  position: relative;
  padding-bottom: 20px;
}
#popResultMap .box-filter .list-near li {
  margin-bottom: 8px;
}
#popResultMap .box-filter .list-near li:last-child {
  margin-bottom: 0;
}
#popResultMap .box-filter .list-near a {
  display: block;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 8px 0 rgba(157, 169, 190, 0.2);
}
#popResultMap .box-filter .list-near .name {
  margin-bottom: 4px;
  font-size: 13px;
  line-height: 19px;
  font-weight: 500;
}
#popResultMap .box-filter .list-near .grade {
  font-size: 12px;
  line-height: 18px;
  color: #757f92;
}
#popResultMap .box-filter .list-near .star {
  display: inline-block;
  vertical-align: middle;
}
#popResultMap .box-filter .list-near .star img {
  position: relative;
  top: 1px;
  vertical-align: top;
  margin-left: 2px;
}
#popResultMap .box-filter .btns-sld-option {
  position: absolute;
  top: 26px;
  left: 100%;
  cursor: pointer;
  border-radius: 0 5px 5px 0;
  background-color: #4e81ff;
}
#popResultMap .box-filter .btns-sld-option::after {
  content: "";
  display: block;
  width: 40px;
  height: 40px;
  background: url(../../assets/images/search/btn-back-wh.png) no-repeat 48% 53%;
  -webkit-transform: rotateZ(180deg);
  -ms-transform: rotateZ(180deg);
  -o-transform: rotateZ(180deg);
  transform: rotateZ(180deg);
}
#popResultMap .box-filter.open {
  left: 0;
  -webkit-transition: left 0.5s ease;
  -moz-transition: left 0.5s ease;
  -o-transition: left 0.5s ease;
  transition: left 0.5s ease;
  box-shadow: -2px 6px 15px 0 rgba(157, 169, 190, 0.3);
}
#popResultMap .box-filter.open .btns-sld-option::after {
  -webkit-transform: rotateZ(0deg);
  -ms-transform: rotateZ(0deg);
  -o-transform: rotateZ(0deg);
  transform: rotateZ(0deg);
}
#popResultMap .btn-room-sel {
  position: absolute;
  top: 60px;
  right: 28px;
  width: 120px;
  line-height: 40px;
  border-radius: 20px;
  background-color: #4e81ff;
  color: #fff;
  font-weight: 500;
}
#popResultMap .close-modal {
  top: 25px;
  left: initial;
  right: 30px;
  z-index: 10;
}
#popResultMap .scrollbar-inner {
  max-height: 570px;
  padding: 0 18px;
}

/* 간격 모음 */
.a-mt5 {
  margin-top: 5px;
}
.a-mt10 {
  margin-top: 10px;
}
.a-mt15 {
  margin-top: 15px;
}
.a-mb30 {
  margin-bottom: 30px;
}

/* 모달 팝업 신규 스타일 */
.nw-modal .modal-head {
  padding: 25px 30px;
}
.nw-modal .modal-head h2 {
  font-size: 22px;
  font-weight: 500;
}
.nw-modal .modal-cotn {
  padding: 0 30px 40px;
}
.nw-modal .modal-cotn .tit {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}
.nw-modal .modal-cotn .desc {
  margin: 5px 0 10px;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
}
.nw-modal .modal-cotn .desc .point {
  color: #ff4e50;
}

.nw-modal .info-list li {
  font-size: 13px;
  line-height: 19px;
}

.table-cmm.type-fare2 {
  border: none;
}
.table-cmm.type-fare2 thead th {
  font-size: 14px;
  font-weight: 500;
  color: #34446e;
  text-align: center;
  border-bottom: 2px solid #34446e;
}
.table-cmm.type-fare2 tbody td {
  height: 50px;
  font-size: 14px;
  border-top: none;
  border-bottom: 1px solid #e2e4e8;
}

.nw-modal .btn-confirm {
  margin-top: 30px;
  text-align: center;
}
.nw-modal .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  background-color: #4e81ff;
  text-align: center;
  font-weight: 500;
  color: #fff;
}

.nw-modal .step-box {
  margin-top: 15px;
  display: flex;
  height: 113px;
}
.nw-modal .step-box.tall {
  height: 134px;
}
.nw-modal .step-box li {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 25%;
  padding: 15px;
  background: #f8faff;
  border-right: 1px solid #ededed;
}
.nw-modal .step-box li:first-of-type {
  border-radius: 5px 0 0 5px;
}
.nw-modal .step-box li:last-of-type {
  border-radius: 0 5px 5px 0;
  border: none;
}
.nw-modal .step-box li .title {
  font-size: 14px;
  font-weight: 700;
}
.nw-modal .step-box li .content {
  font-size: 14px;
  line-height: 21px;
}

.nw-modal .payment-desc-area {
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid #e2e4e8;
}
.nw-modal .payment-desc-area > ol > li + li {
  margin-top: 15px;
}
.nw-modal .arrow-step {
  margin: 15px 0;
  display: flex;
  justify-content: space-between;
}
.nw-modal .arrow-step li {
  position: relative;
  display: flex;
  align-items: center;
  width: 50%;
  padding: 20px;
  font-size: 14px;
  line-height: 21px;
}
.nw-modal .arrow-step li:first-of-type {
  border: 1px solid #e2e4e8;
  border-right: none;
  border-radius: 5px 0 0 5px;
}
.nw-modal .arrow-step li:last-of-type {
  border: 1px solid #e2e4e8;
  border-left: none;
  border-radius: 0 5px 5px 0;
}
.nw-modal .arrow-step li + li:before {
  display: block;
  content: "";
  position: absolute;
  top: 11px;
  left: -80px;
  width: 58px;
  height: 58px;
  border: 1px solid #e2e4e8;
  border-bottom: none;
  border-left: none;
  transform: rotate(45deg);
}
.nw-modal .arrow-step.tall li + li:before {
  top: 15px;
  width: 72px;
  height: 72px;
}
.nw-modal .arrow-step.tall li:first-of-type {
  width: 55%;
}
.nw-modal .arrow-step.tall li:last-of-type {
  width: 45%;
  padding-left: 35px;
}

/* 알림 팝업 레이어 추가 by 2020.07.17 */
#popNotice {
}
#popNotice .modal-head h2 {
  padding: 25px 30px 26px;
  line-height: 38px;
  font-size: 22px;
}
#popNotice .modal-cotn {
  padding: 0 30px 30px;
}
#popNotice .btn-confirm {
  padding-top: 40px;
  text-align: center;
}
#popNotice .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}
#popNotice .tit {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
#popNotice .notice-desc {
  font-size: 13px;
  line-height: 19px;
}
#popNotice .notice-desc .limit {
  padding-left: 22px;
  margin-bottom: 20px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  background: url(../../assets/images/cmm/icn-notice-red.png) 0 50% no-repeat;
}
#popNotice .notice-desc .limit strong {
  color: #ff4e50;
}

.notice-desc p:has(b):nth-child(2) {
  margin-top: -16px !important;
}

/* 취소규정안내 (무료취소기한이 지난 예약) */
#freeCancelClose {
}
#freeCancelClose .modal-head h2 {
  padding: 25px 30px 26px;
  line-height: 38px;
  font-size: 22px;
}
#freeCancelClose .modal-cotn {
  padding: 0 30px 30px;
}
#freeCancelClose .tit {
  margin-bottom: 4px;
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
}
#freeCancelClose .cancel-desc {
  font-size: 13px;
  line-height: 19px;
}
#freeCancelClose .cancel-desc dt {
  padding-left: 22px;
  margin-bottom: 20px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  background: url(../../assets/images/cmm/icn-notice-red.png) 0 50% no-repeat;
}
#freeCancelClose .cancel-desc dt strong {
  color: #ff4e50;
  font-weight: bold;
  color: #4e81ff;
}
#freeCancelClose .cancel-desc dd {
  position: relative;
  padding-left: 13px;
  font-size: 13px;
  color: #757f92;
}
#freeCancelClose .cancel-desc dd::after {
  content: "";
  position: absolute;
  top: 6px;
  left: 0;
  width: 5px;
  height: 5px;
  background-color: #757f92;
}
#freeCancelClose .btn-confirm {
  padding-top: 40px;
  text-align: center;
}
#freeCancelClose .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}
#freeCancelClose .btn-confirm .bg-grey {
  color: #fff;
  background-color: #808080;
}

.price .fare-detail {
  padding-left: 99px;
}
.price .fare-detail dd {
  font-size: 12px;
  color: #3f4e73;
}
.price .fare-detail .tit {
  float: left;
}
.price .fare-detail .sum {
}
.dot_red {
  color: #f4516c !important;
}

/* 비교함 > 경유 > 여행정보 팝업 */
#popFlightDetail {
}
#popFlightDetail .modal-head h2 {
  padding: 24px 30px 30px;
}
#popFlightDetail .modal-cotn {
  padding: 0 0 30px 30px;
}
#popFlightDetail .scrollbar-inner {
  max-height: 394px;
  overflow-y: auto;
  margin-right: 40px;
  overflow-x: hidden;
}
#popFlightDetail .tab-btm {
  overflow: hidden;
  margin-bottom: 14px;
}
#popFlightDetail .tab-btm li {
  float: left;
  margin-right: 6px;
}
#popFlightDetail .tab-btm li.ui-tabs-active a {
  background-color: #3e4d91;
  color: #fff;
}
#popFlightDetail .tab-btm li.active a {
  background-color: #3e4d91;
  color: #fff;
}
#popFlightDetail .tab-btm a {
  display: block;
  width: 84px;
  height: 30px;
  border: 1px solid #3e4d91;
  border-radius: 16px;
  text-align: center;
  font-size: 13px;
  line-height: 30px;
  font-weight: 500;
  color: #3e4d91;
}
#popFlightDetail .tab-btm .btn-default.fare {
  width: 97px;
}
#popFlightDetail .tbl-cmm {
  display: none;
}
#popFlightDetail .tbl-cmm.active {
  display: block;
}
#popFlightDetail .btn-confirm {
  padding-top: 40px;
  text-align: center;
}
#popFlightDetail .btn-confirm .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  border: solid 1px #3b7ff3;
  font-weight: 500;
  color: #fff;
  background-color: #4e81ff;
}
#popFlightDetail .desc-tbl {
  position: absolute;
  top: 95px;
  right: 35px;
  font-size: 13px;
  color: #293036;
}
#popFlightDetail .slimScrollBar,
#popFlightDetail .slimScrollRail {
  right: 15px !important;
}
#popFlightDetail .close-modal {
  top: 22px;
  right: 22px;
}
#popFlightDetail {
  display: none;
  position: relative;
  padding: 0 22px;
}
#popFlightDetail dt {
  position: relative;
  overflow: hidden;
  padding: 9px;
  margin-bottom: 20px;
  background-color: #f2f4f9;
}
#popFlightDetail .type {
  float: left;
  width: 55px;
  line-height: 28px;
  border-radius: 2px;
  background-color: #3f4e73;
  font-size: 12px;
  font-weight: 500;
  color: #fff;
  text-align: center;
}
#popFlightDetail div.city {
  float: left;
  margin: 4px 0 0 15px;
  font-size: 15px;
  font-weight: 500;
  line-height: 22px;
  font-weight: 500;
}
#popFlightDetail div.city span {
  float: left;
}
#popFlightDetail div.city span.dep {
  margin-right: 15px;
}
#popFlightDetail div.city span.arr {
  padding-left: 34px;
  background: url(../../assets/images/cmm/icn_arrow_city_s.png) no-repeat 0 50%;
}
#popFlightDetail div.time {
  float: right;
  margin: 5px 0 0 15px;
  font-size: 13px;
  line-height: 19px;
  color: #000;
}
#popFlightDetail .boarding {
  position: relative;
  height: 10px;
}
#popFlightDetail .boarding span {
  position: absolute;
  top: -25px;
  left: 27px;
  font-size: 12px;
  line-height: 18px;
  color: #4b75bf;
}
#popFlightDetail .plane {
  overflow: hidden;
  margin-bottom: 28px;
  padding-left: 4px;
}
#popFlightDetail .plane span {
  float: left;
  position: relative;
  padding: 0 8px;
  font-size: 13px;
  line-height: 19px;
  color: #293036;
}
#popFlightDetail .plane span::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 1px;
  height: 14px;
  margin-top: -7px;
  background-color: #babdc3;
}
#popFlightDetail .plane span:first-child {
  padding-left: 0;
}
#popFlightDetail .plane span:first-child img {
  margin-right: 4px;
}
#popFlightDetail .plane span:first-child::before {
  display: none;
}
#popFlightDetail .schdule {
  position: relative;
  line-height: 22px;
}
#popFlightDetail .schdule:last-child {
  margin-bottom: 29px;
}
#popFlightDetail .schdule .date {
  float: left;
  width: 136px;
  text-align: right;
}
#popFlightDetail .schdule .time {
  float: left;
  width: 82px;
  text-align: right;
}
#popFlightDetail .schdule .sign {
  float: left;
  width: 55px;
  height: 22px;
}
#popFlightDetail .schdule .sign::before {
  content: "";
  display: block;
  width: 13px;
  height: 13px;
  border-radius: 100%;
  margin: 3px auto 0;
  border: 1px solid #9da9be;
}
#popFlightDetail .schdule .sign.point::before {
  background-color: #9da9be;
}
#popFlightDetail .schdule .sign.ing::before {
  background-color: #fff;
}
#popFlightDetail .schdule .city {
  float: left;
  width: 450px;
}
#popFlightDetail .schdule .city .en {
  float: left;
  margin-right: 15px;
  font-size: 15px;
  font-weight: bold;
  color: #282d5c;
}
#popFlightDetail .schdule .city .kr {
  float: left;
  color: #293036;
}
#popFlightDetail .schdule .time-plan {
  position: relative;
  margin-left: 245px;
  padding: 7px 0 7px 27px;
  font-size: 12px;
  line-height: 19px;
  color: #9da9be;
}
#popFlightDetail .schdule .time-plan::before {
  content: "";
  position: absolute;
  top: -4px;
  left: 0;
  bottom: -3px;
  width: 1px;
  background-color: #9da9be;
}
#popFlightDetail .schdule .overstop {
  position: relative;
  margin: 8px 0 20px 244px;
  padding: 9px 0 9px 27px;
  font-size: 12px;
  line-height: 18px;
  color: #49b999;
}
#popFlightDetail .schdule .overstop::before {
  content: "";
  position: absolute;
  top: -4px;
  left: 0;
  bottom: -4px;
  width: 4px;
  background: url(../../assets/images/cmm/dot_line_ready.gif) repeat-y 0 0;
}
#popFlightDetail .schdule .etc {
  position: absolute;
  top: -30px;
  right: 12px;
  font-size: 13px;
  line-height: 19px;
  text-align: right;
  color: #757f92;
}
#popFlightDetail .schdule .etc strong {
  font-weight: 700;
  color: #3b7ff3;
}

/* RVYN-496 ---------- */
.blind {
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 0px;
  height: 0px;
  font-size: 0;
  line-height: 0;
}
.t-center {
  text-align: center;
}

#popDetailedJourney .modal-head {
  padding: 25px 30px;
}
#popDetailedJourney a.close-modal {
  top: 12px;
  right: 12px;
  width: 12px;
  height: 12px;
  padding: 10px;
  background-size: 12px;
  background-position: center center;
}
#popDetailedJourney .modal-cont {
  padding: 4px 12px;
}
#popDetailedJourney .modal-cont .sub-tit {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: -0.3px;
}
#popDetailedJourney .modal-cont .desc-info {
  line-height: 20.72px;
  letter-spacing: -0.3px;
}
#popDetailedJourney .modal-cont .btn-reset {
  padding: 30px 0;
}
#popDetailedJourney .modal-cont .btn-reset .btn-default {
  width: 100px;
  height: 40px;
  padding: 9px 0;
  color: #fff;
  font-weight: 500;
  letter-spacing: -0.3px;
  background-color: #4e81ff;
  border-radius: 5px;
}

.base-modal .modal-head {
  padding: 25px 30px;
}
.base-modal a.close-modal {
  top: 20px;
  right: 20px;
  width: 18px;
  height: 18px;
  padding: 10px;
  background-size: 18px;
  background-position: center center;
}
.base-modal .modal-cont {
  padding: 4px 12px;
}
.base-modal .modal-cont .sub-tit {
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: -0.3px;
}
.base-modal .modal-cont .desc-info {
  line-height: 20.72px;
  letter-spacing: -0.3px;
}
.base-modal .modal-cont .btn-reset {
  padding: 0 0 30px;
}
.base-modal .modal-cont .btn-confirm {
  padding: 30px 0;
}
.base-modal .modal-cont .btn-reset .btn-default {
  width: 100px;
  height: 40px;
  padding: 9px 0;
  color: #fff;
  font-weight: 500;
  letter-spacing: -0.3px;
  background-color: #4e81ff;
  border-radius: 5px;
}
.base-modal .modal-cont .btn-confirm .btn-default {
  width: 100px;
  height: 40px;
  padding: 9px 0;
  color: #fff;
  font-weight: 500;
  letter-spacing: -0.3px;
  background-color: #4e81ff;
  border-radius: 5px;
}
/* RVYN-496 ---------- */

/* 호텔무료취소 */
popFreeCancel4Hotel .modal-wrap {
  width: 80px;
}
#popFreeCancel4Hotel .modal-head h2 {
  padding: 24px 30px 30px;
  text-align: center;
}
#popFreeCancel4Hotel .modal-cotn {
  padding: 0 30px 30px 30px;
}
#popFreeCancel4Hotel .desc {
  margin-bottom: 30px;
  font-size: 20px;
  line-height: 19px;
  text-align: center;
}
#popFreeCancel4Hotel .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popFreeCancel4Hotel .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
  text-align: left;
  padding-left: 10px;
}
#popFreeCancel4Hotel .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
  text-align: left;
  padding-left: 10px;
}
#popFreeCancel4Hotel .box-text {
  margin-bottom: 40px;
  border: 1px solid #e2e4e8;
  border-radius: 2px;
}
#popFreeCancel4Hotel .box-text textarea {
  width: 100%;
  height: 130px;
  padding: 15px;
  outline: none;
  resize: none;
  border: 0 none;
  box-sizing: border-box;
}
#popFreeCancel4Hotel .bottom-btn {
  text-align: center;
  padding: 24px 30px 30px;
}
#popFreeCancel4Hotel .bottom-btn .btn-default {
  width: 120px;
  line-height: 46px;
  border-radius: 5px;
  text-align: center;
  color: #fff;
  font-weight: 500;
  background-color: #4e81ff;
}
#popFreeCancel4Hotel .bottom-btn .btn-close {
  color: #4e81ff;
  border-color: #4e81ff;
  background-color: #fff;
  outline: #4e81ff;
  border: 1px solid #4e81ff;
}

/* DT-11325 메인 모달(마일리지) - 20230531 */
.main-modal {
  position: fixed;
  z-index: 100;
  top: 145px;
  left: 310px; /* left: 50%; transform: translateX(-50%); */
}
.main-modal a.close-modal {
  display: none;
}
.main-modal {
  border-radius: 8px;
  background-color: #fff;
  box-sizing: border-box;
}
.main-modal .modal-cotn {
  padding-bottom: 30px;
}
.main-modal * {
  letter-spacing: -0.3px;
}
.main-modal .md-top-lg {
  padding: 15px 0 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
.main-modal .modal-cotn .tit {
  font-size: 22px;
  font-weight: 700;
  line-height: 1;
}
.main-modal .modal-cotn .desc {
  margin: 16px 0 0;
}
.table-cmm.type-fare3 {
  border: none;
}
.table-cmm.type-fare3 thead th {
  font-size: 14px;
  font-weight: 500;
  color: #34446e;
  text-align: center;
  background: #f2f4f9;
  border-top: 2px solid #34446e;
  border-bottom: 1px solid #e2e4e8;
}
.table-cmm.type-fare3 tbody td {
  height: 50px;
  font-size: 14px;
  border-top: none;
  border-bottom: 1px solid #e2e4e8;
}
.main-modal .info-list li {
  font-weight: 400;
  line-height: 20px;
  color: #557ffe;
}
.main-modal .btn-confirm .btn-default {
  width: 150px;
  border: none;
  line-height: 40px;
}
.main-modal .modal-foot {
  overflow: hidden;
  padding: 20px 30px;
  border-top: 1px solid #e2e4e8;
}
.main-modal .modal-foot .form-chkbox {
  cursor: pointer;
}
.main-modal .modal-foot a.close-btn {
  padding-right: 21px;
  padding-bottom: 1px;
  background: url(../../assets/images/svg/btn_close_popup_s.svg) no-repeat center right/14px 14px;
}

/* DT-18341 - 20231107 */
.btn-u-inline {
  position: absolute;
  top: 20px;
  right: 20px;
  text-decoration: underline;
  font-size: 12px;
  font-weight: 400;
  line-height: normal;
  letter-spacing: -0.3px;
  color: #757f92;
}

.md-recent-card.nw-modal .modal-cotn {
  padding: 0 15px 30px 30px;
}
.md-recent-card .scrollbar-inner {
  max-height: 297px;
  overflow-y: auto;
  margin-right: 15px;
  overflow-x: hidden;
}
.md-recent-card .total {
  font-size: 15px;
  letter-spacing: -0.32px;
}
.md-recent-card .total > span {
  font-weight: 700;
  color: #4e81ff;
}
.md-recent-card .table-cmm.type-fare {
  border-top: 0 none;
  border-bottom: 0 none;
}
.md-recent-card .table-cmm.type-fare th {
  border-bottom: solid 2px #34446e;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}
.md-recent-card .table-cmm.type-fare th:first-child,
.md-recent-card .table-cmm.type-fare td:first-child {
  padding-left: 20px;
}
.md-recent-card .table-cmm.type-fare th:last-child,
.md-recent-card .table-cmm.type-fare td:last-child {
  padding-right: 20px;
}
.md-recent-card .table-cmm.type-fare td {
  height: 50px;
  font-size: 14px;
  border-bottom: 1px solid #e2e4e8;
}
.md-recent-card .btn-default.bg-blue {
  width: 60px;
  font-size: 14px;
  font-weight: 500;
  line-height: 32px;
  letter-spacing: -0.3px;
}

/* DT-24190 할인표기 */
.result-item .info-default .btn-add-compare .btn-default.btn-reserv {
  margin-bottom: 16px;
}
.price.sales .fare-detail {
  padding-left: 24px;
}
.price.sales .fare-detail dd {
  display: flex;
  justify-content: space-between;
  color: #757f92;
  font-size: 12px;
  line-height: 17px;
}
.price.sales .fare-detail dd + dd {
  margin-top: 5px;
}
.price.sales .fare-detail .tit,
.price.sales .fare-detail .sum {
  float: none;
}
.price.sales .fare-detail .tit .sale {
  color: #ff4350;
  margin-left: 2px;
}
.price.sales .fare-detail .sum {
  display: block;
}
.price.sales .fare-detail .sum .prev,
.price.sales .fare-detail .sum .result {
  display: block;
}
.price.sales .fare-detail .sum .del {
  text-decoration: line-through;
}
.price.sales .fare-detail .sum .result {
  margin-top: 2px;
  color: #ff4350;
}

.result-item .info-default .price .val.group {
  display: block;
  margin-top: 16px;
}
.result-item .info-default .price .val.group .prev,
.result-item .info-default .price .val.group .result {
  display: block;
  text-align: right;
}
.result-item .info-default .price .val.group .prev {
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  color: #757f92;
}
.result-item .info-default .price .val.group .del {
  text-decoration: line-through;
}
.result-item .info-default .price .val.group .result {
  margin-top: -2px;
  font-size: 22px;
  font-weight: 700;
  line-height: 32px;
  color: #333;
}

.table-cmm.type-fare tbody td .ticket-fee {
  display: block;
}
.table-cmm.type-fare tbody td .ticket-fee .prev {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.table-cmm.type-fare tbody td .ticket-fee .prev .sale {
  color: #ff4e50;
  font-size: 12px;
}
.table-cmm.type-fare tbody td .ticket-fee .prev .price {
  color: #757f92;
  text-decoration: line-through;
}
.table-cmm.type-fare tbody td .ticket-fee .result {
  margin-top: 4px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}
.table-cmm.type-fare tbody td .ticket-fee .result .price {
  color: #333;
}

/*  */

/* DT-29662 영수증 보기 팝업 */
#popReceipt .modal-head h2 {
  padding: 24px 30px 30px;
}
#popReceipt .modal-cotn {
  padding: 0 0 30px 30px;
}
#popReceipt .total {
  font-size: 15px;
  line-height: 22px;
  font-weight: 400;
  margin-bottom: 5px;
}
#popReceipt .total strong {
  font-weight: 700;
  color: #4e81ff;
}
#popReceipt .table-cmm.type-fare {
  margin-bottom: 40px;
  border-bottom: 0 none;
  border-top: 0 none;
  border-color: #3a4972;
}
#popReceipt .table-cmm.type-fare thead th {
  border-bottom: 2px solid #3a4972;
  text-align: center;
  color: #34446e;
}
#popReceipt .table-cmm.type-fare tbody td {
  border-bottom: 1px solid #e2e4e8;
  border-color: #e2e4e8;
  border-top: 0;
  height: 50px;
}
#popReceipt .btn-view-receipt {
  width: 60px;
  line-height: 32px;
  border-radius: 5px;
  background-color: #4e81ff;
  text-align: center;
  color: #ffffff;
}
#popReceipt .scrollbar-inner {
  max-height: 302px;
  overflow-y: auto;
  margin-right: 40px;
  overflow-x: hidden;
}
#popReceipt .slimScrollBar,
#popReceipt .slimScrollRail {
  right: 15px !important;
}
#popReceipt .close-modal {
  top: 22px;
  right: 22px;
}
