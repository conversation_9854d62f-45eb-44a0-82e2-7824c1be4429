@charset "utf-8";
/* RVYN-496 ---------- */
/* .foregin-mode .inner {display: flex;}
.foregin-mode .inner > div {flex-grow: 1;} */

.schedule-inquiry-mode-box {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  padding: 16px 22px;
  background-color: #fff;
  border-radius: 5px;
}
.schedule-inquiry-mode-box .left {
  flex-grow: 1;
  line-height: 19.24px;
  font-size: 13px;
  letter-spacing: -0.3px;
}
.schedule-inquiry-mode-box .left .tit {
  font-weight: 700;
}
.schedule-inquiry-mode-box .right .btn-mode-view {
  width: 165px;
  height: 40px;
  padding: 10px 0;
  color: #49b999;
  letter-spacing: -0.3px;
  background-color: #edfcf8;
  border: 1px solid #49b999;
  border-radius: 5px;
  cursor: pointer;
}

.desc-price-tit {
  font-size: 13px;
  font-weight: 700;
  letter-spacing: -0.3px;
}

.schedule-area + .schedule-area {
  margin-top: 40px;
}
.schedule-area .top {
  margin-bottom: 15px;
}
.schedule-area .top .title {
  float: left;
  margin-right: 20px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
  color: #4e81ff;
}
.schedule-area .top .title.black {
  color: #000;
}
.schedule-area .top .city {
  float: left;
}
.schedule-area .top .city span {
  float: left;
  font-size: 18px;
  line-height: 27px;
  font-weight: 700;
}
.schedule-area .top .city span:first-child {
  padding-right: 27px;
  margin-right: 8px;
  background: url(../../assets/images/search/ico_arrow_city_dome.png) 100% 50% no-repeat;
}
.schedule-area .table-cmm.type-schedul thead .box-in {
  font-size: 13px;
}
.schedule-area .table-cmm.type-schedul thead .box-in * {
  font-size: 13px;
}
.schedule-area .table-cmm.type-schedul tbody .box-in {
  border-top: 1px solid #e2e4e8;
  border-bottom: 1px solid #e2e4e8;
  font-size: 13px;
  background-color: transparent !important;
}
.schedule-area .table-cmm.fixed .fix-head {
  margin-bottom: 4px;
}
.schedule-area .table-cmm.fixed .fix-head.no-margin {
  margin-right: 0;
}
.schedule-area .table-cmm.fixed .fix-head.no-margin + .fix-body {
  padding-right: 0;
}
.schedule-area .table-cmm.type-schedule thead tr {
  border-radius: 5px;
}
.schedule-area .table-cmm.type-schedule thead th {
  overflow: hidden;
  background-image: none;
  border-bottom: 0;
}
.schedule-area .table-cmm.type-schedule thead th .box-in {
  background: #3f4e73;
}
.schedule-area .table-cmm.type-schedule thead th:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
}
.schedule-area .table-cmm.type-schedule thead th:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.schedule-area .table-cmm.type-schedule th:first-child .box-in {
  border-radius: 0;
}
.schedule-area .table-cmm.type-schedule th:first-child .box-in {
  border-radius: 0;
}
.schedule-area .slimScrollDiv {
  padding-right: 9px;
}
.schedule-area .slimScrollDiv .slimScrollBar {
  background-color: #9da9be !important;
  opacity: 1 !important;
  border-radius: 3px !important;
}
.schedule-area .slimScrollDiv .slimScrollRail {
  background-color: #e0e1e4 !important;
  border-radius: 3px !important;
}
.schedule-area .slimScrollDiv .slimScrollBar,
.schedule-area .slimScrollDiv .slimScrollRail {
  width: 3px !important;
  right: 0 !important;
}
.schedule-area.schedule-selected .table-cmm.fixed .fix-body {
  padding-right: 9px;
}
.schedule-area .table-cmm.fixed .fix-body table {
  border-collapse: separate;
  border-spacing: 0px 4px;
}
.schedule-area .table-cmm.type-schedule tbody td {
  height: 53px;
  font-size: 13px;
  background-color: #fff;
  background-image: none;
  border: 1px solid #e2e4e8;
  border-width: 1px 0;
}
.schedule-area .table-cmm.type-schedule tbody td:first-child {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  border-left: 1px solid #e2e4e8;
}
.schedule-area .table-cmm.type-schedule tbody td:last-child {
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  border-right: 1px solid #e2e4e8;
}
.schedule-area .table-cmm.type-schedule tbody td {
  border-bottom: 1px solid #e2e4e8;
}
.schedule-area .table-cmm.type-schedule tbody td .box-in {
  line-height: 18px;
  border: 0 none !important;
  background: none;
}
.schedule-area .table-cmm.type-schedule tbody .aircraft {
  position: relative;
  margin-left: 25px;
  padding-left: 17px;
  text-align: left;
}
.schedule-area .table-cmm.type-schedule tbody .aircraft img {
  position: absolute;
  top: 2px;
  left: 0;
  width: 13px;
  height: 13px;
}
.schedule-area .table-cmm.type-schedule tbody .aircraft .txt {
  display: block;
  max-width: 180px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.schedule-area .table-cmm.type-schedule tbody .aircraft.air-selected .txt {
  max-width: 120px;
}
.schedule-area .table-cmm.type-schedule tbody .aircraft .share {
  display: block;
  color: #9da9be;
  font-size: 11px;
  letter-spacing: -0.5px;
}
.schedule-area .table-cmm.type-schedule tbody .line-two.aircraft {
  padding-top: 8px;
  padding-bottom: 9px;
}
.schedule-area .table-cmm.type-schedule tbody .line-two.aircraft img {
  top: 11px;
}
.schedule-area .table-cmm.type-schedule tbody .line-two.aircraft .txt {
  line-height: 18px;
}
.schedule-area .table-cmm.type-schedule tbody .line-two.aircraft .share {
  line-height: 18px;
}
.schedule-area .table-cmm.type-schedule tbody tr {
  cursor: pointer;
}
.schedule-area:not(.schedule-selected) .table-cmm.type-schedule tbody tr:hover td {
  background-color: rgba(78, 129, 255, 0.07);
  border-color: #4e81ff !important;
}
.schedule-area .table-cmm.type-schedule tbody tr.active td {
  border-color: #4e81ff !important;
  background-color: #4e81ff;
}
.schedule-area .table-cmm.type-schedule tbody tr.active * {
  color: #fff !important;
}

.btn-schedule-select {
  width: 140px;
  height: 50px;
  padding: 13px 0;
  margin-top: 36px;
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  letter-spacing: -0.3px;
  background-color: #4e81ff;
  border-radius: 5px;
}
.btn-schedule-select.long {
  width: 280px;
}

.info-booking.new .plan {
  display: block;
}
.info-booking.new .plan .schdule .etc {
  top: -47px;
}
.info-booking.new .plan div.city {
  margin: 0;
}
.info-booking.new .plan div.time {
  margin: 0;
}

.step-reservation {
  padding-top: 34px;
  margin: 0 auto 30px;
  text-align: center;
}
.step-reservation ol {
  display: inline-block;
}
.step-reservation li {
  padding-right: 69px;
  margin-right: 31px;
  float: left;
  background: url(../../assets/images/cmm/icn_nextgray.png) no-repeat 100% 26px;
}
.step-reservation li:last-child {
  padding-right: 0;
  margin-right: 0;
  background: none !important;
}
.step-reservation li.active {
  background-image: url(../../assets/images/cmm/icn_nextblue.png);
}
.step-reservation li.active .num {
  border-color: #4e81ff;
  color: #4e81ff;
}
.step-reservation li.active .tit {
  font-weight: 500;
  color: #4e81ff;
}
.step-reservation .num {
  width: 58px;
  height: 58px;
  line-height: 58px;
  border: 1px solid #fff;
  border-radius: 100%;
  box-shadow: 0 2px 8px 0 rgba(226, 228, 232, 0.5);
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: #e2e4e8;
}
.step-reservation .tit {
  margin-top: 11px;
  line-height: 20px;
  text-align: center;
  font-weight: 300;
  color: #9da9be;
}

.schedule-area .sec-info-user .plan-info .tit {
  margin-bottom: 15px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
}
.schedule-area .sec-info-user .validate + .guide {
  padding-top: 0 !important;
}

.schedule-area .box-agree .form-chkbox span {
  font-size: 15px;
  letter-spacing: -0.3px;
}
.schedule-area .box-agree .form-chkbox span::after {
  top: 0;
}
.schedule-area .box-agree .etc {
  padding-left: 26px;
  margin-top: 6px;
  font-size: 13px;
  color: #4e81ff;
  line-height: 19.24px;
  letter-spacing: -0.26px;
}
.schedule-area .box-agree .etc.no-pad {
  padding-left: 0;
}
.schedule-area .box-col-area {
  padding: 26px 0 10px;
}

.schedule-area .box-accodion {
  position: relative;
}
.schedule-area .box-accodion + .box-line {
  margin-top: 20px;
}
.schedule-area .box-accodion .box-item {
  position: relative;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.schedule-area .box-accodion .tit {
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
}
.schedule-area .box-accodion .box-cotn {
  position: relative;
  display: none;
  margin-top: 24px;
}
.schedule-area .box-accodion .box-cotn .box-row-area {
  margin-top: 21px;
}
.schedule-area .box-accodion .box-cotn .box-row-area.detail {
  margin: 0;
}
.schedule-area .box-accodion .box-cotn .box-row-area.detail .box-row {
  width: 100%;
  max-width: none;
  margin: 0;
}
.schedule-area .box-accodion .box-cotn .box-row-area.detail .box-row .box-col {
  width: 25%;
  margin: 0;
  padding: 16px 19px;
  border: 1px solid #e2e4e8;
  border-width: 0 1px 1px 0;
}
.schedule-area .box-accodion .box-cotn .box-row-area.detail .box-row .box-col:first-child {
  border-left: 0;
}
.schedule-area .box-accodion .box-cotn .box-row-area.detail .box-row .box-col:last-child {
  width: 50%;
}

.schedule-area .box-accodion .box-cotn .box-row {
  display: flex;
  max-width: 740px;
  margin: 0 -9px;
}
.schedule-area .box-accodion .box-cotn .box-row.no-line .box-col .desc {
  border-bottom: 0;
}
.schedule-area .box-accodion .box-cotn .box-row.three .box-col {
  flex-grow: initial;
  width: calc(100% / 3 - 18px);
}
.schedule-area .box-accodion .box-cotn .box-row .box-col {
  flex-grow: 1;
  margin: 0 9px;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col dt {
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col .desc {
  border-bottom: 1px solid #e2e4e8;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col .desc .text {
  line-height: 34px;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col .guide {
  padding: 10px 0 0;
  font-size: 11px;
  line-height: 17px;
  color: #7f848d;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col .form-radio {
  line-height: 34px;
}
.schedule-area .box-accodion .box-cotn .box-row .box-col .form-radio + .form-radio {
  margin-left: 40px;
}
.schedule-area .box-accodion div.btn-arrow {
  position: absolute;
  top: 13px;
  right: 10px;
}
.schedule-area .box-accodion div.btn-arrow .btn-default {
  width: 40px;
  height: 40px;
  padding: 10px;
  background: url(../../assets/images/cmm/btn_arrow_acoodi_b.png) no-repeat 50% 50%;
}
.schedule-area .box-accodion div.btn-arrow .btn-default,
.schedule-area .box-accodion .box-item.active .box-cotn {
  display: block;
}
.schedule-area .box-accodion .box-item.active div.btn-arrow {
  transform: rotate(180deg);
}

.schedule-area .personal-info .search {
  overflow: hidden;
  padding-bottom: 6px;
  border-bottom: 2px solid #34446e;
}
.schedule-area .personal-info .search p {
  float: left;
  font-size: 16px;
  font-weight: 500;
  line-height: 34px;
}
.schedule-area .personal-info .search .btn-default {
  float: right;
  width: 95px;
  font-size: 13px;
  border-radius: 5px;
  background-color: #34446e;
  line-height: 34px;
  color: #fff;
  text-align: center;
}

.contents {
  padding-bottom: 99px;
}
.form-select select {
  text-align: left;
  box-sizing: border-box;
}
.form-select select option {
  text-align: initial;
}
.schedule-area .box-col input[type="text"],
.schedule-area .box-col input[type="password"],
.schedule-area .box-col .form-select {
  border-bottom: 0;
}
.form-date {
  display: flex;
}
.form-date > div {
  flex-grow: 1;
}
.form-date > div select {
  width: 100%;
}
.form-date > div select option {
  text-align: right;
}
.form-text textarea {
  width: 100%;
  line-height: 20px;
  padding: 16px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
  box-sizing: border-box;
}
.form-text textarea::placeholder {
  color: #babdc3;
  opacity: 1;
}

.schedule-area .box-line {
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.schedule-area .box-line .tit {
  font-size: 18px;
  font-weight: 500;
  line-height: 27px;
}
.schedule-area .box-line .box-cotn {
  margin-top: 24px;
}

.schedule-area .sec-resev-request {
  position: relative;
  min-height: 318px;
  padding-top: 41px;
  border-top: 1px solid #e2e4e8;
  background: url(../../assets/images/search/bg_request.png) no-repeat 95% 100%;
}
.schedule-area .sec-resev-request dt {
  margin-bottom: 12px;
  font-size: 26px;
  line-height: 38px;
}
.schedule-area .sec-resev-request dd {
  margin-bottom: 24px;
  font-size: 15px;
  font-weight: 300;
  line-height: 22px;
}
.schedule-area .sec-resev-request .code {
  font-size: 18px;
  line-height: 27px;
  font-weight: 300;
  color: #4e81ff;
}
.schedule-area .sec-resev-request .code strong {
  font-weight: 500;
}

.title-heading {
  margin-bottom: 34px;
  font-size: 26px;
  line-height: 38px;
  font-weight: normal;
}
.path {
  overflow: hidden;
  margin: 25px 0;
}
.path span {
  float: left;
  font-size: 12px;
  line-height: 18px;
  padding-right: 13px;
  margin-right: 8px;
  background: url(../../assets/images/cmm/icn_snavi_arrow.png) 100% 50% no-repeat;
}
.path span:last-child {
  background: none;
}
.path span.bold {
  font-weight: 500;
}

.comment-etc {
  font-size: 13px;
  line-height: 19px;
  letter-spacing: -0.26px;
  color: #757f92;
}
.comment-etc a {
  color: #4e81ff;
  border-bottom: 1px solid #4e81ff;
}
.comment-etc + .table-cmm {
  margin-top: 10px;
}

.schedule-detail {
  margin-top: 36px;
}
.schedule-area .table-cmm.type-fare {
  border-top: 0 none;
  border-bottom: 0 none;
}
.schedule-area .table-cmm.type-fare.new th {
  color: #34446e;
  border-bottom: solid 2px #34446e;
}
.schedule-area .table-cmm.type-fare.new td {
  padding: 0 5px;
  font-size: 14px;
  border-bottom: 1px solid #e2e4e8;
}
.schedule-area .table-cmm.type-fare.new td .text-over a {
  display: block;
  max-width: 670px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* //RVYN-496 ---------- */
