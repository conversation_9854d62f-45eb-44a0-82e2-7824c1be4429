input.upper {
  text-transform: uppercase;
}
.sec-info-user .agree-rule .box-wrap .desc {
  display: none;
  border-top: 1px solid #e2e4e8;
  padding: 14px 30px;
  line-height: 20px;
  color: #000000;
  font-size: 12px;
  font-weight: 400;
}
.line-table.first-table {
  margin-top: -37px;
}
.line-table,
.line-table a {
  font-size: 12px;
  font-weight: 400;
}
.line-table thead th,
.line-table tbody th,
.line-table tbody td {
  border-bottom: 1px solid #dddddd;
  text-align: left;
}
.borderR thead th,
.borderR tbody td {
  border: 1px solid #dddddd;
  padding: 5px 3px;
}
.borderR thead th {
  text-align: center;
}
.agree-rule a {
  color: inherit;
  text-decoration: underline;
}
.box-chk-info {
  padding-top: 20px;
  padding-bottom: 10px;
}

.document-add .btn-file-group {
  position: absolute;
  top: 20px;
  right: 20px;
}
.document-add .btn-file-group label {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  line-height: 17px;
  border: 1px solid #354e73;
  color: #354e73;
  width: 90px;
  height: 26px;
  box-sizing: border-box;
  cursor: pointer;
  border-radius: 2px;
}
.document-add .btn-file-group .btn-file {
  position: absolute;
  width: 0;
  height: 0;
  padding: 0;
  overflow: hidden;
  border: 0;
}
.document-add .upload-cotn {
  background-color: #f5f7fb;
  border-radius: 8px;
  margin-top: 24px;
}
.document-add .upload-cotn .box-default {
  width: 100%;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.document-add .upload-cotn .box-default p {
  font-size: 13px;
  line-height: 20px;
  font-weight: 400;
  color: #7f848d;
  letter-spacing: -0.28px;
}
.document-add .upload-cotn .box-default p strong {
  font: inherit;
  font-weight: 700;
}
.document-add .upload-cotn .box-default.d-none,
.document-add .upload-cotn .box-upload.d-none {
  display: none;
}
.document-add .upload-cotn .box-upload {
  padding: 8px 16px;
}
.document-add .upload-cotn .box-upload .file-list {
}
.document-add .upload-cotn .box-upload .file-list.scrollbar-inner {
  max-height: 112px;
  padding: 0 18px 0 0;
}
.document-add .upload-cotn .box-upload .file-list li {
  padding: 8px 4px 8px 0;
  border-bottom: 1px solid rgba(141, 145, 153, 0.5);
  display: flex;
  width: 100%;
  height: 26px;
  justify-content: space-between;
  align-items: center;
}
.document-add .upload-cotn .box-upload .file-list li:first-child {
  padding-top: 0;
}
.document-add .upload-cotn .box-upload .file-list li:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}
.document-add .upload-cotn .box-upload .file-list .file-name {
  font-size: 12px;
  font-weight: 400;
  line-height: 17px;
  color: #000;
  letter-spacing: -0.3px;
}
.document-add .upload-cotn .box-upload .file-list .btn-del-file {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  line-height: 17px;
  border: 1px solid #354e73;
  color: #354e73;
  width: 90px;
  height: 26px;
  box-sizing: border-box;
  cursor: pointer;
  border-radius: 2px;
}
