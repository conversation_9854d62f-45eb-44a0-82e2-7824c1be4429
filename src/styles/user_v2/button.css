.custom-button:disabled {
  cursor: not-allowed;
  /* color: #d7d9de; */
}

.custom-button.opacity-disabled:disabled svg path {
  opacity: 0.5;
}

/* BTN WHITE */
.custom-button.btn-white {
  background-color: #ffffff;
  color: #3c4049;
}

.custom-button.btn-white:hover {
  background-color: #f2f2f4;
}

.custom-button.btn-white:active {
  background-color: #eeeef0 !important;
}

.custom-button.btn-white:disabled {
  color: #d7d9de;
  background-color: #ffffff;
}

.custom-button.btn-white:not(.opacity-disabled):disabled svg path {
  fill: #d7d9de;
}

/* BTN BORDER */
.custom-button.btn-border {
  border-width: 1px;
  border: 1px solid #e9eaed;
  background-color: #ffffff;
}

.custom-button.btn-border.active {
  border-color: #4a77e6 !important;
  color: #4a77e6 !important;
}

.custom-button.btn-border:hover {
  background-color: #f2f2f4;
}

.custom-button.btn-border:active {
  background-color: #eeeef0;
}

.custom-button.btn-border:disabled {
  color: #d7d9de !important;
  background-color: #ffffff !important;
  border-color: #e9eaed !important;
}

.custom-button.btn-border:disabled > * {
  color: #d7d9de !important;
}

.custom-button.btn-border:not(.opacity-disabled):disabled svg path {
  fill: #d7d9de;
}

.custom-button.btn-border.selected {
  background-color: #ffffff;
  color: #4e81ff;
  border-color: #4e81ff;
}

/* BTN PRIMARY */
.custom-button.btn-primary {
  background-color: #4e81ff;
  color: #ffffff;
}

.custom-button.btn-primary:hover {
  color: #ffffffcc;
}

.custom-button.btn-primary:hover svg path {
  fill: #ffffffcc;
}

.custom-button.btn-primary:active {
  color: #ffffff99;
}

.custom-button.btn-primary:disabled {
  color: #ffffff99;
  background-color: #d7d9de;
}

.custom-button.btn-primary:not(.opacity-disabled):disabled svg path {
  fill: #ffffff99;
}

/* BTN SECONDARY */
.custom-button.btn-secondary {
  background-color: #edf2ff;
  color: #4e81ff;
}

.custom-button.btn-secondary:hover {
  color: #719aff !important;
}

.custom-button.btn-secondary:active {
  color: #4a77e6 !important;
}

.custom-button.btn-secondary:disabled {
  color: #d7d9de;
  background-color: #eeeef0;
}

.custom-button.btn-secondary:not(.opacity-disabled):disabled svg path {
  fill: #d7d9de;
}

/* BTN THIRD */
.custom-button.btn-third {
  background-color: #eeeef0;
  color: #3c4049;
}

.custom-button.btn-third:hover {
  background-color: #e9eaed;
}

.custom-button.btn-third:active {
  background-color: #d7d9de;
}

.custom-button.btn-third:disabled {
  color: #d7d9de;
  background-color: #eeeef0;
}

.custom-button.btn-third:not(.opacity-disabled):disabled svg path {
  fill: #d7d9de;
}

/* BTN TEXT */
.custom-button.btn-text {
  height: 32px !important;
  padding-right: 4px;
  padding-left: 8px;
  color: #6e7587;
}

.custom-button.btn-text:hover {
  background-color: #f8f9f9;
}

.custom-button.btn-text:active {
  background-color: #f4f4f5;
}

.custom-button.btn-text:disabled {
  opacity: 0.5;
}

/* BTN ICON */
.custom-button.btn-icon {
  background-color: #ffffff;
  padding: 0px !important;
}

.custom-button.btn-icon:hover {
  background-color: #eeeef0;
}

.custom-button.btn-icon:active {
  background-color: #eaeaec !important;
}

.custom-button.btn-icon:disabled {
  color: #d7d9de;
  background-color: #ffffff;
}

.custom-button.btn-icon:not(.opacity-disabled):disabled svg path {
  fill: #d7d9de;
}
