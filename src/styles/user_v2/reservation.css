﻿@charset "utf-8";

/* 예약 요청 */
.pg-reserv {
  position: relative;
}
.pg-reserv .contents .left-side {
  float: left;
  width: 780px;
}
.pg-reserv .contents .right-side {
  float: right;
  width: 280px;
}
.pg-reserv .box-line {
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.pg-reserv .box-line h3.tit {
  font-size: 18px;
  line-height: 20px;
  margin-bottom: 24px;
}
.pg-reserv .step-reservation {
  position: relative;
  overflow: hidden;
  width: 480px;
  padding-top: 34px;
  margin: 0 auto 30px;
}
.pg-reserv .step-reservation li {
  padding-right: 69px;
  margin-right: 31px;
  float: left;
  background: url(../../assets/images/cmm/icn_nextgray.png) no-repeat 100% 26px;
}
.pg-reserv .step-reservation li:last-child {
  padding-right: 0;
  margin-right: 0;
  background: none !important;
}
.pg-reserv .step-reservation li.active {
  background-image: url(../../assets/images/cmm/icn_nextblue.png);
}
.pg-reserv .step-reservation li.active .num {
  border-color: #4e81ff;
  color: #4e81ff;
}
.pg-reserv .step-reservation li.active .tit {
  font-weight: 500;
  color: #4e81ff;
}
.pg-reserv .step-reservation .num {
  width: 58px;
  height: 58px;
  line-height: 58px;
  border: 1px solid #fff;
  border-radius: 100%;
  box-shadow: 0 2px 8px 0 rgba(226, 228, 232, 0.5);
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: #e2e4e8;
}
.pg-reserv .step-reservation .tit {
  margin-top: 11px;
  line-height: 20px;
  text-align: center;
  font-weight: 300;
  color: #9da9be;
}

.sec-pay-to .box-line {
  padding: 22px;
  margin-bottom: 15px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.sec-pay-to .box-line .txt-big {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
  letter-spacing: -1px;
}
.sec-pay-to .box-line .txt-big .fl-l {
  float: left;
}
.sec-pay-to .box-line .txt-big .fl-r {
  float: right;
}
.sec-pay-to .box-line .txt-big .c-price {
  font-size: 22px;
}
.sec-pay-to .box-line .txt-big .c-price strong {
  color: #4e81ff;
}
.sec-pay-to .fare-detail {
  position: relative;
  padding: 17px 0 5px;
}
.sec-pay-to .fare-detail dt {
  padding-bottom: 16px;
  margin-bottom: 5px;
  font-weight: 700;
  line-height: 20px;
  border-bottom: 1px solid #e2e4e8;
}
.sec-pay-to .fare-detail dd {
  overflow: hidden;
  padding-top: 7px;
  font-size: 13px;
  line-height: 19px;
  color: #3f4e73;
}
.sec-pay-to .fare-detail .tit {
  float: left;
}
.sec-pay-to .fare-detail .sum {
  float: right;
}
.sec-pay-to .brakedown {
  position: relative;
  margin-bottom: 12px;
}
.sec-pay-to .brakedown > li {
  position: relative;
  padding: 7px 0 0 14px;
}
.sec-pay-to .brakedown > li:first-child {
  padding-top: 0;
}
.sec-pay-to .brakedown > li::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  width: 8px;
  height: 8px;
  margin-top: -4px;
  background-color: #ff4e50;
  border-radius: 8px;
  font-size: 13px;
  line-height: 19px;
}
.sec-pay-to .brakedown > li span {
  font-weight: 700;
  text-decoration: underline;
}
.sec-pay-to .opinion-insert {
  border: 1px solid #e2e4e8;
  border-radius: 3px;
}
.sec-pay-to .opinion-insert textarea {
  border: 0 none;
  background-color: #fff;
  padding: 12px;
  width: 100%;
  height: 157px;
  box-sizing: border-box;
  font-size: 13px;
}
.sec-pay-to .opinion-insert textarea::placeholder {
  color: #babdc3;
}
.sec-pay-to .box-applys {
  padding: 5px 0 42px;
}
.sec-pay-to .box-applys .btn-default {
  width: 100%;
  margin-bottom: 12px;
  line-height: 48px;
  border-radius: 5px;
  border: 1px solid #4e81ff;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
}
.sec-pay-to .box-applys .btn-request {
  background-color: #4e81ff;
  color: #fff;
}
.sec-pay-to .box-applys .btn-compare {
  background-color: #fff;
  color: #4e81ff;
}

/* RVYN-590 */
.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.box-tit {
  position: relative;
  overflow: hidden;
  padding: 18px 21px;
  margin-bottom: 20px;
  border-radius: 5px;
  background-color: #f1f5ff;
}
.box-tit::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 5px;
  background-color: #4e81ff;
}
.box-tit span,
.box-tit dt {
  margin-bottom: 6px;
  font-size: 20px;
  font-weight: 700;
  line-height: 29px;
  color: #4e81ff;
}
.box-tit p,
.box-tit dd {
  font-size: 13px;
  line-height: 19px;
  color: #596a92;
}
.box-tit .flex-wrap .low {
  display: inline-block;
  margin-right: 25px;
  font-size: 15px;
  color: #ff4e50;
}

.sec-info-user {
  position: relative;
}
.sec-info-user .validate + .guide {
  padding-top: 0 !important;
}

.sec-info-user .box-tit {
  position: relative;
  overflow: hidden;
  padding: 18px 0 18px 21px;
  margin-bottom: 20px;
  border-radius: 5px;
  background-color: #f1f5ff;
}
.sec-info-user .box-tit::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 5px;
  background-color: #4e81ff;
}
.sec-info-user .box-tit dt {
  margin-bottom: 6px;
  font-size: 20px;
  font-weight: 700;
  line-height: 29px;
  color: #4e81ff;
}
.sec-info-user .box-tit dd {
  font-size: 13px;
  line-height: 19px;
  color: #596a92;
}

.sec-info-user .box-accodion {
  position: relative;
  padding-bottom: 70px;
}
.sec-info-user .box-accodion .box-item {
  position: relative;
  padding: 20px;
  border: 1px solid #e2e4e8;
  border-radius: 5px;
}
.sec-info-user .box-accodion .tit {
  font-size: 18px;
  font-weight: 700;
  line-height: 27px;
}
.sec-info-user .box-accodion .box-cotn {
  position: relative;
  display: none;
  margin-top: 24px;
}
.sec-info-user .box-accodion div.btn-arrow {
  position: absolute;
  top: 13px;
  right: 10px;
}
.sec-info-user .box-accodion div.btn-arrow .btn-default {
  width: 40px;
  height: 40px;
  padding: 10px;
  background: url(../../assets/images/cmm/btn_arrow_acoodi_b.png) no-repeat 50% 50%;
}
.sec-info-user .box-accodion div.btn-arrow .btn-default,
.sec-info-user .box-accodion .box-item.active .box-cotn {
  display: block;
}
.sec-info-user .box-accodion .box-item.active div.btn-arrow {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.sec-info-user .result-item {
  border: 0 none;
  padding: 0;
  box-shadow: none;
}
.sec-info-user .result-item .info-booking {
  border-top: 0 none;
}
.sec-info-user .result-item .info-booking .plan {
  padding: 0;
}
.sec-info-user .result-item.active .info-booking .plan {
  display: block;
}
.sec-info-user .result-item .info-booking dd:last-child .schdule:last-child {
  margin-bottom: 0;
}
.sec-info-user .result-item .info-booking .schdule .time-plan {
  color: #4e81ff;
}

.sec-info-user .personal-info .box-cotn {
  overflow: hidden;
}
.sec-info-user .personal-info .box-col {
  float: left;
  margin-left: 20px;
}
.sec-info-user .personal-info .box-col:first-child {
  margin-left: 0 !important;
}
.sec-info-user .personal-info .box-col.name {
  width: 200px;
  margin-left: 0;
}
.sec-info-user .personal-info .box-col.num {
  width: 254px;
}
.sec-info-user .personal-info .box-col.email {
  width: 244px;
}
.sec-info-user .personal-info .box-col dt {
  margin-bottom: 10px;
  font-size: 13px;
  line-height: 19px;
  color: #757f92;
}
/*.sec-info-user .personal-info .box-col .desc{ padding-bottom: 7px; border-bottom: 1px solid #e2e4e8; font-size: 15px; line-height: 22px; }*/
/*.sec-info-user .personal-info .box-col .desc input{ width: 100%; height: 22px;  border: 0 none; padding: 0; }*/
.sec-info-user .personal-info .box-col .guide {
  padding: 10px 0 0;
  font-size: 11px;
  line-height: 17px;
  color: #7f848d;
}
.sec-info-user .personal-info .agree {
  margin-bottom: 6px;
  font-size: 15px;
  line-height: 22px;
}
.sec-info-user .personal-info .passgner .caution-america {
  margin-bottom: 26px;
  padding-left: 27px;
  font-size: 13px;
  line-height: 19px;
  color: #4e81ff;
}
.sec-info-user .personal-info .search {
  overflow: hidden;
  padding-bottom: 6px;
  border-bottom: 2px solid #34446e;
}
.sec-info-user .personal-info .search p {
  float: left;
  font-size: 16px;
  font-weight: 500;
  line-height: 34px;
}
.sec-info-user .personal-info .search .btn-default {
  float: right;
  width: 95px;
  font-size: 13px;
  border-radius: 5px;
  background-color: #34446e;
  line-height: 34px;
  color: #fff;
  text-align: center;
}
.sec-info-user .personal-info .passgner .box-col {
  width: 233px;
  padding-top: 22px;
}
.sec-info-user .personal-info .passgner .box-col:first-child {
  width: 231px;
}
.sec-info-user .personal-info .passgner .col-half .box-col {
  width: 359px;
}
.sec-info-user .personal-info .passgner .col-half .box-col .validate {
  min-height: auto;
}
.sec-info-user .personal-info .passgner .desc {
}
.sec-info-user .personal-info .passgner .validate {
}
.sec-info-user .personal-info .passgner .guide {
}
.sec-info-user .personal-info .passgner .names .validate {
  min-height: 34px;
}
.sec-info-user .personal-info .passgner .box-ymd.desc,
.sec-info-user .personal-info .passgner .privacy .desc {
  padding-bottom: 7px;
  border-bottom: 1px solid #e2e4e8;
  font-size: 15px;
  line-height: 22px;
}
/*.sec-info-user .personal-info .passgner .privacy .desc input{ width: 100%; height: 22px;  border: 0 none; padding: 0; }*/
.sec-info-user .personal-info .form-radio {
  margin-right: 40px;
}
.sec-info-user .personal-info .box-ymd .yy {
  margin-right: 15px;
}
.sec-info-user .personal-info .box-ymd .yy select {
  width: 52px;
}
.sec-info-user .personal-info .box-ymd .mm {
  margin-right: 15px;
}
.sec-info-user .personal-info .box-ymd .mm select {
  width: 37px;
}
.sec-info-user .personal-info .box-ymd .dd select {
  width: 37px;
}

.sec-info-user .agree-rule {
  position: relative;
}
.sec-info-user .agree-rule .all {
  border-bottom: 2px solid #34446e;
  padding-bottom: 19px;
  font-size: 15px;
  line-height: 22px;
}
.sec-info-user .agree-rule .form-chkbox span {
  padding-left: 32px;
}
.sec-info-user .agree-rule .box-wrap {
  border-bottom: 1px solid #e2e4e8;
}
.sec-info-user .agree-rule .box-wrap dl {
  position: relative;
}
.sec-info-user .agree-rule .box-wrap dt {
  padding: 17px 0;
  border-top: 1px solid #e2e4e8;
  line-height: 20px;
}
.sec-info-user .agree-rule .box-wrap dl:first-child dt {
  border-top: 0 none;
}
.sec-info-user .agree-rule .box-wrap .desc {
  display: none;
  border-top: 1px solid #e2e4e8;
  padding: 14px 30px;
  line-height: 20px;
  color: #babdc3;
}
.sec-info-user .agree-rule .box-wrap div {
  overflow: auto;
  height: 120px;
  padding-right: 20px;
  white-space: pre-line;
}
.sec-info-user .agree-rule .box-wrap .btn-arrow {
  position: absolute;
  top: 5px;
  right: 0;
}
.sec-info-user .agree-rule .box-wrap .btn-default {
  width: 42px;
  height: 42px;
  background: url(../../assets/images/cmm/btn_arrow_acoodi_s.png) no-repeat 50% 50%;
}
.sec-info-user .agree-rule .box-wrap .active .desc {
  display: block;
}
.sec-info-user .agree-rule .box-wrap .active .btn-default {
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}

/* 국내 > 출장자 정보 > 여정정보 */

.sec-info-user .caution-id {
  margin-bottom: 20px;
  font-size: 13px;
  line-height: 19px;
  color: #4e81ff;
}
.sec-info-user .plan-info {
  position: relative;
}
.sec-info-user .plan-info .tit {
  margin-bottom: 15px;
  font-size: 18px;
  line-height: 27px;
  font-weight: 500;
}
.sec-info-user .plan-info .box-agree {
  margin-bottom: 44px;
}
.sec-info-user .plan-info .box-agree .form-chkbox span::after {
  top: 1px;
}
.sec-info-user .plan-info .box-agree .validate {
  margin-left: 26px;
  font-size: 13px;
}

.sec-resev-request {
  position: relative;
  min-height: 318px;
  padding-top: 41px;
  border-top: 1px solid #e2e4e8;
  background: url(../../assets/images/search/bg_request.png) no-repeat 95% 100%;
}
.sec-resev-request dt {
  margin-bottom: 12px;
  font-size: 26px;
  line-height: 38px;
}
.sec-resev-request dd {
  margin-bottom: 24px;
  font-size: 15px;
  font-weight: 300;
  line-height: 22px;
}
.sec-resev-request .code {
  font-size: 18px;
  line-height: 27px;
  font-weight: 300;
  color: #4e81ff;
}
.sec-resev-request .code strong {
  font-weight: 500;
}

.sec-info-pay .box-tit.type-cmm {
  margin-bottom: 30px;
}
.sec-info-pay .box-tit.type-cmm .c-default {
  color: #34446e;
  display: inline-block;
}
.sec-info-pay .box-tit.type-cmm .c-blue {
  color: #4e81ff;
}
.sec-info-pay .box-tit.type-cmm dt .c-default {
  margin-right: 15px;
}
.sec-info-pay .box-guide {
  margin-bottom: 30px;
}

.sec-info-pay .card-insert {
  position: relative;
}
.sec-info-pay .card-insert dt {
  font-size: 13px;
  line-height: 40px;
  color: #757f92;
}
.sec-info-pay .card-insert .box-top {
  margin-bottom: 22px;
}
.sec-info-pay .card-insert .col-01 {
  overflow: hidden;
  margin-bottom: 30px;
}
.sec-info-pay .card-insert .col-01 dt {
  float: left;
  width: 80px;
}
.sec-info-pay .card-insert .col-01 dd {
  float: left;
}
.sec-info-pay .card-insert .col-01 .btn-default {
  float: left;
  width: 138px;
  text-indent: 16px;
  line-height: 38px;
  border: 1px solid #757f92;
  color: #757f92;
}
.sec-info-pay .card-insert .col-01 .btn-default.my {
  border-radius: 5px;
  margin-right: 10px;
}
.sec-info-pay .card-insert .col-01 .btn-default.my-comp {
  border-radius: 5px 0 0 5px;
  border-right: 0 none;
}
.sec-info-pay .card-insert .col-01 .btn-default.comp {
  border-radius: 0 5px 5px 0;
}
.sec-info-pay .card-insert .col-01 .btn-default.active {
  color: #4e81ff;
  border-color: #4e81ff;
  background: rgba(78, 129, 255, 0.07) url(../../assets/images/cmm/icn_agree_check.png) 115px 50% no-repeat;
}
.sec-info-pay .card-insert .box-col {
  float: left;
  margin-left: 24px;
  position: relative;
  width: 230px;
}
.sec-info-pay .card-insert .box-col:first-child {
  margin-left: 0;
}
.sec-info-pay .card-insert .box-col dt {
  margin-bottom: 5px;
  line-height: 19px;
}
.sec-info-pay .card-insert .box-col input {
  width: 100%;
  font-size: 15px;
}
.sec-info-pay .card-insert .box-col input::-ms-input-placeholder {
  color: #babdc3;
}
.sec-info-pay .card-insert .box-col input::-webkit-input-placeholder {
  color: #babdc3;
}
.sec-info-pay .card-insert .box-col input::-moz-placeholder {
  color: #babdc3;
}
.sec-info-pay .card-insert .box-col .form-select {
  border-bottom: 1px solid #e2e4e8;
}
.sec-info-pay .card-insert .box-col select {
  height: 35px;
}
.sec-info-pay .card-insert .box-col .validate {
  letter-spacing: -0.5px;
}
.sec-info-pay .agree {
  padding-bottom: 100px;
}
.sec-info-pay .agree .validate {
  margin-left: 27px;
}

/* DT-24190 할인표기 */
.sec-pay-to .box-line .txt-big .c-price.del {
  font-size: 14px;
  letter-spacing: -0.3px;
  color: #757f92;
  font-weight: 500;
  line-height: 20px;
  margin-top: 3px;
}
.sec-pay-to .box-line .txt-big .c-price.del strong {
  text-decoration: line-through;
  color: #757f92;
}
.sec-pay-to .box-line .txt-big .c-price.result {
  margin-top: -2px;
}
.sec-pay-to .fare-detail.sales dd {
  display: flex;
  justify-content: space-between;
}
.sec-pay-to .fare-detail.sales .tit .sale {
  margin-left: 2px;
  color: #ff4e50;
}
.sec-pay-to .fare-detail.sales .sum {
  display: block;
}
.sec-pay-to .fare-detail.sales .prev,
.sec-pay-to .fare-detail.sales .result {
  display: block;
}
.sec-pay-to .fare-detail.sales .del {
  text-decoration: line-through;
}
.sec-pay-to .fare-detail.sales .result {
  color: #ff4e50;
}
.sec-info-user .box-accodion .box-item + .box-item {
  margin-top: 20px;
}
.sec-info-pay .card-insert .comp .box-col {
  width: calc(50% - 24px);
}
