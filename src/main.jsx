import ReactDOM from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import Routers from "@/app/routers";
import { Provider } from "react-redux";
import { store, persistor } from "@/store";
import { PersistGate } from "redux-persist/integration/react";
import { APIProvider } from "@vis.gl/react-google-maps";

ReactDOM.createRoot(document.querySelector("body")).render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <BrowserRouter>
        <APIProvider apiKey={import.meta.env.VITE_GOOGLE_API_KEY} libraries={["marker"]}>
          <Routers />
        </APIProvider>
      </BrowserRouter>
    </PersistGate>
  </Provider>,
);
