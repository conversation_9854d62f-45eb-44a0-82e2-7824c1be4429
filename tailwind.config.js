/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        "custom-gray-100": "#E9EAED",
        "custom-gray-200": "#ECEDEF",
        "custom-gray-300": "#3C4049",
        "custom-gray-400": "#F6F9FF",
        "custom-gray-500": "#6E7587",
        "custom-gray-600": "#292C32",
        "custom-gray-700": "#F8F9F9",
        "custom-gray-800": "#242A37CC",
        "custom-gray-900": "#FFFFFF99",
        "custom-gray-1000": "#AFB3BD",
        "custom-gray-1100": "#F2F2F4",
        "custom-gray-1200": "#EEEEF0",
        "custom-gray-1300": "#F4F4F5",
        "custom-blue-100": "#4E81FF",
        "custom-bg-100": "#242A3708",
        "custom-bg-200": "#ECFBF4",
        "custom-bg-300": "#EDF2FF",
        "custom-bg-400": "#D7D9DE",
        "custom-green-100": "#00AE8B",
        "custom-red-100": "#FF4E50",
        "custom-red-200": "#FFF6F6",
      },
      boxShadow: {
        "custom-100": "0px 4px 8px 0px #292C3229",
        "custom-200": "0px 4px 16px 0px #00000040",
        "custom-300": "2px 2px 4px 0px #292C3229",
        "custom-400": "0 2px 8px 0 rgba(157, 169, 190, 0.15)",
        "custom-500": "0px 1px 0px 0px #E9EAED inset",
        "custom-600": "0px 4px 4px 0px #292C3233",
      },
    },
  },
  plugins: [],
};
