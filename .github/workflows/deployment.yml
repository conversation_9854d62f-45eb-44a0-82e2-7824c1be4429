# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: deploy

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]
    types: [synchronize]

jobs:
  deploy:
    name: deploy
    runs-on: hn-btms

    steps:
      - name: Install dependencies
        run: |
          cd ~/hn-btms-user
          git reset --hard HEAD
          git checkout main
          git pull origin main
          yarn
          yarn build
      - name: Copy to webapps
        run: |
          cp ~/hn-btms-user/dist/index.html /app/servers/BTMS-front/webapps/ROOT/WEB-INF/hbs/react-user.hbs
          rm -r /app/servers/BTMS-front/webapps/ROOT/static/user/react
          cp -r ~/hn-btms-user/dist/static/user/react /app/servers/BTMS-front/webapps/ROOT/static/user
      - name: Update backend project
        run: |
          cd ~/hn-btms
          git reset --hard HEAD
          git checkout staging
          git pull origin staging
          rm -r src/main/webapp/static/user/react
          cp ~/hn-btms-user/dist/index.html src/main/webapp/WEB-INF/hbs/react-user.hbs
          cp -r ~/hn-btms-user/dist/static/user/react src/main/webapp/static/user
          git add .
          git diff-index --quiet HEAD || git commit -m "[UPDATE] code deploy hn-btms-user [skip ci]"
          git push origin staging
